package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWorksheetitemdetailPojo;
import inks.service.std.manu.domain.pojo.WkWorksheetmatPojo;
import inks.service.std.manu.domain.pojo.WkWorksheetmergePojo;

import java.util.List;

/**
 * 厂制物料(WkWorksheetmat)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-09 10:33:43
 */
public interface WkWorksheetmatService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetmatPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWorksheetmatPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWorksheetmatPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkWorksheetmatPojo 实例对象
     * @return 实例对象
     */
    WkWorksheetmatPojo insert(WkWorksheetmatPojo wkWorksheetmatPojo);

    /**
     * 修改数据
     *
     * @param wkWorksheetmatpojo 实例对象
     * @return 实例对象
     */
    WkWorksheetmatPojo update(WkWorksheetmatPojo wkWorksheetmatpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkWorksheetmatpojo 实例对象
     * @return 实例对象
     */
    WkWorksheetmatPojo clearNull(WkWorksheetmatPojo wkWorksheetmatpojo);

    void mergeItem(List<WkWorksheetitemdetailPojo> lstitem, LoginUser loginUser);

    List<WkWorksheetmergePojo> getMergeListByItemid(String key, String tenantid);

    List<WkWorksheetmatPojo> getMatListByItemIds(List<String> itemIdList, String tenantid);
}
