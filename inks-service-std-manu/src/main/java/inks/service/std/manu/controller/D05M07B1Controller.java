package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.api.feign.QmsFeignService;
import inks.api.feign.StoreFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.SnowflakeByDate;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.exception.WipNoneProcessException;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.mapper.WkWipqtyMapper;
import inks.service.std.manu.service.WkWipqtyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 生产过数(Wk_WipQty)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:51:53
 */
@RestController
@RequestMapping("D05M07B1")
@Api(tags = "D05M07B1:生产过数")
public class D05M07B1Controller extends WkWipqtyController {
    @Resource
    private StoreFeignService storeFeignService;
    /**
     * 服务对象
     */
    @Resource
    private WkWipqtyService wkWipqtyService;
    @Resource
    private WkWipqtyMapper wkWipqtyMapper;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 引用FeignService服务
     */
    @Resource
    private UtilsFeignService utilsFeignService;
    @Resource
    private QmsFeignService qmsFeignService;
    @Resource
    private RedisTemplate redisTemplate;

    /**
     * @return R<String>
     * @Description uuid转为雪花id
     * <AUTHOR>
     * @param[1] table 表名
     * @time 2023/4/6 14:11
     */
    @ApiOperation(value = "复制id的值到Custom5字段", notes = "复制id的值到Custom5字段", produces = "application/json")
    @RequestMapping(value = "/copyIdToCustom", method = RequestMethod.GET)
    public R<String> copyIdToCustom(String table) {
        try {
            // 复制id的值到Custom1字段
            wkWipqtyService.copyIdToCustom(table);
            return R.ok("ok");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<String>
     * @Description uuid转为雪花id
     * <AUTHOR>
     * @param[1] table 表名
     * @time 2023/4/6 14:11
     */
    @ApiOperation(value = "uuid转为雪花id", notes = "uuid转为雪花id,降序ordertype=desc", produces = "application/json")
    @RequestMapping(value = "/uuidToSnowflakeId", method = RequestMethod.GET)
    public R<String> uuidToSnowflakeId(String table, String ordertype) {
        try {

            // 复制id的值到Custom1字段
            wkWipqtyService.copyIdToCustom(table);

            if (ordertype == null) ordertype = "";
            // 获取该表的id,CreateDate,Custom1
            List<Map<String, Object>> maps = wkWipqtyService.getidAndDateAndCus(table, ordertype);
            AtomicReference<Integer> num = new AtomicReference<>(0);
            maps.forEach(map -> {
                num.getAndSet(num.get() + 1);
                String id = map.get("id").toString();
                String createDate = map.get("CreateDate").toString();
                String custom1 = map.get("Custom5").toString();
                String newSnowflakeId = SnowflakeByDate.getSnowflake().nextIdStr(DateUtils.getTimestamp(DateUtils.parseDate(createDate)));
                System.out.print("no." + num + " \u001B[31m" + "CreateDate = " + createDate + "\u001B[0m" + "\u001B[32m" + "Custom5 = " + custom1 + "\u001B[0m" + "\u001B[34m" + "newSnowflakeId = " + newSnowflakeId + "\u001B[0m");
                // uuid更新为雪花id
                wkWipqtyService.updateSnowflakeId(table, newSnowflakeId, custom1);
            });
            return R.ok("ok");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    /**
//     * @return R<String>
//     * @Description uuid转为雪花id
//     * <AUTHOR>
//     * @param[1] table 表名
//     * @time 2023/4/6 14:11
//     */
//    @ApiOperation(value = "复制CiteItemid的值到Custom2字段", notes = "复制CiteItemid的值到Custom2字段", produces = "application/json")
//    @RequestMapping(value = "/copyCiteItemidToCustom", method = RequestMethod.GET)
//    public R<String> copyCiteItemidToCustom(String tgtable, String tgcolumn) {
//        try {
//            // 复制id的值到Custom1字段
//            wkWipqtyService.copyCiteItemidToCustom(tgtable, tgcolumn);
//            return R.ok("ok");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

    /**
     * @return R<String>
     * @Description uuid转为雪花id
     * <AUTHOR>
     * @param[1] table 表名
     * @time 2023/4/6 14:11
     */
    @ApiOperation(value = "更新引用id", notes = "更新引用id,tgtable目标表，tgcolumn citeitem目标字段，orgtable原始id的表格", produces = "application/json")
    @RequestMapping(value = "/updateCiteItemid", method = RequestMethod.GET)
    public R<String> updateCiteItemid(String tgtable, String tgcolumn, String orgtable) {
        try {

            // 复制citeitemid的值到Custom2字段
            wkWipqtyService.copyCiteItemidToCustom(tgtable, tgcolumn);

            // 复制id的值到Custom1字段
            wkWipqtyService.updateCiteItemid(tgtable, tgcolumn, orgtable);
            return R.ok("ok");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询 wpid为工序 dir=in 入组 out 出组", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<PageInfo<WkWipqtyPojo>> getPageList(@RequestBody String json, String wpid, String dir) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQty.CreateDate");

            String qpfilter = "";
            if (wpid != null) {
                qpfilter += " and Wk_WipQty.wpid='" + wpid + "'";
            }
            if (dir != null) {
                if (dir.equals("in")) {
                    qpfilter += " and Wk_WipQty.direction='入组'";
                } else {
                    qpfilter += " and Wk_WipQty.direction='出组'";
                }
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWipqtyService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "增减料：按条件分页查询", notes = "按条件分页查询 wpid为工序 dir=in 入组 out 出组", produces = "application/json")
    @RequestMapping(value = "/getStorePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<PageInfo<WkWipqtyPojo>> getStorePageList(@RequestBody String json, String wpid, String dir) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQty.CreateDate");

            String qpfilter = " and Wk_WipQty.StoreMark=1";
            if (wpid != null) {
                qpfilter += " and Wk_WipQty.wpid='" + wpid + "'";
            }
            if (dir != null) {
                if (dir.equals("in")) {
                    qpfilter += " and Wk_WipQty.direction='入组'";
                } else {
                    qpfilter += " and Wk_WipQty.direction='出组'";
                }
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWipqtyService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageListByLastMark", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<PageInfo<WkWipqtyLastPojo>> getOnlinePageListByLastMark(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQty.CreateDate");
            String qpfilter = " AND (Wk_WipQty.Acceid = '' OR Wk_WipQty.Acceid IS NULL)";
            qpfilter += " AND Wk_Process.LastMark=1";
            qpfilter += " AND Wk_WipQty.Direction='出组'";
            qpfilter += " AND Wk_WipNote.WkWpid= Wk_Process.id";
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWipqtyService.getOnlinePageListByLastMark(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "扫码入组2.0", notes = "扫码入组2.0", produces = "application/json")
    @RequestMapping(value = "/quickInput", method = RequestMethod.POST)
    public R<WkWipnotePojo> quickInput(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
            quickWipqtyPojo.setLister(loginUser.getRealName());
            WkWipnotePojo wkWipnotePojo = this.wkWipqtyService.quickInput(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (WipNoneProcessException e) {
            String message = e.getMessage(); //e.getMessage();抛出的是wkWipnotePojo对象
            WkWipnotePojo wkWipnotePojo = JSON.parseObject(message, new TypeReference<WkWipnotePojo>() {
            });
            return R.info(111, "单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达前工序", wkWipnotePojo); // 捕获自定义异常并返回错误消息
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "仅入组", notes = "仅入组", produces = "application/json")
    @RequestMapping(value = "/quickStart", method = RequestMethod.POST)
    public R<WkWipnotePojo> quickStart(@RequestBody String json) throws InterruptedException {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        //key:接口+工序id
        String key = "quickOutput:" + quickWipqtyPojo.getWpid();
        try {
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, 1, 6, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            quickWipqtyPojo.setLister(loginUser.getRealName());
            WkWipnotePojo wkWipnotePojo = this.wkWipqtyService.quickStart(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisService.deleteObject(key);
        }
    }

    @ApiOperation(value = "仅入组 fb用于增料(取消校验本次入组数量超出wip结余数量)", notes = "仅入组", produces = "application/json")
    @RequestMapping(value = "/quickStartAdd", method = RequestMethod.POST)
    public R<WkWipnotePojo> quickStartAdd(@RequestBody String json) throws InterruptedException {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        //key:接口+工序id
        String key = "quickOutput:" + quickWipqtyPojo.getWpid();
        try {
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, 1, 6, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            quickWipqtyPojo.setLister(loginUser.getRealName());
            WkWipnotePojo wkWipnotePojo = this.wkWipqtyService.quickStartAdd(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisService.deleteObject(key);
        }
    }


    @ApiOperation(value = "仅入组(任意工序下)", notes = "仅入组", produces = "application/json")
    @RequestMapping(value = "/anyQuickStart", method = RequestMethod.POST)
    public R<WkWipnotePojo> anyQuickStart(@RequestBody String json) throws InterruptedException {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        //key:接口+工序id
        String key = "quickOutput:" + quickWipqtyPojo.getWpid();
        try {
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, 1, 6, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            quickWipqtyPojo.setLister(loginUser.getRealName());
            WkWipnotePojo wkWipnotePojo = this.wkWipqtyService.anyQuickStart(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (WipNoneProcessException e) {
            return R.fail(111, e.getMessage()); // 捕获自定义异常并返回错误消息
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisService.deleteObject(key);
        }
    }


    @ApiOperation(value = "在WipNote表子表最后一行增加工序Item(WipNoteItem) 传入wpid(工序id),wipid,remark", notes = " (配合/anyQuickStart使用,仅入组(任意工序下)抛异常当前工单中没有**工序)", produces = "application/json")
    @RequestMapping(value = "/anyQuickAddWipItem", method = RequestMethod.POST)
    public R<WkWipnoteitemPojo> anyQuickAddWipItem(@RequestBody String json) {
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkWipnoteitemPojo wkWipnoteitemPojo = this.wkWipqtyService.anyQuickAddWipItem(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnoteitemPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "批量过数（需要权限为：Wk_WipQty.Admin）", notes = "", produces = "application/json")
    @RequestMapping(value = "/adminQuickInput", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.Admin")
    public R<String> adminQuickInput(@RequestBody String json) throws InterruptedException {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        //key:接口+工序id
        String key = "adminQuickInput:" + quickWipqtyPojo.getWpid();
        try {
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, 1, 6, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            quickWipqtyPojo.setLister(loginUser.getRealName());
            String result = this.wkWipqtyService.adminQuickInput(quickWipqtyPojo, loginUser);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisService.deleteObject(key);
        }
    }

    @ApiOperation(value = "快速出组2.0", notes = "快速出组2.0", produces = "application/json")
    @RequestMapping(value = "/quickOutput", method = RequestMethod.POST)
    public R<WkWipnotePojo> quickOutput(@RequestBody String json) throws InterruptedException {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        //key:接口+工序id
        String key = "quickOutput:" + quickWipqtyPojo.getWpid();
        try {
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, 1, 6, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            quickWipqtyPojo.setLister(loginUser.getRealName());
            WkWipnotePojo wkWipnotePojo = this.wkWipqtyService.quickOutput(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisService.deleteObject(key);
        }
    }


    @ApiOperation(value = "仅出组", notes = "仅出组", produces = "application/json")
    @RequestMapping(value = "/quickFinish", method = RequestMethod.POST)
    public R<WkWipnotePojo> quickFinish(@RequestBody String json) throws InterruptedException {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        //key:接口+工序id
        String key = "quickOutput:" + quickWipqtyPojo.getWpid();
        try {
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, 1, 6, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            quickWipqtyPojo.setLister(loginUser.getRealName());
            WkWipnotePojo wkWipnotePojo = this.wkWipqtyService.quickFinish(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisService.deleteObject(key);
        }
    }


    @ApiOperation(value = "仅出组 fb用于减料(取消校验本次出组数量超出wip结余数量)", notes = "仅出组", produces = "application/json")
    @RequestMapping(value = "/quickFinishSub", method = RequestMethod.POST)
    public R<WkWipnotePojo> quickFinishSub(@RequestBody String json) throws InterruptedException {
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        //key:接口+工序id
        String key = "quickOutput:" + quickWipqtyPojo.getWpid();
        try {
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, 1, 6, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            quickWipqtyPojo.setLister(loginUser.getRealName());
            WkWipnotePojo wkWipnotePojo = this.wkWipqtyService.quickFinishSub(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisService.deleteObject(key);
        }
    }


    @ApiOperation(value = "仅出组(任意工序下)", notes = "仅出组", produces = "application/json")
    @RequestMapping(value = "/anyQuickFinish", method = RequestMethod.POST)
    public R<WkWipnotePojo> anyQuickFinish(@RequestBody String json) throws InterruptedException {
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        //key:接口+工序id
        String key = "quickOutput:" + quickWipqtyPojo.getWpid();
        try {
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, 1, 6, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            quickWipqtyPojo.setLister(loginUser.getRealName());
            WkWipnotePojo wkWipnotePojo = this.wkWipqtyService.anyQuickFinish(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisService.deleteObject(key);
        }
    }

    @ApiOperation(value = "指定一道工序同时出入组", notes = "指定一道工序同时出入组", produces = "application/json")
    @RequestMapping(value = "/anyQuickOneWk", method = RequestMethod.POST)
    public R<WkWipnotePojo> anyQuickOneWk(@RequestBody String json) throws InterruptedException {
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        //key:接口+工序id
        String key = "quickOutput:" + quickWipqtyPojo.getWpid();
        try {
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, 1, 6, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            quickWipqtyPojo.setLister(loginUser.getRealName());
            WkWipnotePojo wkWipnotePojo = this.wkWipqtyService.anyQuickOneWk(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisService.deleteObject(key);
        }
    }

    @ApiOperation(value = "仅出组(任意工序下) 循环每道工序都出组", notes = "", produces = "application/json")
    @RequestMapping(value = "/anyQuickFinishByFinishid", method = RequestMethod.POST)
    public R<String> anyQuickFinishByFinishid(String key) throws InterruptedException {
        // redisKey:接口+完工单id+租户id
        String redisKey = "manu/anyQuickFinishByFinishid:" + key;
        try {
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(redisKey, 1, 6, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWipqtyService.anyQuickFinishByFinishid(key, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisTemplate.delete(redisKey);
        }
    }


    @ApiOperation(value = "快速返回上一道出组2.0", notes = "快速出组2.0", produces = "application/json")
    @RequestMapping(value = "/quickReturn", method = RequestMethod.POST)
    public R<WkWipnotePojo> quickReturn(@RequestBody String json) throws InterruptedException {
        // 获取工序id
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        //key:接口+工序id
        String key = "quickOutput:" + quickWipqtyPojo.getWpid();
        try {
            // 加锁，设置60秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, 1, 60, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("接口繁忙，请稍后再试！");
            }
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            quickWipqtyPojo.setLister(loginUser.getRealName());
            WkWipnotePojo wkWipnotePojo = this.wkWipqtyService.quickReturn(quickWipqtyPojo, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 延迟1秒后删除锁
            Thread.sleep(1000);
            redisService.deleteObject(key);
        }
    }

    @ApiOperation(value = "过数入库2.0", notes = "过数入库2.0", produces = "application/json")
    @RequestMapping(value = "/quickInStore", method = RequestMethod.POST)
    public R<WkWipnotePojo> quickInStore(@RequestBody String json) {
        QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        quickWipqtyPojo.setLister(loginUser.getRealName());
        Map<String, String> m = this.wkWipqtyService.quickInStore(quickWipqtyPojo, loginUser);
        // 生产入库单生成
        R racce = this.storeFeignService.createWsInStore(m.get("acceJson"), loginUser.getToken());
        if (racce.getCode() != 200) {
            this.wkWipqtyService.delete(m.get("billid"), loginUser.getTenantid());
            // 还需回滚wip主表的CompPcsQty
            this.wkWipqtyMapper.updateWipComp(m.get("wipitemid"), loginUser.getTenantid());
            throw new BaseBusinessException("生产入库单保存出错" + racce.getMsg());
        }
        return R.ok(null);
    }

    @ApiOperation(value = "Mrb快速打报", produces = "application/json")
    @RequestMapping(value = "/quickMrb", method = RequestMethod.POST)
    public R<WkWipnotePojo> quickMrb(@RequestBody String json) {
        try {
            QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            quickWipqtyPojo.setLister(loginUser.getRealName());
            Map<String, String> m = this.wkWipqtyService.quickMrb(quickWipqtyPojo, loginUser);
            // 生产入库单生成  m.get("mrbJson")即为一行构建出来的Qms_Mrb对象
            R racce = this.qmsFeignService.createWipMrb(m.get("mrbJson"), loginUser.getToken());
            if (racce.getCode() != 200) {
                this.wkWipqtyService.delete(m.get("billid"), loginUser.getTenantid());
                throw new BaseBusinessException("Mrb打报单保存出错:/quickMrb" + racce.getMsg());
            }
            return R.ok(null);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "过数入库2.0(不检查工序)", notes = "过数入库2.0", produces = "application/json")
    @RequestMapping(value = "/anyQuickInStore", method = RequestMethod.POST)
    public R<WkWipnotePojo> anyQuickInStore(@RequestBody String json) {
        try {
            QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            quickWipqtyPojo.setLister(loginUser.getRealName());
            Map<String, String> m = this.wkWipqtyService.anyQuickInStore(quickWipqtyPojo, loginUser);
            // 生产入库单生成
            R racce = this.storeFeignService.createWsInStore(m.get("acceJson"), loginUser.getToken());
            if (racce.getCode() != 200) {
                this.wkWipqtyService.delete(m.get("billid"), loginUser.getTenantid());
                // 还需回滚wip主表的CompPcsQty
                this.wkWipqtyMapper.updateWipComp(m.get("wipitemid"), loginUser.getTenantid());
                throw new BaseBusinessException("生产入库单保存出错" + racce.getMsg());
            }
            return R.ok(null);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "FB:最后工序过数2.0", notes = "过数2.0", produces = "application/json")
    @RequestMapping(value = "/quickFinishFB", method = RequestMethod.POST)
    public R<String> quickFinishFB(@RequestBody String json) {
        try {
            QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            quickWipqtyPojo.setLister(loginUser.getRealName());
            this.wkWipqtyService.quickInStore(quickWipqtyPojo, loginUser);
            return R.ok("OK");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "传入WipNoteItem.id,检查关联的N条入组过速记录是否有数量PcsQty为0的,返回该过数记录对象WkWipqtyPojo", notes = "", produces = "application/json")
    @RequestMapping(value = "/checkWipQtyPcsQty", method = RequestMethod.GET)
    public R<WkWipqtyPojo> checkWipQtyPcsQty(String wipitemid) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            WkWipqtyPojo wkWipqtyPojo = wkWipqtyMapper.checkWipQtyPcsQty(wipitemid, loginUser.getTenantid());
            return R.ok(wkWipqtyPojo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印销售订单明细报表", notes = "打印销售订单明细报表", produces = "application/json")
    @RequestMapping(value = "/printCostPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printCostPageList(@RequestBody String json, String groupid, String ptid, String wpid, String dir) throws IOException, JRException {

        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Wk_WipQty.WkDate");
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Wk_WipQty.MachGroupid='" + groupid + "'";
        }

        if (wpid != null) {
            qpfilter += " and Wk_WipQty.wpid='" + wpid + "'";
        }
        if (dir != null) {
            if (dir.equals("in")) {
                qpfilter += " and Wk_WipQty.direction='入组'";
            } else {
                qpfilter += " and Wk_WipQty.direction='出组'";
            }
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<WkWipqtyPojo> lst = this.wkWipqtyService.getCostPageList(queryParam).getList();

        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }

        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkWipqtyPojo wkWipnoteitemPojo = new WkWipqtyPojo();
                    lst.add(wkWipnoteitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrcostListToMaps(lst);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "分页云打印订单明细", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebCostPageList", method = RequestMethod.POST)
    public R<String> printWebCostPageList(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis, String wpid, String dir) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQty.WkDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Wk_WipQty.MachGroupid='" + groupid + "'";
            }
            if (wpid != null) {
                qpfilter += " and Wk_WipQty.wpid='" + wpid + "'";
            }
//            if (dir != null) {
//                if (dir.equals("in")) {sc
//                    qpfilter += " and Wk_WipQty.direction='入组'";
//                } else {
            qpfilter += " and Wk_WipQty.direction='出组'";
//                }
//            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<WkWipqtyPojo> lstitem = this.wkWipqtyService.getCostPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrcostListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "过数明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
