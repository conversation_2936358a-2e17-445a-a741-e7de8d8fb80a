package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkProccostEntity;
import inks.service.std.manu.domain.pojo.WkProccostPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工序成本(WkProccost)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-25 14:02:32
 */
@Mapper
public interface WkProccostMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProccostPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkProccostPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param wkProccostEntity 实例对象
     * @return 影响行数
     */
    int insert(WkProccostEntity wkProccostEntity);

    
    /**
     * 修改数据
     *
     * @param wkProccostEntity 实例对象
     * @return 影响行数
     */
    int update(WkProccostEntity wkProccostEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<WkProccostPojo> getAllList(String tid  );

}

