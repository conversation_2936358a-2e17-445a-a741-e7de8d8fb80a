package inks.service.std.manu.controller;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.service.WkManureportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 生产报工(Wk_ManuReport)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-17 16:16:42
 */
@RestController
@RequestMapping("D05M06R1")
@Api(tags = "D05M06R1:生产报工:公共报表")
public class D05M06R1Controller {
    /**
     * 服务对象
     */
    @Resource
    private WkManureportService wkManureportService;
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据加工单信息，汇总报工总数", notes = "根据加工单信息，汇总报工总数", produces = "application/json")
    @RequestMapping(value = "/getSumQtyByWork", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_ManuReport.List")
    public R<ChartPojo> getSumQtyByWork(String code, String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkManureportService.getSumQtyByWork(code, key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
