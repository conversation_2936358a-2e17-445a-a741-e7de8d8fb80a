package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWscarryoverPojo;
import inks.service.std.manu.domain.pojo.WkWscarryoveritemdetailPojo;

/**
 * 车间结转(WkWscarryover)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-09 10:53:17
 */
public interface WkWscarryoverService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWscarryoverPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWscarryoveritemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWscarryoverPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWscarryoverPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWscarryoverPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWscarryoverPojo 实例对象
     * @return 实例对象
     */
    WkWscarryoverPojo insert(WkWscarryoverPojo wkWscarryoverPojo);

    /**
     * 修改数据
     *
     * @param wkWscarryoverpojo 实例对象
     * @return 实例对象
     */
    WkWscarryoverPojo update(WkWscarryoverPojo wkWscarryoverpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 新增数据
     *
     * @param wkWscarryoverpojo 实例对象
     * @return 实例对象
     */
    WkWscarryoverPojo createCarry(WkWscarryoverPojo wkWscarryoverpojo);

}
