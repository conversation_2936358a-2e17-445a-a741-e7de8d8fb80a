package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipepiboleitemEntity;
import inks.service.std.manu.domain.pojo.WkWipepiboleitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Wip委外项目(WkWipepiboleitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 13:36:07
 */
 @Mapper
public interface WkWipepiboleitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipepiboleitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipepiboleitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipepiboleitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkWipepiboleitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipepiboleitemEntity wkWipepiboleitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkWipepiboleitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipepiboleitemEntity wkWipepiboleitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

