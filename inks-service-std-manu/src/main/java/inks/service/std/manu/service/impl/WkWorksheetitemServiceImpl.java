package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWorksheetitemEntity;
import inks.service.std.manu.domain.pojo.WkWorksheetitemPojo;
import inks.service.std.manu.domain.pojo.WkWorksheetitemdetailPojo;
import inks.service.std.manu.mapper.WkWorksheetitemMapper;
import inks.service.std.manu.service.WkWorksheetitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 厂制项目(WkWorksheetitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-09 13:14:11
 */
@Service("wkWorksheetitemService")
public class WkWorksheetitemServiceImpl implements WkWorksheetitemService {
    @Resource
    private WkWorksheetitemMapper wkWorksheetitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWorksheetitemPojo getEntity(String key, String tid) {
        return this.wkWorksheetitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWorksheetitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWorksheetitemPojo> lst = wkWorksheetitemMapper.getPageList(queryParam);
            PageInfo<WkWorksheetitemPojo> pageInfo = new PageInfo<WkWorksheetitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkWorksheetitemPojo> getList(String Pid, String tid) {
        try {
            List<WkWorksheetitemPojo> lst = wkWorksheetitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkWorksheetitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetitemPojo insert(WkWorksheetitemPojo wkWorksheetitemPojo) {
        //初始化item的NULL
        WkWorksheetitemPojo itempojo = this.clearNull(wkWorksheetitemPojo);
        WkWorksheetitemEntity wkWorksheetitemEntity = new WkWorksheetitemEntity();
        BeanUtils.copyProperties(itempojo, wkWorksheetitemEntity);

        wkWorksheetitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWorksheetitemEntity.setRevision(1);  //乐观锁
        this.wkWorksheetitemMapper.insert(wkWorksheetitemEntity);
        return this.getEntity(wkWorksheetitemEntity.getId(), wkWorksheetitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWorksheetitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetitemPojo update(WkWorksheetitemPojo wkWorksheetitemPojo) {
        WkWorksheetitemEntity wkWorksheetitemEntity = new WkWorksheetitemEntity();
        BeanUtils.copyProperties(wkWorksheetitemPojo, wkWorksheetitemEntity);
        this.wkWorksheetitemMapper.update(wkWorksheetitemEntity);
        return this.getEntity(wkWorksheetitemEntity.getId(), wkWorksheetitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWorksheetitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkWorksheetitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetitemPojo clearNull(WkWorksheetitemPojo wkWorksheetitemPojo) {
        //初始化NULL字段
        if (wkWorksheetitemPojo.getPid() == null) wkWorksheetitemPojo.setPid("");
        if (wkWorksheetitemPojo.getGoodsid() == null) wkWorksheetitemPojo.setGoodsid("");
        if (wkWorksheetitemPojo.getQuantity() == null) wkWorksheetitemPojo.setQuantity(0D);
        if (wkWorksheetitemPojo.getPrice() == null) wkWorksheetitemPojo.setPrice(0D);
        if (wkWorksheetitemPojo.getAmount() == null) wkWorksheetitemPojo.setAmount(0D);
        if (wkWorksheetitemPojo.getItemhour() == null) wkWorksheetitemPojo.setItemhour(0D);
        if (wkWorksheetitemPojo.getPlanhour() == null) wkWorksheetitemPojo.setPlanhour(0D);
        if (wkWorksheetitemPojo.getStartdate() == null) wkWorksheetitemPojo.setStartdate(new Date());
        if (wkWorksheetitemPojo.getPlandate() == null) wkWorksheetitemPojo.setPlandate(new Date());
        if (wkWorksheetitemPojo.getFinishqty() == null) wkWorksheetitemPojo.setFinishqty(0D);
        if (wkWorksheetitemPojo.getFinishhour() == null) wkWorksheetitemPojo.setFinishhour(0D);
        if (wkWorksheetitemPojo.getMrbqty() == null) wkWorksheetitemPojo.setMrbqty(0D);
        if (wkWorksheetitemPojo.getInstorage() == null) wkWorksheetitemPojo.setInstorage(0);
        if (wkWorksheetitemPojo.getEnabledmark() == null) wkWorksheetitemPojo.setEnabledmark(0);
        if (wkWorksheetitemPojo.getClosed() == null) wkWorksheetitemPojo.setClosed(0);
        if (wkWorksheetitemPojo.getStartwpid() == null) wkWorksheetitemPojo.setStartwpid("");
        if (wkWorksheetitemPojo.getEndwpid() == null) wkWorksheetitemPojo.setEndwpid("");
        if (wkWorksheetitemPojo.getRemark() == null) wkWorksheetitemPojo.setRemark("");
        if (wkWorksheetitemPojo.getStatecode() == null) wkWorksheetitemPojo.setStatecode("");
        if (wkWorksheetitemPojo.getStatedate() == null) wkWorksheetitemPojo.setStatedate(new Date());
        if (wkWorksheetitemPojo.getRownum() == null) wkWorksheetitemPojo.setRownum(0);
        if (wkWorksheetitemPojo.getMachtype() == null) wkWorksheetitemPojo.setMachtype("");
        if (wkWorksheetitemPojo.getMachuid() == null) wkWorksheetitemPojo.setMachuid("");
        if (wkWorksheetitemPojo.getMachitemid() == null) wkWorksheetitemPojo.setMachitemid("");
        if (wkWorksheetitemPojo.getMachbatch() == null) wkWorksheetitemPojo.setMachbatch("");
        if (wkWorksheetitemPojo.getMachgroupid() == null) wkWorksheetitemPojo.setMachgroupid("");
        if (wkWorksheetitemPojo.getMrpuid() == null) wkWorksheetitemPojo.setMrpuid("");
        if (wkWorksheetitemPojo.getMrpitemid() == null) wkWorksheetitemPojo.setMrpitemid("");
        if (wkWorksheetitemPojo.getCustomer() == null) wkWorksheetitemPojo.setCustomer("");
        if (wkWorksheetitemPojo.getCustpo() == null) wkWorksheetitemPojo.setCustpo("");
        if (wkWorksheetitemPojo.getCiteuid() == null) wkWorksheetitemPojo.setCiteuid("");
        if (wkWorksheetitemPojo.getCiteitemid() == null) wkWorksheetitemPojo.setCiteitemid("");
        if (wkWorksheetitemPojo.getMainplanuid() == null) wkWorksheetitemPojo.setMainplanuid("");
        if (wkWorksheetitemPojo.getMainplanitemid() == null) wkWorksheetitemPojo.setMainplanitemid("");
        if (wkWorksheetitemPojo.getLocation() == null) wkWorksheetitemPojo.setLocation("");
        if (wkWorksheetitemPojo.getBatchno() == null) wkWorksheetitemPojo.setBatchno("");
        if (wkWorksheetitemPojo.getWipused() == null) wkWorksheetitemPojo.setWipused(0);
        if (wkWorksheetitemPojo.getWkwpid() == null) wkWorksheetitemPojo.setWkwpid("");
        if (wkWorksheetitemPojo.getWkwpcode() == null) wkWorksheetitemPojo.setWkwpcode("");
        if (wkWorksheetitemPojo.getWkwpname() == null) wkWorksheetitemPojo.setWkwpname("");
        if (wkWorksheetitemPojo.getWkrownum() == null) wkWorksheetitemPojo.setWkrownum("");
        if (wkWorksheetitemPojo.getDisannullisterid() == null) wkWorksheetitemPojo.setDisannullisterid("");
        if (wkWorksheetitemPojo.getDisannullister() == null) wkWorksheetitemPojo.setDisannullister("");
        if (wkWorksheetitemPojo.getDisannuldate() == null) wkWorksheetitemPojo.setDisannuldate(new Date());
        if (wkWorksheetitemPojo.getDisannulmark() == null) wkWorksheetitemPojo.setDisannulmark(0);
        if (wkWorksheetitemPojo.getAttributejson() == null) wkWorksheetitemPojo.setAttributejson("");
        if (wkWorksheetitemPojo.getReportqty() == null) wkWorksheetitemPojo.setReportqty(0D);
        if (wkWorksheetitemPojo.getCompqty() == null) wkWorksheetitemPojo.setCompqty(0D);
        if (wkWorksheetitemPojo.getFinishrate() == null) wkWorksheetitemPojo.setFinishrate(0D);
        if (wkWorksheetitemPojo.getSecqty() == null) wkWorksheetitemPojo.setSecqty(0D);
        if (wkWorksheetitemPojo.getPanelwidth() == null) wkWorksheetitemPojo.setPanelwidth(0D);
        if (wkWorksheetitemPojo.getPanelheight() == null) wkWorksheetitemPojo.setPanelheight(0D);
        if (wkWorksheetitemPojo.getPcsinpanel() == null) wkWorksheetitemPojo.setPcsinpanel(0);
        if (wkWorksheetitemPojo.getPanelthick() == null) wkWorksheetitemPojo.setPanelthick(0D);
        if (wkWorksheetitemPojo.getMatcode() == null) wkWorksheetitemPojo.setMatcode("");
        if (wkWorksheetitemPojo.getMatused() == null) wkWorksheetitemPojo.setMatused(0);
        if (wkWorksheetitemPojo.getRowcode() == null) wkWorksheetitemPojo.setRowcode("");
        if (wkWorksheetitemPojo.getWkpcsqty() == null) wkWorksheetitemPojo.setWkpcsqty(0D);
        if (wkWorksheetitemPojo.getWksecqty() == null) wkWorksheetitemPojo.setWksecqty(0D);
        if (wkWorksheetitemPojo.getMergemark() == null) wkWorksheetitemPojo.setMergemark(0);
        if (wkWorksheetitemPojo.getSourcetype() == null) wkWorksheetitemPojo.setSourcetype(0);
        if (wkWorksheetitemPojo.getCostitemjson() == null) wkWorksheetitemPojo.setCostitemjson("");
        if (wkWorksheetitemPojo.getCostgroupjson() == null) wkWorksheetitemPojo.setCostgroupjson("");
        if (wkWorksheetitemPojo.getVirtualitem() == null) wkWorksheetitemPojo.setVirtualitem(0);
        if (wkWorksheetitemPojo.getCustom1() == null) wkWorksheetitemPojo.setCustom1("");
        if (wkWorksheetitemPojo.getCustom2() == null) wkWorksheetitemPojo.setCustom2("");
        if (wkWorksheetitemPojo.getCustom3() == null) wkWorksheetitemPojo.setCustom3("");
        if (wkWorksheetitemPojo.getCustom4() == null) wkWorksheetitemPojo.setCustom4("");
        if (wkWorksheetitemPojo.getCustom5() == null) wkWorksheetitemPojo.setCustom5("");
        if (wkWorksheetitemPojo.getCustom6() == null) wkWorksheetitemPojo.setCustom6("");
        if (wkWorksheetitemPojo.getCustom7() == null) wkWorksheetitemPojo.setCustom7("");
        if (wkWorksheetitemPojo.getCustom8() == null) wkWorksheetitemPojo.setCustom8("");
        if (wkWorksheetitemPojo.getCustom9() == null) wkWorksheetitemPojo.setCustom9("");
        if (wkWorksheetitemPojo.getCustom10() == null) wkWorksheetitemPojo.setCustom10("");
        if (wkWorksheetitemPojo.getTenantid() == null) wkWorksheetitemPojo.setTenantid("");
        if (wkWorksheetitemPojo.getRevision() == null) wkWorksheetitemPojo.setRevision(0);
        return wkWorksheetitemPojo;
    }

    @Override
    public int updateMergemark1(String itemids, String tid) {
        return this.wkWorksheetitemMapper.updateMergemark1(itemids, tid);
    }

    @Override
    public WkWorksheetitemdetailPojo getEntityDetail(String key, String tid) {
        return wkWorksheetitemMapper.getEntityDetail(key, tid);
    }

    @Override
    public int updateMergemark0(String key, String tid) {
        return this.wkWorksheetitemMapper.updateMergemark0(key, tid);
    }

    @Override
    public List<WkWorksheetitemPojo> getItemListByIds(String ids, String key, String tid) {
        return this.wkWorksheetitemMapper.getItemListByIds(ids, key, tid);
    }
}
