package inks.service.std.manu.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkSectionPojo;
import inks.service.std.manu.service.WkSectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产工段(Wk_Section)表控制层
 *
 * <AUTHOR>
 * @since 2024-10-28 11:21:19
 */
@RestController
@RequestMapping("D05M21S12")
@Api(tags = "D05M21S12:生产工段")
public class D05M21S12Controller extends WkSectionController {

    @Resource
    private WkSectionService wkSectionService;
    @Resource
    private TokenService tokenService;


    @ApiOperation(value = " 获取所有生产工段及关联工位，若传入key工段id,则只返回该工段及关联工位", notes = "获取生产工段详细信息", produces = "application/json")
    @RequestMapping(value = "/getSectionsAndStations", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Section.List")
    public R<List<WkSectionPojo>> getSectionsAndStations(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkSectionService.getSectionsAndStations(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
