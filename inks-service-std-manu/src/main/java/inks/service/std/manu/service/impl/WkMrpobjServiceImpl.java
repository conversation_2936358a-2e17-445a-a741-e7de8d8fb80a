package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkMrpobjEntity;
import inks.service.std.manu.domain.pojo.WkMrpobjPojo;
import inks.service.std.manu.mapper.WkMrpobjMapper;
import inks.service.std.manu.service.WkMrpobjService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * MRP对象(WkMrpobj)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-04 16:43:13
 */
@Service("wkMrpobjService")
public class WkMrpobjServiceImpl implements WkMrpobjService {
    @Resource
    private WkMrpobjMapper wkMrpobjMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMrpobjPojo getEntity(String key, String tid) {
        return this.wkMrpobjMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMrpobjPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMrpobjPojo> lst = wkMrpobjMapper.getPageList(queryParam);
            PageInfo<WkMrpobjPojo> pageInfo = new PageInfo<WkMrpobjPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkMrpobjPojo> getList(String Pid, String tid) {
        try {
            return wkMrpobjMapper.getList(Pid, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkMrpobjPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMrpobjPojo insert(WkMrpobjPojo wkMrpobjPojo) {
        //初始化item的NULL
        WkMrpobjPojo itempojo = this.clearNull(wkMrpobjPojo);
        WkMrpobjEntity wkMrpobjEntity = new WkMrpobjEntity();
        BeanUtils.copyProperties(itempojo, wkMrpobjEntity);

        wkMrpobjEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkMrpobjEntity.setRevision(1);  //乐观锁
        this.wkMrpobjMapper.insert(wkMrpobjEntity);
        return this.getEntity(wkMrpobjEntity.getId(), wkMrpobjEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkMrpobjPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMrpobjPojo update(WkMrpobjPojo wkMrpobjPojo) {
        WkMrpobjEntity wkMrpobjEntity = new WkMrpobjEntity();
        BeanUtils.copyProperties(wkMrpobjPojo, wkMrpobjEntity);
        this.wkMrpobjMapper.update(wkMrpobjEntity);
        return this.getEntity(wkMrpobjEntity.getId(), wkMrpobjEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkMrpobjMapper.delete(key, tid);
    }


    @Override
    public List<WkMrpobjPojo> getListInids(Set<String> objids, String tid) {
        return wkMrpobjMapper.getListInids(objids, tid);
    }

    /**
     * 修改数据
     *
     * @param wkMrpobjPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMrpobjPojo clearNull(WkMrpobjPojo wkMrpobjPojo) {
        //初始化NULL字段
        if (wkMrpobjPojo.getPid() == null) wkMrpobjPojo.setPid("");
        if (wkMrpobjPojo.getGoodsid() == null) wkMrpobjPojo.setGoodsid("");
        if (wkMrpobjPojo.getItemcode() == null) wkMrpobjPojo.setItemcode("");
        if (wkMrpobjPojo.getItemname() == null) wkMrpobjPojo.setItemname("");
        if (wkMrpobjPojo.getItemspec() == null) wkMrpobjPojo.setItemspec("");
        if (wkMrpobjPojo.getItemunit() == null) wkMrpobjPojo.setItemunit("");
        if (wkMrpobjPojo.getQuantity() == null) wkMrpobjPojo.setQuantity(0D);
        if (wkMrpobjPojo.getPlandate() == null) wkMrpobjPojo.setPlandate(new Date());
        if (wkMrpobjPojo.getMachuid() == null) wkMrpobjPojo.setMachuid("");
        if (wkMrpobjPojo.getMachitemid() == null) wkMrpobjPojo.setMachitemid("");
        if (wkMrpobjPojo.getMachbatch() == null) wkMrpobjPojo.setMachbatch("");
        if (wkMrpobjPojo.getMachgroupid() == null) wkMrpobjPojo.setMachgroupid("");
        if (wkMrpobjPojo.getMainplanuid() == null) wkMrpobjPojo.setMainplanuid("");
        if (wkMrpobjPojo.getMainplanitemid() == null) wkMrpobjPojo.setMainplanitemid("");
        if (wkMrpobjPojo.getCustomer() == null) wkMrpobjPojo.setCustomer("");
        if (wkMrpobjPojo.getRownum() == null) wkMrpobjPojo.setRownum(0);
        if (wkMrpobjPojo.getRemark() == null) wkMrpobjPojo.setRemark("");
        if (wkMrpobjPojo.getCustpo() == null) wkMrpobjPojo.setCustpo("");
     if(wkMrpobjPojo.getAttributejson()==null) wkMrpobjPojo.setAttributejson("");
     if(wkMrpobjPojo.getWkpcsqty()==null) wkMrpobjPojo.setWkpcsqty(0D);
     if(wkMrpobjPojo.getWksecqty()==null) wkMrpobjPojo.setWksecqty(0D);
        if (wkMrpobjPojo.getCustom1() == null) wkMrpobjPojo.setCustom1("");
        if (wkMrpobjPojo.getCustom2() == null) wkMrpobjPojo.setCustom2("");
        if (wkMrpobjPojo.getCustom3() == null) wkMrpobjPojo.setCustom3("");
        if (wkMrpobjPojo.getCustom4() == null) wkMrpobjPojo.setCustom4("");
        if (wkMrpobjPojo.getCustom5() == null) wkMrpobjPojo.setCustom5("");
        if (wkMrpobjPojo.getCustom6() == null) wkMrpobjPojo.setCustom6("");
        if (wkMrpobjPojo.getCustom7() == null) wkMrpobjPojo.setCustom7("");
        if (wkMrpobjPojo.getCustom8() == null) wkMrpobjPojo.setCustom8("");
        if (wkMrpobjPojo.getCustom9() == null) wkMrpobjPojo.setCustom9("");
        if (wkMrpobjPojo.getCustom10() == null) wkMrpobjPojo.setCustom10("");
        if (wkMrpobjPojo.getTenantid() == null) wkMrpobjPojo.setTenantid("");
        if (wkMrpobjPojo.getTenantname() == null) wkMrpobjPojo.setTenantname("");
        if (wkMrpobjPojo.getRevision() == null) wkMrpobjPojo.setRevision(0);
        return wkMrpobjPojo;
    }
}
