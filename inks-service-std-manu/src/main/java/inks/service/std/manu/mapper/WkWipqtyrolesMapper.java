package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipqtyrolesEntity;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesPojo;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 过数角色(WkWipqtyroles)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:53:28
 */
@Mapper
public interface WkWipqtyrolesMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtyrolesPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipqtyrolesitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipqtyrolesPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param wkWipqtyrolesEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipqtyrolesEntity wkWipqtyrolesEntity);

    
    /**
     * 修改数据
     *
     * @param wkWipqtyrolesEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipqtyrolesEntity wkWipqtyrolesEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param wkWipqtyrolesPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(WkWipqtyrolesPojo wkWipqtyrolesPojo);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtyrolesPojo getEntityByUserid(@Param("key") String key,@Param("tid") String tid);

    List<WkWipqtyrolesPojo> getListByWpid(String wpid, String tid);
}

