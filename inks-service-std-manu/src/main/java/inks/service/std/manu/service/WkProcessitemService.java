package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkProcessitemPojo;

import java.util.List;

/**
 * 工序子项(WkProcessitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-26 10:05:42
 */
public interface WkProcessitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProcessitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkProcessitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkProcessitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkProcessitemPojo 实例对象
     * @return 实例对象
     */
    WkProcessitemPojo insert(WkProcessitemPojo wkProcessitemPojo);

    /**
     * 修改数据
     *
     * @param wkProcessitempojo 实例对象
     * @return 实例对象
     */
    WkProcessitemPojo update(WkProcessitemPojo wkProcessitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkProcessitempojo 实例对象
     * @return 实例对象
     */
    WkProcessitemPojo clearNull(WkProcessitemPojo wkProcessitempojo);
}
