package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWscarryoverEntity;
import inks.service.std.manu.domain.pojo.WkWscarryoverPojo;
import inks.service.std.manu.domain.pojo.WkWscarryoveritemPojo;
import inks.service.std.manu.domain.pojo.WkWscarryoveritemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 车间结转(WkWscarryover)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-09 10:53:17
 */
@Mapper
public interface WkWscarryoverMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWscarryoverPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWscarryoveritemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWscarryoverPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWscarryoverEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWscarryoverEntity wkWscarryoverEntity);


    /**
     * 修改数据
     *
     * @param wkWscarryoverEntity 实例对象
     * @return 影响行数
     */
    int update(WkWscarryoverEntity wkWscarryoverEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkWscarryoverPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkWscarryoverPojo wkWscarryoverPojo);


    /**
     * 根据
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<WkWscarryoveritemPojo> getGoodsWsList(QueryParam queryParam);

    /**
     * 根据
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<WkWscarryoveritemPojo> getGoodsAcceList(QueryParam queryParam);
}

