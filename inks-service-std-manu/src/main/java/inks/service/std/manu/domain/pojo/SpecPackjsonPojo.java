package inks.service.std.manu.domain.pojo;

// 地板专用规格
public class SpecPackjsonPojo {
    // 长
    private String spuchang;
    // 宽
    private String spukuan;
    // 数
    private Double qty;
    // 面积
    private Double area;
    // 厚 备用
    private String spuhou;

    // 每件/片
    private Integer inner;
    // 件数
    private Integer ctn;
    // 1 散件 0 为整件
    private Integer spare;

    public String getSpuchang() {
        return spuchang;
    }

    public void setSpuchang(String spuchang) {
        this.spuchang = spuchang;
    }

    public String getSpukuan() {
        return spukuan;
    }

    public void setSpukuan(String spukuan) {
        this.spukuan = spukuan;
    }

    public Double getQty() {
        return qty;
    }

    public void setQty(Double qty) {
        this.qty = qty;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getSpuhou() {
        return spuhou;
    }

    public void setSpuhou(String spuhou) {
        this.spuhou = spuhou;
    }

    public Integer getInner() {
        return inner;
    }

    public void setInner(Integer inner) {
        this.inner = inner;
    }

    public Integer getCtn() {
        return ctn;
    }

    public void setCtn(Integer ctn) {
        this.ctn = ctn;
    }

    public Integer getSpare() {
        return spare;
    }

    public void setSpare(Integer spare) {
        this.spare = spare;
    }
}
