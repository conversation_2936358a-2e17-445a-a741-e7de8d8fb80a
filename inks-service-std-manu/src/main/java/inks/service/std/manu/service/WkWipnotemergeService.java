package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipnotemergePojo;

/**
 * WIP合并(WkWipnotemerge)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-13 09:51:14
 */
public interface WkWipnotemergeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipnotemergePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipnotemergePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipnotemergePojo 实例对象
     * @return 实例对象
     */
    WkWipnotemergePojo insert(WkWipnotemergePojo wkWipnotemergePojo);

    /**
     * 修改数据
     *
     * @param wkWipnotemergepojo 实例对象
     * @return 实例对象
     */
    WkWipnotemergePojo update(WkWipnotemergePojo wkWipnotemergepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    int updateMergeid(String mergeIds, String insertId, String tid);
}
