package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSmtpartEntity;
import inks.service.std.manu.domain.pojo.WkSmtpartPojo;
import inks.service.std.manu.domain.pojo.WkSmtpartitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SMT上料表(WkSmtpart)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-12 13:59:10
 */
@Mapper
public interface WkSmtpartMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSmtpartPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSmtpartitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSmtpartPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSmtpartEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSmtpartEntity wkSmtpartEntity);


    /**
     * 修改数据
     *
     * @param wkSmtpartEntity 实例对象
     * @return 影响行数
     */
    int update(WkSmtpartEntity wkSmtpartEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkSmtpartPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkSmtpartPojo wkSmtpartPojo);

    /**
     * 修改数据
     *
     * @param wkSmtpartEntity 实例对象
     * @return 影响行数
     */
    int approval(WkSmtpartEntity wkSmtpartEntity);

}

