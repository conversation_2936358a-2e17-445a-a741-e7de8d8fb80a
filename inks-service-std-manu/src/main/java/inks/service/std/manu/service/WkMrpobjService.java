package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkMrpobjPojo;

import java.util.List;
import java.util.Set;

/**
 * MRP对象(WkMrpobj)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-23 10:00:36
 */
public interface WkMrpobjService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMrpobjPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMrpobjPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMrpobjPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkMrpobjPojo 实例对象
     * @return 实例对象
     */
    WkMrpobjPojo insert(WkMrpobjPojo wkMrpobjPojo);

    /**
     * 修改数据
     *
     * @param wkMrpobjpojo 实例对象
     * @return 实例对象
     */
    WkMrpobjPojo update(WkMrpobjPojo wkMrpobjpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkMrpobjpojo 实例对象
     * @return 实例对象
     */
    WkMrpobjPojo clearNull(WkMrpobjPojo wkMrpobjpojo);

    List<WkMrpobjPojo> getListInids(Set<String> objids, String tid);
}
