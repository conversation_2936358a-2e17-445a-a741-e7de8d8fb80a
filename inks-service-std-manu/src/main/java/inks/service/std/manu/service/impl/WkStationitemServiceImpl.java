package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkStationitemEntity;
import inks.service.std.manu.domain.pojo.WkStationitemPojo;
import inks.service.std.manu.mapper.WkStationitemMapper;
import inks.service.std.manu.service.WkStationitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工位员工(WkStationitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-22 12:36:34
 */
@Service("wkStationitemService")
public class WkStationitemServiceImpl implements WkStationitemService {
    @Resource
    private WkStationitemMapper wkStationitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkStationitemPojo getEntity(String key, String tid) {
        return this.wkStationitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkStationitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkStationitemPojo> lst = wkStationitemMapper.getPageList(queryParam);
            PageInfo<WkStationitemPojo> pageInfo = new PageInfo<WkStationitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkStationitemPojo> getList(String Pid, String tid) {
        try {
            List<WkStationitemPojo> lst = wkStationitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkStationitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkStationitemPojo insert(WkStationitemPojo wkStationitemPojo) {
        //初始化item的NULL
        WkStationitemPojo itempojo = this.clearNull(wkStationitemPojo);
        WkStationitemEntity wkStationitemEntity = new WkStationitemEntity();
        BeanUtils.copyProperties(itempojo, wkStationitemEntity);
        //生成雪花id
        wkStationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkStationitemEntity.setRevision(1);  //乐观锁
        this.wkStationitemMapper.insert(wkStationitemEntity);
        return this.getEntity(wkStationitemEntity.getId(), wkStationitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkStationitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkStationitemPojo update(WkStationitemPojo wkStationitemPojo) {
        WkStationitemEntity wkStationitemEntity = new WkStationitemEntity();
        BeanUtils.copyProperties(wkStationitemPojo, wkStationitemEntity);
        this.wkStationitemMapper.update(wkStationitemEntity);
        return this.getEntity(wkStationitemEntity.getId(), wkStationitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkStationitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkStationitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkStationitemPojo clearNull(WkStationitemPojo wkStationitemPojo) {
        //初始化NULL字段
        if (wkStationitemPojo.getPid() == null) wkStationitemPojo.setPid("");
        if (wkStationitemPojo.getStaffid() == null) wkStationitemPojo.setStaffid("");
        if (wkStationitemPojo.getRealname() == null) wkStationitemPojo.setRealname("");
        if (wkStationitemPojo.getIsadmin() == null) wkStationitemPojo.setIsadmin(0);
        if (wkStationitemPojo.getRownum() == null) wkStationitemPojo.setRownum(0);
        if (wkStationitemPojo.getCustom1() == null) wkStationitemPojo.setCustom1("");
        if (wkStationitemPojo.getCustom2() == null) wkStationitemPojo.setCustom2("");
        if (wkStationitemPojo.getCustom3() == null) wkStationitemPojo.setCustom3("");
        if (wkStationitemPojo.getCustom4() == null) wkStationitemPojo.setCustom4("");
        if (wkStationitemPojo.getCustom5() == null) wkStationitemPojo.setCustom5("");
        if (wkStationitemPojo.getCustom6() == null) wkStationitemPojo.setCustom6("");
        if (wkStationitemPojo.getCustom7() == null) wkStationitemPojo.setCustom7("");
        if (wkStationitemPojo.getCustom8() == null) wkStationitemPojo.setCustom8("");
        if (wkStationitemPojo.getCustom9() == null) wkStationitemPojo.setCustom9("");
        if (wkStationitemPojo.getCustom10() == null) wkStationitemPojo.setCustom10("");
        if (wkStationitemPojo.getTenantid() == null) wkStationitemPojo.setTenantid("");
        if (wkStationitemPojo.getRevision() == null) wkStationitemPojo.setRevision(0);
        return wkStationitemPojo;
    }
}
