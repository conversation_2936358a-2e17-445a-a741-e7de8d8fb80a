package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSubcontractitemPojo;

import java.util.List;

/**
 * 委制项目(WkSubcontractitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 13:33:01
 */
public interface WkSubcontractitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSubcontractitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSubcontractitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSubcontractitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkSubcontractitemPojo 实例对象
     * @return 实例对象
     */
    WkSubcontractitemPojo insert(WkSubcontractitemPojo wkSubcontractitemPojo);

    /**
     * 修改数据
     *
     * @param wkSubcontractitempojo 实例对象
     * @return 实例对象
     */
    WkSubcontractitemPojo update(WkSubcontractitemPojo wkSubcontractitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkSubcontractitempojo 实例对象
     * @return 实例对象
     */
    WkSubcontractitemPojo clearNull(WkSubcontractitemPojo wkSubcontractitempojo);
}
