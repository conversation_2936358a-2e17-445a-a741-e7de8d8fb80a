package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkVisualplanEntity;
import inks.service.std.manu.domain.pojo.WkVisualplanPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 可视化排程(WkVisualplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-01 15:10:03
 */
@Mapper
public interface WkVisualplanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkVisualplanPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkVisualplanPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param wkVisualplanEntity 实例对象
     * @return 影响行数
     */
    int insert(WkVisualplanEntity wkVisualplanEntity);

    
    /**
     * 修改数据
     *
     * @param wkVisualplanEntity 实例对象
     * @return 影响行数
     */
    int update(WkVisualplanEntity wkVisualplanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                                                                                                                       }

