package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkWipqtygroupPojo;
import inks.service.std.manu.domain.pojo.WkWipqtygroupitemPojo;
import inks.service.std.manu.domain.pojo.WkWipqtygroupitemdetailPojo;
import inks.service.std.manu.service.WkWipqtygroupService;
import inks.service.std.manu.service.WkWipqtygroupitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 过数小组(WkWipqtygroup)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-10 11:07:59
 */
public class WkWipqtygroupController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(WkWipqtygroupController.class);
    /**
     * 服务对象
     */
    @Resource
    private WkWipqtygroupService wkWipqtygroupService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkWipqtygroupitemService wkWipqtygroupitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取过数小组详细信息", notes = "获取过数小组详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.List")
    public R<WkWipqtygroupPojo> getEntity(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWipqtygroupService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.List")
    public R<PageInfo<WkWipqtygroupitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQtyGroup.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWipqtygroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取过数小组详细信息", notes = "获取过数小组详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.List")
    public R<WkWipqtygroupPojo> getBillEntity(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWipqtygroupService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.List")
    public R<PageInfo<WkWipqtygroupPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQtyGroup.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipqtygroupService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.List")
    public R<PageInfo<WkWipqtygroupPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQtyGroup.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWipqtygroupService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增过数小组", notes = "新增过数小组", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.Add")
    public R<WkWipqtygroupPojo> create(@RequestBody String json) {
        try {
            WkWipqtygroupPojo wkWipqtygroupPojo = JSONArray.parseObject(json, WkWipqtygroupPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            wkWipqtygroupPojo.setCreateby(loginUser.getRealName());   // 创建者
            wkWipqtygroupPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkWipqtygroupPojo.setCreatedate(new Date());   // 创建时间
            wkWipqtygroupPojo.setLister(loginUser.getRealname());   // 制表
            wkWipqtygroupPojo.setListerid(loginUser.getUserid());    // 制表id            
            wkWipqtygroupPojo.setModifydate(new Date());   //修改时间
            wkWipqtygroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.wkWipqtygroupService.insert(wkWipqtygroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改过数小组", notes = "修改过数小组", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.Edit")
    public R<WkWipqtygroupPojo> update(@RequestBody String json) {
        try {
            WkWipqtygroupPojo wkWipqtygroupPojo = JSONArray.parseObject(json, WkWipqtygroupPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            wkWipqtygroupPojo.setLister(loginUser.getRealname());   // 制表
            wkWipqtygroupPojo.setListerid(loginUser.getUserid());    // 制表id   
            wkWipqtygroupPojo.setModifydate(new Date());   //修改时间
            wkWipqtygroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.wkWipqtygroupService.update(wkWipqtygroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除过数小组", notes = "删除过数小组", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.Delete")
    public R<Integer> delete(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWipqtygroupService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增过数小组Item", notes = "新增过数小组Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.Add")
    public R<WkWipqtygroupitemPojo> createItem(@RequestBody String json) {
        try {
            WkWipqtygroupitemPojo wkWipqtygroupitemPojo = JSONArray.parseObject(json, WkWipqtygroupitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            wkWipqtygroupitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWipqtygroupitemService.insert(wkWipqtygroupitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除过数小组Item", notes = "删除过数小组Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWipqtygroupitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipQtyGroup.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        WkWipqtygroupPojo wkWipqtygroupPojo = this.wkWipqtygroupService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(wkWipqtygroupPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = wkWipqtygroupPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkWipqtygroupitemPojo wkWipqtygroupitemPojo = new WkWipqtygroupitemPojo();
                    wkWipqtygroupPojo.getItem().add(wkWipqtygroupitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(wkWipqtygroupPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

