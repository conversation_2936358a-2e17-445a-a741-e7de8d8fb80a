package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipepiboleitemEntity;
import inks.service.std.manu.domain.pojo.WkWipepiboleitemPojo;
import inks.service.std.manu.mapper.WkWipepiboleitemMapper;
import inks.service.std.manu.service.WkWipepiboleitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Wip委外项目(WkWipepiboleitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-04 11:13:44
 */
@Service("wkWipepiboleitemService")
public class WkWipepiboleitemServiceImpl implements WkWipepiboleitemService {
    @Resource
    private WkWipepiboleitemMapper wkWipepiboleitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipepiboleitemPojo getEntity(String key, String tid) {
        return this.wkWipepiboleitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipepiboleitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipepiboleitemPojo> lst = wkWipepiboleitemMapper.getPageList(queryParam);
            PageInfo<WkWipepiboleitemPojo> pageInfo = new PageInfo<WkWipepiboleitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkWipepiboleitemPojo> getList(String Pid, String tid) {
        try {
            List<WkWipepiboleitemPojo> lst = wkWipepiboleitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkWipepiboleitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipepiboleitemPojo insert(WkWipepiboleitemPojo wkWipepiboleitemPojo) {
        //初始化item的NULL
        WkWipepiboleitemPojo itempojo = this.clearNull(wkWipepiboleitemPojo);
        WkWipepiboleitemEntity wkWipepiboleitemEntity = new WkWipepiboleitemEntity();
        BeanUtils.copyProperties(itempojo, wkWipepiboleitemEntity);

        wkWipepiboleitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipepiboleitemEntity.setRevision(1);  //乐观锁
        this.wkWipepiboleitemMapper.insert(wkWipepiboleitemEntity);
        return this.getEntity(wkWipepiboleitemEntity.getId(), wkWipepiboleitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipepiboleitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipepiboleitemPojo update(WkWipepiboleitemPojo wkWipepiboleitemPojo) {
        WkWipepiboleitemEntity wkWipepiboleitemEntity = new WkWipepiboleitemEntity();
        BeanUtils.copyProperties(wkWipepiboleitemPojo, wkWipepiboleitemEntity);
        this.wkWipepiboleitemMapper.update(wkWipepiboleitemEntity);
        return this.getEntity(wkWipepiboleitemEntity.getId(), wkWipepiboleitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWipepiboleitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkWipepiboleitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipepiboleitemPojo clearNull(WkWipepiboleitemPojo wkWipepiboleitemPojo) {
        //初始化NULL字段
        if (wkWipepiboleitemPojo.getPid() == null) wkWipepiboleitemPojo.setPid("");
        if (wkWipepiboleitemPojo.getWipitemid() == null) wkWipepiboleitemPojo.setWipitemid("");
        if (wkWipepiboleitemPojo.getWsid() == null) wkWipepiboleitemPojo.setWsid("");
        if (wkWipepiboleitemPojo.getWsuid() == null) wkWipepiboleitemPojo.setWsuid("");
        if (wkWipepiboleitemPojo.getWpid() == null) wkWipepiboleitemPojo.setWpid("");
        if (wkWipepiboleitemPojo.getEndwpid() == null) wkWipepiboleitemPojo.setEndwpid("");
        if (wkWipepiboleitemPojo.getGoodsid() == null) wkWipepiboleitemPojo.setGoodsid("");
        if (wkWipepiboleitemPojo.getSubitemid() == null) wkWipepiboleitemPojo.setSubitemid("");
        if (wkWipepiboleitemPojo.getSubuse() == null) wkWipepiboleitemPojo.setSubuse("");
        if (wkWipepiboleitemPojo.getSubunit() == null) wkWipepiboleitemPojo.setSubunit("");
        if (wkWipepiboleitemPojo.getSubqty() == null) wkWipepiboleitemPojo.setSubqty(0D);
        if (wkWipepiboleitemPojo.getTaxprice() == null) wkWipepiboleitemPojo.setTaxprice(0D);
        if (wkWipepiboleitemPojo.getTaxamount() == null) wkWipepiboleitemPojo.setTaxamount(0D);
        if (wkWipepiboleitemPojo.getTaxtotal() == null) wkWipepiboleitemPojo.setTaxtotal(0D);
        if (wkWipepiboleitemPojo.getPrice() == null) wkWipepiboleitemPojo.setPrice(0D);
        if (wkWipepiboleitemPojo.getAmount() == null) wkWipepiboleitemPojo.setAmount(0D);
        if (wkWipepiboleitemPojo.getItemtaxrate() == null) wkWipepiboleitemPojo.setItemtaxrate(0);
        if (wkWipepiboleitemPojo.getStartdate() == null) wkWipepiboleitemPojo.setStartdate(new Date());
        if (wkWipepiboleitemPojo.getPlandate() == null) wkWipepiboleitemPojo.setPlandate(new Date());
        if (wkWipepiboleitemPojo.getQuantity() == null) wkWipepiboleitemPojo.setQuantity(0D);
        if (wkWipepiboleitemPojo.getRequqty() == null) wkWipepiboleitemPojo.setRequqty(0D);
        if (wkWipepiboleitemPojo.getFinishqty() == null) wkWipepiboleitemPojo.setFinishqty(0D);
        if (wkWipepiboleitemPojo.getMrbqty() == null) wkWipepiboleitemPojo.setMrbqty(0D);
        if (wkWipepiboleitemPojo.getClosed() == null) wkWipepiboleitemPojo.setClosed(0);
        if (wkWipepiboleitemPojo.getRemark() == null) wkWipepiboleitemPojo.setRemark("");
        if (wkWipepiboleitemPojo.getVirtualitem() == null) wkWipepiboleitemPojo.setVirtualitem(0);
        if (wkWipepiboleitemPojo.getRownum() == null) wkWipepiboleitemPojo.setRownum(0);
        if (wkWipepiboleitemPojo.getWkwpname() == null) wkWipepiboleitemPojo.setWkwpname("");
        if (wkWipepiboleitemPojo.getCustomer() == null) wkWipepiboleitemPojo.setCustomer("");
        if (wkWipepiboleitemPojo.getCustpo() == null) wkWipepiboleitemPojo.setCustpo("");
        if (wkWipepiboleitemPojo.getMachuid() == null) wkWipepiboleitemPojo.setMachuid("");
        if (wkWipepiboleitemPojo.getMachgroupid() == null) wkWipepiboleitemPojo.setMachgroupid("");
        if (wkWipepiboleitemPojo.getMachitemid() == null) wkWipepiboleitemPojo.setMachitemid("");
        if (wkWipepiboleitemPojo.getMainplanuid() == null) wkWipepiboleitemPojo.setMainplanuid("");
        if (wkWipepiboleitemPojo.getMainplanitemid() == null) wkWipepiboleitemPojo.setMainplanitemid("");
        if (wkWipepiboleitemPojo.getStatecode() == null) wkWipepiboleitemPojo.setStatecode("");
        if (wkWipepiboleitemPojo.getStatedate() == null) wkWipepiboleitemPojo.setStatedate(new Date());
        if (wkWipepiboleitemPojo.getDisannullisterid() == null) wkWipepiboleitemPojo.setDisannullisterid("");
        if (wkWipepiboleitemPojo.getDisannullister() == null) wkWipepiboleitemPojo.setDisannullister("");
        if (wkWipepiboleitemPojo.getDisannuldate() == null) wkWipepiboleitemPojo.setDisannuldate(new Date());
        if (wkWipepiboleitemPojo.getDisannulmark() == null) wkWipepiboleitemPojo.setDisannulmark(0);
        if (wkWipepiboleitemPojo.getAttributejson() == null) wkWipepiboleitemPojo.setAttributejson("");
        if (wkWipepiboleitemPojo.getCustom1() == null) wkWipepiboleitemPojo.setCustom1("");
        if (wkWipepiboleitemPojo.getCustom2() == null) wkWipepiboleitemPojo.setCustom2("");
        if (wkWipepiboleitemPojo.getCustom3() == null) wkWipepiboleitemPojo.setCustom3("");
        if (wkWipepiboleitemPojo.getCustom4() == null) wkWipepiboleitemPojo.setCustom4("");
        if (wkWipepiboleitemPojo.getCustom5() == null) wkWipepiboleitemPojo.setCustom5("");
        if (wkWipepiboleitemPojo.getCustom6() == null) wkWipepiboleitemPojo.setCustom6("");
        if (wkWipepiboleitemPojo.getCustom7() == null) wkWipepiboleitemPojo.setCustom7("");
        if (wkWipepiboleitemPojo.getCustom8() == null) wkWipepiboleitemPojo.setCustom8("");
        if (wkWipepiboleitemPojo.getCustom9() == null) wkWipepiboleitemPojo.setCustom9("");
        if (wkWipepiboleitemPojo.getCustom10() == null) wkWipepiboleitemPojo.setCustom10("");
        if (wkWipepiboleitemPojo.getTenantid() == null) wkWipepiboleitemPojo.setTenantid("");
        if (wkWipepiboleitemPojo.getRevision() == null) wkWipepiboleitemPojo.setRevision(0);
        return wkWipepiboleitemPojo;
    }
}
