package inks.service.std.manu.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 上料记录(WkSmtpartrec)实体类
 *
 * <AUTHOR>
 * @since 2022-03-21 11:14:04
 */
public class WkSmtpartrecEntity implements Serializable {
    private static final long serialVersionUID = -52361299349679263L;
         // id
         private String id;
         // 编码
         private String refno;
         // 单据类型
         private String billtype;
         // 单据标题
         private String billtitle;
         // 单据日期
         private Date billdate;
         // 上料单号
         private String partuid;
         // 上料单Itemid
         private String partitemid;
         // 加工单号
         private String workuid;
         // 加工单Itemid
         private String workitemid;
         // 销售单号
         private String machuid;
         // 销售子项id
         private String machitemid;
         // 销售客户id
         private String machgroupid;
         // 设备编码
         private String devcode;
         // 飞达序号
         private Integer feedernum;
         // 飞达编码
         private String feedercode;
         // 站位序号
         private Integer stationnum;
         // 站位编码
         private String stationcode;
         // 点位符号
         private String pointmark;
         // 货品id
         private String goodsid;
         // 前个包装SN
         private String prevpacksn;
         // 当前包装SN
         private String packsn;
         // 批号
         private String batchno;
         // 产品编码
         private String itemcode;
         // 产品名称
         private String itemname;
         // 产品规格
         private String itemspec;
         // 产品单位
         private String itemunit;
         // 单件配数
         private Double singleqty;
         // 上料数量
         private Double quantity;
         // 经办人id
         private String operatorid;
         // 经办人
         private String operator;
         // 行号
         private Integer rownum;
         // Qc飞达编码
         private String qcfeedercode;
         // Qc站位编码
         private String qcstationcode;
         // Qc前个包装SN
         private String qcprevpacksn;
         // Qc当前包装SN
         private String qcpacksn;
         // Qc状态0未检1通过2不过
         private Integer qcstatusnum;
         // 检查员
         private String inspector;
         // 检查员id
         private String inspectorid;
         // 检查时间
         private Date inspdate;
         // 备注
         private String remark;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 自定义6
         private String custom6;
         // 自定义7
         private String custom7;
         // 自定义8
         private String custom8;
         // 自定义9
         private String custom9;
         // 自定义10
         private String custom10;
         // 租户id
         private String tenantid;
         // 租户id
         private String tenantname;
         // 乐观锁
         private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 编码
    public String getRefno() {
        return refno;
    }
    
    public void setRefno(String refno) {
        this.refno = refno;
    }
        
// 单据类型
    public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
// 单据标题
    public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
// 单据日期
    public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
// 上料单号
    public String getPartuid() {
        return partuid;
    }
    
    public void setPartuid(String partuid) {
        this.partuid = partuid;
    }
        
// 上料单Itemid
    public String getPartitemid() {
        return partitemid;
    }
    
    public void setPartitemid(String partitemid) {
        this.partitemid = partitemid;
    }
        
// 加工单号
    public String getWorkuid() {
        return workuid;
    }
    
    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }
        
// 加工单Itemid
    public String getWorkitemid() {
        return workitemid;
    }
    
    public void setWorkitemid(String workitemid) {
        this.workitemid = workitemid;
    }
        
// 销售单号
    public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
// 销售子项id
    public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
// 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }
    
    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }
        
// 设备编码
    public String getDevcode() {
        return devcode;
    }
    
    public void setDevcode(String devcode) {
        this.devcode = devcode;
    }
        
// 飞达序号
    public Integer getFeedernum() {
        return feedernum;
    }
    
    public void setFeedernum(Integer feedernum) {
        this.feedernum = feedernum;
    }
        
// 飞达编码
    public String getFeedercode() {
        return feedercode;
    }
    
    public void setFeedercode(String feedercode) {
        this.feedercode = feedercode;
    }
        
// 站位序号
    public Integer getStationnum() {
        return stationnum;
    }
    
    public void setStationnum(Integer stationnum) {
        this.stationnum = stationnum;
    }
        
// 站位编码
    public String getStationcode() {
        return stationcode;
    }
    
    public void setStationcode(String stationcode) {
        this.stationcode = stationcode;
    }
        
// 点位符号
    public String getPointmark() {
        return pointmark;
    }
    
    public void setPointmark(String pointmark) {
        this.pointmark = pointmark;
    }
        
// 货品id
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
// 前个包装SN
    public String getPrevpacksn() {
        return prevpacksn;
    }
    
    public void setPrevpacksn(String prevpacksn) {
        this.prevpacksn = prevpacksn;
    }
        
// 当前包装SN
    public String getPacksn() {
        return packsn;
    }
    
    public void setPacksn(String packsn) {
        this.packsn = packsn;
    }
        
// 批号
    public String getBatchno() {
        return batchno;
    }
    
    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }
        
// 产品编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
// 产品名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
// 产品规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
// 产品单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
// 单件配数
    public Double getSingleqty() {
        return singleqty;
    }
    
    public void setSingleqty(Double singleqty) {
        this.singleqty = singleqty;
    }
        
// 上料数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
// 经办人id
    public String getOperatorid() {
        return operatorid;
    }
    
    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }
        
// 经办人
    public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// Qc飞达编码
    public String getQcfeedercode() {
        return qcfeedercode;
    }
    
    public void setQcfeedercode(String qcfeedercode) {
        this.qcfeedercode = qcfeedercode;
    }
        
// Qc站位编码
    public String getQcstationcode() {
        return qcstationcode;
    }
    
    public void setQcstationcode(String qcstationcode) {
        this.qcstationcode = qcstationcode;
    }
        
// Qc前个包装SN
    public String getQcprevpacksn() {
        return qcprevpacksn;
    }
    
    public void setQcprevpacksn(String qcprevpacksn) {
        this.qcprevpacksn = qcprevpacksn;
    }
        
// Qc当前包装SN
    public String getQcpacksn() {
        return qcpacksn;
    }
    
    public void setQcpacksn(String qcpacksn) {
        this.qcpacksn = qcpacksn;
    }
        
// Qc状态0未检1通过2不过
    public Integer getQcstatusnum() {
        return qcstatusnum;
    }
    
    public void setQcstatusnum(Integer qcstatusnum) {
        this.qcstatusnum = qcstatusnum;
    }
        
// 检查员
    public String getInspector() {
        return inspector;
    }
    
    public void setInspector(String inspector) {
        this.inspector = inspector;
    }
        
// 检查员id
    public String getInspectorid() {
        return inspectorid;
    }
    
    public void setInspectorid(String inspectorid) {
        this.inspectorid = inspectorid;
    }
        
// 检查时间
    public Date getInspdate() {
        return inspdate;
    }
    
    public void setInspdate(Date inspdate) {
        this.inspdate = inspdate;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户id
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

