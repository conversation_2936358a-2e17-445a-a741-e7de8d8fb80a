package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkManureportitemEntity;
import inks.service.std.manu.domain.pojo.WkManureportitemPojo;
import inks.service.std.manu.mapper.WkManureportitemMapper;
import inks.service.std.manu.service.WkManureportitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 生产报工明细(WkManureportitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-04 16:19:07
 */
@Service("wkManureportitemService")
public class WkManureportitemServiceImpl implements WkManureportitemService {
    @Resource
    private WkManureportitemMapper wkManureportitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkManureportitemPojo getEntity(String key, String tid) {
        return this.wkManureportitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkManureportitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkManureportitemPojo> lst = wkManureportitemMapper.getPageList(queryParam);
            PageInfo<WkManureportitemPojo> pageInfo = new PageInfo<WkManureportitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkManureportitemPojo> getList(String Pid, String tid) {
        try {
            List<WkManureportitemPojo> lst = wkManureportitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkManureportitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkManureportitemPojo insert(WkManureportitemPojo wkManureportitemPojo) {
        //初始化item的NULL
        WkManureportitemPojo itempojo = this.clearNull(wkManureportitemPojo);
        WkManureportitemEntity wkManureportitemEntity = new WkManureportitemEntity();
        BeanUtils.copyProperties(itempojo, wkManureportitemEntity);

        wkManureportitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkManureportitemEntity.setRevision(1);  //乐观锁
        this.wkManureportitemMapper.insert(wkManureportitemEntity);
        return this.getEntity(wkManureportitemEntity.getId(), wkManureportitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkManureportitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkManureportitemPojo update(WkManureportitemPojo wkManureportitemPojo) {
        WkManureportitemEntity wkManureportitemEntity = new WkManureportitemEntity();
        BeanUtils.copyProperties(wkManureportitemPojo, wkManureportitemEntity);
        this.wkManureportitemMapper.update(wkManureportitemEntity);
        return this.getEntity(wkManureportitemEntity.getId(), wkManureportitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkManureportitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkManureportitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkManureportitemPojo clearNull(WkManureportitemPojo wkManureportitemPojo) {
        //初始化NULL字段
        if (wkManureportitemPojo.getPid() == null) wkManureportitemPojo.setPid("");
        if (wkManureportitemPojo.getWorkdate() == null) wkManureportitemPojo.setWorkdate("");
        if (wkManureportitemPojo.getWorkuid() == null) wkManureportitemPojo.setWorkuid("");
        if (wkManureportitemPojo.getWorkitemid() == null) wkManureportitemPojo.setWorkitemid("");
        if (wkManureportitemPojo.getGoodsid() == null) wkManureportitemPojo.setGoodsid("");
        if (wkManureportitemPojo.getItemcode() == null) wkManureportitemPojo.setItemcode("");
        if (wkManureportitemPojo.getItemname() == null) wkManureportitemPojo.setItemname("");
        if (wkManureportitemPojo.getItemspec() == null) wkManureportitemPojo.setItemspec("");
        if (wkManureportitemPojo.getItemunit() == null) wkManureportitemPojo.setItemunit("");
        if (wkManureportitemPojo.getWorkqty() == null) wkManureportitemPojo.setWorkqty(0D);
        if (wkManureportitemPojo.getQuantity() == null) wkManureportitemPojo.setQuantity(0D);
        if (wkManureportitemPojo.getMrbqty() == null) wkManureportitemPojo.setMrbqty(0D);
        if (wkManureportitemPojo.getSubitemid() == null) wkManureportitemPojo.setSubitemid("");
        if (wkManureportitemPojo.getSubuse() == null) wkManureportitemPojo.setSubuse("");
        if (wkManureportitemPojo.getSubunit() == null) wkManureportitemPojo.setSubunit("");
        if (wkManureportitemPojo.getSubqty() == null) wkManureportitemPojo.setSubqty(0D);
        if (wkManureportitemPojo.getTaxprice() == null) wkManureportitemPojo.setTaxprice(0D);
        if (wkManureportitemPojo.getTaxamount() == null) wkManureportitemPojo.setTaxamount(0D);
        if (wkManureportitemPojo.getPrice() == null) wkManureportitemPojo.setPrice(0D);
        if (wkManureportitemPojo.getAmount() == null) wkManureportitemPojo.setAmount(0D);
        if (wkManureportitemPojo.getTaxtotal() == null) wkManureportitemPojo.setTaxtotal(0D);
        if (wkManureportitemPojo.getItemtaxrate() == null) wkManureportitemPojo.setItemtaxrate(0);
        if (wkManureportitemPojo.getStartdate() == null) wkManureportitemPojo.setStartdate(new Date());
        if (wkManureportitemPojo.getPlandate() == null) wkManureportitemPojo.setPlandate(new Date());
        if (wkManureportitemPojo.getFinishqty() == null) wkManureportitemPojo.setFinishqty(0D);
        if (wkManureportitemPojo.getFinishhour() == null) wkManureportitemPojo.setFinishhour(0D);
        if (wkManureportitemPojo.getClosed() == null) wkManureportitemPojo.setClosed(0);
        if (wkManureportitemPojo.getRemark() == null) wkManureportitemPojo.setRemark("");
        if (wkManureportitemPojo.getStatecode() == null) wkManureportitemPojo.setStatecode("");
        if (wkManureportitemPojo.getStatedate() == null) wkManureportitemPojo.setStatedate(new Date());
        if (wkManureportitemPojo.getRownum() == null) wkManureportitemPojo.setRownum(0);
        if (wkManureportitemPojo.getMachuid() == null) wkManureportitemPojo.setMachuid("");
        if (wkManureportitemPojo.getMachitemid() == null) wkManureportitemPojo.setMachitemid("");
        if (wkManureportitemPojo.getMachgroupid() == null) wkManureportitemPojo.setMachgroupid("");
        if (wkManureportitemPojo.getMrpuid() == null) wkManureportitemPojo.setMrpuid("");
        if (wkManureportitemPojo.getMrpitemid() == null) wkManureportitemPojo.setMrpitemid("");
        if (wkManureportitemPojo.getCustomer() == null) wkManureportitemPojo.setCustomer("");
        if (wkManureportitemPojo.getCustpo() == null) wkManureportitemPojo.setCustpo("");
        if (wkManureportitemPojo.getMainplanuid() == null) wkManureportitemPojo.setMainplanuid("");
        if (wkManureportitemPojo.getMainplanitemid() == null) wkManureportitemPojo.setMainplanitemid("");
        if (wkManureportitemPojo.getLocation() == null) wkManureportitemPojo.setLocation("");
        if (wkManureportitemPojo.getBatchno() == null) wkManureportitemPojo.setBatchno("");
        if (wkManureportitemPojo.getDisannulmark() == null) wkManureportitemPojo.setDisannulmark(0);
        if (wkManureportitemPojo.getDisannullister() == null) wkManureportitemPojo.setDisannullister("");
        if (wkManureportitemPojo.getDisannullisterid() == null) wkManureportitemPojo.setDisannullisterid("");
        if (wkManureportitemPojo.getDisannuldate() == null) wkManureportitemPojo.setDisannuldate(new Date());
        if (wkManureportitemPojo.getAttributejson() == null) wkManureportitemPojo.setAttributejson("");
        if (wkManureportitemPojo.getCustom1() == null) wkManureportitemPojo.setCustom1("");
        if (wkManureportitemPojo.getCustom2() == null) wkManureportitemPojo.setCustom2("");
        if (wkManureportitemPojo.getCustom3() == null) wkManureportitemPojo.setCustom3("");
        if (wkManureportitemPojo.getCustom4() == null) wkManureportitemPojo.setCustom4("");
        if (wkManureportitemPojo.getCustom5() == null) wkManureportitemPojo.setCustom5("");
        if (wkManureportitemPojo.getCustom6() == null) wkManureportitemPojo.setCustom6("");
        if (wkManureportitemPojo.getCustom7() == null) wkManureportitemPojo.setCustom7("");
        if (wkManureportitemPojo.getCustom8() == null) wkManureportitemPojo.setCustom8("");
        if (wkManureportitemPojo.getCustom9() == null) wkManureportitemPojo.setCustom9("");
        if (wkManureportitemPojo.getCustom10() == null) wkManureportitemPojo.setCustom10("");
        if (wkManureportitemPojo.getTenantid() == null) wkManureportitemPojo.setTenantid("");
        if (wkManureportitemPojo.getRevision() == null) wkManureportitemPojo.setRevision(0);
        return wkManureportitemPojo;
    }
}
