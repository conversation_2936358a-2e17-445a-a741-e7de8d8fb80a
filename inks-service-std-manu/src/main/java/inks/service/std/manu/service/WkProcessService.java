package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkProcessPojo;
import inks.service.std.manu.domain.pojo.WkProcessitemdetailPojo;

import java.util.List;

/**
 * 生产工序(WkProcess)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 20:42:50
 */
public interface WkProcessService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProcessPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkProcessitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProcessPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkProcessPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkProcessPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkProcessPojo 实例对象
     * @return 实例对象
     */
    WkProcessPojo insert(WkProcessPojo wkProcessPojo);

    /**
     * 修改数据
     *
     * @param wkProcesspojo 实例对象
     * @return 实例对象
     */
    WkProcessPojo update(WkProcessPojo wkProcesspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    List<WkProcessPojo> getPostList(String tenantid);

    WkProcessPojo getEntityByWpName(String wpname, String tenantid);
}
