package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWorksheetmergeEntity;
import inks.service.std.manu.domain.pojo.WkWorksheetmergePojo;
import inks.service.std.manu.mapper.WkWorksheetmergeMapper;
import inks.service.std.manu.service.WkWorksheetmergeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 加工单合并记录(WkWorksheetmerge)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-15 10:20:46
 */
@Service("wkWorksheetmergeService")
public class WkWorksheetmergeServiceImpl implements WkWorksheetmergeService {
    @Resource
    private WkWorksheetmergeMapper wkWorksheetmergeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWorksheetmergePojo getEntity(String key, String tid) {
        return this.wkWorksheetmergeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWorksheetmergePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWorksheetmergePojo> lst = wkWorksheetmergeMapper.getPageList(queryParam);
            PageInfo<WkWorksheetmergePojo> pageInfo = new PageInfo<WkWorksheetmergePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWorksheetmergePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetmergePojo insert(WkWorksheetmergePojo wkWorksheetmergePojo) {
        //初始化NULL字段
        if (wkWorksheetmergePojo.getItemid() == null) wkWorksheetmergePojo.setItemid("");
        if (wkWorksheetmergePojo.getGoodsid() == null) wkWorksheetmergePojo.setGoodsid("");
        if (wkWorksheetmergePojo.getQuantity() == null) wkWorksheetmergePojo.setQuantity(0D);
        if (wkWorksheetmergePojo.getWorkuid() == null) wkWorksheetmergePojo.setWorkuid("");
        if (wkWorksheetmergePojo.getWorkitemid() == null) wkWorksheetmergePojo.setWorkitemid("");
        if (wkWorksheetmergePojo.getMainmark() == null) wkWorksheetmergePojo.setMainmark(0);
        if (wkWorksheetmergePojo.getRownum() == null) wkWorksheetmergePojo.setRownum(0);
        if (wkWorksheetmergePojo.getMachtype() == null) wkWorksheetmergePojo.setMachtype("");
        if (wkWorksheetmergePojo.getMachuid() == null) wkWorksheetmergePojo.setMachuid("");
        if (wkWorksheetmergePojo.getMachitemid() == null) wkWorksheetmergePojo.setMachitemid("");
        if (wkWorksheetmergePojo.getMachgroupid() == null) wkWorksheetmergePojo.setMachgroupid("");
        if (wkWorksheetmergePojo.getCustomer() == null) wkWorksheetmergePojo.setCustomer("");
        if (wkWorksheetmergePojo.getCustpo() == null) wkWorksheetmergePojo.setCustpo("");
        if (wkWorksheetmergePojo.getCiteuid() == null) wkWorksheetmergePojo.setCiteuid("");
        if (wkWorksheetmergePojo.getCiteitemid() == null) wkWorksheetmergePojo.setCiteitemid("");
        if (wkWorksheetmergePojo.getMainplanuid() == null) wkWorksheetmergePojo.setMainplanuid("");
        if (wkWorksheetmergePojo.getMainplanitemid() == null) wkWorksheetmergePojo.setMainplanitemid("");
        if (wkWorksheetmergePojo.getAttributejson() == null) wkWorksheetmergePojo.setAttributejson("");
        if (wkWorksheetmergePojo.getWkpcsqty() == null) wkWorksheetmergePojo.setWkpcsqty(0D);
        if (wkWorksheetmergePojo.getWksecqty() == null) wkWorksheetmergePojo.setWksecqty(0D);
        if (wkWorksheetmergePojo.getRemark() == null) wkWorksheetmergePojo.setRemark("");
        if (wkWorksheetmergePojo.getCreateby() == null) wkWorksheetmergePojo.setCreateby("");
        if (wkWorksheetmergePojo.getCreatebyid() == null) wkWorksheetmergePojo.setCreatebyid("");
        if (wkWorksheetmergePojo.getCreatedate() == null) wkWorksheetmergePojo.setCreatedate(new Date());
        if (wkWorksheetmergePojo.getLister() == null) wkWorksheetmergePojo.setLister("");
        if (wkWorksheetmergePojo.getListerid() == null) wkWorksheetmergePojo.setListerid("");
        if (wkWorksheetmergePojo.getModifydate() == null) wkWorksheetmergePojo.setModifydate(new Date());
        if (wkWorksheetmergePojo.getCustom1() == null) wkWorksheetmergePojo.setCustom1("");
        if (wkWorksheetmergePojo.getCustom2() == null) wkWorksheetmergePojo.setCustom2("");
        if (wkWorksheetmergePojo.getCustom3() == null) wkWorksheetmergePojo.setCustom3("");
        if (wkWorksheetmergePojo.getCustom4() == null) wkWorksheetmergePojo.setCustom4("");
        if (wkWorksheetmergePojo.getCustom5() == null) wkWorksheetmergePojo.setCustom5("");
        if (wkWorksheetmergePojo.getCustom6() == null) wkWorksheetmergePojo.setCustom6("");
        if (wkWorksheetmergePojo.getCustom7() == null) wkWorksheetmergePojo.setCustom7("");
        if (wkWorksheetmergePojo.getCustom8() == null) wkWorksheetmergePojo.setCustom8("");
        if (wkWorksheetmergePojo.getCustom9() == null) wkWorksheetmergePojo.setCustom9("");
        if (wkWorksheetmergePojo.getCustom10() == null) wkWorksheetmergePojo.setCustom10("");
        if (wkWorksheetmergePojo.getTenantid() == null) wkWorksheetmergePojo.setTenantid("");
        if (wkWorksheetmergePojo.getTenantname() == null) wkWorksheetmergePojo.setTenantname("");
        if (wkWorksheetmergePojo.getRevision() == null) wkWorksheetmergePojo.setRevision(0);
        WkWorksheetmergeEntity wkWorksheetmergeEntity = new WkWorksheetmergeEntity();
        BeanUtils.copyProperties(wkWorksheetmergePojo, wkWorksheetmergeEntity);
        //生成雪花id
        wkWorksheetmergeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWorksheetmergeEntity.setRevision(1);  //乐观锁
        this.wkWorksheetmergeMapper.insert(wkWorksheetmergeEntity);
        return this.getEntity(wkWorksheetmergeEntity.getId(), wkWorksheetmergeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWorksheetmergePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetmergePojo update(WkWorksheetmergePojo wkWorksheetmergePojo) {
        WkWorksheetmergeEntity wkWorksheetmergeEntity = new WkWorksheetmergeEntity();
        BeanUtils.copyProperties(wkWorksheetmergePojo, wkWorksheetmergeEntity);
        this.wkWorksheetmergeMapper.update(wkWorksheetmergeEntity);
        return this.getEntity(wkWorksheetmergeEntity.getId(), wkWorksheetmergeEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWorksheetmergeMapper.delete(key, tid);
    }

    @Override
    public int updateMergeItemid(String mergeItemid, String itemids, String tid) {
        return this.wkWorksheetmergeMapper.updateMergeItemid(mergeItemid, itemids, tid);
    }
}
