package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWiptasksitemPojo;
import inks.service.std.manu.domain.WkWiptasksitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 派工单子表(WkWiptasksitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-06 14:34:14
 */
 @Mapper
public interface WkWiptasksitemMapper {

    WkWiptasksitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<WkWiptasksitemPojo> getPageList(QueryParam queryParam);

    List<WkWiptasksitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(WkWiptasksitemEntity wkWiptasksitemEntity);

    int update(WkWiptasksitemEntity wkWiptasksitemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    void syncWipItemTasksQty(String wipitemid, String tid);
}

