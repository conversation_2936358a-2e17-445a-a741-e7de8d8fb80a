package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipgroupitemPojo;

import java.util.List;

/**
 * WIP分管工序(WkWipgroupitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-26 10:08:03
 */
public interface WkWipgroupitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipgroupitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipgroupitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipgroupitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkWipgroupitemPojo 实例对象
     * @return 实例对象
     */
    WkWipgroupitemPojo insert(WkWipgroupitemPojo wkWipgroupitemPojo);

    /**
     * 修改数据
     *
     * @param wkWipgroupitempojo 实例对象
     * @return 实例对象
     */
    WkWipgroupitemPojo update(WkWipgroupitemPojo wkWipgroupitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkWipgroupitempojo 实例对象
     * @return 实例对象
     */
    WkWipgroupitemPojo clearNull(WkWipgroupitemPojo wkWipgroupitempojo);
}
