package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkStepprogroupitemEntity;
import inks.service.std.manu.domain.pojo.WkStepprogroupitemPojo;
import inks.service.std.manu.mapper.WkStepprogroupitemMapper;
import inks.service.std.manu.service.WkStepprogroupitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 阶梯项目(WkStepprogroupitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-01 16:33:50
 */
@Service("wkStepprogroupitemService")
public class WkStepprogroupitemServiceImpl implements WkStepprogroupitemService {
    @Resource
    private WkStepprogroupitemMapper wkStepprogroupitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkStepprogroupitemPojo getEntity(String key, String tid) {
        return this.wkStepprogroupitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkStepprogroupitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkStepprogroupitemPojo> lst = wkStepprogroupitemMapper.getPageList(queryParam);
            PageInfo<WkStepprogroupitemPojo> pageInfo = new PageInfo<WkStepprogroupitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkStepprogroupitemPojo> getList(String Pid, String tid) {
        try {
            List<WkStepprogroupitemPojo> lst = wkStepprogroupitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkStepprogroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkStepprogroupitemPojo insert(WkStepprogroupitemPojo wkStepprogroupitemPojo) {
        //初始化item的NULL
        WkStepprogroupitemPojo itempojo = this.clearNull(wkStepprogroupitemPojo);
        WkStepprogroupitemEntity wkStepprogroupitemEntity = new WkStepprogroupitemEntity();
        BeanUtils.copyProperties(itempojo, wkStepprogroupitemEntity);
        //生成雪花id
        wkStepprogroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkStepprogroupitemEntity.setRevision(1);  //乐观锁
        this.wkStepprogroupitemMapper.insert(wkStepprogroupitemEntity);
        return this.getEntity(wkStepprogroupitemEntity.getId(), wkStepprogroupitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkStepprogroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkStepprogroupitemPojo update(WkStepprogroupitemPojo wkStepprogroupitemPojo) {
        WkStepprogroupitemEntity wkStepprogroupitemEntity = new WkStepprogroupitemEntity();
        BeanUtils.copyProperties(wkStepprogroupitemPojo, wkStepprogroupitemEntity);
        this.wkStepprogroupitemMapper.update(wkStepprogroupitemEntity);
        return this.getEntity(wkStepprogroupitemEntity.getId(), wkStepprogroupitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkStepprogroupitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkStepprogroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkStepprogroupitemPojo clearNull(WkStepprogroupitemPojo wkStepprogroupitemPojo) {
        //初始化NULL字段
        if (wkStepprogroupitemPojo.getPid() == null) wkStepprogroupitemPojo.setPid("");
        if (wkStepprogroupitemPojo.getStartqty() == null) wkStepprogroupitemPojo.setStartqty(0);
        if (wkStepprogroupitemPojo.getEndqty() == null) wkStepprogroupitemPojo.setEndqty(0);
        if (wkStepprogroupitemPojo.getGroupcode() == null) wkStepprogroupitemPojo.setGroupcode("");
        if (wkStepprogroupitemPojo.getGroupname() == null) wkStepprogroupitemPojo.setGroupname("");
        if (wkStepprogroupitemPojo.getFlowdesc() == null) wkStepprogroupitemPojo.setFlowdesc("");
        if (wkStepprogroupitemPojo.getFlowcount() == null) wkStepprogroupitemPojo.setFlowcount(0);
        if (wkStepprogroupitemPojo.getFlowjson() == null) wkStepprogroupitemPojo.setFlowjson("");
        if (wkStepprogroupitemPojo.getRownum() == null) wkStepprogroupitemPojo.setRownum(0);
        if (wkStepprogroupitemPojo.getRemark() == null) wkStepprogroupitemPojo.setRemark("");
        if (wkStepprogroupitemPojo.getCustom1() == null) wkStepprogroupitemPojo.setCustom1("");
        if (wkStepprogroupitemPojo.getCustom2() == null) wkStepprogroupitemPojo.setCustom2("");
        if (wkStepprogroupitemPojo.getCustom3() == null) wkStepprogroupitemPojo.setCustom3("");
        if (wkStepprogroupitemPojo.getCustom4() == null) wkStepprogroupitemPojo.setCustom4("");
        if (wkStepprogroupitemPojo.getCustom5() == null) wkStepprogroupitemPojo.setCustom5("");
        if (wkStepprogroupitemPojo.getCustom6() == null) wkStepprogroupitemPojo.setCustom6("");
        if (wkStepprogroupitemPojo.getCustom7() == null) wkStepprogroupitemPojo.setCustom7("");
        if (wkStepprogroupitemPojo.getCustom8() == null) wkStepprogroupitemPojo.setCustom8("");
        if (wkStepprogroupitemPojo.getCustom9() == null) wkStepprogroupitemPojo.setCustom9("");
        if (wkStepprogroupitemPojo.getCustom10() == null) wkStepprogroupitemPojo.setCustom10("");
        if (wkStepprogroupitemPojo.getTenantid() == null) wkStepprogroupitemPojo.setTenantid("");
        if (wkStepprogroupitemPojo.getRevision() == null) wkStepprogroupitemPojo.setRevision(0);
        return wkStepprogroupitemPojo;
    }
}
