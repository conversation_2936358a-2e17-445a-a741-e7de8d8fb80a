package inks.service.std.manu.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 上料表项目(WkSmtpartitem)Entity
 *
 * <AUTHOR>
 * @since 2022-05-28 09:24:34
 */
public class WkSmtpartitemEntity implements Serializable {
    private static final long serialVersionUID = 664471460182101349L;
          // id
         private String id;
          // Pid
         private String pid;
          // 设备编码
         private String devcode;
          // 表名(备用)
         private String tablecode;
          // 站位序号
         private Integer stationnum;
          // 站位编码
         private String stationcode;
          // 点位符号
         private String pointmark;
          // 货品id
         private String goodsid;
          // 物料类型
         private String itemtype;
          // 物料编码
         private String itemcode;
          // 物料名称
         private String itemname;
          // 物料规格
         private String itemspec;
          // 物料单位
         private String itemunit;
          // 单件配数
         private Double singleqty;
          // 数量
         private Double quantity;
          // 完工数量
         private Double finishqty;
          // 通过检查
         private Double acceptqty;
          // 行号
         private Integer rownum;
          // 备注
         private String remark;
          // 拣货料号
         private String pickcode;
          // 拣货日期
         private Date pickdate;
          // 拣货标记
         private Integer pickmark;
          // 拣货员
         private String picker;
          // 拣货员id
         private String pickerid;
          // 自定义1
         private String custom1;
          // 自定义2
         private String custom2;
          // 自定义3
         private String custom3;
          // 自定义4
         private String custom4;
          // 自定义5
         private String custom5;
          // 自定义6
         private String custom6;
          // 自定义7
         private String custom7;
          // 自定义8
         private String custom8;
          // 自定义9
         private String custom9;
          // 自定义10
         private String custom10;
          // 租户id
         private String tenantid;
          // 乐观锁
         private Integer revision;

    // id
      public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
    // Pid
      public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
    // 设备编码
      public String getDevcode() {
        return devcode;
    }
    
    public void setDevcode(String devcode) {
        this.devcode = devcode;
    }
        
    // 表名(备用)
      public String getTablecode() {
        return tablecode;
    }
    
    public void setTablecode(String tablecode) {
        this.tablecode = tablecode;
    }
        
    // 站位序号
      public Integer getStationnum() {
        return stationnum;
    }
    
    public void setStationnum(Integer stationnum) {
        this.stationnum = stationnum;
    }
        
    // 站位编码
      public String getStationcode() {
        return stationcode;
    }
    
    public void setStationcode(String stationcode) {
        this.stationcode = stationcode;
    }
        
    // 点位符号
      public String getPointmark() {
        return pointmark;
    }
    
    public void setPointmark(String pointmark) {
        this.pointmark = pointmark;
    }
        
    // 货品id
      public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
    // 物料类型
      public String getItemtype() {
        return itemtype;
    }
    
    public void setItemtype(String itemtype) {
        this.itemtype = itemtype;
    }
        
    // 物料编码
      public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
    // 物料名称
      public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
    // 物料规格
      public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
    // 物料单位
      public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
    // 单件配数
      public Double getSingleqty() {
        return singleqty;
    }
    
    public void setSingleqty(Double singleqty) {
        this.singleqty = singleqty;
    }
        
    // 数量
      public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
    // 完工数量
      public Double getFinishqty() {
        return finishqty;
    }
    
    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }
        
    // 通过检查
      public Double getAcceptqty() {
        return acceptqty;
    }
    
    public void setAcceptqty(Double acceptqty) {
        this.acceptqty = acceptqty;
    }
        
    // 行号
      public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
    // 备注
      public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
    // 拣货料号
      public String getPickcode() {
        return pickcode;
    }
    
    public void setPickcode(String pickcode) {
        this.pickcode = pickcode;
    }
        
    // 拣货日期
      public Date getPickdate() {
        return pickdate;
    }
    
    public void setPickdate(Date pickdate) {
        this.pickdate = pickdate;
    }
        
    // 拣货标记
      public Integer getPickmark() {
        return pickmark;
    }
    
    public void setPickmark(Integer pickmark) {
        this.pickmark = pickmark;
    }
        
    // 拣货员
      public String getPicker() {
        return picker;
    }
    
    public void setPicker(String picker) {
        this.picker = picker;
    }
        
    // 拣货员id
      public String getPickerid() {
        return pickerid;
    }
    
    public void setPickerid(String pickerid) {
        this.pickerid = pickerid;
    }
        
    // 自定义1
      public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
    // 自定义2
      public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
    // 自定义3
      public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
    // 自定义4
      public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
    // 自定义5
      public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
    // 自定义6
      public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
    // 自定义7
      public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
    // 自定义8
      public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
    // 自定义9
      public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
    // 自定义10
      public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
    // 租户id
      public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
    // 乐观锁
      public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

