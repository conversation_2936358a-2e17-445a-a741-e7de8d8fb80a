package inks.service.std.manu.utils;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.SerializationUtils;

import java.io.Serializable;

public class DeepCopyUtil {

    //1. 通过 JSON 序列化和反序列化实现深拷贝
    public static <T> T deepCopy(T object, Class<T> clazz) {
        String json = JSON.toJSONString(object);
        return JSON.parseObject(json, clazz);
    }

    //2. 使用 Apache Commons Lang 库的 SerializationUtils
    //如果对象实现了 Serializable 接口，可以使用 SerializationUtils 来实现深拷贝：
    public static <T extends Serializable> T deepCopy(T object) {
        return SerializationUtils.clone(object);
    }
}
