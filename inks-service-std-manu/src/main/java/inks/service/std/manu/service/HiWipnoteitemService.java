package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.HiWipnoteitemPojo;

import java.util.List;

/**
 * Wip记录子表(HiWipnoteitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-16 16:20:27
 */
public interface HiWipnoteitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    HiWipnoteitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<HiWipnoteitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<HiWipnoteitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param hiWipnoteitemPojo 实例对象
     * @return 实例对象
     */
    HiWipnoteitemPojo insert(HiWipnoteitemPojo hiWipnoteitemPojo);

    /**
     * 修改数据
     *
     * @param hiWipnoteitempojo 实例对象
     * @return 实例对象
     */
    HiWipnoteitemPojo update(HiWipnoteitemPojo hiWipnoteitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param hiWipnoteitempojo 实例对象
     * @return 实例对象
     */
    HiWipnoteitemPojo clearNull(HiWipnoteitemPojo hiWipnoteitempojo);
}
