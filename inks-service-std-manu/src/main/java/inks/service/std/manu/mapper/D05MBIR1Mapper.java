package inks.service.std.manu.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface D05MBIR1Mapper {
    /**
     * @return R<Map < Object>>
     * @Description 获取今日进度(工序进度)返回工序已完成数量, 总数量
     * <AUTHOR>
     * @time 2023/3/25 12:20
     */
    Map<String, Object> getProcessProgressToday(String tid);

    HashMap<String, String> getAuthByCode(String auth);

    int getFinishCountDeliToday(String tenantid);

    int getOnlineCountMachToday(String tenantid);

    Map<String, Object> getWipAddAndCompToday(@Param("tenantid") String tenantid, @Param("workshopid") String workshopid);

    List<Map<String, Object>> getWipQtyGroupByGoods(@Param("tenantid") String tenantid, @Param("workshopid") String workshopid, @Param("queryParam") QueryParam queryParam);

    double getAllWipQtyByWorkShopid(@Param("tenantid") String tenantid, @Param("workshopid") String workshopid, @Param("queryParam") QueryParam queryParam);

    List<Map<String, Object>> getSumWipCompQtyGroupByGoods( @Param("queryParam") QueryParam queryParam,@Param("tenantid")String tenantid);

    List<Map<String, Object>> getWipOnlineAndCompQtyEveryDay(@Param("queryParam") QueryParam queryParam, @Param("percent") Double percent, @Param("tenantid") String tenantid);

    List<Map<String, Object>> getWipSumWorkTime(@Param("queryParam") QueryParam queryParam, @Param("tenantid") String tenantid);

    List<ChartPojo> getSpuWeightGroupByGroup(QueryParam queryParam);
}
