package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkProcessEntity;
import inks.service.std.manu.domain.pojo.WkProcessPojo;
import inks.service.std.manu.domain.pojo.WkProcessitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 生产工序(WkProcess)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:42:50
 */
@Mapper
public interface WkProcessMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProcessPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkProcessitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkProcessPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param wkProcessEntity 实例对象
     * @return 影响行数
     */
    int insert(WkProcessEntity wkProcessEntity);

    
    /**
     * 修改数据
     *
     * @param wkProcessEntity 实例对象
     * @return 影响行数
     */
    int update(WkProcessEntity wkProcessEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param wkProcessPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(WkProcessPojo wkProcessPojo);

    List<WkProcessPojo> getAll(String tid);

    List<String> getDelCostIds(WkProcessPojo wkProcessPojo);

    List<String> getDelStatIds(WkProcessPojo wkProcessPojo);

    Map<String,String> getDefNameCode(String defectid, String tid);

    List<WkProcessitemdetailPojo> getList(String tenantid);

    List<WkProcessitemdetailPojo> getListByWpid(String tenantid);

    List<WkProcessPojo> getPostList(String tenantid);

    WkProcessPojo getEntityByWpName(String wpname, String tenantid);

    int getOnlineBatchCount(String wpid, String tid);

    String checkSectByWkid(String wpid, String tid);
}

