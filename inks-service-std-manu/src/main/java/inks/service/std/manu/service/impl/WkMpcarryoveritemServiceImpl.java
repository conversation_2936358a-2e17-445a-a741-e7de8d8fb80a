package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkMpcarryoveritemEntity;
import inks.service.std.manu.domain.pojo.WkMpcarryoveritemPojo;
import inks.service.std.manu.mapper.WkMpcarryoveritemMapper;
import inks.service.std.manu.service.WkMpcarryoveritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 结转子表(WkMpcarryoveritem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-09 09:43:09
 */
@Service("wkMpcarryoveritemService")
public class WkMpcarryoveritemServiceImpl implements WkMpcarryoveritemService {
    @Resource
    private WkMpcarryoveritemMapper wkMpcarryoveritemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMpcarryoveritemPojo getEntity(String key, String tid) {
        return this.wkMpcarryoveritemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMpcarryoveritemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMpcarryoveritemPojo> lst = wkMpcarryoveritemMapper.getPageList(queryParam);
            PageInfo<WkMpcarryoveritemPojo> pageInfo = new PageInfo<WkMpcarryoveritemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkMpcarryoveritemPojo> getList(String Pid, String tid) {
        try {
            List<WkMpcarryoveritemPojo> lst = wkMpcarryoveritemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkMpcarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMpcarryoveritemPojo insert(WkMpcarryoveritemPojo wkMpcarryoveritemPojo) {
        //初始化item的NULL
        WkMpcarryoveritemPojo itempojo = this.clearNull(wkMpcarryoveritemPojo);
        WkMpcarryoveritemEntity wkMpcarryoveritemEntity = new WkMpcarryoveritemEntity();
        BeanUtils.copyProperties(itempojo, wkMpcarryoveritemEntity);

        wkMpcarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkMpcarryoveritemEntity.setRevision(1);  //乐观锁
        this.wkMpcarryoveritemMapper.insert(wkMpcarryoveritemEntity);
        return this.getEntity(wkMpcarryoveritemEntity.getId(), wkMpcarryoveritemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkMpcarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMpcarryoveritemPojo update(WkMpcarryoveritemPojo wkMpcarryoveritemPojo) {
        WkMpcarryoveritemEntity wkMpcarryoveritemEntity = new WkMpcarryoveritemEntity();
        BeanUtils.copyProperties(wkMpcarryoveritemPojo, wkMpcarryoveritemEntity);
        this.wkMpcarryoveritemMapper.update(wkMpcarryoveritemEntity);
        return this.getEntity(wkMpcarryoveritemEntity.getId(), wkMpcarryoveritemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkMpcarryoveritemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkMpcarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMpcarryoveritemPojo clearNull(WkMpcarryoveritemPojo wkMpcarryoveritemPojo) {
        //初始化NULL字段
        if (wkMpcarryoveritemPojo.getPid() == null) wkMpcarryoveritemPojo.setPid("");
        if (wkMpcarryoveritemPojo.getGoodsid() == null) wkMpcarryoveritemPojo.setGoodsid("");
        if (wkMpcarryoveritemPojo.getItemcode() == null) wkMpcarryoveritemPojo.setItemcode("");
        if (wkMpcarryoveritemPojo.getItemname() == null) wkMpcarryoveritemPojo.setItemname("");
        if (wkMpcarryoveritemPojo.getItemspec() == null) wkMpcarryoveritemPojo.setItemspec("");
        if (wkMpcarryoveritemPojo.getItemunit() == null) wkMpcarryoveritemPojo.setItemunit("");
        if (wkMpcarryoveritemPojo.getOpenqty() == null) wkMpcarryoveritemPojo.setOpenqty(0D);
        if (wkMpcarryoveritemPojo.getOpenamount() == null) wkMpcarryoveritemPojo.setOpenamount(0D);
        if (wkMpcarryoveritemPojo.getInqty() == null) wkMpcarryoveritemPojo.setInqty(0D);
        if (wkMpcarryoveritemPojo.getInamount() == null) wkMpcarryoveritemPojo.setInamount(0D);
        if (wkMpcarryoveritemPojo.getOutqty() == null) wkMpcarryoveritemPojo.setOutqty(0D);
        if (wkMpcarryoveritemPojo.getOutamount() == null) wkMpcarryoveritemPojo.setOutamount(0D);
        if (wkMpcarryoveritemPojo.getCloseqty() == null) wkMpcarryoveritemPojo.setCloseqty(0D);
        if (wkMpcarryoveritemPojo.getCloseamount() == null) wkMpcarryoveritemPojo.setCloseamount(0D);
        if (wkMpcarryoveritemPojo.getSkuid() == null) wkMpcarryoveritemPojo.setSkuid("");
        if (wkMpcarryoveritemPojo.getAttributejson() == null) wkMpcarryoveritemPojo.setAttributejson("");
        if (wkMpcarryoveritemPojo.getRownum() == null) wkMpcarryoveritemPojo.setRownum(0);
        if (wkMpcarryoveritemPojo.getCustom1() == null) wkMpcarryoveritemPojo.setCustom1("");
        if (wkMpcarryoveritemPojo.getCustom2() == null) wkMpcarryoveritemPojo.setCustom2("");
        if (wkMpcarryoveritemPojo.getCustom3() == null) wkMpcarryoveritemPojo.setCustom3("");
        if (wkMpcarryoveritemPojo.getCustom4() == null) wkMpcarryoveritemPojo.setCustom4("");
        if (wkMpcarryoveritemPojo.getCustom5() == null) wkMpcarryoveritemPojo.setCustom5("");
        if (wkMpcarryoveritemPojo.getCustom6() == null) wkMpcarryoveritemPojo.setCustom6("");
        if (wkMpcarryoveritemPojo.getCustom7() == null) wkMpcarryoveritemPojo.setCustom7("");
        if (wkMpcarryoveritemPojo.getCustom8() == null) wkMpcarryoveritemPojo.setCustom8("");
        if (wkMpcarryoveritemPojo.getCustom9() == null) wkMpcarryoveritemPojo.setCustom9("");
        if (wkMpcarryoveritemPojo.getCustom10() == null) wkMpcarryoveritemPojo.setCustom10("");
        if (wkMpcarryoveritemPojo.getTenantid() == null) wkMpcarryoveritemPojo.setTenantid("");
        if (wkMpcarryoveritemPojo.getRevision() == null) wkMpcarryoveritemPojo.setRevision(0);
        return wkMpcarryoveritemPojo;
    }
}
