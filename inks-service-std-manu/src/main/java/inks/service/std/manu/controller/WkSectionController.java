package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkSectionPojo;
import inks.service.std.manu.domain.pojo.WkSectionitemPojo;
import inks.service.std.manu.domain.pojo.WkSectionitemdetailPojo;
import inks.service.std.manu.service.WkSectionService;
import inks.service.std.manu.service.WkSectionitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 生产工段(Wk_Section)表控制层
 *
 * <AUTHOR>
 * @since 2024-10-28 11:21:19
 */
//@RestController
//@RequestMapping("wkSection")
public class WkSectionController {

    private final static Logger logger = LoggerFactory.getLogger(WkSectionController.class);
    @Resource
    private WkSectionService wkSectionService;
    @Resource
    private WkSectionitemService wkSectionitemService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取生产工段详细信息", notes = "获取生产工段详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Section.List")
    public R<WkSectionPojo> getEntity(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkSectionService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Section.List")
    public R<PageInfo<WkSectionitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Section.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSectionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取生产工段详细信息", notes = "获取生产工段详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Section.List")
    public R<WkSectionPojo> getBillEntity(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkSectionService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Section.List")
    public R<PageInfo<WkSectionPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Section.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSectionService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Section.List")
    public R<PageInfo<WkSectionPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Section.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSectionService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增生产工段", notes = "新增生产工段", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Section.Add")
    public R<WkSectionPojo> create(@RequestBody String json) {
        try {
            WkSectionPojo wkSectionPojo = JSONArray.parseObject(json, WkSectionPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            wkSectionPojo.setCreateby(loginUser.getRealName());   // 创建者
            wkSectionPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkSectionPojo.setCreatedate(new Date());   // 创建时间
            wkSectionPojo.setLister(loginUser.getRealname());   // 制表
            wkSectionPojo.setListerid(loginUser.getUserid());    // 制表id            
            wkSectionPojo.setModifydate(new Date());   //修改时间
            wkSectionPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.wkSectionService.insert(wkSectionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改生产工段", notes = "修改生产工段", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Section.Edit")
    public R<WkSectionPojo> update(@RequestBody String json) {
        try {
            WkSectionPojo wkSectionPojo = JSONArray.parseObject(json, WkSectionPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            wkSectionPojo.setLister(loginUser.getRealname());   // 制表
            wkSectionPojo.setListerid(loginUser.getUserid());    // 制表id   
            wkSectionPojo.setModifydate(new Date());   //修改时间
            wkSectionPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.wkSectionService.update(wkSectionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除生产工段", notes = "删除生产工段", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Section.Delete")
    public R<Integer> delete(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkSectionService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value = " 新增生产工段Item", notes = "新增生产工段Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Section.Add")
    public R<WkSectionitemPojo> createItem(@RequestBody String json) {
        try {
            WkSectionitemPojo wkSectionitemPojo = JSONArray.parseObject(json, WkSectionitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            wkSectionitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkSectionitemService.insert(wkSectionitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改生产工段Item", notes = "修改生产工段Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Section.Edit")
    public R<WkSectionitemPojo> updateItem(@RequestBody String json) {
        try {
            WkSectionitemPojo wkSectionitemPojo = JSONArray.parseObject(json, WkSectionitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            wkSectionitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkSectionitemService.update(wkSectionitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除生产工段Item", notes = "删除生产工段Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Section.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkSectionitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Section.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        WkSectionPojo wkSectionPojo = this.wkSectionService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(wkSectionPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = wkSectionPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkSectionitemPojo wkSectionitemPojo = new WkSectionitemPojo();
                    wkSectionPojo.getItem().add(wkSectionitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(wkSectionPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

