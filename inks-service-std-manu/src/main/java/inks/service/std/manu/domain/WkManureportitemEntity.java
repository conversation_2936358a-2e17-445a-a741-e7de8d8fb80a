package inks.service.std.manu.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产报工明细(WkManureportitem)Entity
 *
 * <AUTHOR>
 * @since 2022-05-28 13:00:54
 */
public class WkManureportitemEntity implements Serializable {
    private static final long serialVersionUID = -88471200422280881L;
          // id
         private String id;
          // Pid
         private String pid;
          // 单据日期
         private String workdate;
          // 生产单号
         private String workuid;
          // 生产子项id
         private String workitemid;
          // 货品id
         private String goodsid;
          // 产品编码
         private String itemcode;
          // 产品名称
         private String itemname;
          // 产品规格
         private String itemspec;
          // 产品单位
         private String itemunit;
          // 生产数量
         private Double workqty;
          // 报工数量
         private Double quantity;
          // 报废数量
         private Double mrbqty;
          // 辅助单位id
         private String subitemid;
          // 结算方式
         private String subuse;
          // 辅助单位
         private String subunit;
          // 加工数量
         private Double subqty;
          // 含税单价
         private Double taxprice;
          // 含税金额
         private Double taxamount;
          // 加工单价
         private Double price;
          // 加工金额
         private Double amount;
          // 税额
         private Double taxtotal;
          // 税率(备用)
         private Integer itemtaxrate;
          // 计划开工
         private Date startdate;
          // 计划完工
         private Date plandate;
          // 完工数量
         private Double finishqty;
          // 完工工时
         private Double finishhour;
          // 关闭
         private Integer closed;
          // 备注
         private String remark;
          // 状态
         private String statecode;
          // 状态时间
         private Date statedate;
          // 行号
         private Integer rownum;
          // 销售单号
         private String machuid;
          // 销售子项id
         private String machitemid;
          // 销售客户id
         private String machgroupid;
          // Mrp单号
         private String mrpuid;
          // Mrp子项id
         private String mrpitemid;
          // 客户
         private String customer;
          // 客户PO
         private String custpo;
          // 主计划单号
         private String mainplanuid;
          // 主计划子项id
         private String mainplanitemid;
          // 指定库位
         private String location;
          // 指定批号
         private String batchno;
          // 作废
         private Integer disannulmark;
          // 作废制表
         private String disannullister;
          // 作废制表id
         private String disannullisterid;
          // 作废日期
         private Date disannuldate;
         private String attributejson;
          // 自定义1
         private String custom1;
          // 自定义2
         private String custom2;
          // 自定义3
         private String custom3;
          // 自定义4
         private String custom4;
          // 自定义5
         private String custom5;
          // 自定义6
         private String custom6;
          // 自定义7
         private String custom7;
          // 自定义8
         private String custom8;
          // 自定义9
         private String custom9;
          // 自定义10
         private String custom10;
          // 租户id
         private String tenantid;
          // 乐观锁
         private Integer revision;

    // id
      public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
    // Pid
      public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
    // 单据日期
      public String getWorkdate() {
        return workdate;
    }
    
    public void setWorkdate(String workdate) {
        this.workdate = workdate;
    }
        
    // 生产单号
      public String getWorkuid() {
        return workuid;
    }
    
    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }
        
    // 生产子项id
      public String getWorkitemid() {
        return workitemid;
    }
    
    public void setWorkitemid(String workitemid) {
        this.workitemid = workitemid;
    }
        
    // 货品id
      public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
    // 产品编码
      public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
    // 产品名称
      public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
    // 产品规格
      public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
    // 产品单位
      public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
    // 生产数量
      public Double getWorkqty() {
        return workqty;
    }
    
    public void setWorkqty(Double workqty) {
        this.workqty = workqty;
    }
        
    // 报工数量
      public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
    // 报废数量
      public Double getMrbqty() {
        return mrbqty;
    }
    
    public void setMrbqty(Double mrbqty) {
        this.mrbqty = mrbqty;
    }
        
    // 辅助单位id
      public String getSubitemid() {
        return subitemid;
    }
    
    public void setSubitemid(String subitemid) {
        this.subitemid = subitemid;
    }
        
    // 结算方式
      public String getSubuse() {
        return subuse;
    }
    
    public void setSubuse(String subuse) {
        this.subuse = subuse;
    }
        
    // 辅助单位
      public String getSubunit() {
        return subunit;
    }
    
    public void setSubunit(String subunit) {
        this.subunit = subunit;
    }
        
    // 加工数量
      public Double getSubqty() {
        return subqty;
    }
    
    public void setSubqty(Double subqty) {
        this.subqty = subqty;
    }
        
    // 含税单价
      public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
    // 含税金额
      public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
    // 加工单价
      public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
    // 加工金额
      public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
    // 税额
      public Double getTaxtotal() {
        return taxtotal;
    }
    
    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }
        
    // 税率(备用)
      public Integer getItemtaxrate() {
        return itemtaxrate;
    }
    
    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }
        
    // 计划开工
      public Date getStartdate() {
        return startdate;
    }
    
    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }
        
    // 计划完工
      public Date getPlandate() {
        return plandate;
    }
    
    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }
        
    // 完工数量
      public Double getFinishqty() {
        return finishqty;
    }
    
    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }
        
    // 完工工时
      public Double getFinishhour() {
        return finishhour;
    }
    
    public void setFinishhour(Double finishhour) {
        this.finishhour = finishhour;
    }
        
    // 关闭
      public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
    // 备注
      public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
    // 状态
      public String getStatecode() {
        return statecode;
    }
    
    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }
        
    // 状态时间
      public Date getStatedate() {
        return statedate;
    }
    
    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
        
    // 行号
      public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
    // 销售单号
      public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
    // 销售子项id
      public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
    // 销售客户id
      public String getMachgroupid() {
        return machgroupid;
    }
    
    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }
        
    // Mrp单号
      public String getMrpuid() {
        return mrpuid;
    }
    
    public void setMrpuid(String mrpuid) {
        this.mrpuid = mrpuid;
    }
        
    // Mrp子项id
      public String getMrpitemid() {
        return mrpitemid;
    }
    
    public void setMrpitemid(String mrpitemid) {
        this.mrpitemid = mrpitemid;
    }
        
    // 客户
      public String getCustomer() {
        return customer;
    }
    
    public void setCustomer(String customer) {
        this.customer = customer;
    }
        
    // 客户PO
      public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
    // 主计划单号
      public String getMainplanuid() {
        return mainplanuid;
    }
    
    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }
        
    // 主计划子项id
      public String getMainplanitemid() {
        return mainplanitemid;
    }
    
    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }
        
    // 指定库位
      public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
        
    // 指定批号
      public String getBatchno() {
        return batchno;
    }
    
    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }
        
    // 作废
      public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
    // 作废制表
      public String getDisannullister() {
        return disannullister;
    }
    
    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }
        
    // 作废制表id
      public String getDisannullisterid() {
        return disannullisterid;
    }
    
    public void setDisannullisterid(String disannullisterid) {
        this.disannullisterid = disannullisterid;
    }
        
    // 作废日期
      public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
     public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
    // 自定义1
      public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
    // 自定义2
      public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
    // 自定义3
      public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
    // 自定义4
      public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
    // 自定义5
      public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
    // 自定义6
      public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
    // 自定义7
      public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
    // 自定义8
      public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
    // 自定义9
      public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
    // 自定义10
      public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
    // 租户id
      public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
    // 乐观锁
      public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

