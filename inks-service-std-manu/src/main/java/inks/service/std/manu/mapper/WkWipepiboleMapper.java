package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipepiboleEntity;
import inks.service.std.manu.domain.pojo.WkWipepibolePojo;
import inks.service.std.manu.domain.pojo.WkWipepiboleitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Wip委外(WkWipepibole)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-07 15:09:32
 */
@Mapper
public interface WkWipepiboleMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipepibolePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipepiboleitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipepibolePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipepiboleEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipepiboleEntity wkWipepiboleEntity);


    /**
     * 修改数据
     *
     * @param wkWipepiboleEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipepiboleEntity wkWipepiboleEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkWipepibolePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkWipepibolePojo wkWipepibolePojo);

    /**
     * 修改数据
     *
     * @param wkWipepiboleEntity 实例对象
     * @return 影响行数
     */
    int approval(WkWipepiboleEntity wkWipepiboleEntity);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDisannulCount(@Param("key") String key, @Param("tid") String tid);


    /**
     * 修改完工记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    void updatePrintcount(WkWipepibolePojo billPrintPojo);
}

