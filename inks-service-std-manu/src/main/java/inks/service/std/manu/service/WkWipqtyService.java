package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 生产过数(WkWipqty)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 20:51:55
 */
public interface WkWipqtyService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtyPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipqtyPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipqtyPojo 实例对象
     * @return 实例对象
     */
    WkWipqtyPojo insert(WkWipqtyPojo wkWipqtyPojo);

    /**
     * 修改数据
     *
     * @param wkWipqtypojo 实例对象
     * @return 实例对象
     */
    WkWipqtyPojo update(WkWipqtyPojo wkWipqtypojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    WkWipqtyPojo delete(String key, String tid);


    WkWipnotePojo quickInput(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    WkWipnotePojo quickStart(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    WkWipnotePojo quickStartAdd(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    WkWipnotePojo anyQuickStart(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);


    WkWipnotePojo quickOutput(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    WkWipnotePojo quickFinish(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    WkWipnotePojo quickFinishSub(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    WkWipnotePojo anyQuickFinish(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    // 指定一道工序:同时进行入组和出组相同数量
    WkWipnotePojo anyQuickOneWk(QuickWipqtyPojo quickPojo, LoginUser loginUser);

    Map<String, String> quickInStore(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    Map<String, String> anyQuickInStore(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    Map<String, String> quickMrb(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<Map<String, Object>> getSumPageListByWp(QueryParam queryParam);

    void quickFinishFB(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    PageInfo<WkWipqtyLastPojo> getOnlinePageListByLastMark(QueryParam queryParam);

    WkWipnotePojo quickReturn(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    List<Map<String, Object>> getidAndDateAndCus(String table, String ordertype);

    int updateSnowflakeId(String table, String newSnowflakeId, String custom1);

    int copyIdToCustom(String table);

    int copyCiteItemidToCustom(String table, String tgcolumn);

    int updateCiteItemid(String tgtable, String tgcolumn, String orgtable);

    PageInfo<WkWipqtyPojo> getCostPageList(QueryParam queryParam);

    List<ChartPojo> getSumPageListByCost(QueryParam queryParam);

    List<HashMap<String, Object>> getSumCostPageListByWpMachuid(String machuid, String tid);

    List<HashMap<String, Object>> getSumCostPageListByWpDate(QueryParam queryParam, String tid);

    PageInfo<HashMap<String, Object>> getAvgWorkTimeGroupByWp(QueryParam queryParam, String tenantid);


    WkWipnoteitemPojo anyQuickAddWipItem(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);

    List<HashMap<String, Object>> getSumOutPcsQtyGroupByWorker(QueryParam queryParam);

    List<Map<String, Object>> getSumQtyAndWorkTimeByWp(QueryParam queryParam, String userid);

    List<HashMap<String, Object>> getSumWpByPlanItemid(String mainplanitemid, String tenantid);

    String anyQuickFinishByFinishid(String key, LoginUser loginUser);

    String adminQuickInput(QuickWipqtyPojo quickWipqtyPojo, LoginUser loginUser);
}
