package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.*;

import java.util.List;
import java.util.Map;

/**
 * MRP运算(WkMrp)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 21:00:35
 */
public interface WkMrpService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMrpPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMrpitemdetailPojo> getPageList(QueryParam queryParam);

    PageInfo<WkMrpobjdetailPojo> getObjPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMrpPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMrpPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMrpPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkMrpPojo 实例对象
     * @return 实例对象
     */
    WkMrpPojo insert(WkMrpPojo wkMrpPojo);

    /**
     * 修改数据
     *
     * @param wkMrppojo 实例对象
     * @return 实例对象
     */
    WkMrpPojo update(WkMrpPojo wkMrppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);


    /**
     * 通过Obj 建立Mrp明细，但不包括计算
     *
     * @param lstobj 主键
     * @return 实例对象
     */
    List<WkMrpitemPojo> pullItemList(List<WkMrpobjPojo> lstobj, String Pid, String mrpqtyupper, int mrpqtydec);

    void pullItemListStart(String redisKey, String lockKey, List<WkMrpobjPojo> lstobj, String pid, String mrpqtyupper, int mrpqtydec, LoginUser loginUser);


    /**
     * 通过ID单条MRP运算
     *
     * @return 实例对象
     */
    WkMrpitemPojo pullMrpitem(WkMrpitemPojo wkMrpitemPojo, MrpCalculationPojo mrpCalculationPojo, String mrpqtyupper, int mrpqtydec, String tid);

    String pullMrpitemByMrpidStart(String mrpid, MrpCalculationPojo mrpFront, String mrpqtyupper, int mrpqtydec, String tid);

    /**
     * 通过Obj 建立Mrp明细，但不包括计算
     *
     * @param key 主键
     * @return 实例对象
     */
    Map<String, Object> refreshItemList(String key, String cmd, String mrpqtyupper, int mrpqtydec, LoginUser loginUser);

    List<WkMrpitemPojo> contrastItemList(String key, String mrpqtyupper, int mrpqtydec, LoginUser loginUser);

    WkMrpobjdetailPojo getMatListAndParent(String key,String tid);

    WkMrpobjdetailPojo getMatListAndParentByids(List<String> mrpids, String tenantid);


//    /**
//     * 通过ID查询单条数据
//     *
//     * @param key 主键
//     * @return 实例对象
//     */
//    List<WkMrpitemPojo> pullMrpAll(String key, String tid);


}
