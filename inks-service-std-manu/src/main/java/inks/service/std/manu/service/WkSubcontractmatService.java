package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSubcontractmatPojo;

import java.util.List;

/**
 * 委制物料(WkSubcontractmat)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-04 10:41:24
 */
public interface WkSubcontractmatService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSubcontractmatPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSubcontractmatPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSubcontractmatPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkSubcontractmatPojo 实例对象
     * @return 实例对象
     */
    WkSubcontractmatPojo insert(WkSubcontractmatPojo wkSubcontractmatPojo);

    /**
     * 修改数据
     *
     * @param wkSubcontractmatpojo 实例对象
     * @return 实例对象
     */
    WkSubcontractmatPojo update(WkSubcontractmatPojo wkSubcontractmatpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkSubcontractmatpojo 实例对象
     * @return 实例对象
     */
    WkSubcontractmatPojo clearNull(WkSubcontractmatPojo wkSubcontractmatpojo);

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    List<WkSubcontractmatPojo> getListByPid(String key, String tid);
}
