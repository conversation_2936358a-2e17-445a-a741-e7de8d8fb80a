package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSccompletePojo;
import inks.service.std.manu.domain.pojo.WkSccompleteitemPojo;
import inks.service.std.manu.domain.pojo.WkSccompleteitemdetailPojo;

import java.util.List;

/**
 * 委制验收(WkSccomplete)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-09 12:50:11
 */
public interface WkSccompleteService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccompletePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSccompleteitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccompletePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSccompletePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSccompletePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSccompletePojo 实例对象
     * @return 实例对象
     */
    WkSccompletePojo insert(WkSccompletePojo wkSccompletePojo);

    /**
     * 修改数据
     *
     * @param wkSccompletepojo 实例对象
     * @return 实例对象
     */
    WkSccompletePojo update(WkSccompletePojo wkSccompletepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkSccompletePojo 实例对象
     * @return 实例对象
     */
    WkSccompletePojo approval(WkSccompletePojo wkSccompletePojo);

    void updatePrintcount(WkSccompletePojo billPrintPojo);

    WkSccompletePojo closed(List<WkSccompleteitemPojo> lst, Integer type, LoginUser loginUser);

    WkSccompletePojo disannul(List<WkSccompleteitemPojo> lst, Integer type, LoginUser loginUser);
}
