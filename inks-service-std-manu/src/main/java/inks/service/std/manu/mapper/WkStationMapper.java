package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkStationEntity;
import inks.service.std.manu.domain.pojo.WkStationPojo;
import inks.service.std.manu.domain.pojo.WkStationitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * 工位(WkStation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-22 12:36:16
 */
@Mapper
public interface WkStationMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStationPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkStationitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkStationPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param wkStationEntity 实例对象
     * @return 影响行数
     */
    int insert(WkStationEntity wkStationEntity);

    
    /**
     * 修改数据
     *
     * @param wkStationEntity 实例对象
     * @return 影响行数
     */
    int update(WkStationEntity wkStationEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param wkStationPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(WkStationPojo wkStationPojo);

    List<WkStationPojo> getAllList(String tenantid);

    List<WkStationPojo> getListBySectid(String sectid,String tid);

    HashMap<String, Object> getStationStateAndWk(@Param("id") String key, @Param("tid") String tenantid);

    String getStateJsonByWpid(@Param("wpid") String key, @Param("tid") String tenantid);

    String getStationItemRealNameByWpid(String wpid, String tid);}

