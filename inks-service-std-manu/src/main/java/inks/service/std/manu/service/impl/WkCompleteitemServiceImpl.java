package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkCompleteitemEntity;
import inks.service.std.manu.domain.pojo.WkCompleteitemPojo;
import inks.service.std.manu.mapper.WkCompleteitemMapper;
import inks.service.std.manu.service.WkCompleteitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 验收项目(WkCompleteitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-04 16:16:35
 */
@Service("wkCompleteitemService")
public class WkCompleteitemServiceImpl implements WkCompleteitemService {
    @Resource
    private WkCompleteitemMapper wkCompleteitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkCompleteitemPojo getEntity(String key, String tid) {
        return this.wkCompleteitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCompleteitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCompleteitemPojo> lst = wkCompleteitemMapper.getPageList(queryParam);
            PageInfo<WkCompleteitemPojo> pageInfo = new PageInfo<WkCompleteitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkCompleteitemPojo> getList(String Pid, String tid) {
        try {
            List<WkCompleteitemPojo> lst = wkCompleteitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkCompleteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCompleteitemPojo insert(WkCompleteitemPojo wkCompleteitemPojo) {
        //初始化item的NULL
        WkCompleteitemPojo itempojo = this.clearNull(wkCompleteitemPojo);
        WkCompleteitemEntity wkCompleteitemEntity = new WkCompleteitemEntity();
        BeanUtils.copyProperties(itempojo, wkCompleteitemEntity);

        wkCompleteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkCompleteitemEntity.setRevision(1);  //乐观锁
        this.wkCompleteitemMapper.insert(wkCompleteitemEntity);
        return this.getEntity(wkCompleteitemEntity.getId(), wkCompleteitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkCompleteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCompleteitemPojo update(WkCompleteitemPojo wkCompleteitemPojo) {
        WkCompleteitemEntity wkCompleteitemEntity = new WkCompleteitemEntity();
        BeanUtils.copyProperties(wkCompleteitemPojo, wkCompleteitemEntity);
        this.wkCompleteitemMapper.update(wkCompleteitemEntity);
        return this.getEntity(wkCompleteitemEntity.getId(), wkCompleteitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkCompleteitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkCompleteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCompleteitemPojo clearNull(WkCompleteitemPojo wkCompleteitemPojo) {
        //初始化NULL字段
        if (wkCompleteitemPojo.getPid() == null) wkCompleteitemPojo.setPid("");
        if (wkCompleteitemPojo.getWorkdate() == null) wkCompleteitemPojo.setWorkdate("");
        if (wkCompleteitemPojo.getWorkuid() == null) wkCompleteitemPojo.setWorkuid("");
        if (wkCompleteitemPojo.getWorkitemid() == null) wkCompleteitemPojo.setWorkitemid("");
        if (wkCompleteitemPojo.getGoodsid() == null) wkCompleteitemPojo.setGoodsid("");
        if (wkCompleteitemPojo.getItemcode() == null) wkCompleteitemPojo.setItemcode("");
        if (wkCompleteitemPojo.getItemname() == null) wkCompleteitemPojo.setItemname("");
        if (wkCompleteitemPojo.getItemspec() == null) wkCompleteitemPojo.setItemspec("");
        if (wkCompleteitemPojo.getItemunit() == null) wkCompleteitemPojo.setItemunit("");
        if (wkCompleteitemPojo.getQuantity() == null) wkCompleteitemPojo.setQuantity(0D);
        if (wkCompleteitemPojo.getSubitemid() == null) wkCompleteitemPojo.setSubitemid("");
        if (wkCompleteitemPojo.getSubuse() == null) wkCompleteitemPojo.setSubuse("");
        if (wkCompleteitemPojo.getSubunit() == null) wkCompleteitemPojo.setSubunit("");
        if (wkCompleteitemPojo.getSubqty() == null) wkCompleteitemPojo.setSubqty(0D);
        if (wkCompleteitemPojo.getTaxprice() == null) wkCompleteitemPojo.setTaxprice(0D);
        if (wkCompleteitemPojo.getTaxamount() == null) wkCompleteitemPojo.setTaxamount(0D);
        if (wkCompleteitemPojo.getPrice() == null) wkCompleteitemPojo.setPrice(0D);
        if (wkCompleteitemPojo.getAmount() == null) wkCompleteitemPojo.setAmount(0D);
        if (wkCompleteitemPojo.getTaxtotal() == null) wkCompleteitemPojo.setTaxtotal(0D);
        if (wkCompleteitemPojo.getItemtaxrate() == null) wkCompleteitemPojo.setItemtaxrate(0);
        if (wkCompleteitemPojo.getStartdate() == null) wkCompleteitemPojo.setStartdate(new Date());
        if (wkCompleteitemPojo.getPlandate() == null) wkCompleteitemPojo.setPlandate(new Date());
        if (wkCompleteitemPojo.getFinishqty() == null) wkCompleteitemPojo.setFinishqty(0D);
        if (wkCompleteitemPojo.getFinishhour() == null) wkCompleteitemPojo.setFinishhour(0D);
        if (wkCompleteitemPojo.getMrbqty() == null) wkCompleteitemPojo.setMrbqty(0D);
        if (wkCompleteitemPojo.getEnabledmark() == null) wkCompleteitemPojo.setEnabledmark(0);
        if (wkCompleteitemPojo.getClosed() == null) wkCompleteitemPojo.setClosed(0);
        if (wkCompleteitemPojo.getRemark() == null) wkCompleteitemPojo.setRemark("");
        if (wkCompleteitemPojo.getStatecode() == null) wkCompleteitemPojo.setStatecode("");
        if (wkCompleteitemPojo.getStatedate() == null) wkCompleteitemPojo.setStatedate(new Date());
        if (wkCompleteitemPojo.getRownum() == null) wkCompleteitemPojo.setRownum(0);
        if (wkCompleteitemPojo.getMachuid() == null) wkCompleteitemPojo.setMachuid("");
        if (wkCompleteitemPojo.getMachitemid() == null) wkCompleteitemPojo.setMachitemid("");
        if (wkCompleteitemPojo.getMachgroupid() == null) wkCompleteitemPojo.setMachgroupid("");
        if (wkCompleteitemPojo.getMrpuid() == null) wkCompleteitemPojo.setMrpuid("");
        if (wkCompleteitemPojo.getMrpitemid() == null) wkCompleteitemPojo.setMrpitemid("");
        if (wkCompleteitemPojo.getCustomer() == null) wkCompleteitemPojo.setCustomer("");
        if (wkCompleteitemPojo.getCustpo() == null) wkCompleteitemPojo.setCustpo("");
        if (wkCompleteitemPojo.getMainplanuid() == null) wkCompleteitemPojo.setMainplanuid("");
        if (wkCompleteitemPojo.getMainplanitemid() == null) wkCompleteitemPojo.setMainplanitemid("");
        if (wkCompleteitemPojo.getCiteuid() == null) wkCompleteitemPojo.setCiteuid("");
        if (wkCompleteitemPojo.getCiteitemid() == null) wkCompleteitemPojo.setCiteitemid("");
        if (wkCompleteitemPojo.getLocation() == null) wkCompleteitemPojo.setLocation("");
        if (wkCompleteitemPojo.getBatchno() == null) wkCompleteitemPojo.setBatchno("");
        if (wkCompleteitemPojo.getAttributejson() == null) wkCompleteitemPojo.setAttributejson("");
        if (wkCompleteitemPojo.getCustom1() == null) wkCompleteitemPojo.setCustom1("");
        if (wkCompleteitemPojo.getCustom2() == null) wkCompleteitemPojo.setCustom2("");
        if (wkCompleteitemPojo.getCustom3() == null) wkCompleteitemPojo.setCustom3("");
        if (wkCompleteitemPojo.getCustom4() == null) wkCompleteitemPojo.setCustom4("");
        if (wkCompleteitemPojo.getCustom5() == null) wkCompleteitemPojo.setCustom5("");
        if (wkCompleteitemPojo.getCustom6() == null) wkCompleteitemPojo.setCustom6("");
        if (wkCompleteitemPojo.getCustom7() == null) wkCompleteitemPojo.setCustom7("");
        if (wkCompleteitemPojo.getCustom8() == null) wkCompleteitemPojo.setCustom8("");
        if (wkCompleteitemPojo.getCustom9() == null) wkCompleteitemPojo.setCustom9("");
        if (wkCompleteitemPojo.getCustom10() == null) wkCompleteitemPojo.setCustom10("");
        if (wkCompleteitemPojo.getTenantid() == null) wkCompleteitemPojo.setTenantid("");
        if (wkCompleteitemPojo.getRevision() == null) wkCompleteitemPojo.setRevision(0);
        return wkCompleteitemPojo;
    }
}
