package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipepiboleEntity;
import inks.service.std.manu.domain.WkWipepiboleitemEntity;
import inks.service.std.manu.domain.pojo.WkWipepibolePojo;
import inks.service.std.manu.domain.pojo.WkWipepiboleitemPojo;
import inks.service.std.manu.domain.pojo.WkWipepiboleitemdetailPojo;
import inks.service.std.manu.mapper.WkWipepiboleMapper;
import inks.service.std.manu.mapper.WkWipepiboleitemMapper;
import inks.service.std.manu.service.WkWipepiboleService;
import inks.service.std.manu.service.WkWipepiboleitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Wip委外(WkWipepibole)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-04 11:11:45
 */
@Service("wkWipepiboleService")
public class WkWipepiboleServiceImpl implements WkWipepiboleService {
    @Resource
    private WkWipepiboleMapper wkWipepiboleMapper;

    @Resource
    private WkWipepiboleitemMapper wkWipepiboleitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkWipepiboleitemService wkWipepiboleitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipepibolePojo getEntity(String key, String tid) {
        return this.wkWipepiboleMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipepiboleitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipepiboleitemdetailPojo> lst = wkWipepiboleMapper.getPageList(queryParam);
            PageInfo<WkWipepiboleitemdetailPojo> pageInfo = new PageInfo<WkWipepiboleitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipepibolePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkWipepibolePojo wkWipepibolePojo = this.wkWipepiboleMapper.getEntity(key, tid);
            //读取子表
            wkWipepibolePojo.setItem(wkWipepiboleitemMapper.getList(wkWipepibolePojo.getId(), wkWipepibolePojo.getTenantid()));
            return wkWipepibolePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipepibolePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipepibolePojo> lst = wkWipepiboleMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkWipepiboleitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkWipepibolePojo> pageInfo = new PageInfo<WkWipepibolePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipepibolePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipepibolePojo> lst = wkWipepiboleMapper.getPageTh(queryParam);
            PageInfo<WkWipepibolePojo> pageInfo = new PageInfo<WkWipepibolePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWipepibolePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipepibolePojo insert(WkWipepibolePojo wkWipepibolePojo) {
//初始化NULL字段
        if (wkWipepibolePojo.getRefno() == null) wkWipepibolePojo.setRefno("");
        if (wkWipepibolePojo.getBilltype() == null) wkWipepibolePojo.setBilltype("");
        if (wkWipepibolePojo.getBilldate() == null) wkWipepibolePojo.setBilldate(new Date());
        if (wkWipepibolePojo.getGroupid() == null) wkWipepibolePojo.setGroupid("");
        if (wkWipepibolePojo.getBilltitle() == null) wkWipepibolePojo.setBilltitle("");
        if (wkWipepibolePojo.getOperator() == null) wkWipepibolePojo.setOperator("");
        if (wkWipepibolePojo.getMultiwp() == null) wkWipepibolePojo.setMultiwp(0);
        if (wkWipepibolePojo.getSummary() == null) wkWipepibolePojo.setSummary("");
        if (wkWipepibolePojo.getCreateby() == null) wkWipepibolePojo.setCreateby("");
        if (wkWipepibolePojo.getCreatebyid() == null) wkWipepibolePojo.setCreatebyid("");
        if (wkWipepibolePojo.getCreatedate() == null) wkWipepibolePojo.setCreatedate(new Date());
        if (wkWipepibolePojo.getLister() == null) wkWipepibolePojo.setLister("");
        if (wkWipepibolePojo.getListerid() == null) wkWipepibolePojo.setListerid("");
        if (wkWipepibolePojo.getModifydate() == null) wkWipepibolePojo.setModifydate(new Date());
        if (wkWipepibolePojo.getAssessor() == null) wkWipepibolePojo.setAssessor("");
        if (wkWipepibolePojo.getAssessorid() == null) wkWipepibolePojo.setAssessorid("");
        if (wkWipepibolePojo.getAssessdate() == null) wkWipepibolePojo.setAssessdate(new Date());
        if (wkWipepibolePojo.getBillstatetext() == null) wkWipepibolePojo.setBillstatetext("");
        if (wkWipepibolePojo.getBillstatedate() == null) wkWipepibolePojo.setBillstatedate(new Date());
        if (wkWipepibolePojo.getBillstartdate() == null) wkWipepibolePojo.setBillstartdate(new Date());
        if (wkWipepibolePojo.getBillplandate() == null) wkWipepibolePojo.setBillplandate(new Date());
        if (wkWipepibolePojo.getBilltaxamount() == null) wkWipepibolePojo.setBilltaxamount(0D);
        if (wkWipepibolePojo.getBillamount() == null) wkWipepibolePojo.setBillamount(0D);
        if (wkWipepibolePojo.getItemcount() == null) wkWipepibolePojo.setItemcount(0);
        if (wkWipepibolePojo.getItem() != null) wkWipepibolePojo.setItemcount(wkWipepibolePojo.getItem().size());
        if (wkWipepibolePojo.getDisannulcount() == null) wkWipepibolePojo.setDisannulcount(0);
        if (wkWipepibolePojo.getFinishcount() == null) wkWipepibolePojo.setFinishcount(0);
        if (wkWipepibolePojo.getPrintcount() == null) wkWipepibolePojo.setPrintcount(0);
        if (wkWipepibolePojo.getCustom1() == null) wkWipepibolePojo.setCustom1("");
        if (wkWipepibolePojo.getCustom2() == null) wkWipepibolePojo.setCustom2("");
        if (wkWipepibolePojo.getCustom3() == null) wkWipepibolePojo.setCustom3("");
        if (wkWipepibolePojo.getCustom4() == null) wkWipepibolePojo.setCustom4("");
        if (wkWipepibolePojo.getCustom5() == null) wkWipepibolePojo.setCustom5("");
        if (wkWipepibolePojo.getCustom6() == null) wkWipepibolePojo.setCustom6("");
        if (wkWipepibolePojo.getCustom7() == null) wkWipepibolePojo.setCustom7("");
        if (wkWipepibolePojo.getCustom8() == null) wkWipepibolePojo.setCustom8("");
        if (wkWipepibolePojo.getCustom9() == null) wkWipepibolePojo.setCustom9("");
        if (wkWipepibolePojo.getCustom10() == null) wkWipepibolePojo.setCustom10("");
        if (wkWipepibolePojo.getTenantid() == null) wkWipepibolePojo.setTenantid("");
        if (wkWipepibolePojo.getTenantname() == null) wkWipepibolePojo.setTenantname("");
        if (wkWipepibolePojo.getRevision() == null) wkWipepibolePojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkWipepiboleEntity wkWipepiboleEntity = new WkWipepiboleEntity();
        BeanUtils.copyProperties(wkWipepibolePojo, wkWipepiboleEntity);
        //设置id和新建日期
        wkWipepiboleEntity.setId(id);
        wkWipepiboleEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkWipepiboleMapper.insert(wkWipepiboleEntity);
        //Item子表处理
        List<WkWipepiboleitemPojo> lst = wkWipepibolePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (WkWipepiboleitemPojo wkWipepiboleitemPojo : lst) {
                //初始化item的NULL
                WkWipepiboleitemPojo itemPojo = this.wkWipepiboleitemService.clearNull(wkWipepiboleitemPojo);
                WkWipepiboleitemEntity wkWipepiboleitemEntity = new WkWipepiboleitemEntity();
                BeanUtils.copyProperties(itemPojo, wkWipepiboleitemEntity);
                //设置id和Pid
                wkWipepiboleitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipepiboleitemEntity.setPid(id);
                wkWipepiboleitemEntity.setTenantid(wkWipepibolePojo.getTenantid());
                wkWipepiboleitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkWipepiboleitemMapper.insert(wkWipepiboleitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipepiboleEntity.getId(), wkWipepiboleEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipepibolePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipepibolePojo update(WkWipepibolePojo wkWipepibolePojo) {
        //主表更改
        if (wkWipepibolePojo.getItem() != null)
            wkWipepibolePojo.setItemcount(wkWipepibolePojo.getItem().size());
        else
            wkWipepibolePojo.setItemcount(0);
        WkWipepiboleEntity wkWipepiboleEntity = new WkWipepiboleEntity();
        BeanUtils.copyProperties(wkWipepibolePojo, wkWipepiboleEntity);
        this.wkWipepiboleMapper.update(wkWipepiboleEntity);
        if (wkWipepibolePojo.getItem() != null) {
            //Item子表处理
            List<WkWipepiboleitemPojo> lst = wkWipepibolePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkWipepiboleMapper.getDelItemIds(wkWipepibolePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.wkWipepiboleitemMapper.delete(lstDelId, wkWipepiboleEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (WkWipepiboleitemPojo wkWipepiboleitemPojo : lst) {
                    WkWipepiboleitemEntity wkWipepiboleitemEntity = new WkWipepiboleitemEntity();
                    if ("".equals(wkWipepiboleitemPojo.getId()) || wkWipepiboleitemPojo.getId() == null) {
                        //初始化item的NULL
                        WkWipepiboleitemPojo itemPojo = this.wkWipepiboleitemService.clearNull(wkWipepiboleitemPojo);
                        BeanUtils.copyProperties(itemPojo, wkWipepiboleitemEntity);
                        //设置id和Pid
                        wkWipepiboleitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkWipepiboleitemEntity.setPid(wkWipepiboleEntity.getId());  // 主表 id
                        wkWipepiboleitemEntity.setTenantid(wkWipepibolePojo.getTenantid());   // 租户id
                        wkWipepiboleitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkWipepiboleitemMapper.insert(wkWipepiboleitemEntity);
                    } else {
                        BeanUtils.copyProperties(wkWipepiboleitemPojo, wkWipepiboleitemEntity);
                        wkWipepiboleitemEntity.setTenantid(wkWipepibolePojo.getTenantid());
                        this.wkWipepiboleitemMapper.update(wkWipepiboleitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipepiboleEntity.getId(), wkWipepiboleEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkWipepibolePojo wkWipepibolePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkWipepiboleitemPojo> lst = wkWipepibolePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkWipepiboleitemPojo wkWipepiboleitemPojo : lst) {
                this.wkWipepiboleitemMapper.delete(wkWipepiboleitemPojo.getId(), tid);
            }
        }
        this.wkWipepiboleMapper.delete(key, tid);
        return wkWipepibolePojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkWipepibolePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipepibolePojo approval(WkWipepibolePojo wkWipepibolePojo) {
        //主表更改
        WkWipepiboleEntity wkWipepiboleEntity = new WkWipepiboleEntity();
        BeanUtils.copyProperties(wkWipepibolePojo, wkWipepiboleEntity);
        this.wkWipepiboleMapper.approval(wkWipepiboleEntity);
        //返回Bill实例
        return this.getBillEntity(wkWipepiboleEntity.getId(), wkWipepiboleEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipepibolePojo disannul(List<WkWipepiboleitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            WkWipepiboleitemPojo Pojo = lst.get(i);
            WkWipepiboleitemPojo dbPojo = this.wkWipepiboleitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (dbPojo.getDisannulmark() != type) {
                    if (Pid.equals("")) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    WkWipepiboleitemEntity entity = new WkWipepiboleitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.wkWipepiboleitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.wkWipepiboleMapper.updateDisannulCount(Pid, tid);
            //主表更改
            WkWipepiboleEntity wkWipepiboleEntity = new WkWipepiboleEntity();
            wkWipepiboleEntity.setId(Pid);
            wkWipepiboleEntity.setLister(loginUser.getRealname());
            wkWipepiboleEntity.setListerid(loginUser.getUserid());
            wkWipepiboleEntity.setModifydate(new Date());
            wkWipepiboleEntity.setTenantid(loginUser.getTenantid());
            this.wkWipepiboleMapper.update(wkWipepiboleEntity);
            //返回Bill实例
            return this.getBillEntity(wkWipepiboleEntity.getId(), wkWipepiboleEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipepibolePojo closed(List<WkWipepiboleitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            WkWipepiboleitemPojo Pojo = lst.get(i);
            WkWipepiboleitemPojo dbPojo = this.wkWipepiboleitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    WkWipepiboleitemEntity entity = new WkWipepiboleitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.wkWipepiboleitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.wkWipepiboleMapper.updateFinishCount(Pid, tid);
            //主表更改
            WkWipepiboleEntity wkWipepiboleEntity = new WkWipepiboleEntity();
            wkWipepiboleEntity.setId(Pid);
            wkWipepiboleEntity.setLister(loginUser.getRealname());
            wkWipepiboleEntity.setListerid(loginUser.getUserid());
            wkWipepiboleEntity.setModifydate(new Date());
            wkWipepiboleEntity.setTenantid(loginUser.getTenantid());
            this.wkWipepiboleMapper.update(wkWipepiboleEntity);
            //返回Bill实例
            return this.getBillEntity(wkWipepiboleEntity.getId(), wkWipepiboleEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    public void updatePrintcount(WkWipepibolePojo billPrintPojo) {
        this.wkWipepiboleMapper.updatePrintcount(billPrintPojo);
    }
}
