package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSubcontractitemEntity;
import inks.service.std.manu.domain.pojo.WkSubcontractitemPojo;
import inks.service.std.manu.mapper.WkSubcontractitemMapper;
import inks.service.std.manu.service.WkSubcontractitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 委制项目(WkSubcontractitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-07 11:01:56
 */
@Service("wkSubcontractitemService")
public class WkSubcontractitemServiceImpl implements WkSubcontractitemService {
    @Resource
    private WkSubcontractitemMapper wkSubcontractitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSubcontractitemPojo getEntity(String key, String tid) {
        return this.wkSubcontractitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSubcontractitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSubcontractitemPojo> lst = wkSubcontractitemMapper.getPageList(queryParam);
            PageInfo<WkSubcontractitemPojo> pageInfo = new PageInfo<WkSubcontractitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkSubcontractitemPojo> getList(String Pid, String tid) {
        try {
            List<WkSubcontractitemPojo> lst = wkSubcontractitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkSubcontractitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSubcontractitemPojo insert(WkSubcontractitemPojo wkSubcontractitemPojo) {
        //初始化item的NULL
        WkSubcontractitemPojo itempojo = this.clearNull(wkSubcontractitemPojo);
        WkSubcontractitemEntity wkSubcontractitemEntity = new WkSubcontractitemEntity();
        BeanUtils.copyProperties(itempojo, wkSubcontractitemEntity);
        //生成雪花id
        wkSubcontractitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkSubcontractitemEntity.setRevision(1);  //乐观锁
        this.wkSubcontractitemMapper.insert(wkSubcontractitemEntity);
        return this.getEntity(wkSubcontractitemEntity.getId(), wkSubcontractitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSubcontractitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSubcontractitemPojo update(WkSubcontractitemPojo wkSubcontractitemPojo) {
        WkSubcontractitemEntity wkSubcontractitemEntity = new WkSubcontractitemEntity();
        BeanUtils.copyProperties(wkSubcontractitemPojo, wkSubcontractitemEntity);
        this.wkSubcontractitemMapper.update(wkSubcontractitemEntity);
        return this.getEntity(wkSubcontractitemEntity.getId(), wkSubcontractitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkSubcontractitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkSubcontractitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSubcontractitemPojo clearNull(WkSubcontractitemPojo wkSubcontractitemPojo) {
        //初始化NULL字段
        if (wkSubcontractitemPojo.getPid() == null) wkSubcontractitemPojo.setPid("");
        if (wkSubcontractitemPojo.getGoodsid() == null) wkSubcontractitemPojo.setGoodsid("");
        if (wkSubcontractitemPojo.getSubitemid() == null) wkSubcontractitemPojo.setSubitemid("");
        if (wkSubcontractitemPojo.getSubuse() == null) wkSubcontractitemPojo.setSubuse("");
        if (wkSubcontractitemPojo.getSubunit() == null) wkSubcontractitemPojo.setSubunit("");
        if (wkSubcontractitemPojo.getSubqty() == null) wkSubcontractitemPojo.setSubqty(0D);
        if (wkSubcontractitemPojo.getTaxprice() == null) wkSubcontractitemPojo.setTaxprice(0D);
        if (wkSubcontractitemPojo.getTaxamount() == null) wkSubcontractitemPojo.setTaxamount(0D);
        if (wkSubcontractitemPojo.getPrice() == null) wkSubcontractitemPojo.setPrice(0D);
        if (wkSubcontractitemPojo.getAmount() == null) wkSubcontractitemPojo.setAmount(0D);
        if (wkSubcontractitemPojo.getTaxtotal() == null) wkSubcontractitemPojo.setTaxtotal(0D);
        if (wkSubcontractitemPojo.getItemtaxrate() == null) wkSubcontractitemPojo.setItemtaxrate(0);
        if (wkSubcontractitemPojo.getStartdate() == null) wkSubcontractitemPojo.setStartdate(new Date());
        if (wkSubcontractitemPojo.getPlandate() == null) wkSubcontractitemPojo.setPlandate(new Date());
        if (wkSubcontractitemPojo.getQuantity() == null) wkSubcontractitemPojo.setQuantity(0D);
        if (wkSubcontractitemPojo.getFinishqty() == null) wkSubcontractitemPojo.setFinishqty(0D);
        if (wkSubcontractitemPojo.getMrbqty() == null) wkSubcontractitemPojo.setMrbqty(0D);
        if (wkSubcontractitemPojo.getInstorage() == null) wkSubcontractitemPojo.setInstorage(0);
        if (wkSubcontractitemPojo.getEnabledmark() == null) wkSubcontractitemPojo.setEnabledmark(0);
        if (wkSubcontractitemPojo.getClosed() == null) wkSubcontractitemPojo.setClosed(0);
        if (wkSubcontractitemPojo.getRemark() == null) wkSubcontractitemPojo.setRemark("");
        if (wkSubcontractitemPojo.getStatecode() == null) wkSubcontractitemPojo.setStatecode("");
        if (wkSubcontractitemPojo.getStatedate() == null) wkSubcontractitemPojo.setStatedate(new Date());
        if (wkSubcontractitemPojo.getRownum() == null) wkSubcontractitemPojo.setRownum(0);
        if (wkSubcontractitemPojo.getMachuid() == null) wkSubcontractitemPojo.setMachuid("");
        if (wkSubcontractitemPojo.getMachitemid() == null) wkSubcontractitemPojo.setMachitemid("");
        if (wkSubcontractitemPojo.getMachgroupid() == null) wkSubcontractitemPojo.setMachgroupid("");
        if (wkSubcontractitemPojo.getCustomer() == null) wkSubcontractitemPojo.setCustomer("");
        if (wkSubcontractitemPojo.getCustpo() == null) wkSubcontractitemPojo.setCustpo("");
        if (wkSubcontractitemPojo.getMainplanuid() == null) wkSubcontractitemPojo.setMainplanuid("");
        if (wkSubcontractitemPojo.getMainplanitemid() == null) wkSubcontractitemPojo.setMainplanitemid("");
        if (wkSubcontractitemPojo.getMrpuid() == null) wkSubcontractitemPojo.setMrpuid("");
        if (wkSubcontractitemPojo.getMrpitemid() == null) wkSubcontractitemPojo.setMrpitemid("");
        if (wkSubcontractitemPojo.getCiteuid() == null) wkSubcontractitemPojo.setCiteuid("");
        if (wkSubcontractitemPojo.getCiteitemid() == null) wkSubcontractitemPojo.setCiteitemid("");
        if (wkSubcontractitemPojo.getDisannullisterid() == null) wkSubcontractitemPojo.setDisannullisterid("");
        if (wkSubcontractitemPojo.getDisannullister() == null) wkSubcontractitemPojo.setDisannullister("");
        if (wkSubcontractitemPojo.getDisannuldate() == null) wkSubcontractitemPojo.setDisannuldate(new Date());
        if (wkSubcontractitemPojo.getDisannulmark() == null) wkSubcontractitemPojo.setDisannulmark(0);
        if (wkSubcontractitemPojo.getAttributejson() == null) wkSubcontractitemPojo.setAttributejson("");
        if (wkSubcontractitemPojo.getReportqty() == null) wkSubcontractitemPojo.setReportqty(0D);
        if (wkSubcontractitemPojo.getCompqty() == null) wkSubcontractitemPojo.setCompqty(0D);
        if (wkSubcontractitemPojo.getFinishrate() == null) wkSubcontractitemPojo.setFinishrate(0D);
        if (wkSubcontractitemPojo.getSecqty() == null) wkSubcontractitemPojo.setSecqty(0D);
        if (wkSubcontractitemPojo.getPanelwidth() == null) wkSubcontractitemPojo.setPanelwidth(0D);
        if (wkSubcontractitemPojo.getPanelheight() == null) wkSubcontractitemPojo.setPanelheight(0D);
        if (wkSubcontractitemPojo.getPcsinpanel() == null) wkSubcontractitemPojo.setPcsinpanel(0);
        if (wkSubcontractitemPojo.getPanelthick() == null) wkSubcontractitemPojo.setPanelthick(0D);
        if (wkSubcontractitemPojo.getRowcode() == null) wkSubcontractitemPojo.setRowcode("");
        if (wkSubcontractitemPojo.getWkpcsqty() == null) wkSubcontractitemPojo.setWkpcsqty(0D);
        if (wkSubcontractitemPojo.getWksecqty() == null) wkSubcontractitemPojo.setWksecqty(0D);
        if (wkSubcontractitemPojo.getSourcetype() == null) wkSubcontractitemPojo.setSourcetype(0);
        if (wkSubcontractitemPojo.getVirtualitem() == null) wkSubcontractitemPojo.setVirtualitem(0);
        if (wkSubcontractitemPojo.getSubbypcs() == null) wkSubcontractitemPojo.setSubbypcs(0D);
        if (wkSubcontractitemPojo.getCustom1() == null) wkSubcontractitemPojo.setCustom1("");
        if (wkSubcontractitemPojo.getCustom2() == null) wkSubcontractitemPojo.setCustom2("");
        if (wkSubcontractitemPojo.getCustom3() == null) wkSubcontractitemPojo.setCustom3("");
        if (wkSubcontractitemPojo.getCustom4() == null) wkSubcontractitemPojo.setCustom4("");
        if (wkSubcontractitemPojo.getCustom5() == null) wkSubcontractitemPojo.setCustom5("");
        if (wkSubcontractitemPojo.getCustom6() == null) wkSubcontractitemPojo.setCustom6("");
        if (wkSubcontractitemPojo.getCustom7() == null) wkSubcontractitemPojo.setCustom7("");
        if (wkSubcontractitemPojo.getCustom8() == null) wkSubcontractitemPojo.setCustom8("");
        if (wkSubcontractitemPojo.getCustom9() == null) wkSubcontractitemPojo.setCustom9("");
        if (wkSubcontractitemPojo.getCustom10() == null) wkSubcontractitemPojo.setCustom10("");
        if (wkSubcontractitemPojo.getTenantid() == null) wkSubcontractitemPojo.setTenantid("");
        if (wkSubcontractitemPojo.getRevision() == null) wkSubcontractitemPojo.setRevision(0);
        return wkSubcontractitemPojo;
    }
}
