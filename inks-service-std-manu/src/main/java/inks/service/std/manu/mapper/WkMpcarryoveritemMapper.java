package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkMpcarryoveritemEntity;
import inks.service.std.manu.domain.pojo.WkMpcarryoveritemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 结转子表(WkMpcarryoveritem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-09 09:43:09
 */
 @Mapper
public interface WkMpcarryoveritemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMpcarryoveritemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMpcarryoveritemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMpcarryoveritemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkMpcarryoveritemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkMpcarryoveritemEntity wkMpcarryoveritemEntity);

    
    /**
     * 修改数据
     *
     * @param wkMpcarryoveritemEntity 实例对象
     * @return 影响行数
     */
    int update(WkMpcarryoveritemEntity wkMpcarryoveritemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

