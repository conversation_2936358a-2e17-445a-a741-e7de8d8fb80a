package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSubcontractmatEntity;
import inks.service.std.manu.domain.pojo.WkSubcontractmatPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 委制物料(WkSubcontractmat)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-04 10:42:05
 */
@Mapper
public interface WkSubcontractmatMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSubcontractmatPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSubcontractmatPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSubcontractmatPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param wkSubcontractmatEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSubcontractmatEntity wkSubcontractmatEntity);


    /**
     * 修改数据
     *
     * @param wkSubcontractmatEntity 实例对象
     * @return 影响行数
     */
    int update(WkSubcontractmatEntity wkSubcontractmatEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int deleteByItemid(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param key 主键
     * @return 对象列表
     */
    List<WkSubcontractmatPojo> getListByItemid(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param key 主键
     * @return 对象列表
     */
    List<WkSubcontractmatPojo> getListByPid(@Param("key") String key, @Param("tid") String tid);


}

