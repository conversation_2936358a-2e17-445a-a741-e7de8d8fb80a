package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.HiWipnoteitemEntity;
import inks.service.std.manu.domain.pojo.HiWipnoteitemPojo;
import inks.service.std.manu.mapper.HiWipnoteitemMapper;
import inks.service.std.manu.service.HiWipnoteitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Wip记录子表(HiWipnoteitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-16 16:20:27
 */
@Service("hiWipnoteitemService")
public class HiWipnoteitemServiceImpl implements HiWipnoteitemService {
    @Resource
    private HiWipnoteitemMapper hiWipnoteitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public HiWipnoteitemPojo getEntity(String key, String tid) {
        return this.hiWipnoteitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<HiWipnoteitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<HiWipnoteitemPojo> lst = hiWipnoteitemMapper.getPageList(queryParam);
            PageInfo<HiWipnoteitemPojo> pageInfo = new PageInfo<HiWipnoteitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<HiWipnoteitemPojo> getList(String Pid, String tid) {
        try {
            List<HiWipnoteitemPojo> lst = hiWipnoteitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param hiWipnoteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public HiWipnoteitemPojo insert(HiWipnoteitemPojo hiWipnoteitemPojo) {
        //初始化item的NULL
        HiWipnoteitemPojo itempojo = this.clearNull(hiWipnoteitemPojo);
        HiWipnoteitemEntity hiWipnoteitemEntity = new HiWipnoteitemEntity();
        BeanUtils.copyProperties(itempojo, hiWipnoteitemEntity);
        //生成雪花id
        hiWipnoteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        hiWipnoteitemEntity.setRevision(1);  //乐观锁
        this.hiWipnoteitemMapper.insert(hiWipnoteitemEntity);
        return this.getEntity(hiWipnoteitemEntity.getId(), hiWipnoteitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param hiWipnoteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public HiWipnoteitemPojo update(HiWipnoteitemPojo hiWipnoteitemPojo) {
        HiWipnoteitemEntity hiWipnoteitemEntity = new HiWipnoteitemEntity();
        BeanUtils.copyProperties(hiWipnoteitemPojo, hiWipnoteitemEntity);
        this.hiWipnoteitemMapper.update(hiWipnoteitemEntity);
        return this.getEntity(hiWipnoteitemEntity.getId(), hiWipnoteitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.hiWipnoteitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param hiWipnoteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public HiWipnoteitemPojo clearNull(HiWipnoteitemPojo hiWipnoteitemPojo) {
        //初始化NULL字段
        if (hiWipnoteitemPojo.getPid() == null) hiWipnoteitemPojo.setPid("");
        if (hiWipnoteitemPojo.getWpid() == null) hiWipnoteitemPojo.setWpid("");
        if (hiWipnoteitemPojo.getWpcode() == null) hiWipnoteitemPojo.setWpcode("");
        if (hiWipnoteitemPojo.getWpname() == null) hiWipnoteitemPojo.setWpname("");
        if (hiWipnoteitemPojo.getRownum() == null) hiWipnoteitemPojo.setRownum(0);
        if (hiWipnoteitemPojo.getPlandate() == null) hiWipnoteitemPojo.setPlandate(new Date());
        if (hiWipnoteitemPojo.getRemark() == null) hiWipnoteitemPojo.setRemark("");
        if (hiWipnoteitemPojo.getInpcsqty() == null) hiWipnoteitemPojo.setInpcsqty(0D);
        if (hiWipnoteitemPojo.getInsecqty() == null) hiWipnoteitemPojo.setInsecqty(0D);
        if (hiWipnoteitemPojo.getOutpcsqty() == null) hiWipnoteitemPojo.setOutpcsqty(0D);
        if (hiWipnoteitemPojo.getOutsecqty() == null) hiWipnoteitemPojo.setOutsecqty(0D);
        if (hiWipnoteitemPojo.getMrbpcsqty() == null) hiWipnoteitemPojo.setMrbpcsqty(0D);
        if (hiWipnoteitemPojo.getMrbsecqty() == null) hiWipnoteitemPojo.setMrbsecqty(0D);
        if (hiWipnoteitemPojo.getComppcsqty() == null) hiWipnoteitemPojo.setComppcsqty(0D);
        if (hiWipnoteitemPojo.getCompsecqty() == null) hiWipnoteitemPojo.setCompsecqty(0D);
        if (hiWipnoteitemPojo.getSubqty() == null) hiWipnoteitemPojo.setSubqty(0D);
        if (hiWipnoteitemPojo.getSubunit() == null) hiWipnoteitemPojo.setSubunit("");
        if (hiWipnoteitemPojo.getStartdate() == null) hiWipnoteitemPojo.setStartdate(new Date());
        if (hiWipnoteitemPojo.getEnddate() == null) hiWipnoteitemPojo.setEnddate(new Date());
        if (hiWipnoteitemPojo.getItemworker() == null) hiWipnoteitemPojo.setItemworker("");
        if (hiWipnoteitemPojo.getEpibolepcsqty() == null) hiWipnoteitemPojo.setEpibolepcsqty(0D);
        if (hiWipnoteitemPojo.getEpibolesecqty() == null) hiWipnoteitemPojo.setEpibolesecqty(0D);
        if (hiWipnoteitemPojo.getLastwp() == null) hiWipnoteitemPojo.setLastwp(0);
        if (hiWipnoteitemPojo.getSpecjson() == null) hiWipnoteitemPojo.setSpecjson("");
        if (hiWipnoteitemPojo.getSpecpackjson() == null) hiWipnoteitemPojo.setSpecpackjson("");
        if (hiWipnoteitemPojo.getWorkparam() == null) hiWipnoteitemPojo.setWorkparam("");
        if (hiWipnoteitemPojo.getLister() == null) hiWipnoteitemPojo.setLister("");
        if (hiWipnoteitemPojo.getCreatedate() == null) hiWipnoteitemPojo.setCreatedate(new Date());
        if (hiWipnoteitemPojo.getModifydate() == null) hiWipnoteitemPojo.setModifydate(new Date());
        if (hiWipnoteitemPojo.getStartplan() == null) hiWipnoteitemPojo.setStartplan(new Date());
        if (hiWipnoteitemPojo.getInspid() == null) hiWipnoteitemPojo.setInspid("");
        if (hiWipnoteitemPojo.getInspuid() == null) hiWipnoteitemPojo.setInspuid("");
        if (hiWipnoteitemPojo.getInspresult() == null) hiWipnoteitemPojo.setInspresult(0);
        if (hiWipnoteitemPojo.getDisablein() == null) hiWipnoteitemPojo.setDisablein(0);
        if (hiWipnoteitemPojo.getDisableout() == null) hiWipnoteitemPojo.setDisableout(0);
        if (hiWipnoteitemPojo.getInworker() == null) hiWipnoteitemPojo.setInworker("");
        if (hiWipnoteitemPojo.getOutworker() == null) hiWipnoteitemPojo.setOutworker("");
        if (hiWipnoteitemPojo.getCustom1() == null) hiWipnoteitemPojo.setCustom1("");
        if (hiWipnoteitemPojo.getCustom2() == null) hiWipnoteitemPojo.setCustom2("");
        if (hiWipnoteitemPojo.getCustom3() == null) hiWipnoteitemPojo.setCustom3("");
        if (hiWipnoteitemPojo.getCustom4() == null) hiWipnoteitemPojo.setCustom4("");
        if (hiWipnoteitemPojo.getCustom5() == null) hiWipnoteitemPojo.setCustom5("");
        if (hiWipnoteitemPojo.getCustom6() == null) hiWipnoteitemPojo.setCustom6("");
        if (hiWipnoteitemPojo.getCustom7() == null) hiWipnoteitemPojo.setCustom7("");
        if (hiWipnoteitemPojo.getCustom8() == null) hiWipnoteitemPojo.setCustom8("");
        if (hiWipnoteitemPojo.getCustom9() == null) hiWipnoteitemPojo.setCustom9("");
        if (hiWipnoteitemPojo.getCustom10() == null) hiWipnoteitemPojo.setCustom10("");
        if (hiWipnoteitemPojo.getTenantid() == null) hiWipnoteitemPojo.setTenantid("");
        if (hiWipnoteitemPojo.getRevision() == null) hiWipnoteitemPojo.setRevision(0);
        return hiWipnoteitemPojo;
    }
}
