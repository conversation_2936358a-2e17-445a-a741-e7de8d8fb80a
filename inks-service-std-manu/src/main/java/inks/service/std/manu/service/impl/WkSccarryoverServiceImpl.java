package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.std.manu.domain.WkSccarryoverEntity;
import inks.service.std.manu.domain.WkSccarryoveritemEntity;
import inks.service.std.manu.domain.pojo.WkSccarryoverPojo;
import inks.service.std.manu.domain.pojo.WkSccarryoveritemPojo;
import inks.service.std.manu.domain.pojo.WkSccarryoveritemdetailPojo;
import inks.service.std.manu.mapper.WkSccarryoverMapper;
import inks.service.std.manu.mapper.WkSccarryoveritemMapper;
import inks.service.std.manu.service.WkSccarryoverService;
import inks.service.std.manu.service.WkSccarryoveritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 加工结转(WkSccarryover)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-09 14:04:49
 */
@Service("wkSccarryoverService")
public class WkSccarryoverServiceImpl implements WkSccarryoverService {
    @Resource
    private WkSccarryoverMapper wkSccarryoverMapper;

    @Resource
    private WkSccarryoveritemMapper wkSccarryoveritemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkSccarryoveritemService wkSccarryoveritemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSccarryoverPojo getEntity(String key, String tid) {
        return this.wkSccarryoverMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSccarryoveritemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSccarryoveritemdetailPojo> lst = wkSccarryoverMapper.getPageList(queryParam);
            PageInfo<WkSccarryoveritemdetailPojo> pageInfo = new PageInfo<WkSccarryoveritemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSccarryoverPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkSccarryoverPojo wkSccarryoverPojo = this.wkSccarryoverMapper.getEntity(key, tid);
            //读取子表
            wkSccarryoverPojo.setItem(wkSccarryoveritemMapper.getList(wkSccarryoverPojo.getId(), wkSccarryoverPojo.getTenantid()));
            return wkSccarryoverPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSccarryoverPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSccarryoverPojo> lst = wkSccarryoverMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkSccarryoveritemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkSccarryoverPojo> pageInfo = new PageInfo<WkSccarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSccarryoverPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSccarryoverPojo> lst = wkSccarryoverMapper.getPageTh(queryParam);
            PageInfo<WkSccarryoverPojo> pageInfo = new PageInfo<WkSccarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkSccarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSccarryoverPojo insert(WkSccarryoverPojo wkSccarryoverPojo) {
//初始化NULL字段
        if (wkSccarryoverPojo.getRefno() == null) wkSccarryoverPojo.setRefno("");
        if (wkSccarryoverPojo.getBilltype() == null) wkSccarryoverPojo.setBilltype("");
        if (wkSccarryoverPojo.getBilldate() == null) wkSccarryoverPojo.setBilldate(new Date());
        if (wkSccarryoverPojo.getBilltitle() == null) wkSccarryoverPojo.setBilltitle("");
        if (wkSccarryoverPojo.getGroupid() == null) wkSccarryoverPojo.setGroupid("");
        if (wkSccarryoverPojo.getTradercode() == null) wkSccarryoverPojo.setTradercode("");
        if (wkSccarryoverPojo.getTradername() == null) wkSccarryoverPojo.setTradername("");
        if (wkSccarryoverPojo.getCarryyear() == null) wkSccarryoverPojo.setCarryyear(0);
        if (wkSccarryoverPojo.getCarrymonth() == null) wkSccarryoverPojo.setCarrymonth(0);
        if (wkSccarryoverPojo.getStartdate() == null) wkSccarryoverPojo.setStartdate(new Date());
        if (wkSccarryoverPojo.getEnddate() == null) wkSccarryoverPojo.setEnddate(new Date());
        if (wkSccarryoverPojo.getOperator() == null) wkSccarryoverPojo.setOperator("");
        if (wkSccarryoverPojo.getOperatorid() == null) wkSccarryoverPojo.setOperatorid("");
        if (wkSccarryoverPojo.getSummary() == null) wkSccarryoverPojo.setSummary("");
        if (wkSccarryoverPojo.getCreateby() == null) wkSccarryoverPojo.setCreateby("");
        if (wkSccarryoverPojo.getCreatebyid() == null) wkSccarryoverPojo.setCreatebyid("");
        if (wkSccarryoverPojo.getCreatedate() == null) wkSccarryoverPojo.setCreatedate(new Date());
        if (wkSccarryoverPojo.getLister() == null) wkSccarryoverPojo.setLister("");
        if (wkSccarryoverPojo.getListerid() == null) wkSccarryoverPojo.setListerid("");
        if (wkSccarryoverPojo.getModifydate() == null) wkSccarryoverPojo.setModifydate(new Date());
        if (wkSccarryoverPojo.getBillopenamount() == null) wkSccarryoverPojo.setBillopenamount(0D);
        if (wkSccarryoverPojo.getBillinamount() == null) wkSccarryoverPojo.setBillinamount(0D);
        if (wkSccarryoverPojo.getBilloutamount() == null) wkSccarryoverPojo.setBilloutamount(0D);
        if (wkSccarryoverPojo.getBillcloseamount() == null) wkSccarryoverPojo.setBillcloseamount(0D);
        if (wkSccarryoverPojo.getItemcount() == null) wkSccarryoverPojo.setItemcount(0);
        if (wkSccarryoverPojo.getPrintcount() == null) wkSccarryoverPojo.setPrintcount(0);
        if (wkSccarryoverPojo.getCustom1() == null) wkSccarryoverPojo.setCustom1("");
        if (wkSccarryoverPojo.getCustom2() == null) wkSccarryoverPojo.setCustom2("");
        if (wkSccarryoverPojo.getCustom3() == null) wkSccarryoverPojo.setCustom3("");
        if (wkSccarryoverPojo.getCustom4() == null) wkSccarryoverPojo.setCustom4("");
        if (wkSccarryoverPojo.getCustom5() == null) wkSccarryoverPojo.setCustom5("");
        if (wkSccarryoverPojo.getCustom6() == null) wkSccarryoverPojo.setCustom6("");
        if (wkSccarryoverPojo.getCustom7() == null) wkSccarryoverPojo.setCustom7("");
        if (wkSccarryoverPojo.getCustom8() == null) wkSccarryoverPojo.setCustom8("");
        if (wkSccarryoverPojo.getCustom9() == null) wkSccarryoverPojo.setCustom9("");
        if (wkSccarryoverPojo.getCustom10() == null) wkSccarryoverPojo.setCustom10("");
        if (wkSccarryoverPojo.getTenantid() == null) wkSccarryoverPojo.setTenantid("");
        if (wkSccarryoverPojo.getTenantname() == null) wkSccarryoverPojo.setTenantname("");
        if (wkSccarryoverPojo.getRevision() == null) wkSccarryoverPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkSccarryoverEntity wkSccarryoverEntity = new WkSccarryoverEntity();
        BeanUtils.copyProperties(wkSccarryoverPojo, wkSccarryoverEntity);
        //设置id和新建日期
        wkSccarryoverEntity.setId(id);
        wkSccarryoverEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkSccarryoverMapper.insert(wkSccarryoverEntity);
        //Item子表处理
        List<WkSccarryoveritemPojo> lst = wkSccarryoverPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkSccarryoveritemPojo itemPojo = this.wkSccarryoveritemService.clearNull(lst.get(i));
                WkSccarryoveritemEntity wkSccarryoveritemEntity = new WkSccarryoveritemEntity();
                BeanUtils.copyProperties(itemPojo, wkSccarryoveritemEntity);
                //设置id和Pid
                wkSccarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkSccarryoveritemEntity.setPid(id);
                wkSccarryoveritemEntity.setTenantid(wkSccarryoverPojo.getTenantid());
                wkSccarryoveritemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkSccarryoveritemMapper.insert(wkSccarryoveritemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkSccarryoverEntity.getId(), wkSccarryoverEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSccarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSccarryoverPojo update(WkSccarryoverPojo wkSccarryoverPojo) {
        //主表更改
        WkSccarryoverEntity wkSccarryoverEntity = new WkSccarryoverEntity();
        BeanUtils.copyProperties(wkSccarryoverPojo, wkSccarryoverEntity);
        this.wkSccarryoverMapper.update(wkSccarryoverEntity);
        if (wkSccarryoverPojo.getItem() != null) {
            //Item子表处理
            List<WkSccarryoveritemPojo> lst = wkSccarryoverPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkSccarryoverMapper.getDelItemIds(wkSccarryoverPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkSccarryoveritemMapper.delete(lstDelIds.get(i), wkSccarryoverEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    WkSccarryoveritemEntity wkSccarryoveritemEntity = new WkSccarryoveritemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        WkSccarryoveritemPojo itemPojo = this.wkSccarryoveritemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, wkSccarryoveritemEntity);
                        //设置id和Pid
                        wkSccarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkSccarryoveritemEntity.setPid(wkSccarryoverEntity.getId());  // 主表 id
                        wkSccarryoveritemEntity.setTenantid(wkSccarryoverPojo.getTenantid());   // 租户id
                        wkSccarryoveritemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkSccarryoveritemMapper.insert(wkSccarryoveritemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkSccarryoveritemEntity);
                        wkSccarryoveritemEntity.setTenantid(wkSccarryoverPojo.getTenantid());
                        this.wkSccarryoveritemMapper.update(wkSccarryoveritemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkSccarryoverEntity.getId(), wkSccarryoverEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkSccarryoverPojo wkSccarryoverPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkSccarryoveritemPojo> lst = wkSccarryoverPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkSccarryoveritemPojo wkSccarryoveritemPojo : lst) {
                this.wkSccarryoveritemMapper.delete(wkSccarryoveritemPojo.getId(), tid);
            }
        }
        this.wkSccarryoverMapper.delete(key, tid);
        return wkSccarryoverPojo.getRefno();
    }


    /**
     * 新增数据
     *
     * @param wkSccarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSccarryoverPojo createCarry(WkSccarryoverPojo wkSccarryoverPojo) {

        // 查询当前客户之前的销售账单
        QueryParam queryParam = new QueryParam();
        queryParam.setFilterstr(" and Wk_ScCarryover.Groupid='" + wkSccarryoverPojo.getGroupid() + "'");
        queryParam.setOrderBy("Wk_ScCarryover.EndDate");
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1);
        queryParam.setTenantid(wkSccarryoverPojo.getTenantid());
        List<WkSccarryoverPojo> lstCarry = this.wkSccarryoverMapper.getPageTh(queryParam);
        if (lstCarry.size() == 0) {
            throw new RuntimeException("请先初始化车间结转");
        }

        // 查询上个结转表内容
        List<WkSccarryoveritemPojo> lstOpen = this.wkSccarryoveritemMapper.getList(lstCarry.get(0).getId(), wkSccarryoverPojo.getTenantid());

        // =============本期出入库汇总 =====================
        // 初始化时间
        Date dtStart = lstCarry.get(0).getEnddate();
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(wkSccarryoverPojo.getEnddate()) + " 23:59:59");
        wkSccarryoverPojo.setStartdate(lstCarry.get(0).getEnddate());

        queryParam = new QueryParam();
        queryParam.setFilterstr(" and Wk_Subcontract.Groupid='" + wkSccarryoverPojo.getGroupid() + "'");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setTenantid(wkSccarryoverPojo.getTenantid());
        List<WkSccarryoveritemPojo> lstWs = this.wkSccarryoverMapper.getGoodsScList(queryParam);

        queryParam = new QueryParam();
        queryParam.setFilterstr(" and Mat_Access.Groupid='" + wkSccarryoverPojo.getGroupid() + "'");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setTenantid(wkSccarryoverPojo.getTenantid());
        List<WkSccarryoveritemPojo> lstAcce = this.wkSccarryoverMapper.getGoodsAcceList(queryParam);
        //  ==========开始拼接 进项==============
        int rowNum = 1;
        List<WkSccarryoveritemPojo> lstNew = new ArrayList<>();
        for (WkSccarryoveritemPojo openPojo : lstOpen) {
            WkSccarryoveritemPojo newPojo = new WkSccarryoveritemPojo();
            newPojo.setGoodsid(openPojo.getGoodsid());
            newPojo.setOpenqty(openPojo.getCloseqty());
            newPojo.setOpenamount(openPojo.getCloseamount());
            if (lstWs.size() > 0) {
                for (WkSccarryoveritemPojo mpPojo : lstWs) {
                    if (openPojo.getGoodsid().equals(mpPojo.getGoodsid())) {
                        newPojo.setInqty(mpPojo.getInqty());
                        newPojo.setInamount(mpPojo.getInamount());
                        lstWs.remove(mpPojo);
                        break;
                    }
                }
            } else {
                newPojo.setInqty(0D);
                newPojo.setInamount(0D);
            }
            if (lstAcce.size() > 0) {
                for (WkSccarryoveritemPojo accePojo : lstAcce) {
                    if (openPojo.getGoodsid().equals(accePojo.getGoodsid()) || newPojo.getGoodsid().equals(accePojo.getGoodsid())) {
                        newPojo.setOutqty(accePojo.getOutqty());
                        newPojo.setOutamount(accePojo.getOutamount());
                        lstAcce.remove(accePojo);
                        break;
                    }
                }
            } else {
                newPojo.setOutqty(0D);
                newPojo.setOutamount(0D);
            }

            // 三项非空，添加到列表
            if (newPojo.getOpenqty() != 0D || newPojo.getInqty() != 0D || newPojo.getOutqty() != 0D) {
                newPojo.setOutqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setOutamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }

        // =========本期新增================
        if (lstWs.size() > 0) {
            for (WkSccarryoveritemPojo mpPojo : lstWs) {
                WkSccarryoveritemPojo newPojo = new WkSccarryoveritemPojo();
                newPojo.setGoodsid(mpPojo.getGoodsid());
                newPojo.setOpenqty(0D);
                newPojo.setOpenamount(0D);
                newPojo.setInqty(mpPojo.getInqty());
                newPojo.setInamount(mpPojo.getInamount());
                if (lstAcce.size() > 0) {
                    for (WkSccarryoveritemPojo accePojo : lstAcce) {
                        if (newPojo.getGoodsid().equals(accePojo.getGoodsid())) {
                            newPojo.setOutqty(accePojo.getOutqty());
                            newPojo.setOutamount(accePojo.getOutamount());
                            lstAcce.remove(accePojo);
                            break;
                        }
                    }
                } else {
                    newPojo.setOutqty(0D);
                    newPojo.setOutamount(0D);
                }
                /// 添加到列表
                newPojo.setOutqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setOutamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }
        if (lstAcce.size() > 0) {
            for (WkSccarryoveritemPojo accePojo : lstAcce) {
                WkSccarryoveritemPojo newPojo = new WkSccarryoveritemPojo();
                newPojo.setGoodsid(accePojo.getGoodsid());
                newPojo.setOpenqty(0D);
                newPojo.setOpenamount(0D);
                newPojo.setInqty(0D);
                newPojo.setInamount(0D);
                newPojo.setOutqty(accePojo.getOutqty());
                newPojo.setOutamount(accePojo.getOutamount());
                /// 添加到列表
                newPojo.setOutqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setOutamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }
        //  =======插入数据========
        wkSccarryoverPojo.setItem(lstNew);
        wkSccarryoverPojo.setItemcount(lstNew.size());
        wkSccarryoverPojo = insert(wkSccarryoverPojo);

        //返回Bill实例
        return this.getEntity(wkSccarryoverPojo.getId(), wkSccarryoverPojo.getTenantid());

    }

}
