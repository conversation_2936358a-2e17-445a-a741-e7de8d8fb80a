package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkProgroupPojo;
import inks.service.std.manu.domain.pojo.WkProgroupitemdetailPojo;

/**
 * 生产制程(WkProgroup)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 20:43:17
 */
public interface WkProgroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProgroupPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkProgroupitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProgroupPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkProgroupPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkProgroupPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkProgroupPojo 实例对象
     * @return 实例对象
     */
    WkProgroupPojo insert(WkProgroupPojo wkProgroupPojo);

    /**
     * 修改数据
     *
     * @param wkProgrouppojo 实例对象
     * @return 实例对象
     */
    WkProgroupPojo update(WkProgroupPojo wkProgrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

}
