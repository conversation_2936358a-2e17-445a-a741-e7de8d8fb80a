package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipgroupEntity;
import inks.service.std.manu.domain.WkWipgroupitemEntity;
import inks.service.std.manu.domain.pojo.WkWipgroupPojo;
import inks.service.std.manu.domain.pojo.WkWipgroupitemPojo;
import inks.service.std.manu.domain.pojo.WkWipgroupitemdetailPojo;
import inks.service.std.manu.mapper.WkWipgroupMapper;
import inks.service.std.manu.mapper.WkWipgroupitemMapper;
import inks.service.std.manu.service.WkWipgroupService;
import inks.service.std.manu.service.WkWipgroupitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * WIP设定(WkWipgroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-14 20:45:45
 */
@Service("wkWipgroupService")
public class WkWipgroupServiceImpl implements WkWipgroupService {
    @Resource
    private WkWipgroupMapper wkWipgroupMapper;

    @Resource
    private WkWipgroupitemMapper wkWipgroupitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkWipgroupitemService wkWipgroupitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipgroupPojo getEntity(String key, String tid) {
        return this.wkWipgroupMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipgroupitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipgroupitemdetailPojo> lst = wkWipgroupMapper.getPageList(queryParam);
            PageInfo<WkWipgroupitemdetailPojo> pageInfo = new PageInfo<WkWipgroupitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipgroupPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkWipgroupPojo wkWipgroupPojo = this.wkWipgroupMapper.getEntity(key, tid);
            //读取子表
            wkWipgroupPojo.setItem(wkWipgroupitemMapper.getList(wkWipgroupPojo.getId(), wkWipgroupPojo.getTenantid()));
            return wkWipgroupPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipgroupPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipgroupPojo> lst = wkWipgroupMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkWipgroupitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkWipgroupPojo> pageInfo = new PageInfo<WkWipgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipgroupPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipgroupPojo> lst = wkWipgroupMapper.getPageTh(queryParam);
            PageInfo<WkWipgroupPojo> pageInfo = new PageInfo<WkWipgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWipgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipgroupPojo insert(WkWipgroupPojo wkWipgroupPojo) {
//初始化NULL字段
        if (wkWipgroupPojo.getWipname() == null) wkWipgroupPojo.setWipname("");
        if (wkWipgroupPojo.getWipinfo() == null) wkWipgroupPojo.setWipinfo("");
        if (wkWipgroupPojo.getWipicon() == null) wkWipgroupPojo.setWipicon("");
        if (wkWipgroupPojo.getWipmanager() == null) wkWipgroupPojo.setWipmanager("");
        if (wkWipgroupPojo.getEnabledmark() == null) wkWipgroupPojo.setEnabledmark(0);
        if (wkWipgroupPojo.getCreateby() == null) wkWipgroupPojo.setCreateby("");
        if (wkWipgroupPojo.getCreatebyid() == null) wkWipgroupPojo.setCreatebyid("");
        if (wkWipgroupPojo.getCreatedate() == null) wkWipgroupPojo.setCreatedate(new Date());
        if (wkWipgroupPojo.getLister() == null) wkWipgroupPojo.setLister("");
        if (wkWipgroupPojo.getListerid() == null) wkWipgroupPojo.setListerid("");
        if (wkWipgroupPojo.getModifydate() == null) wkWipgroupPojo.setModifydate(new Date());
        if (wkWipgroupPojo.getCustom1() == null) wkWipgroupPojo.setCustom1("");
        if (wkWipgroupPojo.getCustom2() == null) wkWipgroupPojo.setCustom2("");
        if (wkWipgroupPojo.getCustom3() == null) wkWipgroupPojo.setCustom3("");
        if (wkWipgroupPojo.getCustom4() == null) wkWipgroupPojo.setCustom4("");
        if (wkWipgroupPojo.getCustom5() == null) wkWipgroupPojo.setCustom5("");
        if (wkWipgroupPojo.getCustom6() == null) wkWipgroupPojo.setCustom6("");
        if (wkWipgroupPojo.getCustom7() == null) wkWipgroupPojo.setCustom7("");
        if (wkWipgroupPojo.getCustom8() == null) wkWipgroupPojo.setCustom8("");
        if (wkWipgroupPojo.getCustom9() == null) wkWipgroupPojo.setCustom9("");
        if (wkWipgroupPojo.getCustom10() == null) wkWipgroupPojo.setCustom10("");
        if (wkWipgroupPojo.getTenantid() == null) wkWipgroupPojo.setTenantid("");
        if (wkWipgroupPojo.getRevision() == null) wkWipgroupPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkWipgroupEntity wkWipgroupEntity = new WkWipgroupEntity();
        BeanUtils.copyProperties(wkWipgroupPojo, wkWipgroupEntity);
        //设置id和新建日期
        wkWipgroupEntity.setId(id);
        wkWipgroupEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkWipgroupMapper.insert(wkWipgroupEntity);
        //Item子表处理
        List<WkWipgroupitemPojo> lst = wkWipgroupPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkWipgroupitemPojo itemPojo = this.wkWipgroupitemService.clearNull(lst.get(i));
                WkWipgroupitemEntity wkWipgroupitemEntity = new WkWipgroupitemEntity();
                BeanUtils.copyProperties(itemPojo, wkWipgroupitemEntity);
                //设置id和Pid
                wkWipgroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipgroupitemEntity.setPid(id);
                wkWipgroupitemEntity.setTenantid(wkWipgroupPojo.getTenantid());
                wkWipgroupitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkWipgroupitemMapper.insert(wkWipgroupitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipgroupEntity.getId(), wkWipgroupEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipgroupPojo update(WkWipgroupPojo wkWipgroupPojo) {
        //主表更改
        WkWipgroupEntity wkWipgroupEntity = new WkWipgroupEntity();
        BeanUtils.copyProperties(wkWipgroupPojo, wkWipgroupEntity);
        this.wkWipgroupMapper.update(wkWipgroupEntity);
        //Item子表处理
        List<WkWipgroupitemPojo> lst = wkWipgroupPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = wkWipgroupMapper.getDelItemIds(wkWipgroupPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.wkWipgroupitemMapper.delete(lstDelIds.get(i), wkWipgroupEntity.getTenantid());
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                WkWipgroupitemEntity wkWipgroupitemEntity = new WkWipgroupitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    WkWipgroupitemPojo itemPojo = this.wkWipgroupitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, wkWipgroupitemEntity);
                    //设置id和Pid
                    wkWipgroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    wkWipgroupitemEntity.setPid(wkWipgroupEntity.getId());  // 主表 id
                    wkWipgroupitemEntity.setTenantid(wkWipgroupPojo.getTenantid());   // 租户id
                    wkWipgroupitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.wkWipgroupitemMapper.insert(wkWipgroupitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), wkWipgroupitemEntity);
                    wkWipgroupitemEntity.setTenantid(wkWipgroupPojo.getTenantid());
                    this.wkWipgroupitemMapper.update(wkWipgroupitemEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipgroupEntity.getId(), wkWipgroupEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        WkWipgroupPojo wkWipgroupPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkWipgroupitemPojo> lst = wkWipgroupPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.wkWipgroupitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.wkWipgroupMapper.delete(key, tid);
    }


}
