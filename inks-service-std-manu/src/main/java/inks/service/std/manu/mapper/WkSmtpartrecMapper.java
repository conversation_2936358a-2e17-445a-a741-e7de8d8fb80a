package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSmtpartrecEntity;
import inks.service.std.manu.domain.pojo.WkSmtpartrecPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 上料记录(WkSmtpartrec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-21 11:14:04
 */
@Mapper
public interface WkSmtpartrecMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSmtpartrecPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSmtpartrecPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param wkSmtpartrecEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSmtpartrecEntity wkSmtpartrecEntity);


    /**
     * 修改数据
     *
     * @param wkSmtpartrecEntity 实例对象
     * @return 影响行数
     */
    int update(WkSmtpartrecEntity wkSmtpartrecEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updatePartFinish(@Param("key") String key,@Param("refno") String refno, @Param("tid") String tid);


    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updatePartAccept(@Param("key") String key,@Param("refno") String refno, @Param("tid") String tid);
}

