package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipepiboleitemPojo;

import java.util.List;

/**
 * Wip委外项目(WkWipepiboleitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 13:36:08
 */
public interface WkWipepiboleitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipepiboleitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipepiboleitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipepiboleitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkWipepiboleitemPojo 实例对象
     * @return 实例对象
     */
    WkWipepiboleitemPojo insert(WkWipepiboleitemPojo wkWipepiboleitemPojo);

    /**
     * 修改数据
     *
     * @param wkWipepiboleitempojo 实例对象
     * @return 实例对象
     */
    WkWipepiboleitemPojo update(WkWipepiboleitemPojo wkWipepiboleitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkWipepiboleitempojo 实例对象
     * @return 实例对象
     */
    WkWipepiboleitemPojo clearNull(WkWipepiboleitemPojo wkWipepiboleitempojo);
}
