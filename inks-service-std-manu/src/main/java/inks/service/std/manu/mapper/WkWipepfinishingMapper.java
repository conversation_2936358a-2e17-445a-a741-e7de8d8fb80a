package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipepfinishingEntity;
import inks.service.std.manu.domain.pojo.WkWipepfinishingPojo;
import inks.service.std.manu.domain.pojo.WkWipepfinishingitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工序收货(WkWipepfinishing)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-04 11:14:55
 */
@Mapper
public interface WkWipepfinishingMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipepfinishingPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipepfinishingitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipepfinishingPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipepfinishingEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipepfinishingEntity wkWipepfinishingEntity);


    /**
     * 修改数据
     *
     * @param wkWipepfinishingEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipepfinishingEntity wkWipepfinishingEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkWipepfinishingPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkWipepfinishingPojo wkWipepfinishingPojo);

    /**
     * 修改数据
     *
     * @param wkWipepfinishingEntity 实例对象
     * @return 影响行数
     */
    int approval(WkWipepfinishingEntity wkWipepfinishingEntity);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateEpibFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateEpibFinishCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    void updatePrintcount(WkWipepfinishingPojo billPrintPojo);
}

