package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkCompletematEntity;
import inks.service.std.manu.domain.pojo.WkCompletematPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 验收物料(WkCompletemat)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-01 14:40:52
 */
 @Mapper
public interface WkCompletematMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCompletematPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkCompletematPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkCompletematPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkCompletematEntity 实例对象
     * @return 影响行数
     */
    int insert(WkCompletematEntity wkCompletematEntity);

    
    /**
     * 修改数据
     *
     * @param wkCompletematEntity 实例对象
     * @return 影响行数
     */
    int update(WkCompletematEntity wkCompletematEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

