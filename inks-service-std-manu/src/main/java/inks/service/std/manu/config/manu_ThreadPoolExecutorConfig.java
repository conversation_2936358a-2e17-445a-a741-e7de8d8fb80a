package inks.service.std.manu.config;

import inks.service.std.manu.utils.PrintColor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class manu_ThreadPoolExecutorConfig {

    @Bean(name= "manu_threadPoolExecutor")
    public Executor manu_threadPoolExecutor(){
        ThreadPoolTaskExecutor threadPoolExecutor = new ThreadPoolTaskExecutor();
        int processNum = Runtime.getRuntime().availableProcessors(); // 返回可用处理器的Java虚拟机的数量
        int corePoolSize = (int) (processNum / (1 - 0.2)); // 核心池大小
        int maxPoolSize = (int) (processNum / (1 - 0.5)); // 最大线程数
        int queueCapacity = maxPoolSize * 100; // 队列程度，这里我们把它减小了一点以减少内存消耗

        threadPoolExecutor.setCorePoolSize(corePoolSize);
        threadPoolExecutor.setMaxPoolSize(maxPoolSize);
        threadPoolExecutor.setQueueCapacity(queueCapacity);
        threadPoolExecutor.setThreadPriority(Thread.MAX_PRIORITY);
        threadPoolExecutor.setDaemon(false);
        threadPoolExecutor.setKeepAliveSeconds(300); // 线程空闲时间
        threadPoolExecutor.setThreadNamePrefix("manu-Executor-"); // 线程名字前缀

        // 设置拒绝策略。当任务无法被线程池处理时，让调用者线程自己来执行任务。
        threadPoolExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //processNum: 28 corePoolSize: 35 maxPoolSize: 56 queueCapacity: 5600 Thread.MAX_PRIORITY: 10
        PrintColor.printColor("processNum: " + processNum
                + " corePoolSize: " + corePoolSize
                + " maxPoolSize: " + maxPoolSize
                + " queueCapacity: " + queueCapacity
                + " Thread.MAX_PRIORITY: " + Thread.MAX_PRIORITY);
        return threadPoolExecutor;
    }
}
