package inks.service.std.manu.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * Wip记录子表(HiWipnoteitem)Pojo
 *
 * <AUTHOR>
 * @since 2023-10-16 16:20:26
 */
public class HiWipnoteitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -77812949377520523L;
    @Excel(name = "")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 工序id
    @Excel(name = "工序id")
    private String wpid;
    // 工序编码
    @Excel(name = "工序编码")
    private String wpcode;
    // 工序名称
    @Excel(name = "工序名称")
    private String wpname;
    // 生产顺序
    @Excel(name = "生产顺序")
    private Integer rownum;
    // 计划完工
    @Excel(name = "计划完工")
    private Date plandate;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 入组pcs数
    @Excel(name = "入组pcs数")
    private Double inpcsqty;
    // 入组Sec数
    @Excel(name = "入组Sec数")
    private Double insecqty;
    // 出组pcs数
    @Excel(name = "出组pcs数")
    private Double outpcsqty;
    // 出组Sec数
    @Excel(name = "出组Sec数")
    private Double outsecqty;
    // 报废pcs数
    @Excel(name = "报废pcs数")
    private Double mrbpcsqty;
    // 报废Sec数
    @Excel(name = "报废Sec数")
    private Double mrbsecqty;
    // 完工Pcs数
    @Excel(name = "完工Pcs数")
    private Double comppcsqty;
    // 完工Sec数
    @Excel(name = "完工Sec数")
    private Double compsecqty;
    // 辅助数量
    @Excel(name = "辅助数量")
    private Double subqty;
    // 辅助单位
    @Excel(name = "辅助单位")
    private String subunit;
    // 开始日期
    @Excel(name = "开始日期")
    private Date startdate;
    // 结束日期
    @Excel(name = "结束日期")
    private Date enddate;
    // 工人
    @Excel(name = "工人")
    private String itemworker;
    // 委外Pcs数
    @Excel(name = "委外Pcs数")
    private Double epibolepcsqty;
    // 委外Sec数
    @Excel(name = "委外Sec数")
    private Double epibolesecqty;
    // 最后工序
    @Excel(name = "最后工序")
    private Integer lastwp;
    // 当前规格JSON
    @Excel(name = "当前规格JSON")
    private String specjson;
    // 包装规格JSON
    @Excel(name = "包装规格JSON")
    private String specpackjson;
    // 工作参数
    @Excel(name = "工作参数")
    private String workparam;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 计划开始
    @Excel(name = "计划开始")
    private Date startplan;
    // 检验单id
    @Excel(name = "检验单id")
    private String inspid;
    // 检验单号
    @Excel(name = "检验单号")
    private String inspuid;
    // 1Pass2Fail3Pending
    @Excel(name = "1Pass2Fail3Pending")
    private Integer inspresult;
    // 禁止入组
    @Excel(name = "禁止入组")
    private Integer disablein;
    // 禁止出组
    @Excel(name = "禁止出组")
    private Integer disableout;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // 单据编码
    @Excel(name = "单据编码")
    private String refno;
    // 单据日期
    @Excel(name = "单据日期")
    private Date billdate;
    // 生产类型
    @Excel(name = "生产类型")
    private String worktype;
    // 生产车间id
    @Excel(name = "生产车间id")
    private String workshopid;
    // 生产车间
    @Excel(name = "生产车间")
    private String workshop;
    // 货品id
    @Excel(name = "货品id")
    private String goodsid;
    // 数量
    @Excel(name = "数量")
    private Double quantity;
    // 总投数
    @Excel(name = "总投数")
    private Double wkpcsqty;
    // 投料Sec
    @Excel(name = "投料Sec")
    private Double wksecqty;
    // 状态
    @Excel(name = "状态")
    private String statecode;
    // 状态日期
    @Excel(name = "状态日期")
    private Date statedate;
    // 当前工序id
    @Excel(name = "当前工序id")
    private String wkwpid;
    // 当前工序编码
    @Excel(name = "当前工序编码")
    private String wkwpcode;
    // 当前工序名称
    @Excel(name = "当前工序名称")
    private String wkwpname;
    // 当前行号
    @Excel(name = "当前行号")
    private Integer wkrownum;
    // 客户
    @Excel(name = "客户")
    private String customer;
    // 客户PO
    @Excel(name = "客户PO")
    private String custpo;
    // 销售单号
    @Excel(name = "销售单号")
    private String machuid;
    // 销售子项id
    @Excel(name = "销售子项id")
    private String machitemid;
    // 销售客户id
    @Excel(name = "销售客户id")
    private String machgroupid;
    // 主计划号
    @Excel(name = "主计划号")
    private String mainplanuid;
    // 主计划Itemid
    @Excel(name = "主计划Itemid")
    private String mainplanitemid;
    // 加工单号
    @Excel(name = "加工单号")
    private String workuid;
    // 加工单Itemid
    @Excel(name = "加工单Itemid")
    private String workitemid;
    // 分单工序id
    @Excel(name = "分单工序id")
    private String substwpid;
    // 分单工序编码
    @Excel(name = "分单工序编码")
    private String substwpcode;
    // 分单工序名称
    @Excel(name = "分单工序名称")
    private String substwpname;
    // 合单工序id
    @Excel(name = "合单工序id")
    private String subendwpid;
    // 合单工序编码
    @Excel(name = "合单工序编码")
    private String subendwpcode;
    // 合单工序名称
    @Excel(name = "合单工序名称")
    private String subendwpname;
    // 分单编码
    @Excel(name = "分单编码")
    private String subuid;
    // 开工日期
    @Excel(name = "开工日期")
    private Date workdate;
    // 完工工序id
    @Excel(name = "完工工序id")
    private String compwpid;
    // 完工工序编码
    @Excel(name = "完工工序编码")
    private String compwpcode;
    // 完工工序名称
    @Excel(name = "完工工序名称")
    private String compwpname;
    // Wip分管表
    @Excel(name = "Wip分管表")
    private String wipgroupid;
    // 摘要
    @Excel(name = "摘要")
    private String summary;
    // SPU属性
    @Excel(name = "SPU属性")
    private String attributejson;
    // 主料Code
    @Excel(name = "主料Code")
    private String matcode;
    // 主料已领
    @Excel(name = "主料已领")
    private Integer matused;
    // 当前规格JSON
    @Excel(name = "当前规格JSON")
    private String wkspecjson;
    // item行数
    @Excel(name = "item行数")
    private Integer itemcount;
    // 完成行数
    @Excel(name = "完成行数")
    private Integer finishcount;
    // 打印次数
    @Excel(name = "打印次数")
    private Integer printcount;
    // 源自定义1
    @Excel(name = "源自定义1")
    private String orgcustom1;
    // 源自定义2
    private String orgcustom2;
    // 源自定义3
    private String orgcustom3;
    // 源自定义4
    private String orgcustom4;
    // 源自定义5
    private String orgcustom5;
    // 源自定义6
    private String orgcustom6;
    // 源自定义7
    private String orgcustom7;
    // 源自定义8
    private String orgcustom8;
    // 源自定义9
    private String orgcustom9;
    // 源自定义10
    private String orgcustom10;
    // 源自定义11
    private String orgcustom11;
    // 源自定义12
    private String orgcustom12;
    // 源自定义13
    private String orgcustom13;
    // 源自定义14
    private String orgcustom14;
    // 源自定义15
    private String orgcustom15;
    // 源自定义16
    private String orgcustom16;
    // 颜色等级
    private String colorlevel;
    // 长
    private Double sizex;
    // 宽
    private Double sizey;
    // 厚
    private Double sizez;

    // 编码
    private String groupuid;
    // 名称
    private String groupname;
    // 缩写
    private String abbreviate;
    // 厂商等级
    private String grouplevel;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 工序id
    public String getWpid() {
        return wpid;
    }

    public void setWpid(String wpid) {
        this.wpid = wpid;
    }

    // 工序编码
    public String getWpcode() {
        return wpcode;
    }

    public void setWpcode(String wpcode) {
        this.wpcode = wpcode;
    }

    // 工序名称
    public String getWpname() {
        return wpname;
    }

    public void setWpname(String wpname) {
        this.wpname = wpname;
    }

    public Integer getDisablein() {
        return disablein;
    }

    public void setDisablein(Integer disablein) {
        this.disablein = disablein;
    }

    public Integer getDisableout() {
        return disableout;
    }

    public void setDisableout(Integer disableout) {
        this.disableout = disableout;
    }

    // 生产顺序
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 计划完工
    public Date getPlandate() {
        return plandate;
    }

    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 入组pcs数
    public Double getInpcsqty() {
        return inpcsqty;
    }

    public void setInpcsqty(Double inpcsqty) {
        this.inpcsqty = inpcsqty;
    }

    // 入组Sec数
    public Double getInsecqty() {
        return insecqty;
    }

    public void setInsecqty(Double insecqty) {
        this.insecqty = insecqty;
    }

    // 出组pcs数
    public Double getOutpcsqty() {
        return outpcsqty;
    }

    public void setOutpcsqty(Double outpcsqty) {
        this.outpcsqty = outpcsqty;
    }

    // 出组Sec数
    public Double getOutsecqty() {
        return outsecqty;
    }

    public void setOutsecqty(Double outsecqty) {
        this.outsecqty = outsecqty;
    }

    // 报废pcs数
    public Double getMrbpcsqty() {
        return mrbpcsqty;
    }

    public void setMrbpcsqty(Double mrbpcsqty) {
        this.mrbpcsqty = mrbpcsqty;
    }

    // 报废Sec数
    public Double getMrbsecqty() {
        return mrbsecqty;
    }

    public void setMrbsecqty(Double mrbsecqty) {
        this.mrbsecqty = mrbsecqty;
    }

    // 完工Pcs数
    public Double getComppcsqty() {
        return comppcsqty;
    }

    public void setComppcsqty(Double comppcsqty) {
        this.comppcsqty = comppcsqty;
    }

    // 完工Sec数
    public Double getCompsecqty() {
        return compsecqty;
    }

    public void setCompsecqty(Double compsecqty) {
        this.compsecqty = compsecqty;
    }

    // 辅助数量
    public Double getSubqty() {
        return subqty;
    }

    public void setSubqty(Double subqty) {
        this.subqty = subqty;
    }

    // 辅助单位
    public String getSubunit() {
        return subunit;
    }

    public void setSubunit(String subunit) {
        this.subunit = subunit;
    }

    // 开始日期
    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }

    // 结束日期
    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }

    // 工人
    public String getItemworker() {
        return itemworker;
    }

    public void setItemworker(String itemworker) {
        this.itemworker = itemworker;
    }

    // 委外Pcs数
    public Double getEpibolepcsqty() {
        return epibolepcsqty;
    }

    public void setEpibolepcsqty(Double epibolepcsqty) {
        this.epibolepcsqty = epibolepcsqty;
    }

    // 委外Sec数
    public Double getEpibolesecqty() {
        return epibolesecqty;
    }

    public void setEpibolesecqty(Double epibolesecqty) {
        this.epibolesecqty = epibolesecqty;
    }

    // 最后工序
    public Integer getLastwp() {
        return lastwp;
    }

    public void setLastwp(Integer lastwp) {
        this.lastwp = lastwp;
    }

    // 当前规格JSON
    public String getSpecjson() {
        return specjson;
    }

    public void setSpecjson(String specjson) {
        this.specjson = specjson;
    }

    // 包装规格JSON
    public String getSpecpackjson() {
        return specpackjson;
    }

    public void setSpecpackjson(String specpackjson) {
        this.specpackjson = specpackjson;
    }

    // 工作参数
    public String getWorkparam() {
        return workparam;
    }

    public void setWorkparam(String workparam) {
        this.workparam = workparam;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 计划开始
    public Date getStartplan() {
        return startplan;
    }

    public void setStartplan(Date startplan) {
        this.startplan = startplan;
    }

    // 检验单id
    public String getInspid() {
        return inspid;
    }

    public void setInspid(String inspid) {
        this.inspid = inspid;
    }

    // 检验单号
    public String getInspuid() {
        return inspuid;
    }

    public void setInspuid(String inspuid) {
        this.inspuid = inspuid;
    }

    // 1Pass2Fail3Pending
    public Integer getInspresult() {
        return inspresult;
    }

    public void setInspresult(Integer inspresult) {
        this.inspresult = inspresult;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    public String getWorktype() {
        return worktype;
    }

    public void setWorktype(String worktype) {
        this.worktype = worktype;
    }

    public String getWorkshopid() {
        return workshopid;
    }

    public void setWorkshopid(String workshopid) {
        this.workshopid = workshopid;
    }

    public String getWorkshop() {
        return workshop;
    }

    public void setWorkshop(String workshop) {
        this.workshop = workshop;
    }

    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public Double getWkpcsqty() {
        return wkpcsqty;
    }

    public void setWkpcsqty(Double wkpcsqty) {
        this.wkpcsqty = wkpcsqty;
    }

    public Double getWksecqty() {
        return wksecqty;
    }

    public void setWksecqty(Double wksecqty) {
        this.wksecqty = wksecqty;
    }

    public String getStatecode() {
        return statecode;
    }

    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }

    public Date getStatedate() {
        return statedate;
    }

    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }

    public String getWkwpid() {
        return wkwpid;
    }

    public void setWkwpid(String wkwpid) {
        this.wkwpid = wkwpid;
    }

    public String getWkwpcode() {
        return wkwpcode;
    }

    public void setWkwpcode(String wkwpcode) {
        this.wkwpcode = wkwpcode;
    }

    public String getWkwpname() {
        return wkwpname;
    }

    public void setWkwpname(String wkwpname) {
        this.wkwpname = wkwpname;
    }

    public Integer getWkrownum() {
        return wkrownum;
    }

    public void setWkrownum(Integer wkrownum) {
        this.wkrownum = wkrownum;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getCustpo() {
        return custpo;
    }

    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }

    public String getMachuid() {
        return machuid;
    }

    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }

    public String getMachitemid() {
        return machitemid;
    }

    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }

    public String getMachgroupid() {
        return machgroupid;
    }

    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }

    public String getMainplanuid() {
        return mainplanuid;
    }

    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }

    public String getMainplanitemid() {
        return mainplanitemid;
    }

    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }

    public String getWorkuid() {
        return workuid;
    }

    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }

    public String getWorkitemid() {
        return workitemid;
    }

    public void setWorkitemid(String workitemid) {
        this.workitemid = workitemid;
    }

    public String getSubstwpid() {
        return substwpid;
    }

    public void setSubstwpid(String substwpid) {
        this.substwpid = substwpid;
    }

    public String getSubstwpcode() {
        return substwpcode;
    }

    public void setSubstwpcode(String substwpcode) {
        this.substwpcode = substwpcode;
    }

    public String getSubstwpname() {
        return substwpname;
    }

    public void setSubstwpname(String substwpname) {
        this.substwpname = substwpname;
    }

    public String getSubendwpid() {
        return subendwpid;
    }

    public void setSubendwpid(String subendwpid) {
        this.subendwpid = subendwpid;
    }

    public String getSubendwpcode() {
        return subendwpcode;
    }

    public void setSubendwpcode(String subendwpcode) {
        this.subendwpcode = subendwpcode;
    }

    public String getSubendwpname() {
        return subendwpname;
    }

    public void setSubendwpname(String subendwpname) {
        this.subendwpname = subendwpname;
    }

    public String getSubuid() {
        return subuid;
    }

    public void setSubuid(String subuid) {
        this.subuid = subuid;
    }

    public Date getWorkdate() {
        return workdate;
    }

    public void setWorkdate(Date workdate) {
        this.workdate = workdate;
    }

    public String getCompwpid() {
        return compwpid;
    }

    public void setCompwpid(String compwpid) {
        this.compwpid = compwpid;
    }

    public String getCompwpcode() {
        return compwpcode;
    }

    public void setCompwpcode(String compwpcode) {
        this.compwpcode = compwpcode;
    }

    public String getCompwpname() {
        return compwpname;
    }

    public void setCompwpname(String compwpname) {
        this.compwpname = compwpname;
    }

    public String getWipgroupid() {
        return wipgroupid;
    }

    public void setWipgroupid(String wipgroupid) {
        this.wipgroupid = wipgroupid;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getAttributejson() {
        return attributejson;
    }

    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }

    public String getMatcode() {
        return matcode;
    }

    public void setMatcode(String matcode) {
        this.matcode = matcode;
    }

    public Integer getMatused() {
        return matused;
    }

    public void setMatused(Integer matused) {
        this.matused = matused;
    }

    public String getWkspecjson() {
        return wkspecjson;
    }

    public void setWkspecjson(String wkspecjson) {
        this.wkspecjson = wkspecjson;
    }

    public Integer getItemcount() {
        return itemcount;
    }

    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }

    public Integer getFinishcount() {
        return finishcount;
    }

    public void setFinishcount(Integer finishcount) {
        this.finishcount = finishcount;
    }

    public Integer getPrintcount() {
        return printcount;
    }

    public void setPrintcount(Integer printcount) {
        this.printcount = printcount;
    }

    public String getOrgcustom1() {
        return orgcustom1;
    }

    public void setOrgcustom1(String orgcustom1) {
        this.orgcustom1 = orgcustom1;
    }

    public String getOrgcustom2() {
        return orgcustom2;
    }

    public void setOrgcustom2(String orgcustom2) {
        this.orgcustom2 = orgcustom2;
    }

    public String getOrgcustom3() {
        return orgcustom3;
    }

    public void setOrgcustom3(String orgcustom3) {
        this.orgcustom3 = orgcustom3;
    }

    public String getOrgcustom4() {
        return orgcustom4;
    }

    public void setOrgcustom4(String orgcustom4) {
        this.orgcustom4 = orgcustom4;
    }

    public String getOrgcustom5() {
        return orgcustom5;
    }

    public void setOrgcustom5(String orgcustom5) {
        this.orgcustom5 = orgcustom5;
    }

    public String getOrgcustom6() {
        return orgcustom6;
    }

    public void setOrgcustom6(String orgcustom6) {
        this.orgcustom6 = orgcustom6;
    }

    public String getOrgcustom7() {
        return orgcustom7;
    }

    public void setOrgcustom7(String orgcustom7) {
        this.orgcustom7 = orgcustom7;
    }

    public String getOrgcustom8() {
        return orgcustom8;
    }

    public void setOrgcustom8(String orgcustom8) {
        this.orgcustom8 = orgcustom8;
    }

    public String getOrgcustom9() {
        return orgcustom9;
    }

    public void setOrgcustom9(String orgcustom9) {
        this.orgcustom9 = orgcustom9;
    }

    public String getOrgcustom10() {
        return orgcustom10;
    }

    public void setOrgcustom10(String orgcustom10) {
        this.orgcustom10 = orgcustom10;
    }

    public String getOrgcustom11() {
        return orgcustom11;
    }

    public void setOrgcustom11(String orgcustom11) {
        this.orgcustom11 = orgcustom11;
    }

    public String getOrgcustom12() {
        return orgcustom12;
    }

    public void setOrgcustom12(String orgcustom12) {
        this.orgcustom12 = orgcustom12;
    }

    public String getOrgcustom13() {
        return orgcustom13;
    }

    public void setOrgcustom13(String orgcustom13) {
        this.orgcustom13 = orgcustom13;
    }

    public String getOrgcustom14() {
        return orgcustom14;
    }

    public void setOrgcustom14(String orgcustom14) {
        this.orgcustom14 = orgcustom14;
    }

    public String getOrgcustom15() {
        return orgcustom15;
    }

    public void setOrgcustom15(String orgcustom15) {
        this.orgcustom15 = orgcustom15;
    }

    public String getOrgcustom16() {
        return orgcustom16;
    }

    public void setOrgcustom16(String orgcustom16) {
        this.orgcustom16 = orgcustom16;
    }

    public String getColorlevel() {
        return colorlevel;
    }

    public void setColorlevel(String colorlevel) {
        this.colorlevel = colorlevel;
    }

    public Double getSizex() {
        return sizex;
    }

    public void setSizex(Double sizex) {
        this.sizex = sizex;
    }

    public Double getSizey() {
        return sizey;
    }

    public void setSizey(Double sizey) {
        this.sizey = sizey;
    }

    public Double getSizez() {
        return sizez;
    }

    public void setSizez(Double sizez) {
        this.sizez = sizez;
    }

    public String getGroupuid() {
        return groupuid;
    }

    public void setGroupuid(String groupuid) {
        this.groupuid = groupuid;
    }

    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    public String getAbbreviate() {
        return abbreviate;
    }

    public void setAbbreviate(String abbreviate) {
        this.abbreviate = abbreviate;
    }

    public String getGrouplevel() {
        return grouplevel;
    }

    public void setGrouplevel(String grouplevel) {
        this.grouplevel = grouplevel;
    }
}

