package inks.service.std.manu.domain.pojo;

import java.io.Serializable;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

/**
 * MRB项目(QmsMrbitem)Pojo
 *
 * <AUTHOR>
 * @since 2022-10-28 16:18:45
 */
public class QmsMrbitemClonePojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = -54694633623298012L;
    // id
    @Excel(name = "id")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 货品编码
    @Excel(name = "货品编码")
    private String goodsid;
    // 产品编码
    @Excel(name = "产品编码")
    private String itemcode;
    // 产品名称
    @Excel(name = "产品名称")
    private String itemname;
    // 产品规格
    @Excel(name = "产品规格")
    private String itemspec;
    // 产品单位
    @Excel(name = "产品单位")
    private String itemunit;
    // 疵点编码
    @Excel(name = "疵点编码")
    private String defectid;
    // 数量
    @Excel(name = "数量")
    private Double quantity;
    // Sec数量
    @Excel(name = "Sec数量")
    private Double secqty;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 负责工序id
    @Excel(name = "负责工序id")
    private String dutywpid;
    // 工序编码
    @Excel(name = "工序编码")
    private String dutywpcode;
    // 责任工序
    @Excel(name = "责任工序")
    private String dutywpname;
    // 原始单id
    @Excel(name = "原始单id")
    private String redorgitemid;
    // 销售单号
    @Excel(name = "销售单号")
    private String machuid;
    // 销售子项id
    @Excel(name = "销售子项id")
    private String machitemid;
    // 销售客户id
    @Excel(name = "销售客户id")
    private String machgroupid;
    // 客户PO
    @Excel(name = "客户PO")
    private String custpo;
    // 主计划号
    @Excel(name = "主计划号")
    private String mainplanuid;
    // 主计划id
    @Excel(name = "主计划id")
    private String mainplanitemid;
    // 1厂制2委制
    @Excel(name = "1厂制2委制")
    private Integer wkbilltype;
    // 加工单号
    @Excel(name = "加工单号")
    private String workuid;
    // 加工单id
    @Excel(name = "加工单id")
    private String workitemid;
    // WIP单号
    @Excel(name = "WIP单号")
    private String wipuid;
    // WIP单Itemid
    @Excel(name = "WIP单Itemid")
    private String wipitemid;
    // 库存id
    @Excel(name = "库存id")
    private String inveid;
    // 长
    @Excel(name = "长")
    private Double sizex;
    // 宽
    @Excel(name = "宽")
    private Double sizey;
    // 高
    @Excel(name = "高")
    private Double sizez;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    //疵点名称
    private String defname;
    //疵点code
    private String defcode;

    public String getDefname() {
        return defname;
    }

    public void setDefname(String defname) {
        this.defname = defname;
    }

    public String getDefcode() {
        return defcode;
    }

    public void setDefcode(String defcode) {
        this.defcode = defcode;
    }

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 货品编码
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 产品编码
    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    // 产品名称
    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    // 产品规格
    public String getItemspec() {
        return itemspec;
    }

    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }

    // 产品单位
    public String getItemunit() {
        return itemunit;
    }

    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }

    // 疵点编码
    public String getDefectid() {
        return defectid;
    }

    public void setDefectid(String defectid) {
        this.defectid = defectid;
    }

    // 数量
    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    // Sec数量
    public Double getSecqty() {
        return secqty;
    }

    public void setSecqty(Double secqty) {
        this.secqty = secqty;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 负责工序id
    public String getDutywpid() {
        return dutywpid;
    }

    public void setDutywpid(String dutywpid) {
        this.dutywpid = dutywpid;
    }

    // 工序编码
    public String getDutywpcode() {
        return dutywpcode;
    }

    public void setDutywpcode(String dutywpcode) {
        this.dutywpcode = dutywpcode;
    }

    // 责任工序
    public String getDutywpname() {
        return dutywpname;
    }

    public void setDutywpname(String dutywpname) {
        this.dutywpname = dutywpname;
    }

    // 原始单id
    public String getRedorgitemid() {
        return redorgitemid;
    }

    public void setRedorgitemid(String redorgitemid) {
        this.redorgitemid = redorgitemid;
    }

    // 销售单号
    public String getMachuid() {
        return machuid;
    }

    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }

    // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }

    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }

    // 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }

    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }

    // 客户PO
    public String getCustpo() {
        return custpo;
    }

    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }

    // 主计划号
    public String getMainplanuid() {
        return mainplanuid;
    }

    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }

    // 主计划id
    public String getMainplanitemid() {
        return mainplanitemid;
    }

    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }

    // 1厂制2委制
    public Integer getWkbilltype() {
        return wkbilltype;
    }

    public void setWkbilltype(Integer wkbilltype) {
        this.wkbilltype = wkbilltype;
    }

    // 加工单号
    public String getWorkuid() {
        return workuid;
    }

    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }

    // 加工单id
    public String getWorkitemid() {
        return workitemid;
    }

    public void setWorkitemid(String workitemid) {
        this.workitemid = workitemid;
    }

    // WIP单号
    public String getWipuid() {
        return wipuid;
    }

    public void setWipuid(String wipuid) {
        this.wipuid = wipuid;
    }

    // WIP单Itemid
    public String getWipitemid() {
        return wipitemid;
    }

    public void setWipitemid(String wipitemid) {
        this.wipitemid = wipitemid;
    }

    // 库存id
    public String getInveid() {
        return inveid;
    }

    public void setInveid(String inveid) {
        this.inveid = inveid;
    }

    // 长
    public Double getSizex() {
        return sizex;
    }

    public void setSizex(Double sizex) {
        this.sizex = sizex;
    }

    // 宽
    public Double getSizey() {
        return sizey;
    }

    public void setSizey(Double sizey) {
        this.sizey = sizey;
    }

    // 高
    public Double getSizez() {
        return sizez;
    }

    public void setSizez(Double sizez) {
        this.sizez = sizez;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

