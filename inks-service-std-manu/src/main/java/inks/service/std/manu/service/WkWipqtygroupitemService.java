package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipqtygroupitemPojo;

import java.util.List;

/**
 * 过数小组人员(WkWipqtygroupitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-10 11:08:19
 */
public interface WkWipqtygroupitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtygroupitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipqtygroupitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipqtygroupitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkWipqtygroupitemPojo 实例对象
     * @return 实例对象
     */
    WkWipqtygroupitemPojo insert(WkWipqtygroupitemPojo wkWipqtygroupitemPojo);

    /**
     * 修改数据
     *
     * @param wkWipqtygroupitempojo 实例对象
     * @return 实例对象
     */
    WkWipqtygroupitemPojo update(WkWipqtygroupitemPojo wkWipqtygroupitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkWipqtygroupitempojo 实例对象
     * @return 实例对象
     */
    WkWipqtygroupitemPojo clearNull(WkWipqtygroupitemPojo wkWipqtygroupitempojo);
}
