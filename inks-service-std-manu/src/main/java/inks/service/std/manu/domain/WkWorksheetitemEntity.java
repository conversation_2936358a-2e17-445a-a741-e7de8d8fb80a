package inks.service.std.manu.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 厂制项目(WkWorksheetitem)Entity
 *
 * <AUTHOR>
 * @since 2024-03-07 10:53:38
 */
public class WkWorksheetitemEntity implements Serializable {
    private static final long serialVersionUID = -70091236923765221L;
     // id
    private String id;
     // Pid
    private String pid;
     // 货品id
    private String goodsid;
     // 产品数量
    private Double quantity;
     // 加工单价
    private Double price;
     // 加工金额
    private Double amount;
     // 单件工时
    private Double itemhour;
     // 计划工时
    private Double planhour;
     // 计划开工
    private Date startdate;
     // 计划完工
    private Date plandate;
     // 完工数量
    private Double finishqty;
     // 完工工时
    private Double finishhour;
     // 报废数量
    private Double mrbqty;
     // 是否入库
    private Integer instorage;
     // 有效标识
    private Integer enabledmark;
     // 关闭
    private Integer closed;
     // 开始工序
    private String startwpid;
     // 结束工序
    private String endwpid;
     // 备注
    private String remark;
     // 状态
    private String statecode;
     // 状态时间
    private Date statedate;
     // 行号
    private Integer rownum;
     // 生产类型 订单 样品
    private String machtype;
     // 销售单号
    private String machuid;
     // 销售子项id
    private String machitemid;
     // 订单批次
    private String machbatch;
     // 销售客户id
    private String machgroupid;
     // Mrp单号
    private String mrpuid;
     // Mrp子项id
    private String mrpitemid;
     // 客户
    private String customer;
     // 客户PO
    private String custpo;
     // 应用单号
    private String citeuid;
     // 引用子项id
    private String citeitemid;
     // 主计划单号
    private String mainplanuid;
     // 主计划子项id
    private String mainplanitemid;
     // 指定库位
    private String location;
     // 指定批号
    private String batchno;
     // Wip已用
    private Integer wipused;
     // 当前工序id
    private String wkwpid;
     // 当前工序编码
    private String wkwpcode;
     // 当前工序名称
    private String wkwpname;
     // 当前行号
    private String wkrownum;
     // 作废制表id
    private String disannullisterid;
     // 作废制表
    private String disannullister;
     // 作废日期
    private Date disannuldate;
     // 作废
    private Integer disannulmark;
     // 属性Josn
    private String attributejson;
     // 报工数量
    private Double reportqty;
     // 验收数量
    private Double compqty;
     // 完工率
    private Double finishrate;
     // 第二数量
    private Double secqty;
     // 板宽
    private Double panelwidth;
     // 板高
    private Double panelheight;
     // 板面 枚数
    private Integer pcsinpanel;
     // 板厚
    private Double panelthick;
     // 主料Code
    private String matcode;
     // 主料已领
    private Integer matused;
     // 行编码
    private String rowcode;
     // 总投数
    private Double wkpcsqty;
     // 第二总投数
    private Double wksecqty;
     // 是否合并1被合并2合并为
    private Integer mergemark;
     // 来源:0=其他
    private Integer sourcetype;
     // 成本Item
    private String costitemjson;
     // 成本分类
    private String costgroupjson;
     // 虚拟货品
    private Integer virtualitem;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 货品id
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 产品数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
   // 加工单价
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
   // 加工金额
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
   // 单件工时
    public Double getItemhour() {
        return itemhour;
    }
    
    public void setItemhour(Double itemhour) {
        this.itemhour = itemhour;
    }
        
   // 计划工时
    public Double getPlanhour() {
        return planhour;
    }
    
    public void setPlanhour(Double planhour) {
        this.planhour = planhour;
    }
        
   // 计划开工
    public Date getStartdate() {
        return startdate;
    }
    
    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }
        
   // 计划完工
    public Date getPlandate() {
        return plandate;
    }
    
    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }
        
   // 完工数量
    public Double getFinishqty() {
        return finishqty;
    }
    
    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }
        
   // 完工工时
    public Double getFinishhour() {
        return finishhour;
    }
    
    public void setFinishhour(Double finishhour) {
        this.finishhour = finishhour;
    }
        
   // 报废数量
    public Double getMrbqty() {
        return mrbqty;
    }
    
    public void setMrbqty(Double mrbqty) {
        this.mrbqty = mrbqty;
    }
        
   // 是否入库
    public Integer getInstorage() {
        return instorage;
    }
    
    public void setInstorage(Integer instorage) {
        this.instorage = instorage;
    }
        
   // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 关闭
    public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
   // 开始工序
    public String getStartwpid() {
        return startwpid;
    }
    
    public void setStartwpid(String startwpid) {
        this.startwpid = startwpid;
    }
        
   // 结束工序
    public String getEndwpid() {
        return endwpid;
    }
    
    public void setEndwpid(String endwpid) {
        this.endwpid = endwpid;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 状态
    public String getStatecode() {
        return statecode;
    }
    
    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }
        
   // 状态时间
    public Date getStatedate() {
        return statedate;
    }
    
    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 生产类型 订单 样品
    public String getMachtype() {
        return machtype;
    }
    
    public void setMachtype(String machtype) {
        this.machtype = machtype;
    }
        
   // 销售单号
    public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
   // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
   // 订单批次
    public String getMachbatch() {
        return machbatch;
    }
    
    public void setMachbatch(String machbatch) {
        this.machbatch = machbatch;
    }
        
   // 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }
    
    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }
        
   // Mrp单号
    public String getMrpuid() {
        return mrpuid;
    }
    
    public void setMrpuid(String mrpuid) {
        this.mrpuid = mrpuid;
    }
        
   // Mrp子项id
    public String getMrpitemid() {
        return mrpitemid;
    }
    
    public void setMrpitemid(String mrpitemid) {
        this.mrpitemid = mrpitemid;
    }
        
   // 客户
    public String getCustomer() {
        return customer;
    }
    
    public void setCustomer(String customer) {
        this.customer = customer;
    }
        
   // 客户PO
    public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
   // 应用单号
    public String getCiteuid() {
        return citeuid;
    }
    
    public void setCiteuid(String citeuid) {
        this.citeuid = citeuid;
    }
        
   // 引用子项id
    public String getCiteitemid() {
        return citeitemid;
    }
    
    public void setCiteitemid(String citeitemid) {
        this.citeitemid = citeitemid;
    }
        
   // 主计划单号
    public String getMainplanuid() {
        return mainplanuid;
    }
    
    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }
        
   // 主计划子项id
    public String getMainplanitemid() {
        return mainplanitemid;
    }
    
    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }
        
   // 指定库位
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
        
   // 指定批号
    public String getBatchno() {
        return batchno;
    }
    
    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }
        
   // Wip已用
    public Integer getWipused() {
        return wipused;
    }
    
    public void setWipused(Integer wipused) {
        this.wipused = wipused;
    }
        
   // 当前工序id
    public String getWkwpid() {
        return wkwpid;
    }
    
    public void setWkwpid(String wkwpid) {
        this.wkwpid = wkwpid;
    }
        
   // 当前工序编码
    public String getWkwpcode() {
        return wkwpcode;
    }
    
    public void setWkwpcode(String wkwpcode) {
        this.wkwpcode = wkwpcode;
    }
        
   // 当前工序名称
    public String getWkwpname() {
        return wkwpname;
    }
    
    public void setWkwpname(String wkwpname) {
        this.wkwpname = wkwpname;
    }
        
   // 当前行号
    public String getWkrownum() {
        return wkrownum;
    }
    
    public void setWkrownum(String wkrownum) {
        this.wkrownum = wkrownum;
    }
        
   // 作废制表id
    public String getDisannullisterid() {
        return disannullisterid;
    }
    
    public void setDisannullisterid(String disannullisterid) {
        this.disannullisterid = disannullisterid;
    }
        
   // 作废制表
    public String getDisannullister() {
        return disannullister;
    }
    
    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }
        
   // 作废日期
    public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
   // 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
   // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // 报工数量
    public Double getReportqty() {
        return reportqty;
    }
    
    public void setReportqty(Double reportqty) {
        this.reportqty = reportqty;
    }
        
   // 验收数量
    public Double getCompqty() {
        return compqty;
    }
    
    public void setCompqty(Double compqty) {
        this.compqty = compqty;
    }
        
   // 完工率
    public Double getFinishrate() {
        return finishrate;
    }
    
    public void setFinishrate(Double finishrate) {
        this.finishrate = finishrate;
    }
        
   // 第二数量
    public Double getSecqty() {
        return secqty;
    }
    
    public void setSecqty(Double secqty) {
        this.secqty = secqty;
    }
        
   // 板宽
    public Double getPanelwidth() {
        return panelwidth;
    }
    
    public void setPanelwidth(Double panelwidth) {
        this.panelwidth = panelwidth;
    }
        
   // 板高
    public Double getPanelheight() {
        return panelheight;
    }
    
    public void setPanelheight(Double panelheight) {
        this.panelheight = panelheight;
    }
        
   // 板面 枚数
    public Integer getPcsinpanel() {
        return pcsinpanel;
    }
    
    public void setPcsinpanel(Integer pcsinpanel) {
        this.pcsinpanel = pcsinpanel;
    }
        
   // 板厚
    public Double getPanelthick() {
        return panelthick;
    }
    
    public void setPanelthick(Double panelthick) {
        this.panelthick = panelthick;
    }
        
   // 主料Code
    public String getMatcode() {
        return matcode;
    }
    
    public void setMatcode(String matcode) {
        this.matcode = matcode;
    }
        
   // 主料已领
    public Integer getMatused() {
        return matused;
    }
    
    public void setMatused(Integer matused) {
        this.matused = matused;
    }
        
   // 行编码
    public String getRowcode() {
        return rowcode;
    }
    
    public void setRowcode(String rowcode) {
        this.rowcode = rowcode;
    }
        
   // 总投数
    public Double getWkpcsqty() {
        return wkpcsqty;
    }
    
    public void setWkpcsqty(Double wkpcsqty) {
        this.wkpcsqty = wkpcsqty;
    }
        
   // 第二总投数
    public Double getWksecqty() {
        return wksecqty;
    }
    
    public void setWksecqty(Double wksecqty) {
        this.wksecqty = wksecqty;
    }
        
   // 是否合并1被合并2合并为
    public Integer getMergemark() {
        return mergemark;
    }
    
    public void setMergemark(Integer mergemark) {
        this.mergemark = mergemark;
    }
        
   // 来源:0=其他
    public Integer getSourcetype() {
        return sourcetype;
    }
    
    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }
        
   // 成本Item
    public String getCostitemjson() {
        return costitemjson;
    }
    
    public void setCostitemjson(String costitemjson) {
        this.costitemjson = costitemjson;
    }
        
   // 成本分类
    public String getCostgroupjson() {
        return costgroupjson;
    }
    
    public void setCostgroupjson(String costgroupjson) {
        this.costgroupjson = costgroupjson;
    }
        
   // 虚拟货品
    public Integer getVirtualitem() {
        return virtualitem;
    }
    
    public void setVirtualitem(Integer virtualitem) {
        this.virtualitem = virtualitem;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

