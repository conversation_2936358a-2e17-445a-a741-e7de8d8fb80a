package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSccompleteitemEntity;
import inks.service.std.manu.domain.pojo.WkSccompleteitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 验收项目(WkSccompleteitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-09 12:50:36
 */
 @Mapper
public interface WkSccompleteitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccompleteitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSccompleteitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSccompleteitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkSccompleteitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSccompleteitemEntity wkSccompleteitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkSccompleteitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkSccompleteitemEntity wkSccompleteitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int updateCompQty(@Param("subcontractitemid") String subcontractitemid, @Param("tid") String tid);

    int updateWsAcceFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);
}

