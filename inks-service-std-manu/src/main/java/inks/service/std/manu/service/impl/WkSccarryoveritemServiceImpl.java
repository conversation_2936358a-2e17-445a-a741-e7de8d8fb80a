package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSccarryoveritemEntity;
import inks.service.std.manu.domain.pojo.WkSccarryoveritemPojo;
import inks.service.std.manu.mapper.WkSccarryoveritemMapper;
import inks.service.std.manu.service.WkSccarryoveritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 结转子表(WkSccarryoveritem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-09 14:05:01
 */
@Service("wkSccarryoveritemService")
public class WkSccarryoveritemServiceImpl implements WkSccarryoveritemService {
    @Resource
    private WkSccarryoveritemMapper wkSccarryoveritemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSccarryoveritemPojo getEntity(String key, String tid) {
        return this.wkSccarryoveritemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSccarryoveritemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSccarryoveritemPojo> lst = wkSccarryoveritemMapper.getPageList(queryParam);
            PageInfo<WkSccarryoveritemPojo> pageInfo = new PageInfo<WkSccarryoveritemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkSccarryoveritemPojo> getList(String Pid, String tid) {
        try {
            List<WkSccarryoveritemPojo> lst = wkSccarryoveritemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkSccarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccarryoveritemPojo insert(WkSccarryoveritemPojo wkSccarryoveritemPojo) {
        //初始化item的NULL
        WkSccarryoveritemPojo itempojo = this.clearNull(wkSccarryoveritemPojo);
        WkSccarryoveritemEntity wkSccarryoveritemEntity = new WkSccarryoveritemEntity();
        BeanUtils.copyProperties(itempojo, wkSccarryoveritemEntity);

        wkSccarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkSccarryoveritemEntity.setRevision(1);  //乐观锁
        this.wkSccarryoveritemMapper.insert(wkSccarryoveritemEntity);
        return this.getEntity(wkSccarryoveritemEntity.getId(), wkSccarryoveritemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSccarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccarryoveritemPojo update(WkSccarryoveritemPojo wkSccarryoveritemPojo) {
        WkSccarryoveritemEntity wkSccarryoveritemEntity = new WkSccarryoveritemEntity();
        BeanUtils.copyProperties(wkSccarryoveritemPojo, wkSccarryoveritemEntity);
        this.wkSccarryoveritemMapper.update(wkSccarryoveritemEntity);
        return this.getEntity(wkSccarryoveritemEntity.getId(), wkSccarryoveritemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkSccarryoveritemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkSccarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccarryoveritemPojo clearNull(WkSccarryoveritemPojo wkSccarryoveritemPojo) {
        //初始化NULL字段
        if (wkSccarryoveritemPojo.getPid() == null) wkSccarryoveritemPojo.setPid("");
        if (wkSccarryoveritemPojo.getGoodsid() == null) wkSccarryoveritemPojo.setGoodsid("");
        if (wkSccarryoveritemPojo.getItemcode() == null) wkSccarryoveritemPojo.setItemcode("");
        if (wkSccarryoveritemPojo.getItemname() == null) wkSccarryoveritemPojo.setItemname("");
        if (wkSccarryoveritemPojo.getItemspec() == null) wkSccarryoveritemPojo.setItemspec("");
        if (wkSccarryoveritemPojo.getItemunit() == null) wkSccarryoveritemPojo.setItemunit("");
        if (wkSccarryoveritemPojo.getOpenqty() == null) wkSccarryoveritemPojo.setOpenqty(0D);
        if (wkSccarryoveritemPojo.getOpenamount() == null) wkSccarryoveritemPojo.setOpenamount(0D);
        if (wkSccarryoveritemPojo.getInqty() == null) wkSccarryoveritemPojo.setInqty(0D);
        if (wkSccarryoveritemPojo.getInamount() == null) wkSccarryoveritemPojo.setInamount(0D);
        if (wkSccarryoveritemPojo.getOutqty() == null) wkSccarryoveritemPojo.setOutqty(0D);
        if (wkSccarryoveritemPojo.getOutamount() == null) wkSccarryoveritemPojo.setOutamount(0D);
        if (wkSccarryoveritemPojo.getCloseqty() == null) wkSccarryoveritemPojo.setCloseqty(0D);
        if (wkSccarryoveritemPojo.getCloseamount() == null) wkSccarryoveritemPojo.setCloseamount(0D);
        if (wkSccarryoveritemPojo.getSkuid() == null) wkSccarryoveritemPojo.setSkuid("");
        if (wkSccarryoveritemPojo.getAttributejson() == null) wkSccarryoveritemPojo.setAttributejson("");
        if (wkSccarryoveritemPojo.getRownum() == null) wkSccarryoveritemPojo.setRownum(0);
        if (wkSccarryoveritemPojo.getCustom1() == null) wkSccarryoveritemPojo.setCustom1("");
        if (wkSccarryoveritemPojo.getCustom2() == null) wkSccarryoveritemPojo.setCustom2("");
        if (wkSccarryoveritemPojo.getCustom3() == null) wkSccarryoveritemPojo.setCustom3("");
        if (wkSccarryoveritemPojo.getCustom4() == null) wkSccarryoveritemPojo.setCustom4("");
        if (wkSccarryoveritemPojo.getCustom5() == null) wkSccarryoveritemPojo.setCustom5("");
        if (wkSccarryoveritemPojo.getCustom6() == null) wkSccarryoveritemPojo.setCustom6("");
        if (wkSccarryoveritemPojo.getCustom7() == null) wkSccarryoveritemPojo.setCustom7("");
        if (wkSccarryoveritemPojo.getCustom8() == null) wkSccarryoveritemPojo.setCustom8("");
        if (wkSccarryoveritemPojo.getCustom9() == null) wkSccarryoveritemPojo.setCustom9("");
        if (wkSccarryoveritemPojo.getCustom10() == null) wkSccarryoveritemPojo.setCustom10("");
        if (wkSccarryoveritemPojo.getTenantid() == null) wkSccarryoveritemPojo.setTenantid("");
        if (wkSccarryoveritemPojo.getRevision() == null) wkSccarryoveritemPojo.setRevision(0);
        return wkSccarryoveritemPojo;
    }
}
