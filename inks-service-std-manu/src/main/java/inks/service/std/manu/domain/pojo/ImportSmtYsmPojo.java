package inks.service.std.manu.domain.pojo; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/3
 * @param YSM导入数据接收对象
 */

import cn.afterturn.easypoi.excel.annotation.Excel;

public class ImportSmtYsmPojo {
    private String id;
    // Pid
    private String pid;
    // 设备编码
    private String devcode;
    // 表名(备用)
    private String tablecode;
    // 站位序号
    @Excel(name ="安装号码")
    private Integer stationnum;
    // 站位编码
    private String stationcode;
    // 点位符号
    @Excel(name="图样名")
    private String pointmark;
    // 货品id
    private String goodsid;
    // 物料类型
    private String goodstype;
    // 物料编码
    @Excel(name="元件名")
    private String goodsuid;
    // 物料名称
    @Excel(name ="备注")
    private String goodsname;
    // 物料规格
    private String goodsspec;
    // 物料单位
    private String goodsunit;
    // 单件配数
    @Excel(name="总数")
    private Double singleqty;
    // 数量
    private Double quantity;
    // 完工数量
    private Double finishqty;
    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 自定义9
    private String custom9;
    // 自定义10
    private String custom10;
    // 租户id
    private String tenantid;
    // 乐观锁
    private Integer revision;
    private String partid;

    public String getPartid() {
        return partid;
    }

    public void setPartid(String partid) {
        this.partid = partid;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getDevcode() {
        return devcode;
    }

    public void setDevcode(String devcode) {
        this.devcode = devcode;
    }

    public String getTablecode() {
        return tablecode;
    }

    public void setTablecode(String tablecode) {
        this.tablecode = tablecode;
    }

    public Integer getStationnum() {
        return stationnum;
    }

    public void setStationnum(Integer stationnum) {
        this.stationnum = stationnum;
    }

    public String getStationcode() {
        return stationcode;
    }

    public void setStationcode(String stationcode) {
        this.stationcode = stationcode;
    }

    public String getPointmark() {
        return pointmark;
    }

    public void setPointmark(String pointmark) {
        this.pointmark = pointmark;
    }

    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    public String getGoodstype() {
        return goodstype;
    }

    public void setGoodstype(String goodstype) {
        this.goodstype = goodstype;
    }

    public String getGoodsuid() {
        return goodsuid;
    }

    public void setGoodsuid(String goodsuid) {
        this.goodsuid = goodsuid;
    }

    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }

    public String getGoodsspec() {
        return goodsspec;
    }

    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public Double getSingleqty() {
        return singleqty;
    }

    public void setSingleqty(Double singleqty) {
        this.singleqty = singleqty;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public Double getFinishqty() {
        return finishqty;
    }

    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }
}
