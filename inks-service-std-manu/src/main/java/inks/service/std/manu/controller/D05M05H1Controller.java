package inks.service.std.manu.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.service.HiWipnoteService;
import inks.service.std.manu.service.HiWipnoteitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * WIP历史记录(Hi_WipNote)表控制层
 *
 * <AUTHOR>
 * @since 2023-10-16 16:19:14
 */
@RestController
@RequestMapping("D05M05H1")
@Api(tags = "D05M05H1:WIP历史记录")
public class D05M05H1Controller extends HiWipnoteController {
    @Resource
    private HiWipnoteService hiWipnoteService;
    @Resource
    private HiWipnoteitemService hiWipnoteitemService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "時間範圍:將數據遷移到歷史表(並刪除遷移數據)", notes = "A表遷移至B表,並從A表刪除遷移數據", produces = "application/json")
    @RequestMapping(value = "/moveNowToHi", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipNote.List")//时间 年 月
    public R<String> moveNowToHi(String startdate, String enddate) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 同时进行WipNote主子表的迁移
            String result = this.hiWipnoteService.moveNowToHi(startdate, enddate, loginUser.getTenantid());
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "時間範圍:反向/moveNowToHi 將數據从历史表遷移到正常表(並刪除遷移數據),若传入Hi_WipNote.id集合,则忽略时间范围只迁移id集合", notes = "A表遷移至B表,並從A表刪除遷移數據", produces = "application/json")
    @RequestMapping(value = "/moveHiToNow", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipNote.List")
    public R<String> moveHiToNow(String startdate, String enddate, @RequestBody(required = false) List<String> ids) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            System.out.println("ids = " + ids);
            // 同时进行WipNote主子表的迁移
            String result = this.hiWipnoteService.moveHiToNow(startdate, enddate, ids, loginUser.getTenantid());
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
