package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkMainplanitemEntity;
import inks.service.std.manu.domain.pojo.WkMainplanitemPojo;
import inks.service.std.manu.mapper.WkMainplanitemMapper;
import inks.service.std.manu.service.WkMainplanitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 主计划项目(WkMainplanitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-21 13:05:41
 */
@Service("wkMainplanitemService")
public class WkMainplanitemServiceImpl implements WkMainplanitemService {
    @Resource
    private WkMainplanitemMapper wkMainplanitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMainplanitemPojo getEntity(String key, String tid) {
        return this.wkMainplanitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMainplanitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMainplanitemPojo> lst = wkMainplanitemMapper.getPageList(queryParam);
            PageInfo<WkMainplanitemPojo> pageInfo = new PageInfo<WkMainplanitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkMainplanitemPojo> getList(String Pid, String tid) {
        try {
            List<WkMainplanitemPojo> lst = wkMainplanitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkMainplanitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMainplanitemPojo insert(WkMainplanitemPojo wkMainplanitemPojo) {
        //初始化item的NULL
        WkMainplanitemPojo itempojo = this.clearNull(wkMainplanitemPojo);
        WkMainplanitemEntity wkMainplanitemEntity = new WkMainplanitemEntity();
        BeanUtils.copyProperties(itempojo, wkMainplanitemEntity);

        wkMainplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkMainplanitemEntity.setRevision(1);  //乐观锁
        this.wkMainplanitemMapper.insert(wkMainplanitemEntity);
        return this.getEntity(wkMainplanitemEntity.getId(), wkMainplanitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkMainplanitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMainplanitemPojo update(WkMainplanitemPojo wkMainplanitemPojo) {
        WkMainplanitemEntity wkMainplanitemEntity = new WkMainplanitemEntity();
        BeanUtils.copyProperties(wkMainplanitemPojo, wkMainplanitemEntity);
        this.wkMainplanitemMapper.update(wkMainplanitemEntity);
        return this.getEntity(wkMainplanitemEntity.getId(), wkMainplanitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkMainplanitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkMainplanitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMainplanitemPojo clearNull(WkMainplanitemPojo wkMainplanitemPojo) {
        //初始化NULL字段
        if (wkMainplanitemPojo.getPid() == null) wkMainplanitemPojo.setPid("");
        if (wkMainplanitemPojo.getGoodsid() == null) wkMainplanitemPojo.setGoodsid("");
        if (wkMainplanitemPojo.getQuantity() == null) wkMainplanitemPojo.setQuantity(0D);
        if (wkMainplanitemPojo.getPrice() == null) wkMainplanitemPojo.setPrice(0D);
        if (wkMainplanitemPojo.getAmount() == null) wkMainplanitemPojo.setAmount(0D);
        if (wkMainplanitemPojo.getStartdate() == null) wkMainplanitemPojo.setStartdate(new Date());
        if (wkMainplanitemPojo.getPlandate() == null) wkMainplanitemPojo.setPlandate(new Date());
        if (wkMainplanitemPojo.getStartqty() == null) wkMainplanitemPojo.setStartqty(0D);
        if (wkMainplanitemPojo.getFinishqty() == null) wkMainplanitemPojo.setFinishqty(0D);
        if (wkMainplanitemPojo.getMrbqty() == null) wkMainplanitemPojo.setMrbqty(0D);
        if (wkMainplanitemPojo.getStartsecqty() == null) wkMainplanitemPojo.setStartsecqty(0D);
        if (wkMainplanitemPojo.getEnabledmark() == null) wkMainplanitemPojo.setEnabledmark(0);
        if (wkMainplanitemPojo.getClosed() == null) wkMainplanitemPojo.setClosed(0);
        if (wkMainplanitemPojo.getRemark() == null) wkMainplanitemPojo.setRemark("");
        if (wkMainplanitemPojo.getStatecode() == null) wkMainplanitemPojo.setStatecode("");
        if (wkMainplanitemPojo.getStatedate() == null) wkMainplanitemPojo.setStatedate(new Date());
        if (wkMainplanitemPojo.getRownum() == null) wkMainplanitemPojo.setRownum(0);
        if (wkMainplanitemPojo.getMachtype() == null) wkMainplanitemPojo.setMachtype("");
        if (wkMainplanitemPojo.getMachuid() == null) wkMainplanitemPojo.setMachuid("");
        if (wkMainplanitemPojo.getMachitemid() == null) wkMainplanitemPojo.setMachitemid("");
        if (wkMainplanitemPojo.getMachbatch() == null) wkMainplanitemPojo.setMachbatch("");
        if (wkMainplanitemPojo.getMachgroupid() == null) wkMainplanitemPojo.setMachgroupid("");
        if (wkMainplanitemPojo.getCustomer() == null) wkMainplanitemPojo.setCustomer("");
        if (wkMainplanitemPojo.getCustpo() == null) wkMainplanitemPojo.setCustpo("");
        if (wkMainplanitemPojo.getMrpuid() == null) wkMainplanitemPojo.setMrpuid("");
        if (wkMainplanitemPojo.getMrpid() == null) wkMainplanitemPojo.setMrpid("");
        if (wkMainplanitemPojo.getDisannulmark() == null) wkMainplanitemPojo.setDisannulmark(0);
        if (wkMainplanitemPojo.getDisannullister() == null) wkMainplanitemPojo.setDisannullister("");
        if (wkMainplanitemPojo.getDisannullisterid() == null) wkMainplanitemPojo.setDisannullisterid("");
        if (wkMainplanitemPojo.getDisannuldate() == null) wkMainplanitemPojo.setDisannuldate(new Date());
        if (wkMainplanitemPojo.getAttributejson() == null) wkMainplanitemPojo.setAttributejson("");
        if (wkMainplanitemPojo.getStartrate() == null) wkMainplanitemPojo.setStartrate(0D);
        if (wkMainplanitemPojo.getFinishrate() == null) wkMainplanitemPojo.setFinishrate(0D);
        if (wkMainplanitemPojo.getSourcetype() == null) wkMainplanitemPojo.setSourcetype(0);
        if (wkMainplanitemPojo.getWkwpid() == null) wkMainplanitemPojo.setWkwpid("");
        if (wkMainplanitemPojo.getWkwpcode() == null) wkMainplanitemPojo.setWkwpcode("");
     if(wkMainplanitemPojo.getWkwpname()==null) wkMainplanitemPojo.setWkwpname("");
     if(wkMainplanitemPojo.getMergemark()==null) wkMainplanitemPojo.setMergemark(0);
     if(wkMainplanitemPojo.getMergeitems()==null) wkMainplanitemPojo.setMergeitems("");
        if (wkMainplanitemPojo.getCustom1() == null) wkMainplanitemPojo.setCustom1("");
        if (wkMainplanitemPojo.getCustom2() == null) wkMainplanitemPojo.setCustom2("");
        if (wkMainplanitemPojo.getCustom3() == null) wkMainplanitemPojo.setCustom3("");
        if (wkMainplanitemPojo.getCustom4() == null) wkMainplanitemPojo.setCustom4("");
        if (wkMainplanitemPojo.getCustom5() == null) wkMainplanitemPojo.setCustom5("");
        if (wkMainplanitemPojo.getCustom6() == null) wkMainplanitemPojo.setCustom6("");
        if (wkMainplanitemPojo.getCustom7() == null) wkMainplanitemPojo.setCustom7("");
        if (wkMainplanitemPojo.getCustom8() == null) wkMainplanitemPojo.setCustom8("");
        if (wkMainplanitemPojo.getCustom9() == null) wkMainplanitemPojo.setCustom9("");
        if (wkMainplanitemPojo.getCustom10() == null) wkMainplanitemPojo.setCustom10("");
        if (wkMainplanitemPojo.getTenantid() == null) wkMainplanitemPojo.setTenantid("");
        if (wkMainplanitemPojo.getRevision() == null) wkMainplanitemPojo.setRevision(0);
        return wkMainplanitemPojo;
    }
}
