package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkScmachtypeEntity;
import inks.service.std.manu.domain.pojo.WkScmachtypePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 委外加工类型(WkScmachtype)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-13 15:12:07
 */
@Mapper
public interface WkScmachtypeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkScmachtypePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkScmachtypePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param wkScmachtypeEntity 实例对象
     * @return 影响行数
     */
    int insert(WkScmachtypeEntity wkScmachtypeEntity);

    
    /**
     * 修改数据
     *
     * @param wkScmachtypeEntity 实例对象
     * @return 影响行数
     */
    int update(WkScmachtypeEntity wkScmachtypeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                        }

