package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSteppriceitemEntity;
import inks.service.std.manu.domain.pojo.WkSteppriceitemPojo;
import inks.service.std.manu.mapper.WkSteppriceitemMapper;
import inks.service.std.manu.service.WkSteppriceitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 阶梯项目(WkSteppriceitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-26 16:21:33
 */
@Service("wkSteppriceitemService")
public class WkSteppriceitemServiceImpl implements WkSteppriceitemService {
    @Resource
    private WkSteppriceitemMapper wkSteppriceitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSteppriceitemPojo getEntity(String key, String tid) {
        return this.wkSteppriceitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSteppriceitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSteppriceitemPojo> lst = wkSteppriceitemMapper.getPageList(queryParam);
            PageInfo<WkSteppriceitemPojo> pageInfo = new PageInfo<WkSteppriceitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkSteppriceitemPojo> getList(String Pid, String tid) {
        try {
            List<WkSteppriceitemPojo> lst = wkSteppriceitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkSteppriceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSteppriceitemPojo insert(WkSteppriceitemPojo wkSteppriceitemPojo) {
        //初始化item的NULL
        WkSteppriceitemPojo itempojo = this.clearNull(wkSteppriceitemPojo);
        WkSteppriceitemEntity wkSteppriceitemEntity = new WkSteppriceitemEntity();
        BeanUtils.copyProperties(itempojo, wkSteppriceitemEntity);
        //生成雪花id
        wkSteppriceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkSteppriceitemEntity.setRevision(1);  //乐观锁
        this.wkSteppriceitemMapper.insert(wkSteppriceitemEntity);
        return this.getEntity(wkSteppriceitemEntity.getId(), wkSteppriceitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSteppriceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSteppriceitemPojo update(WkSteppriceitemPojo wkSteppriceitemPojo) {
        WkSteppriceitemEntity wkSteppriceitemEntity = new WkSteppriceitemEntity();
        BeanUtils.copyProperties(wkSteppriceitemPojo, wkSteppriceitemEntity);
        this.wkSteppriceitemMapper.update(wkSteppriceitemEntity);
        return this.getEntity(wkSteppriceitemEntity.getId(), wkSteppriceitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkSteppriceitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkSteppriceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSteppriceitemPojo clearNull(WkSteppriceitemPojo wkSteppriceitemPojo) {
        //初始化NULL字段
        if (wkSteppriceitemPojo.getPid() == null) wkSteppriceitemPojo.setPid("");
        if (wkSteppriceitemPojo.getStartqty() == null) wkSteppriceitemPojo.setStartqty(0);
        if (wkSteppriceitemPojo.getEndqty() == null) wkSteppriceitemPojo.setEndqty(0);
        if (wkSteppriceitemPojo.getPrice() == null) wkSteppriceitemPojo.setPrice(0D);
        if (wkSteppriceitemPojo.getWorkhour() == null) wkSteppriceitemPojo.setWorkhour(0D);
        if (wkSteppriceitemPojo.getRownum() == null) wkSteppriceitemPojo.setRownum(0);
        if (wkSteppriceitemPojo.getRemark() == null) wkSteppriceitemPojo.setRemark("");
        if (wkSteppriceitemPojo.getCustom1() == null) wkSteppriceitemPojo.setCustom1("");
        if (wkSteppriceitemPojo.getCustom2() == null) wkSteppriceitemPojo.setCustom2("");
        if (wkSteppriceitemPojo.getCustom3() == null) wkSteppriceitemPojo.setCustom3("");
        if (wkSteppriceitemPojo.getCustom4() == null) wkSteppriceitemPojo.setCustom4("");
        if (wkSteppriceitemPojo.getCustom5() == null) wkSteppriceitemPojo.setCustom5("");
        if (wkSteppriceitemPojo.getCustom6() == null) wkSteppriceitemPojo.setCustom6("");
        if (wkSteppriceitemPojo.getCustom7() == null) wkSteppriceitemPojo.setCustom7("");
        if (wkSteppriceitemPojo.getCustom8() == null) wkSteppriceitemPojo.setCustom8("");
        if (wkSteppriceitemPojo.getCustom9() == null) wkSteppriceitemPojo.setCustom9("");
        if (wkSteppriceitemPojo.getCustom10() == null) wkSteppriceitemPojo.setCustom10("");
        if (wkSteppriceitemPojo.getTenantid() == null) wkSteppriceitemPojo.setTenantid("");
        if (wkSteppriceitemPojo.getRevision() == null) wkSteppriceitemPojo.setRevision(0);
        return wkSteppriceitemPojo;
    }
}
