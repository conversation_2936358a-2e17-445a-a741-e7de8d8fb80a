package inks.service.std.manu.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkProccyclePojo;
import com.github.pagehelper.PageInfo;

/**
 * 工序周期(Wk_ProcCycle)表服务接口
 *
 * <AUTHOR>
 * @since 2024-11-06 16:32:32
 */
public interface WkProccycleService {

    WkProccyclePojo getEntity(String key,String tid);

    PageInfo<WkProccyclePojo> getPageList(QueryParam queryParam);

    WkProccyclePojo insert(WkProccyclePojo wkProccyclePojo);

    WkProccyclePojo update(WkProccyclePojo wkProccyclepojo);

    int delete(String key,String tid);

    WkProccyclePojo getEntityByWpidAndType(String wpid, int type, String tenantid);
}
