package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkWiptasksPojo;
import inks.service.std.manu.domain.pojo.WkWiptasksitemPojo;
import inks.service.std.manu.domain.pojo.WkWiptasksitemdetailPojo;
import inks.service.std.manu.service.WkWiptasksService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 派工单(Wk_WipTasks)表控制层
 *
 * <AUTHOR>
 * @since 2025-01-06 14:33:54
 */
@RestController
@RequestMapping("D05M08B1")
@Api(tags = "D05M08B1:派工单")
public class D05M08B1Controller extends WkWiptasksController {
    @Resource
    private WkWiptasksService wkWiptasksService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipTasks.List")
    public R<PageInfo<WkWiptasksitemdetailPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipTasks.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWiptasksService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "getOnlinePageList基础上，接口加入AttributeJson字段展开，itemplandate日期格式化；根据MachItemid查订单明细wkwpname字段，", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageListByMach", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipTasks.List")
    public R<PageInfo<Map<String, Object>>> getOnlinePageListByMach(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipTasks.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Wk_WipTasksItem.FinishQty<Wk_WipTasksItem.Quantity";
            qpfilter += " and Wk_WipTasksItem.Closed=0 ";  // 未关闭、未注销
            if (groupid != null) {
                qpfilter += " and Wk_WipTasks.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWiptasksService.getPageListByMach(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "getOnlinePageList基础上，接口加入AttributeJson字段展开，itemplandate日期格式化；根据MachItemid查订单明细wkwpname字段，", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageListByWip", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipTasks.List")
    public R<PageInfo<Map<String, Object>>> getOnlinePageListByWip(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipTasks.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWiptasksService.getOnlinePageListByWip(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "作废派工单", notes = "作废派工单,?type=1作废，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipTasks.Edit")
    public R<WkWiptasksPojo> disannul(@RequestBody String json, Integer type) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (type == null) type = 1;
            List<WkWiptasksitemPojo> lst = JSONArray.parseArray(json, WkWiptasksitemPojo.class);
            return R.ok(this.wkWiptasksService.disannul(lst, type, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "中止派工单", notes = "中止派工单,?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipTasks.Edit")
    public R<WkWiptasksPojo> closed(@RequestBody String json, Integer type) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (type == null) type = 1;
            List<WkWiptasksitemPojo> lst = JSONArray.parseArray(json, WkWiptasksitemPojo.class);
            return R.ok(this.wkWiptasksService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
