package inks.service.std.manu.domain.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * MRP项目(WkMrpitem)Pojo
 *
 * <AUTHOR>
 * @since 2024-01-17 09:11:54
 */
public class WkMrpitemAndPatentVO extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = -43928327041107379L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 子表内部关系
  @Excel(name = "子表内部关系")    
  private String itemparentid;
     // 对象id
  @Excel(name = "对象id")    
  private String mrpobjid;
     // 层级数
  @Excel(name = "层级数")    
  private Integer levelnum;
     // 层级符号
  @Excel(name = "层级符号")    
  private String levelsymbol;
     // 商品ID
  @Excel(name = "商品ID")    
  private String goodsid;
     // 产品编码
  @Excel(name = "产品编码")    
  private String itemcode;
     // 产品名称
  @Excel(name = "产品名称")    
  private String itemname;
     // 产品规格
  @Excel(name = "产品规格")    
  private String itemspec;
     // 产品单位
  @Excel(name = "产品单位")    
  private String itemunit;
     // BOMid
  @Excel(name = "BOMid")    
  private String bomid;
     // 标准Bom/订单Bom
  @Excel(name = "标准Bom/订单Bom")    
  private String bomtype;
     // BOM项目id
  @Excel(name = "BOM项目id")    
  private String bomitemid;
     // 子件数量
  @Excel(name = "子件数量")    
  private Double subqty;
     // 主件数量
  @Excel(name = "主件数量")    
  private Double mainqty;
     // 损耗率
  @Excel(name = "损耗率")    
  private Double lossrate;
     // 属性 厂制/委制/外购/客供
  @Excel(name = "属性 厂制/委制/外购/客供")    
  private String attrcode;
     // 流程编码
  @Excel(name = "流程编码")    
  private String flowcode;
     // Bom应需
  @Excel(name = "Bom应需")    
  private Double bomqty;
     // 库存数量
  @Excel(name = "库存数量")    
  private Double stoqty;
     // 安全库存
  @Excel(name = "安全库存")    
  private Double safestock;
     // MRP应需
  @Excel(name = "MRP应需")    
  private Double needqty;
     // 实需数量
  @Excel(name = "实需数量")    
  private Double realqty;
     // 开工日期
  @Excel(name = "开工日期")    
  private Date workdate;
     // 计划完工
  @Excel(name = "计划完工")    
  private Date plandate;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 有效标识
  @Excel(name = "有效标识")    
  private Integer enabledmark;
     // 关闭
  @Excel(name = "关闭")    
  private Integer closed;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 已转请购单
  @Excel(name = "已转请购单")    
  private Double buyplanqty;
     // 已转采购订单
  @Excel(name = "已转采购订单")    
  private Double buyorderqty;
     // 已转客供单
  @Excel(name = "已转客供单")    
  private Double custsuppqty;
     // 已转厂制
  @Excel(name = "已转厂制")    
  private Double wkwsqty;
     // 已转委制
  @Excel(name = "已转委制")    
  private Double wkscqty;
     // 已转其他
  @Excel(name = "已转其他")    
  private Double otherqty;
     // 已申领数
  @Excel(name = "已申领数")    
  private Double matreqqty;
     // 已领用数
  @Excel(name = "已领用数")    
  private Double matcompqty;
     // 当前库存
  @Excel(name = "当前库存")    
  private Double mativqty;
     // 采购待入
  @Excel(name = "采购待入")    
  private Double buyremqty;
     // 生产待入
  @Excel(name = "生产待入")    
  private Double wkwsremqty;
     // 加工待入
  @Excel(name = "加工待入")    
  private Double wkscremqty;
     // 订单待出
  @Excel(name = "订单待出")    
  private Double busremqty;
     // Mrp待用
  @Excel(name = "Mrp待用")    
  private Double mrpremqty;
     // 独立待领
  @Excel(name = "独立待领")    
  private Double freereqremqty;
     // 领料待出
  @Excel(name = "领料待出")    
  private Double reqremqty;
     // 关联厂商id
  @Excel(name = "关联厂商id")    
  private String groupid;
     // 关联厂商
  @Excel(name = "关联厂商")    
  private String groupname;
     // 已申退数
  @Excel(name = "已申退数")    
  private Double matreqrtqty;
     // 已退料数
  @Excel(name = "已退料数")    
  private Double matcomprtqty;
     // 生产完工
  @Excel(name = "生产完工")    
  private Double wkfinishqty;
     // 采购完工
  @Excel(name = "采购完工")    
  private Double buyfinishqty;
     // 客供完工
  @Excel(name = "客供完工")    
  private Double custfinishqty;
     // 销售单号
  @Excel(name = "销售单号")    
  private String machuid;
     // 销售子项id
  @Excel(name = "销售子项id")    
  private String machitemid;
     // 订单批次
  @Excel(name = "订单批次")    
  private String machbatch;
     // 销售客户id
  @Excel(name = "销售客户id")    
  private String machgroupid;
     // 主计划单号
  @Excel(name = "主计划单号")    
  private String mainplanuid;
     // 主计划子项id
  @Excel(name = "主计划子项id")    
  private String mainplanitemid;
     // BOM更新轮次
  @Excel(name = "BOM更新轮次")    
  private Integer bomround;
     // BOM更新时间
  @Excel(name = "BOM更新时间")    
  private Date bomdate;
     // -1移除1新增2更新
  @Excel(name = "-1移除1新增2更新")    
  private Integer bommark;
  @Excel(name = "")
  private String attributejson;
     // 毛需求
  @Excel(name = "毛需求")
  private Double grossqty;
     // 1被合并2合并为
  @Excel(name = "1被合并2合并为")
  private Integer mergemark;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")
  private String custom10;
     // 租户id
  @Excel(name = "租户id")
  private String tenantid;
     // 租户名称
  @Excel(name = "租户名称")
  private String tenantname;
     // 乐观锁
  @Excel(name = "乐观锁")
  private Integer revision;

  //        Bus_Machining.BillType as machtype
    private String machtype;
    //App_Workgroup.GroupName as goodsgroupname,
    private String goodsgroupname;

    //  关联的父级mrpitem的货品信息
    private String pgoodsid;
    private String pgoodsuid;
    private String pgoodsname;
    private String pgoodsspec;
    private String pgoodsunit;
    private Double ppcsx;
    private Double ppcsy;
    private Double psetx;
    private Double psety;
    private Integer pset2pcs;
    private Double ppnlx;
    private Double ppnly;
    private Integer ppnl2pcs;
    private String pgoodsgroupname;

    private Double pneedqty;




    // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public Double getPneedqty() {
        return pneedqty;
    }

    public void setPneedqty(Double pneedqty) {
        this.pneedqty = pneedqty;
    }

    public String getPgoodsgroupname() {
        return pgoodsgroupname;
    }

    public void setPgoodsgroupname(String pgoodsgroupname) {
        this.pgoodsgroupname = pgoodsgroupname;
    }

    public String getGoodsgroupname() {
        return goodsgroupname;
    }

    public void setGoodsgroupname(String goodsgroupname) {
        this.goodsgroupname = goodsgroupname;
    }

    public String getPgoodsid() {
        return pgoodsid;
    }

    public void setPgoodsid(String pgoodsid) {
        this.pgoodsid = pgoodsid;
    }

    public String getPgoodsuid() {
        return pgoodsuid;
    }

    public void setPgoodsuid(String pgoodsuid) {
        this.pgoodsuid = pgoodsuid;
    }

    public String getPgoodsname() {
        return pgoodsname;
    }

    public void setPgoodsname(String pgoodsname) {
        this.pgoodsname = pgoodsname;
    }

    public String getPgoodsspec() {
        return pgoodsspec;
    }

    public void setPgoodsspec(String pgoodsspec) {
        this.pgoodsspec = pgoodsspec;
    }

    public String getPgoodsunit() {
        return pgoodsunit;
    }

    public void setPgoodsunit(String pgoodsunit) {
        this.pgoodsunit = pgoodsunit;
    }

    public Double getPpcsx() {
        return ppcsx;
    }

    public void setPpcsx(Double ppcsx) {
        this.ppcsx = ppcsx;
    }

    public Double getPpcsy() {
        return ppcsy;
    }

    public void setPpcsy(Double ppcsy) {
        this.ppcsy = ppcsy;
    }

    public Double getPsetx() {
        return psetx;
    }

    public void setPsetx(Double psetx) {
        this.psetx = psetx;
    }

    public Double getPsety() {
        return psety;
    }

    public void setPsety(Double psety) {
        this.psety = psety;
    }

    public Integer getPset2pcs() {
        return pset2pcs;
    }

    public void setPset2pcs(Integer pset2pcs) {
        this.pset2pcs = pset2pcs;
    }

    public Double getPpnlx() {
        return ppnlx;
    }

    public void setPpnlx(Double ppnlx) {
        this.ppnlx = ppnlx;
    }

    public Double getPpnly() {
        return ppnly;
    }

    public void setPpnly(Double ppnly) {
        this.ppnly = ppnly;
    }

    public Integer getPpnl2pcs() {
        return ppnl2pcs;
    }

    public void setPpnl2pcs(Integer ppnl2pcs) {
        this.ppnl2pcs = ppnl2pcs;
    }

    public String getMachtype() {
        return machtype;
    }

    public void setMachtype(String machtype) {
        this.machtype = machtype;
    }

    // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 子表内部关系
    public String getItemparentid() {
        return itemparentid;
    }
    
    public void setItemparentid(String itemparentid) {
        this.itemparentid = itemparentid;
    }
        
   // 对象id
    public String getMrpobjid() {
        return mrpobjid;
    }
    
    public void setMrpobjid(String mrpobjid) {
        this.mrpobjid = mrpobjid;
    }
        
   // 层级数
    public Integer getLevelnum() {
        return levelnum;
    }
    
    public void setLevelnum(Integer levelnum) {
        this.levelnum = levelnum;
    }
        
   // 层级符号
    public String getLevelsymbol() {
        return levelsymbol;
    }
    
    public void setLevelsymbol(String levelsymbol) {
        this.levelsymbol = levelsymbol;
    }
        
   // 商品ID
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 产品编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
   // 产品名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
   // 产品规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
   // 产品单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
   // BOMid
    public String getBomid() {
        return bomid;
    }
    
    public void setBomid(String bomid) {
        this.bomid = bomid;
    }
        
   // 标准Bom/订单Bom
    public String getBomtype() {
        return bomtype;
    }
    
    public void setBomtype(String bomtype) {
        this.bomtype = bomtype;
    }
        
   // BOM项目id
    public String getBomitemid() {
        return bomitemid;
    }
    
    public void setBomitemid(String bomitemid) {
        this.bomitemid = bomitemid;
    }
        
   // 子件数量
    public Double getSubqty() {
        return subqty;
    }
    
    public void setSubqty(Double subqty) {
        this.subqty = subqty;
    }
        
   // 主件数量
    public Double getMainqty() {
        return mainqty;
    }
    
    public void setMainqty(Double mainqty) {
        this.mainqty = mainqty;
    }
        
   // 损耗率
    public Double getLossrate() {
        return lossrate;
    }
    
    public void setLossrate(Double lossrate) {
        this.lossrate = lossrate;
    }
        
   // 属性 厂制/委制/外购/客供
    public String getAttrcode() {
        return attrcode;
    }
    
    public void setAttrcode(String attrcode) {
        this.attrcode = attrcode;
    }
        
   // 流程编码
    public String getFlowcode() {
        return flowcode;
    }
    
    public void setFlowcode(String flowcode) {
        this.flowcode = flowcode;
    }
        
   // Bom应需
    public Double getBomqty() {
        return bomqty;
    }
    
    public void setBomqty(Double bomqty) {
        this.bomqty = bomqty;
    }
        
   // 库存数量
    public Double getStoqty() {
        return stoqty;
    }
    
    public void setStoqty(Double stoqty) {
        this.stoqty = stoqty;
    }
        
   // 安全库存
    public Double getSafestock() {
        return safestock;
    }
    
    public void setSafestock(Double safestock) {
        this.safestock = safestock;
    }
        
   // MRP应需
    public Double getNeedqty() {
        return needqty;
    }
    
    public void setNeedqty(Double needqty) {
        this.needqty = needqty;
    }
        
   // 实需数量
    public Double getRealqty() {
        return realqty;
    }
    
    public void setRealqty(Double realqty) {
        this.realqty = realqty;
    }
        
   // 开工日期
    public Date getWorkdate() {
        return workdate;
    }
    
    public void setWorkdate(Date workdate) {
        this.workdate = workdate;
    }
        
   // 计划完工
    public Date getPlandate() {
        return plandate;
    }
    
    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 关闭
    public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 已转请购单
    public Double getBuyplanqty() {
        return buyplanqty;
    }
    
    public void setBuyplanqty(Double buyplanqty) {
        this.buyplanqty = buyplanqty;
    }
        
   // 已转采购订单
    public Double getBuyorderqty() {
        return buyorderqty;
    }
    
    public void setBuyorderqty(Double buyorderqty) {
        this.buyorderqty = buyorderqty;
    }
        
   // 已转客供单
    public Double getCustsuppqty() {
        return custsuppqty;
    }
    
    public void setCustsuppqty(Double custsuppqty) {
        this.custsuppqty = custsuppqty;
    }
        
   // 已转厂制
    public Double getWkwsqty() {
        return wkwsqty;
    }
    
    public void setWkwsqty(Double wkwsqty) {
        this.wkwsqty = wkwsqty;
    }
        
   // 已转委制
    public Double getWkscqty() {
        return wkscqty;
    }
    
    public void setWkscqty(Double wkscqty) {
        this.wkscqty = wkscqty;
    }
        
   // 已转其他
    public Double getOtherqty() {
        return otherqty;
    }
    
    public void setOtherqty(Double otherqty) {
        this.otherqty = otherqty;
    }
        
   // 已申领数
    public Double getMatreqqty() {
        return matreqqty;
    }
    
    public void setMatreqqty(Double matreqqty) {
        this.matreqqty = matreqqty;
    }
        
   // 已领用数
    public Double getMatcompqty() {
        return matcompqty;
    }
    
    public void setMatcompqty(Double matcompqty) {
        this.matcompqty = matcompqty;
    }
        
   // 当前库存
    public Double getMativqty() {
        return mativqty;
    }
    
    public void setMativqty(Double mativqty) {
        this.mativqty = mativqty;
    }
        
   // 采购待入
    public Double getBuyremqty() {
        return buyremqty;
    }
    
    public void setBuyremqty(Double buyremqty) {
        this.buyremqty = buyremqty;
    }
        
   // 生产待入
    public Double getWkwsremqty() {
        return wkwsremqty;
    }
    
    public void setWkwsremqty(Double wkwsremqty) {
        this.wkwsremqty = wkwsremqty;
    }
        
   // 加工待入
    public Double getWkscremqty() {
        return wkscremqty;
    }
    
    public void setWkscremqty(Double wkscremqty) {
        this.wkscremqty = wkscremqty;
    }
        
   // 订单待出
    public Double getBusremqty() {
        return busremqty;
    }
    
    public void setBusremqty(Double busremqty) {
        this.busremqty = busremqty;
    }
        
   // Mrp待用
    public Double getMrpremqty() {
        return mrpremqty;
    }
    
    public void setMrpremqty(Double mrpremqty) {
        this.mrpremqty = mrpremqty;
    }
        
   // 独立待领
    public Double getFreereqremqty() {
        return freereqremqty;
    }
    
    public void setFreereqremqty(Double freereqremqty) {
        this.freereqremqty = freereqremqty;
    }
        
   // 领料待出
    public Double getReqremqty() {
        return reqremqty;
    }
    
    public void setReqremqty(Double reqremqty) {
        this.reqremqty = reqremqty;
    }
        
   // 关联厂商id
    public String getGroupid() {
        return groupid;
    }
    
    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }
        
   // 关联厂商
    public String getGroupname() {
        return groupname;
    }
    
    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }
        
   // 已申退数
    public Double getMatreqrtqty() {
        return matreqrtqty;
    }
    
    public void setMatreqrtqty(Double matreqrtqty) {
        this.matreqrtqty = matreqrtqty;
    }
        
   // 已退料数
    public Double getMatcomprtqty() {
        return matcomprtqty;
    }
    
    public void setMatcomprtqty(Double matcomprtqty) {
        this.matcomprtqty = matcomprtqty;
    }
        
   // 生产完工
    public Double getWkfinishqty() {
        return wkfinishqty;
    }
    
    public void setWkfinishqty(Double wkfinishqty) {
        this.wkfinishqty = wkfinishqty;
    }
        
   // 采购完工
    public Double getBuyfinishqty() {
        return buyfinishqty;
    }
    
    public void setBuyfinishqty(Double buyfinishqty) {
        this.buyfinishqty = buyfinishqty;
    }
        
   // 客供完工
    public Double getCustfinishqty() {
        return custfinishqty;
    }
    
    public void setCustfinishqty(Double custfinishqty) {
        this.custfinishqty = custfinishqty;
    }
        
   // 销售单号
    public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
   // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
   // 订单批次
    public String getMachbatch() {
        return machbatch;
    }
    
    public void setMachbatch(String machbatch) {
        this.machbatch = machbatch;
    }
        
   // 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }
    
    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }
        
   // 主计划单号
    public String getMainplanuid() {
        return mainplanuid;
    }
    
    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }
        
   // 主计划子项id
    public String getMainplanitemid() {
        return mainplanitemid;
    }
    
    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }
        
   // BOM更新轮次
    public Integer getBomround() {
        return bomround;
    }
    
    public void setBomround(Integer bomround) {
        this.bomround = bomround;
    }
        
   // BOM更新时间
    public Date getBomdate() {
        return bomdate;
    }
    
    public void setBomdate(Date bomdate) {
        this.bomdate = bomdate;
    }
        
   // -1移除1新增2更新
    public Integer getBommark() {
        return bommark;
    }
    
    public void setBommark(Integer bommark) {
        this.bommark = bommark;
    }
        
    public String getAttributejson() {
        return attributejson;
    }

    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }

   // 毛需求
    public Double getGrossqty() {
        return grossqty;
    }

    public void setGrossqty(Double grossqty) {
        this.grossqty = grossqty;
    }

   // 1被合并2合并为
    public Integer getMergemark() {
        return mergemark;
    }

    public void setMergemark(Integer mergemark) {
        this.mergemark = mergemark;
    }

   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

