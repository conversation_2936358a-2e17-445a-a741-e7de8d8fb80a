package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkMpcarryoverEntity;
import inks.service.std.manu.domain.pojo.WkMpcarryoverPojo;
import inks.service.std.manu.domain.pojo.WkMpcarryoveritemPojo;
import inks.service.std.manu.domain.pojo.WkMpcarryoveritemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产结转(WkMpcarryover)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-09 09:42:57
 */
@Mapper
public interface WkMpcarryoverMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMpcarryoverPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMpcarryoveritemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMpcarryoverPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkMpcarryoverEntity 实例对象
     * @return 影响行数
     */
    int insert(WkMpcarryoverEntity wkMpcarryoverEntity);


    /**
     * 修改数据
     *
     * @param wkMpcarryoverEntity 实例对象
     * @return 影响行数
     */
    int update(WkMpcarryoverEntity wkMpcarryoverEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkMpcarryoverPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkMpcarryoverPojo wkMpcarryoverPojo);

    /**
     * 根据
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<WkMpcarryoveritemPojo> getGoodsMpList(QueryParam queryParam);

    /**
     * 根据
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<WkMpcarryoveritemPojo> getGoodsAcceList(QueryParam queryParam);
}

