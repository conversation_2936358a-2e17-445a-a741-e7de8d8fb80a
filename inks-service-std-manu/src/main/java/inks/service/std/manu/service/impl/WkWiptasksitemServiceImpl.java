package inks.service.std.manu.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.manu.domain.pojo.WkWiptasksitemPojo;
import inks.service.std.manu.domain.WkWiptasksitemEntity;
import inks.service.std.manu.mapper.WkWiptasksitemMapper;
import inks.service.std.manu.service.WkWiptasksitemService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 派工单子表(WkWiptasksitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-06 14:34:15
 */
@Service("wkWiptasksitemService")
public class WkWiptasksitemServiceImpl implements WkWiptasksitemService {
    @Resource
    private WkWiptasksitemMapper wkWiptasksitemMapper;

    @Override
    public WkWiptasksitemPojo getEntity(String key,String tid) {
        return this.wkWiptasksitemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<WkWiptasksitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWiptasksitemPojo> lst = wkWiptasksitemMapper.getPageList(queryParam);
            PageInfo<WkWiptasksitemPojo> pageInfo = new PageInfo<WkWiptasksitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<WkWiptasksitemPojo> getList(String Pid,String tid) { 
        try {
            List<WkWiptasksitemPojo> lst = wkWiptasksitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public WkWiptasksitemPojo insert(WkWiptasksitemPojo wkWiptasksitemPojo) {
        //初始化item的NULL
        WkWiptasksitemPojo itempojo =this.clearNull(wkWiptasksitemPojo);
        WkWiptasksitemEntity wkWiptasksitemEntity = new WkWiptasksitemEntity(); 
        BeanUtils.copyProperties(itempojo,wkWiptasksitemEntity);
          //生成雪花id
          wkWiptasksitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          wkWiptasksitemEntity.setRevision(1);  //乐观锁      
          this.wkWiptasksitemMapper.insert(wkWiptasksitemEntity);
        return this.getEntity(wkWiptasksitemEntity.getId(),wkWiptasksitemEntity.getTenantid());
  
    }

    @Override
    public WkWiptasksitemPojo update(WkWiptasksitemPojo wkWiptasksitemPojo) {
        WkWiptasksitemEntity wkWiptasksitemEntity = new WkWiptasksitemEntity(); 
        BeanUtils.copyProperties(wkWiptasksitemPojo,wkWiptasksitemEntity);
        this.wkWiptasksitemMapper.update(wkWiptasksitemEntity);
        return this.getEntity(wkWiptasksitemEntity.getId(),wkWiptasksitemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.wkWiptasksitemMapper.delete(key,tid) ;
    }

     @Override
     public WkWiptasksitemPojo clearNull(WkWiptasksitemPojo wkWiptasksitemPojo){
     //初始化NULL字段
     if(wkWiptasksitemPojo.getPid()==null) wkWiptasksitemPojo.setPid("");
     if(wkWiptasksitemPojo.getWipitemid()==null) wkWiptasksitemPojo.setWipitemid("");
     if(wkWiptasksitemPojo.getGoodsid()==null) wkWiptasksitemPojo.setGoodsid("");
     if(wkWiptasksitemPojo.getItemcode()==null) wkWiptasksitemPojo.setItemcode("");
     if(wkWiptasksitemPojo.getItemname()==null) wkWiptasksitemPojo.setItemname("");
     if(wkWiptasksitemPojo.getItemspec()==null) wkWiptasksitemPojo.setItemspec("");
     if(wkWiptasksitemPojo.getItemunit()==null) wkWiptasksitemPojo.setItemunit("");
     if(wkWiptasksitemPojo.getItemlabel()==null) wkWiptasksitemPojo.setItemlabel("");
     if(wkWiptasksitemPojo.getMoldid()==null) wkWiptasksitemPojo.setMoldid("");
     if(wkWiptasksitemPojo.getMoldcode()==null) wkWiptasksitemPojo.setMoldcode("");
     if(wkWiptasksitemPojo.getMoldname()==null) wkWiptasksitemPojo.setMoldname("");
     if(wkWiptasksitemPojo.getWorktype()==null) wkWiptasksitemPojo.setWorktype("");
     if(wkWiptasksitemPojo.getWorkshopid()==null) wkWiptasksitemPojo.setWorkshopid("");
     if(wkWiptasksitemPojo.getWorkshop()==null) wkWiptasksitemPojo.setWorkshop("");
     if(wkWiptasksitemPojo.getQuantity()==null) wkWiptasksitemPojo.setQuantity(0D);
     if(wkWiptasksitemPojo.getWkpcsqty()==null) wkWiptasksitemPojo.setWkpcsqty(0D);
     if(wkWiptasksitemPojo.getWksecqty()==null) wkWiptasksitemPojo.setWksecqty(0D);
     if(wkWiptasksitemPojo.getMrbpcsqty()==null) wkWiptasksitemPojo.setMrbpcsqty(0D);
     if(wkWiptasksitemPojo.getMrbsecqty()==null) wkWiptasksitemPojo.setMrbsecqty(0D);
     if(wkWiptasksitemPojo.getFinishqty()==null) wkWiptasksitemPojo.setFinishqty(0D);
     if(wkWiptasksitemPojo.getCustomer()==null) wkWiptasksitemPojo.setCustomer("");
     if(wkWiptasksitemPojo.getCustpo()==null) wkWiptasksitemPojo.setCustpo("");
     if(wkWiptasksitemPojo.getMachuid()==null) wkWiptasksitemPojo.setMachuid("");
     if(wkWiptasksitemPojo.getMachitemid()==null) wkWiptasksitemPojo.setMachitemid("");
     if(wkWiptasksitemPojo.getMachgroupid()==null) wkWiptasksitemPojo.setMachgroupid("");
     if(wkWiptasksitemPojo.getMainplanuid()==null) wkWiptasksitemPojo.setMainplanuid("");
     if(wkWiptasksitemPojo.getMainplanitemid()==null) wkWiptasksitemPojo.setMainplanitemid("");
     if(wkWiptasksitemPojo.getWorkuid()==null) wkWiptasksitemPojo.setWorkuid("");
     if(wkWiptasksitemPojo.getWorkitemid()==null) wkWiptasksitemPojo.setWorkitemid("");
     if(wkWiptasksitemPojo.getWorkdate()==null) wkWiptasksitemPojo.setWorkdate(new Date());
     if(wkWiptasksitemPojo.getRemark()==null) wkWiptasksitemPojo.setRemark("");
     if(wkWiptasksitemPojo.getAttributejson()==null) wkWiptasksitemPojo.setAttributejson("");
     if(wkWiptasksitemPojo.getAttributestr()==null) wkWiptasksitemPojo.setAttributestr("");
     if(wkWiptasksitemPojo.getClosed()==null) wkWiptasksitemPojo.setClosed(0);
     if(wkWiptasksitemPojo.getSourcetype()==null) wkWiptasksitemPojo.setSourcetype(0);
     if(wkWiptasksitemPojo.getRownum()==null) wkWiptasksitemPojo.setRownum(0);
     if(wkWiptasksitemPojo.getWeight()==null) wkWiptasksitemPojo.setWeight(0);
     if(wkWiptasksitemPojo.getItemplandate()==null) wkWiptasksitemPojo.setItemplandate(new Date());
     if(wkWiptasksitemPojo.getDisannulmark()==null) wkWiptasksitemPojo.setDisannulmark(0);
     if(wkWiptasksitemPojo.getDisannullister()==null) wkWiptasksitemPojo.setDisannullister("");
     if(wkWiptasksitemPojo.getDisannuldate()==null) wkWiptasksitemPojo.setDisannuldate(new Date());
     if(wkWiptasksitemPojo.getCustom1()==null) wkWiptasksitemPojo.setCustom1("");
     if(wkWiptasksitemPojo.getCustom2()==null) wkWiptasksitemPojo.setCustom2("");
     if(wkWiptasksitemPojo.getCustom3()==null) wkWiptasksitemPojo.setCustom3("");
     if(wkWiptasksitemPojo.getCustom4()==null) wkWiptasksitemPojo.setCustom4("");
     if(wkWiptasksitemPojo.getCustom5()==null) wkWiptasksitemPojo.setCustom5("");
     if(wkWiptasksitemPojo.getCustom6()==null) wkWiptasksitemPojo.setCustom6("");
     if(wkWiptasksitemPojo.getCustom7()==null) wkWiptasksitemPojo.setCustom7("");
     if(wkWiptasksitemPojo.getCustom8()==null) wkWiptasksitemPojo.setCustom8("");
     if(wkWiptasksitemPojo.getCustom9()==null) wkWiptasksitemPojo.setCustom9("");
     if(wkWiptasksitemPojo.getCustom10()==null) wkWiptasksitemPojo.setCustom10("");
     if(wkWiptasksitemPojo.getTenantid()==null) wkWiptasksitemPojo.setTenantid("");
     if(wkWiptasksitemPojo.getRevision()==null) wkWiptasksitemPojo.setRevision(0);
     return wkWiptasksitemPojo;
     }
}
