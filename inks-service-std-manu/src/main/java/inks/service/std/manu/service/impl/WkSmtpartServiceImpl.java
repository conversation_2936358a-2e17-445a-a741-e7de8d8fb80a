package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSmtpartEntity;
import inks.service.std.manu.domain.WkSmtpartitemEntity;
import inks.service.std.manu.domain.pojo.WkSmtpartPojo;
import inks.service.std.manu.domain.pojo.WkSmtpartitemPojo;
import inks.service.std.manu.domain.pojo.WkSmtpartitemdetailPojo;
import inks.service.std.manu.mapper.WkSmtpartMapper;
import inks.service.std.manu.mapper.WkSmtpartitemMapper;
import inks.service.std.manu.service.WkSmtpartService;
import inks.service.std.manu.service.WkSmtpartitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * SMT上料表(WkSmtpart)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-12 13:59:10
 */
@Service("wkSmtpartService")
public class WkSmtpartServiceImpl implements WkSmtpartService {
    @Resource
    private WkSmtpartMapper wkSmtpartMapper;

    @Resource
    private WkSmtpartitemMapper wkSmtpartitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkSmtpartitemService wkSmtpartitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSmtpartPojo getEntity(String key, String tid) {
        return this.wkSmtpartMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSmtpartitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSmtpartitemdetailPojo> lst = wkSmtpartMapper.getPageList(queryParam);
            PageInfo<WkSmtpartitemdetailPojo> pageInfo = new PageInfo<WkSmtpartitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSmtpartPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkSmtpartPojo wkSmtpartPojo = this.wkSmtpartMapper.getEntity(key, tid);
            //读取子表
            wkSmtpartPojo.setItem(wkSmtpartitemMapper.getList(wkSmtpartPojo.getId(), wkSmtpartPojo.getTenantid()));
            return wkSmtpartPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSmtpartPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSmtpartPojo> lst = wkSmtpartMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkSmtpartitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkSmtpartPojo> pageInfo = new PageInfo<WkSmtpartPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSmtpartPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSmtpartPojo> lst = wkSmtpartMapper.getPageTh(queryParam);
            PageInfo<WkSmtpartPojo> pageInfo = new PageInfo<WkSmtpartPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkSmtpartPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSmtpartPojo insert(WkSmtpartPojo wkSmtpartPojo) {
//初始化NULL字段
        if (wkSmtpartPojo.getRefno() == null) wkSmtpartPojo.setRefno("");
        if (wkSmtpartPojo.getBilltype() == null) wkSmtpartPojo.setBilltype("");
        if (wkSmtpartPojo.getBilltitle() == null) wkSmtpartPojo.setBilltitle("");
        if (wkSmtpartPojo.getBilldate() == null) wkSmtpartPojo.setBilldate(new Date());
        if (wkSmtpartPojo.getWorkuid() == null) wkSmtpartPojo.setWorkuid("");
        if (wkSmtpartPojo.getWorkitemid() == null) wkSmtpartPojo.setWorkitemid("");
        if (wkSmtpartPojo.getMachuid() == null) wkSmtpartPojo.setMachuid("");
        if (wkSmtpartPojo.getMachitemid() == null) wkSmtpartPojo.setMachitemid("");
        if (wkSmtpartPojo.getMachgroupid() == null) wkSmtpartPojo.setMachgroupid("");
        if (wkSmtpartPojo.getOperator() == null) wkSmtpartPojo.setOperator("");
        if (wkSmtpartPojo.getSummary() == null) wkSmtpartPojo.setSummary("");
        if (wkSmtpartPojo.getCreateby() == null) wkSmtpartPojo.setCreateby("");
        if (wkSmtpartPojo.getCreatebyid() == null) wkSmtpartPojo.setCreatebyid("");
        if (wkSmtpartPojo.getCreatedate() == null) wkSmtpartPojo.setCreatedate(new Date());
        if (wkSmtpartPojo.getLister() == null) wkSmtpartPojo.setLister("");
        if (wkSmtpartPojo.getListerid() == null) wkSmtpartPojo.setListerid("");
        if (wkSmtpartPojo.getModifydate() == null) wkSmtpartPojo.setModifydate(new Date());
        if (wkSmtpartPojo.getAssessor() == null) wkSmtpartPojo.setAssessor("");
        if (wkSmtpartPojo.getAssessorid() == null) wkSmtpartPojo.setAssessorid("");
        if (wkSmtpartPojo.getAssessdate() == null) wkSmtpartPojo.setAssessdate(new Date());
        if (wkSmtpartPojo.getCustom1() == null) wkSmtpartPojo.setCustom1("");
        if (wkSmtpartPojo.getCustom2() == null) wkSmtpartPojo.setCustom2("");
        if (wkSmtpartPojo.getCustom3() == null) wkSmtpartPojo.setCustom3("");
        if (wkSmtpartPojo.getCustom4() == null) wkSmtpartPojo.setCustom4("");
        if (wkSmtpartPojo.getCustom5() == null) wkSmtpartPojo.setCustom5("");
        if (wkSmtpartPojo.getCustom6() == null) wkSmtpartPojo.setCustom6("");
        if (wkSmtpartPojo.getCustom7() == null) wkSmtpartPojo.setCustom7("");
        if (wkSmtpartPojo.getCustom8() == null) wkSmtpartPojo.setCustom8("");
        if (wkSmtpartPojo.getCustom9() == null) wkSmtpartPojo.setCustom9("");
        if (wkSmtpartPojo.getCustom10() == null) wkSmtpartPojo.setCustom10("");
        if (wkSmtpartPojo.getTenantid() == null) wkSmtpartPojo.setTenantid("");
        if (wkSmtpartPojo.getRevision() == null) wkSmtpartPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkSmtpartEntity wkSmtpartEntity = new WkSmtpartEntity();
        BeanUtils.copyProperties(wkSmtpartPojo, wkSmtpartEntity);
        //设置id和新建日期
        wkSmtpartEntity.setId(id);
        wkSmtpartEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkSmtpartMapper.insert(wkSmtpartEntity);
        //Item子表处理
        List<WkSmtpartitemPojo> lst = wkSmtpartPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkSmtpartitemPojo itemPojo = this.wkSmtpartitemService.clearNull(lst.get(i));
                WkSmtpartitemEntity wkSmtpartitemEntity = new WkSmtpartitemEntity();
                BeanUtils.copyProperties(itemPojo, wkSmtpartitemEntity);
                //设置id和Pid
                wkSmtpartitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkSmtpartitemEntity.setPid(id);
                wkSmtpartitemEntity.setTenantid(wkSmtpartPojo.getTenantid());
                wkSmtpartitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkSmtpartitemMapper.insert(wkSmtpartitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkSmtpartEntity.getId(), wkSmtpartEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSmtpartPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSmtpartPojo update(WkSmtpartPojo wkSmtpartPojo) {
        //主表更改
        WkSmtpartEntity wkSmtpartEntity = new WkSmtpartEntity();
        BeanUtils.copyProperties(wkSmtpartPojo, wkSmtpartEntity);
        this.wkSmtpartMapper.update(wkSmtpartEntity);
        //Item子表处理
        List<WkSmtpartitemPojo> lst = wkSmtpartPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = wkSmtpartMapper.getDelItemIds(wkSmtpartPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.wkSmtpartitemMapper.delete(lstDelIds.get(i), wkSmtpartEntity.getTenantid());
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                WkSmtpartitemEntity wkSmtpartitemEntity = new WkSmtpartitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    WkSmtpartitemPojo itemPojo = this.wkSmtpartitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, wkSmtpartitemEntity);
                    //设置id和Pid
                    wkSmtpartitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    wkSmtpartitemEntity.setPid(wkSmtpartEntity.getId());  // 主表 id
                    wkSmtpartitemEntity.setTenantid(wkSmtpartPojo.getTenantid());   // 租户id
                    wkSmtpartitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.wkSmtpartitemMapper.insert(wkSmtpartitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), wkSmtpartitemEntity);
                    wkSmtpartitemEntity.setTenantid(wkSmtpartPojo.getTenantid());
                    this.wkSmtpartitemMapper.update(wkSmtpartitemEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkSmtpartEntity.getId(), wkSmtpartEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkSmtpartPojo wkSmtpartPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkSmtpartitemPojo> lst = wkSmtpartPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkSmtpartitemPojo wkSmtpartitemPojo : lst) {
                this.wkSmtpartitemMapper.delete(wkSmtpartitemPojo.getId(), tid);
            }
        }
        this.wkSmtpartMapper.delete(key, tid);
        return wkSmtpartPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkSmtpartPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSmtpartPojo approval(WkSmtpartPojo wkSmtpartPojo) {
        //主表更改
        WkSmtpartEntity wkSmtpartEntity = new WkSmtpartEntity();
        BeanUtils.copyProperties(wkSmtpartPojo, wkSmtpartEntity);
        this.wkSmtpartMapper.approval(wkSmtpartEntity);
        //返回Bill实例
        return this.getBillEntity(wkSmtpartEntity.getId(), wkSmtpartEntity.getTenantid());
    }

}
