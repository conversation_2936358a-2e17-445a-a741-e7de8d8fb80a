package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkStepprogroupEntity;
import inks.service.std.manu.domain.WkStepprogroupitemEntity;
import inks.service.std.manu.domain.pojo.WkStepprogroupPojo;
import inks.service.std.manu.domain.pojo.WkStepprogroupitemPojo;
import inks.service.std.manu.domain.pojo.WkStepprogroupitemdetailPojo;
import inks.service.std.manu.mapper.WkStepprogroupMapper;
import inks.service.std.manu.mapper.WkStepprogroupitemMapper;
import inks.service.std.manu.service.WkStepprogroupService;
import inks.service.std.manu.service.WkStepprogroupitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static inks.service.std.manu.service.impl.WkSteppriceServiceImpl.spuJsonToSpuValue;

/**
 * 阶梯制程(WkStepprogroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-01 16:33:33
 */
@Service("wkStepprogroupService")
public class WkStepprogroupServiceImpl implements WkStepprogroupService {
    @Resource
    private WkStepprogroupMapper wkStepprogroupMapper;

    @Resource
    private WkStepprogroupitemMapper wkStepprogroupitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkStepprogroupitemService wkStepprogroupitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkStepprogroupPojo getEntity(String key, String tid) {
        return this.wkStepprogroupMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkStepprogroupitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkStepprogroupitemdetailPojo> lst = wkStepprogroupMapper.getPageList(queryParam);
            PageInfo<WkStepprogroupitemdetailPojo> pageInfo = new PageInfo<WkStepprogroupitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkStepprogroupPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkStepprogroupPojo wkStepprogroupPojo = this.wkStepprogroupMapper.getEntity(key, tid);
            //读取子表
            wkStepprogroupPojo.setItem(wkStepprogroupitemMapper.getList(wkStepprogroupPojo.getId(), wkStepprogroupPojo.getTenantid()));
            return wkStepprogroupPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkStepprogroupPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkStepprogroupPojo> lst = wkStepprogroupMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkStepprogroupitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkStepprogroupPojo> pageInfo = new PageInfo<WkStepprogroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkStepprogroupPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkStepprogroupPojo> lst = wkStepprogroupMapper.getPageTh(queryParam);
            PageInfo<WkStepprogroupPojo> pageInfo = new PageInfo<WkStepprogroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkStepprogroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkStepprogroupPojo insert(WkStepprogroupPojo wkStepprogroupPojo) {
//初始化NULL字段
        if (wkStepprogroupPojo.getRefno() == null) wkStepprogroupPojo.setRefno("");
        if (wkStepprogroupPojo.getBilltype() == null) wkStepprogroupPojo.setBilltype("");
        if (wkStepprogroupPojo.getBilltitle() == null) wkStepprogroupPojo.setBilltitle("");
        if (wkStepprogroupPojo.getBilldate() == null) wkStepprogroupPojo.setBilldate(new Date());
        if (wkStepprogroupPojo.getGoodsid() == null) wkStepprogroupPojo.setGoodsid("");
        if (wkStepprogroupPojo.getSpus() == null) wkStepprogroupPojo.setSpus("");
        if (wkStepprogroupPojo.getSpujson() == null) wkStepprogroupPojo.setSpujson("");
        if (wkStepprogroupPojo.getSpuvalue() == null) wkStepprogroupPojo.setSpuvalue("");
        if (wkStepprogroupPojo.getCreateby() == null) wkStepprogroupPojo.setCreateby("");
        if (wkStepprogroupPojo.getCreatebyid() == null) wkStepprogroupPojo.setCreatebyid("");
        if (wkStepprogroupPojo.getCreatedate() == null) wkStepprogroupPojo.setCreatedate(new Date());
        if (wkStepprogroupPojo.getLister() == null) wkStepprogroupPojo.setLister("");
        if (wkStepprogroupPojo.getListerid() == null) wkStepprogroupPojo.setListerid("");
        if (wkStepprogroupPojo.getModifydate() == null) wkStepprogroupPojo.setModifydate(new Date());
        if (wkStepprogroupPojo.getAssessor() == null) wkStepprogroupPojo.setAssessor("");
        if (wkStepprogroupPojo.getAssessorid() == null) wkStepprogroupPojo.setAssessorid("");
        if (wkStepprogroupPojo.getAssessdate() == null) wkStepprogroupPojo.setAssessdate(new Date());
        if (wkStepprogroupPojo.getSummary() == null) wkStepprogroupPojo.setSummary("");
        if (wkStepprogroupPojo.getCustom1() == null) wkStepprogroupPojo.setCustom1("");
        if (wkStepprogroupPojo.getCustom2() == null) wkStepprogroupPojo.setCustom2("");
        if (wkStepprogroupPojo.getCustom3() == null) wkStepprogroupPojo.setCustom3("");
        if (wkStepprogroupPojo.getCustom4() == null) wkStepprogroupPojo.setCustom4("");
        if (wkStepprogroupPojo.getCustom5() == null) wkStepprogroupPojo.setCustom5("");
        if (wkStepprogroupPojo.getCustom6() == null) wkStepprogroupPojo.setCustom6("");
        if (wkStepprogroupPojo.getCustom7() == null) wkStepprogroupPojo.setCustom7("");
        if (wkStepprogroupPojo.getCustom8() == null) wkStepprogroupPojo.setCustom8("");
        if (wkStepprogroupPojo.getCustom9() == null) wkStepprogroupPojo.setCustom9("");
        if (wkStepprogroupPojo.getCustom10() == null) wkStepprogroupPojo.setCustom10("");
        if (wkStepprogroupPojo.getTenantid() == null) wkStepprogroupPojo.setTenantid("");
        if (wkStepprogroupPojo.getTenantname() == null) wkStepprogroupPojo.setTenantname("");
        if (wkStepprogroupPojo.getRevision() == null) wkStepprogroupPojo.setRevision(0);
        //  spuJson转为SpuValue 且按照key的升序排列
        String spuValue = spuJsonToSpuValue(wkStepprogroupPojo.getSpujson());
        //检查goodsid+spuvalue , 2码唯一
        int count = this.wkStepprogroupitemMapper.checkGoodsidSpuvalue(wkStepprogroupPojo.getGoodsid(), spuValue, wkStepprogroupPojo.getTenantid());
        if (count > 0) throw new RuntimeException("该货品下SpuJson值重复");
        wkStepprogroupPojo.setSpuvalue(spuValue);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkStepprogroupEntity wkStepprogroupEntity = new WkStepprogroupEntity();
        BeanUtils.copyProperties(wkStepprogroupPojo, wkStepprogroupEntity);

        //设置id和新建日期
        wkStepprogroupEntity.setId(id);
        wkStepprogroupEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkStepprogroupMapper.insert(wkStepprogroupEntity);
        //Item子表处理
        List<WkStepprogroupitemPojo> lst = wkStepprogroupPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkStepprogroupitemPojo itemPojo = this.wkStepprogroupitemService.clearNull(lst.get(i));
                WkStepprogroupitemEntity wkStepprogroupitemEntity = new WkStepprogroupitemEntity();
                BeanUtils.copyProperties(itemPojo, wkStepprogroupitemEntity);
                //设置id和Pid
                wkStepprogroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkStepprogroupitemEntity.setPid(id);
                wkStepprogroupitemEntity.setTenantid(wkStepprogroupPojo.getTenantid());
                wkStepprogroupitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkStepprogroupitemMapper.insert(wkStepprogroupitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkStepprogroupEntity.getId(), wkStepprogroupEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkStepprogroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkStepprogroupPojo update(WkStepprogroupPojo wkStepprogroupPojo) {
        String tid = wkStepprogroupPojo.getTenantid();
        //  spuJson转为SpuValue 且按照key的升序排列
        String spuValue = spuJsonToSpuValue(wkStepprogroupPojo.getSpujson());
        //当传入spuJson和数据库的spuJson不同时：检查goodsid+spuvalue , 2码唯一
        String spujsonDB = this.wkStepprogroupMapper.getEntity(wkStepprogroupPojo.getId(), tid).getSpujson();
        if (!spujsonDB.equals(wkStepprogroupPojo.getSpujson())) {
            int count = this.wkStepprogroupitemMapper.checkGoodsidSpuvalue(wkStepprogroupPojo.getGoodsid(), spuValue, tid);
            if (count > 0) throw new RuntimeException("该货品下SpuJson值重复");
        }
        wkStepprogroupPojo.setSpuvalue(spuValue);
        //主表更改
        WkStepprogroupEntity wkStepprogroupEntity = new WkStepprogroupEntity();
        BeanUtils.copyProperties(wkStepprogroupPojo, wkStepprogroupEntity);
        this.wkStepprogroupMapper.update(wkStepprogroupEntity);
        if (wkStepprogroupPojo.getItem() != null) {
            //Item子表处理
            List<WkStepprogroupitemPojo> lst = wkStepprogroupPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkStepprogroupMapper.getDelItemIds(wkStepprogroupPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkStepprogroupitemMapper.delete(lstDelIds.get(i), wkStepprogroupEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    WkStepprogroupitemEntity wkStepprogroupitemEntity = new WkStepprogroupitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        WkStepprogroupitemPojo itemPojo = this.wkStepprogroupitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, wkStepprogroupitemEntity);
                        //设置id和Pid
                        wkStepprogroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkStepprogroupitemEntity.setPid(wkStepprogroupEntity.getId());  // 主表 id
                        wkStepprogroupitemEntity.setTenantid(tid);   // 租户id
                        wkStepprogroupitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkStepprogroupitemMapper.insert(wkStepprogroupitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkStepprogroupitemEntity);
                        wkStepprogroupitemEntity.setTenantid(tid);
                        this.wkStepprogroupitemMapper.update(wkStepprogroupitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkStepprogroupEntity.getId(), wkStepprogroupEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkStepprogroupPojo wkStepprogroupPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkStepprogroupitemPojo> lst = wkStepprogroupPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkStepprogroupitemPojo wkStepprogroupitemPojo : lst) {
                this.wkStepprogroupitemMapper.delete(wkStepprogroupitemPojo.getId(), tid);
            }
        }
        this.wkStepprogroupMapper.delete(key, tid);
        return wkStepprogroupPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkStepprogroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkStepprogroupPojo approval(WkStepprogroupPojo wkStepprogroupPojo) {
        //主表更改
        WkStepprogroupEntity wkStepprogroupEntity = new WkStepprogroupEntity();
        BeanUtils.copyProperties(wkStepprogroupPojo, wkStepprogroupEntity);
        this.wkStepprogroupMapper.approval(wkStepprogroupEntity);
        //返回Bill实例
        return this.getBillEntity(wkStepprogroupEntity.getId(), wkStepprogroupEntity.getTenantid());
    }

}
