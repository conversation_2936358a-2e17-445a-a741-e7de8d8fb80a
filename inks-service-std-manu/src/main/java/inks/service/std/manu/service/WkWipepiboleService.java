package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipepibolePojo;
import inks.service.std.manu.domain.pojo.WkWipepiboleitemPojo;
import inks.service.std.manu.domain.pojo.WkWipepiboleitemdetailPojo;

import java.util.List;

/**
 * Wip委外(WkWipepibole)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-07 15:09:33
 */
public interface WkWipepiboleService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipepibolePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipepiboleitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipepibolePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipepibolePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipepibolePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipepibolePojo 实例对象
     * @return 实例对象
     */
    WkWipepibolePojo insert(WkWipepibolePojo wkWipepibolePojo);

    /**
     * 修改数据
     *
     * @param wkWipepibolepojo 实例对象
     * @return 实例对象
     */
    WkWipepibolePojo update(WkWipepibolePojo wkWipepibolepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkWipepibolePojo 实例对象
     * @return 实例对象
     */
    WkWipepibolePojo approval(WkWipepibolePojo wkWipepibolePojo);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    WkWipepibolePojo disannul(List<WkWipepiboleitemPojo> lst, Integer type, LoginUser loginUser);

    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    WkWipepibolePojo closed(List<WkWipepiboleitemPojo> lst, Integer type, LoginUser loginUser);

    void updatePrintcount(WkWipepibolePojo billPrintPojo);
}
