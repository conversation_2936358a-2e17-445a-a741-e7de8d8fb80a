package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkStationPojo;
import inks.service.std.manu.domain.pojo.WkStationitemdetailPojo;

import java.util.HashMap;
import java.util.List;

/**
 * 工位(WkStation)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-22 12:36:16
 */
public interface WkStationService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStationPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkStationitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStationPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkStationPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkStationPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkStationPojo 实例对象
     * @return 实例对象
     */
    WkStationPojo insert(WkStationPojo wkStationPojo);

    /**
     * 修改数据
     *
     * @param wkStationpojo 实例对象
     * @return 实例对象
     */
    WkStationPojo update(WkStationPojo wkStationpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    List<WkStationPojo> getAllList(String tenantid);

    HashMap<String,Object> getStationStateAndWk(String key, String tenantid);

    String getStateJsonByWpid(String key, String tenantid);}
