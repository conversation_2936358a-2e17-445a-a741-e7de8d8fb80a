package inks.service.std.manu.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSectionPojo;
import inks.service.std.manu.domain.pojo.WkSectionitemdetailPojo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 生产工段(WkSection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-28 11:21:20
 */
public interface WkSectionService {

    WkSectionPojo getEntity(String key,String tid);

    PageInfo<WkSectionitemdetailPojo> getPageList(QueryParam queryParam);

    WkSectionPojo getBillEntity(String key,String tid);

    PageInfo<WkSectionPojo> getBillList(QueryParam queryParam);

    PageInfo<WkSectionPojo> getPageTh(QueryParam queryParam);

    WkSectionPojo insert(WkSectionPojo wkSectionPojo);

    WkSectionPojo update(WkSectionPojo wkSectionpojo);

    int delete(String key,String tid);

    List<WkSectionPojo> getSectionsAndStations(String key,String tid);
}
