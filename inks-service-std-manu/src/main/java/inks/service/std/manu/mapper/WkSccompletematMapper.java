package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSccompletematEntity;
import inks.service.std.manu.domain.pojo.WkSccompletematPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 验收物料(WkSccompletemat)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-09 12:50:37
 */
 @Mapper
public interface WkSccompletematMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccompletematPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSccompletematPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSccompletematPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkSccompletematEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSccompletematEntity wkSccompletematEntity);

    
    /**
     * 修改数据
     *
     * @param wkSccompletematEntity 实例对象
     * @return 影响行数
     */
    int update(WkSccompletematEntity wkSccompletematEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

