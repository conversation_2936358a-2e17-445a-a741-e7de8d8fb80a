package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkCompleteEntity;
import inks.service.std.manu.domain.WkCompleteitemEntity;
import inks.service.std.manu.domain.WkCompletematEntity;
import inks.service.std.manu.domain.pojo.WkCompletePojo;
import inks.service.std.manu.domain.pojo.WkCompleteitemPojo;
import inks.service.std.manu.domain.pojo.WkCompleteitemdetailPojo;
import inks.service.std.manu.domain.pojo.WkCompletematPojo;
import inks.service.std.manu.mapper.*;
import inks.service.std.manu.service.WkCompleteService;
import inks.service.std.manu.service.WkCompleteitemService;
import inks.service.std.manu.service.WkCompletematService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 完工验收(WkComplete)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-18 15:17:24
 */
@Service("wkCompleteService")
public class WkCompleteServiceImpl implements WkCompleteService {
    @Resource
    private WkCompleteMapper wkCompleteMapper;

    @Resource
    private WkCompleteitemMapper wkCompleteitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkCompleteitemService wkCompleteitemService;

    /**
     * 服务对象Mat
     */
    @Resource
    private WkCompletematService wkCompletematService;

    @Resource
    private WkCompletematMapper wkCompletematMapper;

    @Resource
    private WkMrpMapper wkMrpMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkCompletePojo getEntity(String key, String tid) {
        return this.wkCompleteMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCompleteitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCompleteitemdetailPojo> lst = wkCompleteMapper.getPageList(queryParam);
            PageInfo<WkCompleteitemdetailPojo> pageInfo = new PageInfo<WkCompleteitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkCompletePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkCompletePojo wkCompletePojo = this.wkCompleteMapper.getEntity(key, tid);
            //读取子表
            wkCompletePojo.setItem(wkCompleteitemMapper.getList(wkCompletePojo.getId(), wkCompletePojo.getTenantid()));
            //读取Mat子表
            wkCompletePojo.setMat(wkCompletematMapper.getList(wkCompletePojo.getId(), wkCompletePojo.getTenantid()));
            return wkCompletePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCompletePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCompletePojo> lst = wkCompleteMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (WkCompletePojo wkCompletePojo : lst) {
                wkCompletePojo.setItem(wkCompleteitemMapper.getList(wkCompletePojo.getId(), wkCompletePojo.getTenantid()));
            }
            //循环设置每个主表对象的item子表
            for (WkCompletePojo wkCompletePojo : lst) {
                wkCompletePojo.setMat(wkCompletematMapper.getList(wkCompletePojo.getId(), wkCompletePojo.getTenantid()));
            }
            PageInfo<WkCompletePojo> pageInfo = new PageInfo<WkCompletePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCompletePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCompletePojo> lst = wkCompleteMapper.getPageTh(queryParam);
            PageInfo<WkCompletePojo> pageInfo = new PageInfo<WkCompletePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkCompletePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkCompletePojo insert(WkCompletePojo wkCompletePojo) {
//初始化NULL字段
        if (wkCompletePojo.getRefno() == null) wkCompletePojo.setRefno("");
        if (wkCompletePojo.getBilltype() == null) wkCompletePojo.setBilltype("");
        if (wkCompletePojo.getBilldate() == null) wkCompletePojo.setBilldate(new Date());
        if (wkCompletePojo.getBilltitle() == null) wkCompletePojo.setBilltitle("");
        if (wkCompletePojo.getOperator() == null) wkCompletePojo.setOperator("");
        if (wkCompletePojo.getGroupid() == null) wkCompletePojo.setGroupid("");
        if (wkCompletePojo.getSummary() == null) wkCompletePojo.setSummary("");
        if (wkCompletePojo.getCreateby() == null) wkCompletePojo.setCreateby("");
        if (wkCompletePojo.getCreatebyid() == null) wkCompletePojo.setCreatebyid("");
        if (wkCompletePojo.getCreatedate() == null) wkCompletePojo.setCreatedate(new Date());
        if (wkCompletePojo.getLister() == null) wkCompletePojo.setLister("");
        if (wkCompletePojo.getListerid() == null) wkCompletePojo.setListerid("");
        if (wkCompletePojo.getModifydate() == null) wkCompletePojo.setModifydate(new Date());
        if (wkCompletePojo.getAssessor() == null) wkCompletePojo.setAssessor("");
        if (wkCompletePojo.getAssessorid() == null) wkCompletePojo.setAssessorid("");
        if (wkCompletePojo.getAssessdate() == null) wkCompletePojo.setAssessdate(new Date());
        if (wkCompletePojo.getBillamount() == null) wkCompletePojo.setBillamount(0D);
        if (wkCompletePojo.getBilltaxamount() == null) wkCompletePojo.setBilltaxamount(0D);
        if (wkCompletePojo.getCustom1() == null) wkCompletePojo.setCustom1("");
        if (wkCompletePojo.getCustom2() == null) wkCompletePojo.setCustom2("");
        if (wkCompletePojo.getCustom3() == null) wkCompletePojo.setCustom3("");
        if (wkCompletePojo.getCustom4() == null) wkCompletePojo.setCustom4("");
        if (wkCompletePojo.getCustom5() == null) wkCompletePojo.setCustom5("");
        if (wkCompletePojo.getCustom6() == null) wkCompletePojo.setCustom6("");
        if (wkCompletePojo.getCustom7() == null) wkCompletePojo.setCustom7("");
        if (wkCompletePojo.getCustom8() == null) wkCompletePojo.setCustom8("");
        if (wkCompletePojo.getCustom9() == null) wkCompletePojo.setCustom9("");
        if (wkCompletePojo.getCustom10() == null) wkCompletePojo.setCustom10("");
        if (wkCompletePojo.getTenantid() == null) wkCompletePojo.setTenantid("");
        if (wkCompletePojo.getRevision() == null) wkCompletePojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkCompleteEntity wkCompleteEntity = new WkCompleteEntity();
        BeanUtils.copyProperties(wkCompletePojo, wkCompleteEntity);
        //设置id和新建日期
        wkCompleteEntity.setId(id);
        wkCompleteEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkCompleteMapper.insert(wkCompleteEntity);
        //Item子表处理
        List<WkCompleteitemPojo> lst = wkCompletePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (WkCompleteitemPojo wkCompleteitemPojo : lst) {
                //初始化item的NULL
                WkCompleteitemPojo itemPojo = this.wkCompleteitemService.clearNull(wkCompleteitemPojo);
                WkCompleteitemEntity wkCompleteitemEntity = new WkCompleteitemEntity();
                BeanUtils.copyProperties(itemPojo, wkCompleteitemEntity);
                //设置id和Pid
                wkCompleteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkCompleteitemEntity.setPid(id);
                wkCompleteitemEntity.setTenantid(wkCompletePojo.getTenantid());
                wkCompleteitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkCompleteitemMapper.insert(wkCompleteitemEntity);
                // 同步Wk_MrpItem.WkFinishQty
                String mrpitemid = wkCompleteitemEntity.getMrpitemid();
                if (StringUtils.isNotBlank(mrpitemid)) {
                    this.wkMrpMapper.syncMrpItemWkFinishQty(mrpitemid, wkCompletePojo.getTenantid());
                }
            }
        }

        //Mat子表处理
        List<WkCompletematPojo> lstmat = wkCompletePojo.getMat();
        if (lstmat != null) {
            //循环每个item子表
            for (WkCompletematPojo wkCompletematPojo : lstmat) {
                //初始化item的NULL
                WkCompletematPojo matPojo = this.wkCompletematService.clearNull(wkCompletematPojo);
                WkCompletematEntity wkCompletematEntity = new WkCompletematEntity();
                BeanUtils.copyProperties(matPojo, wkCompletematEntity);
                //设置id和Pid
                wkCompletematEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkCompletematEntity.setPid(id);
                wkCompletematEntity.setTenantid(wkCompletePojo.getTenantid());
                wkCompletematEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkCompletematMapper.insert(wkCompletematEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkCompleteEntity.getId(), wkCompleteEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkCompletePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkCompletePojo update(WkCompletePojo wkCompletePojo) {
        //主表更改
        WkCompleteEntity wkCompleteEntity = new WkCompleteEntity();
        BeanUtils.copyProperties(wkCompletePojo, wkCompleteEntity);
        this.wkCompleteMapper.update(wkCompleteEntity);
        if (wkCompletePojo.getItem() != null) {
            //Item子表处理
            List<WkCompleteitemPojo> lst = wkCompletePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkCompleteMapper.getDelItemIds(wkCompletePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    WkCompleteitemPojo delPojo = wkCompleteitemMapper.getEntity(lstDelId, wkCompleteEntity.getTenantid());
                    this.wkCompleteitemMapper.delete(lstDelId, wkCompleteEntity.getTenantid());
                    // 同步Wk_MrpItem.WkFinishQty
                    String mrpitemid = delPojo.getMrpitemid();
                    if (StringUtils.isNotBlank(mrpitemid)) {
                        this.wkMrpMapper.syncMrpItemWkFinishQty(mrpitemid, wkCompletePojo.getTenantid());
                    }
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (WkCompleteitemPojo wkCompleteitemPojo : lst) {
                    WkCompleteitemEntity wkCompleteitemEntity = new WkCompleteitemEntity();
                    if ("".equals(wkCompleteitemPojo.getId()) || wkCompleteitemPojo.getId() == null) {
                        //初始化item的NULL
                        WkCompleteitemPojo itemPojo = this.wkCompleteitemService.clearNull(wkCompleteitemPojo);
                        BeanUtils.copyProperties(itemPojo, wkCompleteitemEntity);
                        //设置id和Pid
                        wkCompleteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkCompleteitemEntity.setPid(wkCompleteEntity.getId());  // 主表 id
                        wkCompleteitemEntity.setTenantid(wkCompletePojo.getTenantid());   // 租户id
                        wkCompleteitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkCompleteitemMapper.insert(wkCompleteitemEntity);
                    } else {
                        BeanUtils.copyProperties(wkCompleteitemPojo, wkCompleteitemEntity);
                        wkCompleteitemEntity.setTenantid(wkCompletePojo.getTenantid());
                        this.wkCompleteitemMapper.update(wkCompleteitemEntity);
                    }
                    // 同步Wk_MrpItem.WkFinishQty
                    String mrpitemid = wkCompleteitemEntity.getMrpitemid();
                    if (StringUtils.isNotBlank(mrpitemid)) {
                        this.wkMrpMapper.syncMrpItemWkFinishQty(mrpitemid, wkCompletePojo.getTenantid());
                    }
                }
            }
        }

        if (wkCompletePojo.getMat() != null) {
            //Mat子表处理
            List<WkCompletematPojo> lstmat = wkCompletePojo.getMat();
            //获取被删除的Mat
            List<String> lstmatDelIds = wkCompleteMapper.getDelMatIds(wkCompletePojo);
            if (lstmatDelIds != null) {
                //循环每个删除item子表
                for (String lstmatDelId : lstmatDelIds) {
                    this.wkCompletematMapper.delete(lstmatDelId, wkCompleteEntity.getTenantid());
                }
            }
            if (lstmat != null) {
                //循环每个item子表
                for (WkCompletematPojo wkCompletematPojo : lstmat) {
                    WkCompletematEntity wkCompletematEntity = new WkCompletematEntity();
                    if (wkCompletematPojo.getId() == "" || wkCompletematPojo.getId() == null) {
                        //初始化item的NULL
                        WkCompletematPojo matPojo = this.wkCompletematService.clearNull(wkCompletematPojo);
                        BeanUtils.copyProperties(matPojo, wkCompletematEntity);
                        //设置id和Pid
                        wkCompletematEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkCompletematEntity.setPid(wkCompleteEntity.getId());  // 主表 id
                        wkCompletematEntity.setTenantid(wkCompletePojo.getTenantid());   // 租户id
                        wkCompletematEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkCompletematMapper.insert(wkCompletematEntity);
                    } else {
                        BeanUtils.copyProperties(wkCompletematPojo, wkCompletematEntity);
                        wkCompletematEntity.setTenantid(wkCompletePojo.getTenantid());
                        this.wkCompletematMapper.update(wkCompletematEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkCompleteEntity.getId(), wkCompleteEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkCompletePojo wkCompletePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkCompleteitemPojo> lst = wkCompletePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkCompleteitemPojo wkCompleteitemPojo : lst) {
                this.wkCompleteitemMapper.delete(wkCompleteitemPojo.getId(), tid);
                // 同步Wk_MrpItem.WkFinishQty
                String mrpitemid = wkCompleteitemPojo.getMrpitemid();
                if (StringUtils.isNotBlank(mrpitemid)) {
                    this.wkMrpMapper.syncMrpItemWkFinishQty(mrpitemid, wkCompletePojo.getTenantid());
                }
            }
        }
        this.wkCompleteMapper.delete(key, tid);
        return wkCompletePojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkCompletePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkCompletePojo approval(WkCompletePojo wkCompletePojo) {
        //主表更改
        WkCompleteEntity wkCompleteEntity = new WkCompleteEntity();
        BeanUtils.copyProperties(wkCompletePojo, wkCompleteEntity);
        this.wkCompleteMapper.approval(wkCompleteEntity);
        //返回Bill实例
        return this.getBillEntity(wkCompleteEntity.getId(), wkCompleteEntity.getTenantid());
    }

}
