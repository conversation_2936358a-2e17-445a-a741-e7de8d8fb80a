package inks.service.std.manu.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipfinishitemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 完工单子表(WkWipfinishitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-06 14:34:14
 */
public interface WkWipfinishitemService {

    WkWipfinishitemPojo getEntity(String key,String tid);

    PageInfo<WkWipfinishitemPojo> getPageList(QueryParam queryParam);

    List<WkWipfinishitemPojo> getList(String Pid,String tid);  

    WkWipfinishitemPojo insert(WkWipfinishitemPojo wkWipfinishitemPojo);

    WkWipfinishitemPojo update(WkWipfinishitemPojo wkWipfinishitempojo);

    int delete(String key,String tid);

    WkWipfinishitemPojo clearNull(WkWipfinishitemPojo wkWipfinishitempojo);
}
