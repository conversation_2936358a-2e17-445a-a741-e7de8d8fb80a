package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkMrpitemPojo;

import java.util.List;
import java.util.Set;

/**
 * MRP项目(WkMrpitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-22 18:55:28
 */
public interface WkMrpitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMrpitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMrpitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMrpitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkMrpitemPojo 实例对象
     * @return 实例对象
     */
    WkMrpitemPojo insert(WkMrpitemPojo wkMrpitemPojo);

    /**
     * 修改数据
     *
     * @param wkMrpitempojo 实例对象
     * @return 实例对象
     */
    WkMrpitemPojo update(WkMrpitemPojo wkMrpitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkMrpitempojo 实例对象
     * @return 实例对象
     */
    WkMrpitemPojo clearNull(WkMrpitemPojo wkMrpitempojo);

    String updateItemAttrCode(List<String> itemids, String attrcode, String tenantid);

    List<WkMrpitemPojo> getMrpItemListByMachItemids(List<String> machitems, String tenantid);

    List<WkMrpitemPojo> getListInObjids(Set<String> objids, String tid);
}
