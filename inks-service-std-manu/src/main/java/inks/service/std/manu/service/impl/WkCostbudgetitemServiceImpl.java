package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkCostbudgetitemEntity;
import inks.service.std.manu.domain.pojo.WkCostbudgetitemPojo;
import inks.service.std.manu.mapper.WkCostbudgetitemMapper;
import inks.service.std.manu.service.WkCostbudgetitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 成本预测Item(WkCostbudgetitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-03 19:16:38
 */
@Service("wkCostbudgetitemService")
public class WkCostbudgetitemServiceImpl implements WkCostbudgetitemService {
    @Resource
    private WkCostbudgetitemMapper wkCostbudgetitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkCostbudgetitemPojo getEntity(String key, String tid) {
        return this.wkCostbudgetitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCostbudgetitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCostbudgetitemPojo> lst = wkCostbudgetitemMapper.getPageList(queryParam);
            PageInfo<WkCostbudgetitemPojo> pageInfo = new PageInfo<WkCostbudgetitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkCostbudgetitemPojo> getList(String Pid, String tid) {
        try {
            List<WkCostbudgetitemPojo> lst = wkCostbudgetitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkCostbudgetitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCostbudgetitemPojo insert(WkCostbudgetitemPojo wkCostbudgetitemPojo) {
        //初始化item的NULL
        WkCostbudgetitemPojo itempojo = this.clearNull(wkCostbudgetitemPojo);
        WkCostbudgetitemEntity wkCostbudgetitemEntity = new WkCostbudgetitemEntity();
        BeanUtils.copyProperties(itempojo, wkCostbudgetitemEntity);

        wkCostbudgetitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkCostbudgetitemEntity.setRevision(1);  //乐观锁
        this.wkCostbudgetitemMapper.insert(wkCostbudgetitemEntity);
        return this.getEntity(wkCostbudgetitemEntity.getId(), wkCostbudgetitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkCostbudgetitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCostbudgetitemPojo update(WkCostbudgetitemPojo wkCostbudgetitemPojo) {
        WkCostbudgetitemEntity wkCostbudgetitemEntity = new WkCostbudgetitemEntity();
        BeanUtils.copyProperties(wkCostbudgetitemPojo, wkCostbudgetitemEntity);
        this.wkCostbudgetitemMapper.update(wkCostbudgetitemEntity);
        return this.getEntity(wkCostbudgetitemEntity.getId(), wkCostbudgetitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkCostbudgetitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkCostbudgetitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCostbudgetitemPojo clearNull(WkCostbudgetitemPojo wkCostbudgetitemPojo) {
        //初始化NULL字段
        if (wkCostbudgetitemPojo.getPid() == null) wkCostbudgetitemPojo.setPid("");
        if (wkCostbudgetitemPojo.getGoodsid() == null) wkCostbudgetitemPojo.setGoodsid("");
        if (wkCostbudgetitemPojo.getItemcode() == null) wkCostbudgetitemPojo.setItemcode("");
        if (wkCostbudgetitemPojo.getItemname() == null) wkCostbudgetitemPojo.setItemname("");
        if (wkCostbudgetitemPojo.getItemspec() == null) wkCostbudgetitemPojo.setItemspec("");
        if (wkCostbudgetitemPojo.getItemunit() == null) wkCostbudgetitemPojo.setItemunit("");
        if (wkCostbudgetitemPojo.getQuantity() == null) wkCostbudgetitemPojo.setQuantity(0D);
        if (wkCostbudgetitemPojo.getTaxprice() == null) wkCostbudgetitemPojo.setTaxprice(0D);
        if (wkCostbudgetitemPojo.getTaxamount() == null) wkCostbudgetitemPojo.setTaxamount(0D);
        if (wkCostbudgetitemPojo.getPrice() == null) wkCostbudgetitemPojo.setPrice(0D);
        if (wkCostbudgetitemPojo.getAmount() == null) wkCostbudgetitemPojo.setAmount(0D);
        if (wkCostbudgetitemPojo.getTaxtotal() == null) wkCostbudgetitemPojo.setTaxtotal(0D);
        if (wkCostbudgetitemPojo.getItemtaxrate() == null) wkCostbudgetitemPojo.setItemtaxrate(0);
        if (wkCostbudgetitemPojo.getStartdate() == null) wkCostbudgetitemPojo.setStartdate(new Date());
        if (wkCostbudgetitemPojo.getPlandate() == null) wkCostbudgetitemPojo.setPlandate(new Date());
        if (wkCostbudgetitemPojo.getEnabledmark() == null) wkCostbudgetitemPojo.setEnabledmark(0);
        if (wkCostbudgetitemPojo.getClosed() == null) wkCostbudgetitemPojo.setClosed(0);
        if (wkCostbudgetitemPojo.getRemark() == null) wkCostbudgetitemPojo.setRemark("");
        if (wkCostbudgetitemPojo.getStatecode() == null) wkCostbudgetitemPojo.setStatecode("");
        if (wkCostbudgetitemPojo.getStatedate() == null) wkCostbudgetitemPojo.setStatedate(new Date());
        if (wkCostbudgetitemPojo.getRownum() == null) wkCostbudgetitemPojo.setRownum(0);
        if (wkCostbudgetitemPojo.getMachuid() == null) wkCostbudgetitemPojo.setMachuid("");
        if (wkCostbudgetitemPojo.getMachitemid() == null) wkCostbudgetitemPojo.setMachitemid("");
        if (wkCostbudgetitemPojo.getCustomer() == null) wkCostbudgetitemPojo.setCustomer("");
        if (wkCostbudgetitemPojo.getCustpo() == null) wkCostbudgetitemPojo.setCustpo("");
        if (wkCostbudgetitemPojo.getCustom1() == null) wkCostbudgetitemPojo.setCustom1("");
        if (wkCostbudgetitemPojo.getCustom2() == null) wkCostbudgetitemPojo.setCustom2("");
        if (wkCostbudgetitemPojo.getCustom3() == null) wkCostbudgetitemPojo.setCustom3("");
        if (wkCostbudgetitemPojo.getCustom4() == null) wkCostbudgetitemPojo.setCustom4("");
        if (wkCostbudgetitemPojo.getCustom5() == null) wkCostbudgetitemPojo.setCustom5("");
        if (wkCostbudgetitemPojo.getCustom6() == null) wkCostbudgetitemPojo.setCustom6("");
        if (wkCostbudgetitemPojo.getCustom7() == null) wkCostbudgetitemPojo.setCustom7("");
        if (wkCostbudgetitemPojo.getCustom8() == null) wkCostbudgetitemPojo.setCustom8("");
        if (wkCostbudgetitemPojo.getCustom9() == null) wkCostbudgetitemPojo.setCustom9("");
        if (wkCostbudgetitemPojo.getCustom10() == null) wkCostbudgetitemPojo.setCustom10("");
        if (wkCostbudgetitemPojo.getTenantid() == null) wkCostbudgetitemPojo.setTenantid("");
        if (wkCostbudgetitemPojo.getRevision() == null) wkCostbudgetitemPojo.setRevision(0);
        return wkCostbudgetitemPojo;
    }
}
