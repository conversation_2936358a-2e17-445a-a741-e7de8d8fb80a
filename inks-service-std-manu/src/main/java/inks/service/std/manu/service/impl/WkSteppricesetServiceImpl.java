package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSteppricesetEntity;
import inks.service.std.manu.domain.pojo.WkSteppricesetPojo;
import inks.service.std.manu.mapper.WkSteppricesetMapper;
import inks.service.std.manu.service.WkSteppricesetService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 阶梯工价设置(WkSteppriceset)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-28 09:01:41
 */
@Service("wkSteppricesetService")
public class WkSteppricesetServiceImpl implements WkSteppricesetService {
    @Resource
    private WkSteppricesetMapper wkSteppricesetMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSteppricesetPojo getEntity(String key, String tid) {
        return this.wkSteppricesetMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSteppricesetPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSteppricesetPojo> lst = wkSteppricesetMapper.getPageList(queryParam);
            PageInfo<WkSteppricesetPojo> pageInfo = new PageInfo<WkSteppricesetPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkSteppricesetPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSteppricesetPojo insert(WkSteppricesetPojo wkSteppricesetPojo) {
        //初始化NULL字段
        if (wkSteppricesetPojo.getWpid() == null) wkSteppricesetPojo.setWpid("");
        if (wkSteppricesetPojo.getWpcode() == null) wkSteppricesetPojo.setWpcode("");
        if (wkSteppricesetPojo.getWpname() == null) wkSteppricesetPojo.setWpname("");
        if (wkSteppricesetPojo.getSpus() == null) wkSteppricesetPojo.setSpus("");
        if (wkSteppricesetPojo.getCreateby() == null) wkSteppricesetPojo.setCreateby("");
        if (wkSteppricesetPojo.getCreatebyid() == null) wkSteppricesetPojo.setCreatebyid("");
        if (wkSteppricesetPojo.getCreatedate() == null) wkSteppricesetPojo.setCreatedate(new Date());
        if (wkSteppricesetPojo.getLister() == null) wkSteppricesetPojo.setLister("");
        if (wkSteppricesetPojo.getListerid() == null) wkSteppricesetPojo.setListerid("");
        if (wkSteppricesetPojo.getModifydate() == null) wkSteppricesetPojo.setModifydate(new Date());
        if (wkSteppricesetPojo.getCustom1() == null) wkSteppricesetPojo.setCustom1("");
        if (wkSteppricesetPojo.getCustom2() == null) wkSteppricesetPojo.setCustom2("");
        if (wkSteppricesetPojo.getCustom3() == null) wkSteppricesetPojo.setCustom3("");
        if (wkSteppricesetPojo.getCustom4() == null) wkSteppricesetPojo.setCustom4("");
        if (wkSteppricesetPojo.getCustom5() == null) wkSteppricesetPojo.setCustom5("");
        if (wkSteppricesetPojo.getCustom6() == null) wkSteppricesetPojo.setCustom6("");
        if (wkSteppricesetPojo.getCustom7() == null) wkSteppricesetPojo.setCustom7("");
        if (wkSteppricesetPojo.getCustom8() == null) wkSteppricesetPojo.setCustom8("");
        if (wkSteppricesetPojo.getCustom9() == null) wkSteppricesetPojo.setCustom9("");
        if (wkSteppricesetPojo.getCustom10() == null) wkSteppricesetPojo.setCustom10("");
        if (wkSteppricesetPojo.getTenantid() == null) wkSteppricesetPojo.setTenantid("");
        if (wkSteppricesetPojo.getTenantname() == null) wkSteppricesetPojo.setTenantname("");
        if (wkSteppricesetPojo.getRevision() == null) wkSteppricesetPojo.setRevision(0);
        WkSteppricesetEntity wkSteppricesetEntity = new WkSteppricesetEntity();
        BeanUtils.copyProperties(wkSteppricesetPojo, wkSteppricesetEntity);
        //生成雪花id
        wkSteppricesetEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkSteppricesetEntity.setRevision(1);  //乐观锁
        this.wkSteppricesetMapper.insert(wkSteppricesetEntity);
        return this.getEntity(wkSteppricesetEntity.getId(), wkSteppricesetEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSteppricesetPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSteppricesetPojo update(WkSteppricesetPojo wkSteppricesetPojo) {
        WkSteppricesetEntity wkSteppricesetEntity = new WkSteppricesetEntity();
        BeanUtils.copyProperties(wkSteppricesetPojo, wkSteppricesetEntity);
        this.wkSteppricesetMapper.update(wkSteppricesetEntity);
        return this.getEntity(wkSteppricesetEntity.getId(), wkSteppricesetEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkSteppricesetMapper.delete(key, tid);
    }

    @Override
    public String getSpusByWpid(String wpid, String tid) {
        return this.wkSteppricesetMapper.getSpusByWpid(wpid, tid);
    }

    @Override
    public WkSteppricesetPojo getSpusEntityByWpid(String wpid, String tid) {
        return this.wkSteppricesetMapper.getSpusEntityByWpid(wpid, tid);
    }

    @Override
    public List<WkSteppricesetPojo> getAllList(String tid) {
        return this.wkSteppricesetMapper.getAllList(tid);
    }
}
