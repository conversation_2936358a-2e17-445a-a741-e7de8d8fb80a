package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipfinishPojo;
import inks.service.std.manu.domain.pojo.WkWipfinishitemdetailPojo;
import inks.service.std.manu.domain.WkWipfinishEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 完工表(WkWipfinish)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-06 14:33:54
 */
@Mapper
public interface WkWipfinishMapper {

    WkWipfinishPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<WkWipfinishitemdetailPojo> getPageList(QueryParam queryParam);

    List<WkWipfinishPojo> getPageTh(QueryParam queryParam);

    int insert(WkWipfinishEntity wkWipfinishEntity);

    int update(WkWipfinishEntity wkWipfinishEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(WkWipfinishPojo wkWipfinishPojo);
}

