package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;

import java.util.List;
import java.util.Map;

public interface D05MBIR1Service {


    /**
     * @return R<Map < Object>>
     * @Description 获取今日进度(工序进度)返回工序已完成数量, 总数量
     * <AUTHOR>
     * @time 2023/3/25 12:20
     */
    Map<String, Object> getProcessProgressToday(String tid);

    Map<String, Object> getDeliAndMachQty(String tenantid);

    Map<String, Object> getWipAddAndCompToday(String tenantid, String workshopid);

    List<Map<String, Object>> getWipQtyGroupByGoods(String tenantid, String workshopid, QueryParam queryParam);

    List<Map<String, Object>> getSumWipCompQtyGroupByGoods(QueryParam queryParam, String tenantid);

    PageInfo<Map<String, Object>> getWipOnlineAndCompQtyEveryDay(QueryParam queryParam, Double percent, String tenantid);

    List<Map<String, Object>> getWipSumWorkTime(QueryParam queryParam, String tenantid);

    PageInfo<ChartPojo> getSpuWeightGroupByGroup(QueryParam queryParam);
}
