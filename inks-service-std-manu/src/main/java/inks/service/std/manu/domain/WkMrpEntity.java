package inks.service.std.manu.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * MRP运算(WkMrp)实体类
 *
 * <AUTHOR>
 * @since 2022-04-30 07:47:32
 */
public class WkMrpEntity implements Serializable {
    private static final long serialVersionUID = 687579501568427896L;
      // id
      private String id;
      // 编码
      private String refno;
      // 单据类型
      private String billtype;
      // 单据日期
      private Date billdate;
      // 单据标题
      private String billtitle;
      // 需求日期
      private Date mrpdate;
      // 经办人
      private String operator;
      // 经办人id
      private String operatorid;
      // 摘要
      private String summary;
      // 创建者
      private String createby;
      // 创建者id
      private String createbyid;
      // 新建日期
      private Date createdate;
      // 制表
      private String lister;
      // 制表id
      private String listerid;
      // 修改日期
      private Date modifydate;
      // Obj行数
      private Integer objcount;
      // item行数
      private Integer itemcount;
      // 完成行数
      private Integer finishcount;
      // 打印次数
      private Integer printcount;
      // 厂制item行数
      private Integer wkitemcount;
      // 厂制完成行数
      private Integer wkfinishcount;
      // 委制item行数
      private Integer wsitemcount;
      // 委制完成行数
      private Integer wsfinishcount;
      // 采购item行数
      private Integer buyitemcount;
      // 采购完成行数
      private Integer buyfinishcount;
      // 客供item行数
      private Integer csitemcount;
      // 客供完成行数
      private Integer csfinishcount;
      // 自定义1
      private String custom1;
      // 自定义2
      private String custom2;
      // 自定义3
      private String custom3;
      // 自定义4
      private String custom4;
      // 自定义5
      private String custom5;
      // 自定义6
      private String custom6;
      // 自定义7
      private String custom7;
      // 自定义8
      private String custom8;
      // 自定义9
      private String custom9;
      // 自定义10
      private String custom10;
      // 租户id
      private String tenantid;
      // 租户名称
      private String tenantname;
      // 乐观锁
      private Integer revision;

       // id
         public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
       // 编码
         public String getRefno() {
        return refno;
    }
    
    public void setRefno(String refno) {
        this.refno = refno;
    }
        
       // 单据类型
         public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
       // 单据日期
         public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
       // 单据标题
         public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
       // 需求日期
         public Date getMrpdate() {
        return mrpdate;
    }
    
    public void setMrpdate(Date mrpdate) {
        this.mrpdate = mrpdate;
    }
        
       // 经办人
         public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
        
       // 经办人id
         public String getOperatorid() {
        return operatorid;
    }
    
    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }
        
       // 摘要
         public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
       // 创建者
         public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
       // 创建者id
         public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
       // 新建日期
         public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
       // 制表
         public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
       // 制表id
         public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
       // 修改日期
         public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
       // Obj行数
         public Integer getObjcount() {
        return objcount;
    }
    
    public void setObjcount(Integer objcount) {
        this.objcount = objcount;
    }
        
       // item行数
         public Integer getItemcount() {
        return itemcount;
    }
    
    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }
        
       // 完成行数
         public Integer getFinishcount() {
        return finishcount;
    }
    
    public void setFinishcount(Integer finishcount) {
        this.finishcount = finishcount;
    }
        
       // 打印次数
         public Integer getPrintcount() {
        return printcount;
    }
    
    public void setPrintcount(Integer printcount) {
        this.printcount = printcount;
    }
        
       // 厂制item行数
         public Integer getWkitemcount() {
        return wkitemcount;
    }
    
    public void setWkitemcount(Integer wkitemcount) {
        this.wkitemcount = wkitemcount;
    }
        
       // 厂制完成行数
         public Integer getWkfinishcount() {
        return wkfinishcount;
    }
    
    public void setWkfinishcount(Integer wkfinishcount) {
        this.wkfinishcount = wkfinishcount;
    }
        
       // 委制item行数
         public Integer getWsitemcount() {
        return wsitemcount;
    }
    
    public void setWsitemcount(Integer wsitemcount) {
        this.wsitemcount = wsitemcount;
    }
        
       // 委制完成行数
         public Integer getWsfinishcount() {
        return wsfinishcount;
    }
    
    public void setWsfinishcount(Integer wsfinishcount) {
        this.wsfinishcount = wsfinishcount;
    }
        
       // 采购item行数
         public Integer getBuyitemcount() {
        return buyitemcount;
    }
    
    public void setBuyitemcount(Integer buyitemcount) {
        this.buyitemcount = buyitemcount;
    }
        
       // 采购完成行数
         public Integer getBuyfinishcount() {
        return buyfinishcount;
    }
    
    public void setBuyfinishcount(Integer buyfinishcount) {
        this.buyfinishcount = buyfinishcount;
    }
        
       // 客供item行数
         public Integer getCsitemcount() {
        return csitemcount;
    }
    
    public void setCsitemcount(Integer csitemcount) {
        this.csitemcount = csitemcount;
    }
        
       // 客供完成行数
         public Integer getCsfinishcount() {
        return csfinishcount;
    }
    
    public void setCsfinishcount(Integer csfinishcount) {
        this.csfinishcount = csfinishcount;
    }
        
       // 自定义1
         public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
       // 自定义2
         public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
       // 自定义3
         public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
       // 自定义4
         public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
       // 自定义5
         public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
       // 自定义6
         public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
       // 自定义7
         public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
       // 自定义8
         public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
       // 自定义9
         public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
       // 自定义10
         public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
       // 租户id
         public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
       // 租户名称
         public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
       // 乐观锁
         public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

