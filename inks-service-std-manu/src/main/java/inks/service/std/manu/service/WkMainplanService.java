package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkMainplanPojo;
import inks.service.std.manu.domain.pojo.WkMainplanitemdetailPojo;

import java.util.List;

/**
 * 生产主计划(WkMainplan)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 15:21:38
 */
public interface WkMainplanService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMainplanPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMainplanitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMainplanPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMainplanPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMainplanPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkMainplanPojo 实例对象
     * @return 实例对象
     */
    WkMainplanPojo insert(WkMainplanPojo wkMainplanPojo);

    /**
     * 修改数据
     *
     * @param wkMainplanpojo 实例对象
     * @return 实例对象
     */
    WkMainplanPojo update(WkMainplanPojo wkMainplanpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkMainplanPojo 实例对象
     * @return 实例对象
     */
    WkMainplanPojo approval(WkMainplanPojo wkMainplanPojo);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);

    void updatePrintcount(WkMainplanPojo billPrintPojo);

    void mergeItem(List<String> mainPlanItemIds, LoginUser loginUser);
}
