package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.PreAuthorizeException;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkWipnotePojo;
import inks.service.std.manu.domain.pojo.WkWorksheetPojo;
import inks.service.std.manu.domain.pojo.WkWorksheetitemdetailPojo;
import inks.service.std.manu.mapper.D05MBIR1Mapper;
import inks.service.std.manu.mapper.WkWipqtyMapper;
import inks.service.std.manu.service.D05MBIR1Service;
import inks.service.std.manu.service.WkWipnoteService;
import inks.service.std.manu.service.WkWorksheetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * <AUTHOR>
 * @date 2023年03月24日 13:52
 */
@RestController
@RequestMapping("D05MBIR1")
@Api(tags = "D05MBIR1:生产大屏报表")
public class D05MBIR1Controller {
    @Resource
    private D05MBIR1Service d05MBIR1Service;
    @Resource
    private D05MBIR1Mapper d05MBIR1Mapper;
    @Resource
    private WkWipqtyMapper wkWipqtyMapper;
    @Resource
    private TokenService tokenService;
    @Resource
    private WkWipnoteService wkWipnoteService;
    @Resource
    private WkWorksheetService wkWorksheetService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    // 通过授权码获取LoginUser用户信息
    private LoginUser determineLoginUser(String auth, String dev) {
        if (isBlank(auth)) {
            // 处理之前Token权限流
            return tokenService.getLoginUser(ServletUtils.getRequest());
        } else {
            if (StringUtils.isBlank(dev)) {
                dev = "a";
            }
            String cacheKey = "auth_dev:" + auth + "_" + dev;
            String cachedLoginUser = redisTemplate.opsForValue().get(cacheKey);

            if (cachedLoginUser == null) {
                HashMap<String, String> authMap = d05MBIR1Mapper.getAuthByCode(auth);
                if (authMap == null) {
                    throw new PreAuthorizeException("授权码错误");
                }

                LoginUser loginUserNew = new LoginUser();
                loginUserNew.setUsername(authMap.get("UserName"));
                loginUserNew.setPassword(authMap.get("UserPassword"));
                loginUserNew.setTenantid(authMap.get("Tenantid"));

                redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(loginUserNew), 7200, TimeUnit.SECONDS);
                return loginUserNew;
            } else {
                return JSONArray.parseObject(cachedLoginUser, LoginUser.class);
            }
        }
    }

    /**
     * @return R<Map < Object>>
     * @Description 获取今日进度(工序进度)返回工序已完成数量, 总数量
     * <AUTHOR>
     * @time 2023/3/25 12:20
     */
    @ApiOperation(value = "获取今日进度(工序进度)", notes = "获取今日进度(工序进度)", produces = "application/json")
    @RequestMapping(value = "/getProcessProgressToday", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<Map<String, Object>> getProcessProgressToday(@RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        // auth 获得用户数据
        LoginUser loginUser = determineLoginUser(auth, dev);
        Map<String, Object> processProgressToday = d05MBIR1Service.getProcessProgressToday(loginUser.getTenantid());
        return R.ok(processProgressToday);
    }


    /**
     * 获取明天+后台(全部)WIP计划
     * 通过PlanData查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "获取明天+后台(全部)WIP计划", notes = "", produces = "application/json")
    @RequestMapping(value = "/getPageThTomorrow", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnotePojo>> getPageThTomorrow(@RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        try {
            // 获取当前时间
            Date now = new Date();
            // 计算起始时间（明天的0:00）
            Calendar startCalendar = Calendar.getInstance();
            startCalendar.setTime(now);
            startCalendar.add(Calendar.DAY_OF_MONTH, 1);
            startCalendar.set(Calendar.HOUR_OF_DAY, 0);
            startCalendar.set(Calendar.MINUTE, 0);
            startCalendar.set(Calendar.SECOND, 0);
            // 去除毫秒,避免0:00:00.111-23:59:59.003的情况查不到0:00:00的数据
            startCalendar.set(Calendar.MILLISECOND, 0);
            Date startDate = startCalendar.getTime();

            // 计算结束时间（后天的23:59:59）
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(now);
            endCalendar.add(Calendar.DAY_OF_MONTH, 2);
            endCalendar.set(Calendar.HOUR_OF_DAY, 23);
            endCalendar.set(Calendar.MINUTE, 59);
            endCalendar.set(Calendar.SECOND, 59);
            Date endDate = endCalendar.getTime();

            QueryParam queryParam = new QueryParam();
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            queryParam.setOrderBy("Wk_WipNote.PlanDate");
            queryParam.setDateRange(new DateRange("Wk_WipNote.PlanDate", startDate, endDate));
            // auth 获得用户数据
            LoginUser loginUser = determineLoginUser(auth, dev);
            String qpfilter = "";
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取昨日(结余)+今日(全部)WIP计划
     * 通过PlanData查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "获取昨日(结余)+今日(全部)WIP计划", notes = "", produces = "application/json")
    @RequestMapping(value = "/getPageThToday", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnotePojo>> getPageThToday(@RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        try {
            // auth 获得用户数据
            LoginUser loginUser = determineLoginUser(auth, dev);
            String qpfilter = "";
            QueryParam queryParam = new QueryParam();
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            queryParam.setOrderBy("Wk_WipNote.PlanDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            // 获取当前时间
            Date now = new Date();

            // 计算昨天的时间范围（昨天的0:00到昨天23:59:59）
            Calendar yesterdayStartCalendar = Calendar.getInstance();
            yesterdayStartCalendar.setTime(now);
            yesterdayStartCalendar.add(Calendar.DAY_OF_MONTH, -1);
            yesterdayStartCalendar.set(Calendar.HOUR_OF_DAY, 0);
            yesterdayStartCalendar.set(Calendar.MINUTE, 0);
            yesterdayStartCalendar.set(Calendar.SECOND, 0);
            // 去除毫秒,避免0:00:00.111的情况查不到0:00:00的数据
            yesterdayStartCalendar.set(Calendar.MILLISECOND, 0);
            Date yesterdayStartDate = yesterdayStartCalendar.getTime();

            Calendar yesterdayEndCalendar = Calendar.getInstance();
            yesterdayEndCalendar.setTime(now);
            yesterdayEndCalendar.add(Calendar.DAY_OF_MONTH, -1);
            yesterdayEndCalendar.set(Calendar.HOUR_OF_DAY, 23);
            yesterdayEndCalendar.set(Calendar.MINUTE, 59);
            yesterdayEndCalendar.set(Calendar.SECOND, 59);
            Date yesterdayEndDate = yesterdayEndCalendar.getTime();
            //获取昨天结余的WIP计划
            queryParam.setDateRange(new DateRange("Wk_WipNote.PlanDate", yesterdayStartDate, yesterdayEndDate));
            qpfilter += " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            queryParam.setFilterstr(qpfilter);
            PageInfo<WkWipnotePojo> billListYesterday = this.wkWipnoteService.getBillList(queryParam);

            // 计算今天的时间范围（今天的0:00到今天23:59:59）
            Calendar todayStartCalendar = Calendar.getInstance();
            todayStartCalendar.setTime(now);
            todayStartCalendar.set(Calendar.HOUR_OF_DAY, 0);
            todayStartCalendar.set(Calendar.MINUTE, 0);
            todayStartCalendar.set(Calendar.SECOND, 0);
            // 去除毫秒,避免0:00:00.111的情况查不到0:00:00的数据
            todayStartCalendar.set(Calendar.MILLISECOND, 0);
            Date todayStartDate = todayStartCalendar.getTime();

            Calendar todayEndCalendar = Calendar.getInstance();
            todayEndCalendar.setTime(now);
            todayEndCalendar.set(Calendar.HOUR_OF_DAY, 23);
            todayEndCalendar.set(Calendar.MINUTE, 59);
            todayEndCalendar.set(Calendar.SECOND, 59);
            Date todayEndDate = todayEndCalendar.getTime();
            //获取今天天全部的WIP计划
            qpfilter = "";
            queryParam.setDateRange(new DateRange("Wk_WipNote.PlanDate", todayStartDate, todayEndDate));
            queryParam.setFilterstr(qpfilter);
            PageInfo<WkWipnotePojo> billListToday = this.wkWipnoteService.getBillList(queryParam);
            //合并两个结果
            List<WkWipnotePojo> result = new ArrayList<>();
            result.addAll(billListYesterday.getList());
            result.addAll(billListToday.getList());

            PageInfo<WkWipnotePojo> mergedPage = new PageInfo<>();
            mergedPage.setList(result);
            return R.ok(mergedPage);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 获取明日+后天计划
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "参数：wpid 工序id", produces = "application/json")
    @RequestMapping(value = "/getPageThByPlanData", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnotePojo>> getOnlinePageTh(Date startdate, Date enddate, @RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        try {
            QueryParam queryParam = new QueryParam();
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            queryParam.setOrderBy("Wk_WipNote.PlanDate");
            queryParam.setDateRange(new DateRange("Wk_WipNote.PlanDate", startdate, enddate));
            // auth 获得用户数据
            LoginUser loginUser = determineLoginUser(auth, dev);
            String qpfilter = "";
            //结存的 报废数+完成数<总投数(去掉过滤即查出所有结存/未结存的)
            //String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "获取今日完成的发货单数量和未完成的订单数量", notes = "参数：wpid 工序id", produces = "application/json")
    @RequestMapping(value = "/getDeliAndMachQty", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<Map<String, Object>> getDeliAndMachQty(@RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        try {
            // auth获得用户数据
            LoginUser loginUser = determineLoginUser(auth, dev);
            Map<String, Object> map = d05MBIR1Service.getDeliAndMachQty(loginUser.getTenantid());
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "工序完工汇总", notes = "按条件分页汇总工序完工", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByWp", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<List<Map<String, Object>>> getSumPageListByWp(@RequestBody String json, @RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQty.CreateDate");
            // auth 获得用户数据
            LoginUser loginUser = determineLoginUser(auth, dev);
            if (queryParam.getDateRange() == null) {// 时间范围: 最近30天
                queryParam.setDateRange(new DateRange(null, DateUtils.addMonths(new Date(), -1), new Date()));
            }
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWipqtyMapper.getSumPageListByWp(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //    查询生产WIP，汇总今日新增的生产订单数量汇总，今日完成数量汇总，统计WIP明细最后一个工序的完成数量
    @ApiOperation(value = "查询生产WIP，汇总今日新增的生产订单数量汇总，今日完成数量汇总(统计WIP明细最后一个工序的完成数量,就是主表的CompPcsQty:完工数); 若传入车间id,过滤到车间统计", notes = "按条件分页汇总工序完工", produces = "application/json")
    @RequestMapping(value = "/getWipAddAndCompToday", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<Map<String, Object>> getWipAddAndCompToday(String workshopid, @RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        try {
            // auth 获得用户数据
            LoginUser loginUser = determineLoginUser(auth, dev);
            Map<String, Object> map = d05MBIR1Service.getWipAddAndCompToday(loginUser.getTenantid(), workshopid);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //    查询生产WIP，按照对应的货品编码汇总数量。前五个按照货品编码分类汇总，剩余的按照other统一汇总
    @ApiOperation(value = "查询生产WIP，按照对应的货品编码汇总数量。前五个按照货品编码分类汇总，剩余的按照other统一汇总; 若传入车间id,过滤到车间统计", notes = "按条件分页汇总工序完工", produces = "application/json")
    @RequestMapping(value = "/getWipQtyGroupByGoods", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<List<Map<String, Object>>> getWipQtyGroupByGoods(@RequestBody String json, String workshopid, @RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class); // 传入日期范围
            // auth 获得用户数据
            LoginUser loginUser = determineLoginUser(auth, dev);
            List<Map<String, Object>> map = d05MBIR1Service.getWipQtyGroupByGoods(loginUser.getTenantid(), workshopid, queryParam);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//1. 产品汇总表：按照产品，汇总生产WIP完工记录的订单数量；
//    返回值：货品编码，货品名称，数量
//2. 生产完工工单：统计每日生产WIP记录的完工数量（完工入库数量>=订单item数量）、在线数量（未完工数量）
//            3. 订单工时汇总表：汇总订单item的过数时间（每个item的工序时间，是由item工序时间汇总来的）

    @ApiOperation(value = "查询生产WIP的完工记录:按产品分组统计sum(wip数量)(可传时间范围),返回值：货品编码，货品名称，数量", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSumWipCompQtyGroupByGoods", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<List<Map<String, Object>>> getSumWipCompQtyGroupByGoods(@RequestBody(required = false) String json, @RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class); // 传入日期范围
            // auth 获得用户数据
            LoginUser loginUser = determineLoginUser(auth, dev);
            List<Map<String, Object>> mapList = d05MBIR1Service.getSumWipCompQtyGroupByGoods(queryParam, loginUser.getTenantid());
            return R.ok(mapList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "查询生产WIP: 统计每日生产WIP记录的完工数量、在线数量（未完工数量）", notes = "", produces = "application/json")
    @RequestMapping(value = "/getWipOnlineAndCompQtyEveryDay", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<PageInfo<Map<String, Object>>> getWipOnlineAndCompQtyEveryDay(@RequestBody(required = false) String json, @RequestParam(required = false) Double percent, @RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        try {
            percent = percent == null ? 1.0 : percent;
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class); // 传入日期范围
            // auth 获得用户数据
            LoginUser loginUser = determineLoginUser(auth, dev);
            PageInfo<Map<String, Object>> mapList = d05MBIR1Service.getWipOnlineAndCompQtyEveryDay(queryParam, percent, loginUser.getTenantid());
            return R.ok(mapList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询生产WIP: 汇总每个wip单据耗费工时. 返回wip.refno和耗费工时", notes = "", produces = "application/json")
    @RequestMapping(value = "/getWipSumWorkTime", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<List<Map<String, Object>>> getWipSumWorkTime(@RequestBody(required = false) String json, @RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class); // 传入日期范围
            // auth 获得用户数据
            LoginUser loginUser = determineLoginUser(auth, dev);
            List<Map<String, Object>> mapList = d05MBIR1Service.getWipSumWorkTime(queryParam, loginUser.getTenantid());
            return R.ok(mapList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    // 按客户分组,统计订单AttributeJson字段里面的总重量spuzongzhongliang,退铜屑重量sputuitongxie,退夹头重量sputuijiatou
//    SUM(IF(spu.spukey = 'spuzongzhongliang', spu.spuvalue, 0)) AS 总重量,--value
//    SUM(IF(spu.spukey = 'sputuitongxie', spu.spuvalue, 0)) AS 退铜屑重量, --valueb
//    SUM(IF(spu.spukey = 'sputuijiatou', spu.spuvalue, 0)) AS 退夹头重量   --valuec
    @ApiOperation(value = "统计委外加工单Wk_SubcontractItem.AttributeJson字段: 按客户分组,返回name客户名,code客户编码,value总重量(spuzongzhongliang)", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSpuWeightGroupByGroup", method = RequestMethod.POST)
    public R<PageInfo<ChartPojo>> getSpuWeightGroupByGroup(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 获得用户数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (isNotBlank(groupid)) {
                qpfilter = "And App_Workgroup.id='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d05MBIR1Service.getSpuWeightGroupByGroup(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询加工单", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getWsPageList", method = RequestMethod.POST)
    public R<PageInfo<WkWorksheetitemdetailPojo>> getWsPageList(@RequestBody String json, String groupid, @RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        // auth 获得用户数据
        LoginUser loginUser = determineLoginUser(auth, dev);
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWorksheetService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取厂制工单详细信息", notes = "获取厂制工单详细信息", produces = "application/json")
    @RequestMapping(value = "/getWsBillEntityByRefNo", method = RequestMethod.GET)
    public R<WkWorksheetPojo> getWsBillEntityByRefNo(String refno, @RequestParam(required = false) String auth, @RequestParam(required = false) String dev) {
        // auth 获得用户数据
        LoginUser loginUser = determineLoginUser(auth, dev);
        try {
            String id = wkWorksheetService.getIdByRefNo(refno, loginUser.getTenantid());
            if (isBlank(id)) {
                return R.fail("工单不存在");
            }
            return R.ok(this.wkWorksheetService.getBillEntity(id, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
