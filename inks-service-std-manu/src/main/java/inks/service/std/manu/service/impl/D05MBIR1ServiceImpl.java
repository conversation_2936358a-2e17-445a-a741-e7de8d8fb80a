package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.manu.mapper.D05MBIR1Mapper;
import inks.service.std.manu.service.D05MBIR1Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 生产大屏接口实现
 *
 * <AUTHOR>
 * @date 2023年03月24日 14:05
 */
@Service
public class D05MBIR1ServiceImpl implements D05MBIR1Service {
    @Resource
    private D05MBIR1Mapper d05MBIR1Mapper;

    /**
     * @return R<Map < Object>>
     * @Description 获取今日进度(工序进度)返回工序已完成数量, 总数量
     * <AUTHOR>
     * @time 2023/3/25 12:20
     */
    @Override
    public Map<String, Object> getProcessProgressToday(String tid) {
        try {
            return d05MBIR1Mapper.getProcessProgressToday(tid);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, Object> getDeliAndMachQty(String tenantid) {
        Map<String, Object> map = new HashMap<>();
        int finishCountDeliToday = d05MBIR1Mapper.getFinishCountDeliToday(tenantid);
        map.put("finishCountDeliToday", finishCountDeliToday);
        int onlineCountMachToday = d05MBIR1Mapper.getOnlineCountMachToday(tenantid);
        map.put("onlineCountMachToday", onlineCountMachToday);
        return map;
    }

    @Override
    public Map<String, Object> getWipAddAndCompToday(String tenantid, String workshopid) {
        return d05MBIR1Mapper.getWipAddAndCompToday(tenantid, workshopid);
    }

    @Override
    public List<Map<String, Object>> getWipQtyGroupByGoods(String tenantid, String workshopid, QueryParam queryParam) {
        // 统计前5条
        List<Map<String, Object>> wipQtyGroupByGoodsLimit5 = d05MBIR1Mapper.getWipQtyGroupByGoods(tenantid, workshopid, queryParam);
        // 5条的总数量 sum(sumwkpcsqty)
        double sumLimit5 = 0.0;
        for (Map<String, Object> map : wipQtyGroupByGoodsLimit5) {
            sumLimit5 += Double.parseDouble(map.get("sumwkpcsqty").toString());
        }
        // 统计其他的数量(总数量-前5条的总数量)
        double allWipQtyByWorkShopid = d05MBIR1Mapper.getAllWipQtyByWorkShopid(tenantid, workshopid, queryParam);
        double otherSum = allWipQtyByWorkShopid - sumLimit5;
        // 组装返回数据
        wipQtyGroupByGoodsLimit5.add(new HashMap<String, Object>() {{
            put("goodsname", "其他");
            put("sumwkpcsqty", otherSum);
        }});
        return wipQtyGroupByGoodsLimit5;
    }

    //查询生产WIP的完工记录:按产品分组统计sum(wip数量),返回值：货品编码，货品名称，数量
    @Override
    public List<Map<String, Object>> getSumWipCompQtyGroupByGoods(QueryParam queryParam, String tenantid) {
        return d05MBIR1Mapper.getSumWipCompQtyGroupByGoods(queryParam, tenantid);
    }


    @Override
//    查询生产WIP: 统计每日生产WIP记录的完工数量、在线数量（未完工数量）
    public PageInfo<Map<String, Object>> getWipOnlineAndCompQtyEveryDay(QueryParam queryParam, Double percent, String tenantid) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<Map<String, Object>> lst = d05MBIR1Mapper.getWipOnlineAndCompQtyEveryDay(queryParam, percent, tenantid);
            PageInfo<Map<String, Object>> pageInfo = new PageInfo<Map<String, Object>>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getWipSumWorkTime(QueryParam queryParam, String tenantid) {
        return d05MBIR1Mapper.getWipSumWorkTime(queryParam, tenantid);
    }

    @Override
    public PageInfo<ChartPojo> getSpuWeightGroupByGroup(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        List<ChartPojo> lst = d05MBIR1Mapper.getSpuWeightGroupByGroup(queryParam);
        PageInfo<ChartPojo> pageInfo = new PageInfo<ChartPojo>(lst);
        return pageInfo;
    }
}
