package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkScmachtypePojo;

/**
 * 委外加工类型(WkScmachtype)表服务接口
 *
 * <AUTHOR>
 * @since 2023-11-13 15:12:07
 */
public interface WkScmachtypeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkScmachtypePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkScmachtypePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkScmachtypePojo 实例对象
     * @return 实例对象
     */
    WkScmachtypePojo insert(WkScmachtypePojo wkScmachtypePojo);

    /**
     * 修改数据
     *
     * @param wkScmachtypepojo 实例对象
     * @return 实例对象
     */
    WkScmachtypePojo update(WkScmachtypePojo wkScmachtypepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
