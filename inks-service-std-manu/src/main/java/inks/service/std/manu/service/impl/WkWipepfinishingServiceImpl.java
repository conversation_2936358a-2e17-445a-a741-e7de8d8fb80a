package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipepfinishingEntity;
import inks.service.std.manu.domain.WkWipepfinishingitemEntity;
import inks.service.std.manu.domain.pojo.WkWipepfinishingPojo;
import inks.service.std.manu.domain.pojo.WkWipepfinishingitemPojo;
import inks.service.std.manu.domain.pojo.WkWipepfinishingitemdetailPojo;
import inks.service.std.manu.mapper.WkWipepfinishingMapper;
import inks.service.std.manu.mapper.WkWipepfinishingitemMapper;
import inks.service.std.manu.service.WkWipepfinishingService;
import inks.service.std.manu.service.WkWipepfinishingitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 工序收货(WkWipepfinishing)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-04 11:14:11
 */
@Service("wkWipepfinishingService")
public class WkWipepfinishingServiceImpl implements WkWipepfinishingService {
    @Resource
    private WkWipepfinishingMapper wkWipepfinishingMapper;

    @Resource
    private WkWipepfinishingitemMapper wkWipepfinishingitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkWipepfinishingitemService wkWipepfinishingitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipepfinishingPojo getEntity(String key, String tid) {
        return this.wkWipepfinishingMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipepfinishingitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipepfinishingitemdetailPojo> lst = wkWipepfinishingMapper.getPageList(queryParam);
            PageInfo<WkWipepfinishingitemdetailPojo> pageInfo = new PageInfo<WkWipepfinishingitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipepfinishingPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkWipepfinishingPojo wkWipepfinishingPojo = this.wkWipepfinishingMapper.getEntity(key, tid);
            //读取子表
            wkWipepfinishingPojo.setItem(wkWipepfinishingitemMapper.getList(wkWipepfinishingPojo.getId(), wkWipepfinishingPojo.getTenantid()));
            return wkWipepfinishingPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipepfinishingPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipepfinishingPojo> lst = wkWipepfinishingMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkWipepfinishingitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkWipepfinishingPojo> pageInfo = new PageInfo<WkWipepfinishingPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipepfinishingPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipepfinishingPojo> lst = wkWipepfinishingMapper.getPageTh(queryParam);
            PageInfo<WkWipepfinishingPojo> pageInfo = new PageInfo<WkWipepfinishingPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWipepfinishingPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipepfinishingPojo insert(WkWipepfinishingPojo wkWipepfinishingPojo) {
//初始化NULL字段
        if (wkWipepfinishingPojo.getRefno() == null) wkWipepfinishingPojo.setRefno("");
        if (wkWipepfinishingPojo.getBilltype() == null) wkWipepfinishingPojo.setBilltype("");
        if (wkWipepfinishingPojo.getBilldate() == null) wkWipepfinishingPojo.setBilldate(new Date());
        if (wkWipepfinishingPojo.getGroupid() == null) wkWipepfinishingPojo.setGroupid("");
        if (wkWipepfinishingPojo.getBilltitle() == null) wkWipepfinishingPojo.setBilltitle("");
        if (wkWipepfinishingPojo.getOperator() == null) wkWipepfinishingPojo.setOperator("");
        if (wkWipepfinishingPojo.getSummary() == null) wkWipepfinishingPojo.setSummary("");
        if (wkWipepfinishingPojo.getCreateby() == null) wkWipepfinishingPojo.setCreateby("");
        if (wkWipepfinishingPojo.getCreatebyid() == null) wkWipepfinishingPojo.setCreatebyid("");
        if (wkWipepfinishingPojo.getCreatedate() == null) wkWipepfinishingPojo.setCreatedate(new Date());
        if (wkWipepfinishingPojo.getLister() == null) wkWipepfinishingPojo.setLister("");
        if (wkWipepfinishingPojo.getListerid() == null) wkWipepfinishingPojo.setListerid("");
        if (wkWipepfinishingPojo.getModifydate() == null) wkWipepfinishingPojo.setModifydate(new Date());
        if (wkWipepfinishingPojo.getAssessor() == null) wkWipepfinishingPojo.setAssessor("");
        if (wkWipepfinishingPojo.getAssessorid() == null) wkWipepfinishingPojo.setAssessorid("");
        if (wkWipepfinishingPojo.getAssessdate() == null) wkWipepfinishingPojo.setAssessdate(new Date());
        if (wkWipepfinishingPojo.getBilltaxamount() == null) wkWipepfinishingPojo.setBilltaxamount(0D);
        if (wkWipepfinishingPojo.getBillamount() == null) wkWipepfinishingPojo.setBillamount(0D);
        if (wkWipepfinishingPojo.getItemcount() == null)
            wkWipepfinishingPojo.setItemcount(wkWipepfinishingPojo.getItem().size());
        if (wkWipepfinishingPojo.getDisannulcount() == null) wkWipepfinishingPojo.setDisannulcount(0);
        if (wkWipepfinishingPojo.getFinishcount() == null) wkWipepfinishingPojo.setFinishcount(0);
        if (wkWipepfinishingPojo.getPrintcount() == null) wkWipepfinishingPojo.setPrintcount(0);
        if (wkWipepfinishingPojo.getCustom1() == null) wkWipepfinishingPojo.setCustom1("");
        if (wkWipepfinishingPojo.getCustom2() == null) wkWipepfinishingPojo.setCustom2("");
        if (wkWipepfinishingPojo.getCustom3() == null) wkWipepfinishingPojo.setCustom3("");
        if (wkWipepfinishingPojo.getCustom4() == null) wkWipepfinishingPojo.setCustom4("");
        if (wkWipepfinishingPojo.getCustom5() == null) wkWipepfinishingPojo.setCustom5("");
        if (wkWipepfinishingPojo.getCustom6() == null) wkWipepfinishingPojo.setCustom6("");
        if (wkWipepfinishingPojo.getCustom7() == null) wkWipepfinishingPojo.setCustom7("");
        if (wkWipepfinishingPojo.getCustom8() == null) wkWipepfinishingPojo.setCustom8("");
        if (wkWipepfinishingPojo.getCustom9() == null) wkWipepfinishingPojo.setCustom9("");
        if (wkWipepfinishingPojo.getCustom10() == null) wkWipepfinishingPojo.setCustom10("");
        if (wkWipepfinishingPojo.getTenantid() == null) wkWipepfinishingPojo.setTenantid("");
        if (wkWipepfinishingPojo.getTenantname() == null) wkWipepfinishingPojo.setTenantname("");
        if (wkWipepfinishingPojo.getRevision() == null) wkWipepfinishingPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkWipepfinishingEntity wkWipepfinishingEntity = new WkWipepfinishingEntity();
        BeanUtils.copyProperties(wkWipepfinishingPojo, wkWipepfinishingEntity);
        //设置id和新建日期
        wkWipepfinishingEntity.setId(id);
        wkWipepfinishingEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkWipepfinishingMapper.insert(wkWipepfinishingEntity);
        //Item子表处理
        List<WkWipepfinishingitemPojo> lst = wkWipepfinishingPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (WkWipepfinishingitemPojo wkWipepfinishingitemPojo : lst) {
                //初始化item的NULL
                WkWipepfinishingitemPojo itemPojo = this.wkWipepfinishingitemService.clearNull(wkWipepfinishingitemPojo);
                WkWipepfinishingitemEntity wkWipepfinishingitemEntity = new WkWipepfinishingitemEntity();
                BeanUtils.copyProperties(itemPojo, wkWipepfinishingitemEntity);
                //设置id和Pid
                wkWipepfinishingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipepfinishingitemEntity.setPid(id);
                wkWipepfinishingitemEntity.setTenantid(wkWipepfinishingPojo.getTenantid());
                wkWipepfinishingitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkWipepfinishingitemMapper.insert(wkWipepfinishingitemEntity);
                // 同步工序外协单
                if (wkWipepfinishingitemPojo.getCiteitemid() != null && !"".equals(wkWipepfinishingitemPojo.getCiteitemid())) {
                    this.wkWipepfinishingMapper.updateEpibFinish(wkWipepfinishingitemPojo.getCiteitemid(), wkWipepfinishingitemPojo.getCiteuid(), wkWipepfinishingEntity.getTenantid());
                    this.wkWipepfinishingMapper.updateEpibFinishCount(wkWipepfinishingitemPojo.getCiteitemid(), wkWipepfinishingitemPojo.getCiteuid(), wkWipepfinishingEntity.getTenantid());
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipepfinishingEntity.getId(), wkWipepfinishingEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipepfinishingPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipepfinishingPojo update(WkWipepfinishingPojo wkWipepfinishingPojo) {
        //主表更改
        WkWipepfinishingEntity wkWipepfinishingEntity = new WkWipepfinishingEntity();
        wkWipepfinishingPojo.setItemcount(wkWipepfinishingPojo.getItem().size());
        BeanUtils.copyProperties(wkWipepfinishingPojo, wkWipepfinishingEntity);
        this.wkWipepfinishingMapper.update(wkWipepfinishingEntity);
        if (wkWipepfinishingPojo.getItem() != null) {
            //Item子表处理
            List<WkWipepfinishingitemPojo> lst = wkWipepfinishingPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkWipepfinishingMapper.getDelItemIds(wkWipepfinishingPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    WkWipepfinishingitemPojo dbPojo = this.wkWipepfinishingitemMapper.getEntity(lstDelId, wkWipepfinishingEntity.getTenantid());
                    this.wkWipepfinishingitemMapper.delete(lstDelId, wkWipepfinishingEntity.getTenantid());
                    // 同步工序外协单
                    if (dbPojo.getCiteitemid() != null && !"".equals(dbPojo.getCiteitemid())) {
                        this.wkWipepfinishingMapper.updateEpibFinish(dbPojo.getCiteitemid(), dbPojo.getCiteuid(), wkWipepfinishingEntity.getTenantid());
                        this.wkWipepfinishingMapper.updateEpibFinishCount(dbPojo.getCiteitemid(), dbPojo.getCiteuid(), wkWipepfinishingEntity.getTenantid());
                    }
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (WkWipepfinishingitemPojo wkWipepfinishingitemPojo : lst) {
                    WkWipepfinishingitemEntity wkWipepfinishingitemEntity = new WkWipepfinishingitemEntity();
                    if ("".equals(wkWipepfinishingitemPojo.getId()) || wkWipepfinishingitemPojo.getId() == null) {
                        //初始化item的NULL
                        WkWipepfinishingitemPojo itemPojo = this.wkWipepfinishingitemService.clearNull(wkWipepfinishingitemPojo);
                        BeanUtils.copyProperties(itemPojo, wkWipepfinishingitemEntity);
                        //设置id和Pid
                        wkWipepfinishingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkWipepfinishingitemEntity.setPid(wkWipepfinishingEntity.getId());  // 主表 id
                        wkWipepfinishingitemEntity.setTenantid(wkWipepfinishingPojo.getTenantid());   // 租户id
                        wkWipepfinishingitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkWipepfinishingitemMapper.insert(wkWipepfinishingitemEntity);
                    } else {
                        BeanUtils.copyProperties(wkWipepfinishingitemPojo, wkWipepfinishingitemEntity);
                        wkWipepfinishingitemEntity.setTenantid(wkWipepfinishingPojo.getTenantid());
                        this.wkWipepfinishingitemMapper.update(wkWipepfinishingitemEntity);
                    }
                    // 同步工序外协单
                    if (wkWipepfinishingitemPojo.getCiteitemid() != null && !"".equals(wkWipepfinishingitemPojo.getCiteitemid())) {
                        this.wkWipepfinishingMapper.updateEpibFinish(wkWipepfinishingitemPojo.getCiteitemid(), wkWipepfinishingitemPojo.getCiteuid(), wkWipepfinishingEntity.getTenantid());
                        this.wkWipepfinishingMapper.updateEpibFinishCount(wkWipepfinishingitemPojo.getCiteitemid(), wkWipepfinishingitemPojo.getCiteuid(), wkWipepfinishingEntity.getTenantid());
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipepfinishingEntity.getId(), wkWipepfinishingEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkWipepfinishingPojo wkWipepfinishingPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkWipepfinishingitemPojo> lst = wkWipepfinishingPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkWipepfinishingitemPojo wkWipepfinishingitemPojo : lst) {
                this.wkWipepfinishingitemMapper.delete(wkWipepfinishingitemPojo.getId(), tid);
                // 同步工序外协单
                if (wkWipepfinishingitemPojo.getCiteitemid() != null && !"".equals(wkWipepfinishingitemPojo.getCiteitemid())) {
                    this.wkWipepfinishingMapper.updateEpibFinish(wkWipepfinishingitemPojo.getCiteitemid(), wkWipepfinishingitemPojo.getCiteuid(), tid);
                    this.wkWipepfinishingMapper.updateEpibFinishCount(wkWipepfinishingitemPojo.getCiteitemid(), wkWipepfinishingitemPojo.getCiteuid(), tid);
                }
            }
        }
        this.wkWipepfinishingMapper.delete(key, tid);
        return wkWipepfinishingPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkWipepfinishingPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipepfinishingPojo approval(WkWipepfinishingPojo wkWipepfinishingPojo) {
        //主表更改
        WkWipepfinishingEntity wkWipepfinishingEntity = new WkWipepfinishingEntity();
        BeanUtils.copyProperties(wkWipepfinishingPojo, wkWipepfinishingEntity);
        this.wkWipepfinishingMapper.approval(wkWipepfinishingEntity);
        //返回Bill实例
        return this.getBillEntity(wkWipepfinishingEntity.getId(), wkWipepfinishingEntity.getTenantid());
    }

    @Override
    public void updatePrintcount(WkWipepfinishingPojo billPrintPojo) {
        this.wkWipepfinishingMapper.updatePrintcount(billPrintPojo);
    }
}
