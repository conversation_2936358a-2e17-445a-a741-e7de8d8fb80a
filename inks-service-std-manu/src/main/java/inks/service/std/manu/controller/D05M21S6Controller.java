package inks.service.std.manu.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.service.WkStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * 工位(Wk_Station)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-22 12:36:15
 */
@RestController
@RequestMapping("D05M21S6")
@Api(tags = "D05M21S6:生产工位")
public class D05M21S6Controller extends WkStationController {
    @Resource
    private WkStationService wkStationService;

    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取工位状态StateJson和工序名By工位id", notes = "", produces = "application/json")
    @RequestMapping(value = "/getStationStateAndWk", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Wk_Station.List")
    public R<HashMap<String, Object>> getStationStateAndWk(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkStationService.getStationStateAndWk(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取工位状态StateJson By工序id", notes = "", produces = "application/json")
    @RequestMapping(value = "/getStateJsonByWpid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Station.List")
    public R<String> getStateJsonByWpid(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkStationService.getStateJsonByWpid(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
