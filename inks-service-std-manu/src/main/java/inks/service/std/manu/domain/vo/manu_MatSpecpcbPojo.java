package inks.service.std.manu.domain.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;
import inks.service.std.manu.domain.pojo.MatSpecpcbitemPojo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Pcb工艺(MatSpecpcb)实体类
 *
 * <AUTHOR>
 * @since 2022-06-20 15:09:16
 */
public class manu_MatSpecpcbPojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = 472521008911900147L;
    // id
    @Excel(name = "id")
    private String id;
    // 编码
    @Excel(name = "编码")
    private String refno;
    // 单据日期
    @Excel(name = "单据日期")
    private Date billdate;
    // 单据类型
    @Excel(name = "单据类型")
    private String billtype;
    // 单据标题
    @Excel(name = "单据标题")
    private String billtitle;
    // Goodsid
    @Excel(name = "Goodsid")
    private String goodsid;
    // 货品编码
    @Excel(name = "货品编码")
    private String itemcode;
    // 货品名称
    @Excel(name = "货品名称")
    private String itemname;
    // 货品规格
    @Excel(name = "货品规格")
    private String itemspec;
    // 货品单位
    @Excel(name = "货品单位")
    private String itemunit;
    // 版本号
    @Excel(name = "版本号")
    private String versionnum;
    // 商品分类
    @Excel(name = "商品分类")
    private String goodsclass;
    // 材质
    @Excel(name = "材质")
    private String material;
    // 表面处理
    @Excel(name = "表面处理")
    private String surface;
    // 默认客户
    @Excel(name = "默认客户")
    private String groupid;
    // PcsX
    @Excel(name = "PcsX")
    private Double pcsx;
    // PcsY
    @Excel(name = "PcsY")
    private Double pcsy;
    // 尺寸单位
    @Excel(name = "尺寸单位")
    private String sizeunit;
    // SetX
    @Excel(name = "SetX")
    private Double setx;
    // SetY
    @Excel(name = "SetY")
    private Double sety;
    // Set2Pcs
    @Excel(name = "Set2Pcs")
    private Integer set2pcs;
    // 允收打报
    @Excel(name = "允收打报")
    private Integer allowng;
    // PnlX
    @Excel(name = "PnlX")
    private Double pnlx;
    // PnlY
    @Excel(name = "PnlY")
    private Double pnly;
    // Pnl2Pcs
    @Excel(name = "Pnl2Pcs")
    private Integer pnl2pcs;
    // PnlBX
    @Excel(name = "PnlBX")
    private Double pnlbx;
    // PnlBY
    @Excel(name = "PnlBY")
    private Double pnlby;
    // PnlB2Pcs
    @Excel(name = "PnlB2Pcs")
    private Integer pnlb2pcs;
    // PnlCX
    @Excel(name = "PnlCX")
    private Double pnlcx;
    // PnlCY
    @Excel(name = "PnlCY")
    private Double pnlcy;
    // PnlC2Pcs
    @Excel(name = "PnlC2Pcs")
    private Integer pnlc2pcs;
    // PnlDX
    @Excel(name = "PnlDX")
    private Double pnldx;
    // PnlDY
    @Excel(name = "PnlDY")
    private Double pnldy;
    // PnlD2Pcs
    @Excel(name = "PnlD2Pcs")
    private Integer pnld2pcs;
    // 经办人
    @Excel(name = "经办人")
    private String operator;
    // 父级ID
    @Excel(name = "父级ID")
    private Integer parentid;
    // 层数
    @Excel(name = "层数")
    private Integer layernum;
    // 生产类别
    @Excel(name = "生产类别")
    private String machtype;
    // 生产等级
    @Excel(name = "生产等级")
    private String machclass;
    // 有效
    @Excel(name = "有效")
    private Integer enabledmark;
    // 单据状态
    @Excel(name = "单据状态")
    private String statecode;
    // 生产工艺
    @Excel(name = "生产工艺")
    private String technics;
    // 整料PCS
    @Excel(name = "整料PCS")
    private Integer mat2pcs;
    // 基材厚度
    @Excel(name = "基材厚度")
    private String matthick;
    // 基材铜厚
    @Excel(name = "基材铜厚")
    private String matcuthick;
    // 基材名称
    @Excel(name = "基材名称")
    private String matname;
    // 基材编码
    @Excel(name = "基材编码")
    private String matcode;
    // 基材厂商
    @Excel(name = "基材厂商")
    private String matfactory;
    // 基材颜色
    @Excel(name = "基材颜色")
    private String matcolor;
    // 产品代码
    @Excel(name = "产品代码")
    private String productcode;
    // 成品厚度
    @Excel(name = "成品厚度")
    private String productthick;
    // 成品铜厚
    @Excel(name = "成品铜厚")
    private String productcuthick;
    // 成品重量
    @Excel(name = "成品重量")
    private Double productweight;
    // 印次
    @Excel(name = "印次")
    private Integer printlayer;
    // 生产利用率
    @Excel(name = "生产利用率")
    private Double pnluserate;
    // 开料利用率
    @Excel(name = "开料利用率")
    private Double cutuserate;
    // 摘要
    @Excel(name = "摘要")
    private String summary;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 审核员
    @Excel(name = "审核员")
    private String assessor;
    // 审核员id
    @Excel(name = "审核员id")
    private String assessorid;
    // 审核日期
    @Excel(name = "审核日期")
    private Date assessdate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<MatSpecpcbitemPojo> item;
    // draw
    private List<manu_MatSpecpcbdrawPojo> draw;

    // drl
    private List<manu_MatSpecpcbdrlPojo> drl;


    // 编码
    private String groupuid;
    // 名称
    private String groupname;
    // 缩写
    private String abbreviate;

    //            System.out.println("areaqty: " + result.areaqty);
    //            System.out.println("areaunit: " + result.areaunit);
    private Double areaqty;
    private String areaunit;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Double getAreaqty() {
        return areaqty;
    }

    public void setAreaqty(Double areaqty) {
        this.areaqty = areaqty;
    }

    public String getAreaunit() {
        return areaunit;
    }

    public void setAreaunit(String areaunit) {
        this.areaunit = areaunit;
    }

    // 编码
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 单据日期
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    // 单据类型
    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    // 单据标题
    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }

    // Goodsid
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 货品编码
    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    // 货品名称
    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    // 货品规格
    public String getItemspec() {
        return itemspec;
    }

    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }

    // 货品单位
    public String getItemunit() {
        return itemunit;
    }

    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }

    // 版本号
    public String getVersionnum() {
        return versionnum;
    }

    public void setVersionnum(String versionnum) {
        this.versionnum = versionnum;
    }

    // 商品分类
    public String getGoodsclass() {
        return goodsclass;
    }

    public void setGoodsclass(String goodsclass) {
        this.goodsclass = goodsclass;
    }

    // 材质
    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    // 表面处理
    public String getSurface() {
        return surface;
    }

    public void setSurface(String surface) {
        this.surface = surface;
    }

    // 默认客户
    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    // PcsX
    public Double getPcsx() {
        return pcsx;
    }

    public void setPcsx(Double pcsx) {
        this.pcsx = pcsx;
    }

    // PcsY
    public Double getPcsy() {
        return pcsy;
    }

    public void setPcsy(Double pcsy) {
        this.pcsy = pcsy;
    }

    // 尺寸单位
    public String getSizeunit() {
        return sizeunit;
    }

    public void setSizeunit(String sizeunit) {
        this.sizeunit = sizeunit;
    }

    // SetX
    public Double getSetx() {
        return setx;
    }

    public void setSetx(Double setx) {
        this.setx = setx;
    }

    // SetY
    public Double getSety() {
        return sety;
    }

    public void setSety(Double sety) {
        this.sety = sety;
    }

    // Set2Pcs
    public Integer getSet2pcs() {
        return set2pcs;
    }

    public void setSet2pcs(Integer set2pcs) {
        this.set2pcs = set2pcs;
    }

    // 允收打报
    public Integer getAllowng() {
        return allowng;
    }

    public void setAllowng(Integer allowng) {
        this.allowng = allowng;
    }

    // PnlX
    public Double getPnlx() {
        return pnlx;
    }

    public void setPnlx(Double pnlx) {
        this.pnlx = pnlx;
    }

    // PnlY
    public Double getPnly() {
        return pnly;
    }

    public void setPnly(Double pnly) {
        this.pnly = pnly;
    }

    // Pnl2Pcs
    public Integer getPnl2pcs() {
        return pnl2pcs;
    }

    public void setPnl2pcs(Integer pnl2pcs) {
        this.pnl2pcs = pnl2pcs;
    }

    // PnlBX
    public Double getPnlbx() {
        return pnlbx;
    }

    public void setPnlbx(Double pnlbx) {
        this.pnlbx = pnlbx;
    }

    // PnlBY
    public Double getPnlby() {
        return pnlby;
    }

    public void setPnlby(Double pnlby) {
        this.pnlby = pnlby;
    }

    // PnlB2Pcs
    public Integer getPnlb2pcs() {
        return pnlb2pcs;
    }

    public void setPnlb2pcs(Integer pnlb2pcs) {
        this.pnlb2pcs = pnlb2pcs;
    }

    // PnlCX
    public Double getPnlcx() {
        return pnlcx;
    }

    public void setPnlcx(Double pnlcx) {
        this.pnlcx = pnlcx;
    }

    // PnlCY
    public Double getPnlcy() {
        return pnlcy;
    }

    public void setPnlcy(Double pnlcy) {
        this.pnlcy = pnlcy;
    }

    // PnlC2Pcs
    public Integer getPnlc2pcs() {
        return pnlc2pcs;
    }

    public void setPnlc2pcs(Integer pnlc2pcs) {
        this.pnlc2pcs = pnlc2pcs;
    }

    // PnlDX
    public Double getPnldx() {
        return pnldx;
    }

    public void setPnldx(Double pnldx) {
        this.pnldx = pnldx;
    }

    // PnlDY
    public Double getPnldy() {
        return pnldy;
    }

    public void setPnldy(Double pnldy) {
        this.pnldy = pnldy;
    }

    // PnlD2Pcs
    public Integer getPnld2pcs() {
        return pnld2pcs;
    }

    public void setPnld2pcs(Integer pnld2pcs) {
        this.pnld2pcs = pnld2pcs;
    }

    // 经办人
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    // 父级ID
    public Integer getParentid() {
        return parentid;
    }

    public void setParentid(Integer parentid) {
        this.parentid = parentid;
    }

    // 层数
    public Integer getLayernum() {
        return layernum;
    }

    public void setLayernum(Integer layernum) {
        this.layernum = layernum;
    }

    // 生产类别
    public String getMachtype() {
        return machtype;
    }

    public void setMachtype(String machtype) {
        this.machtype = machtype;
    }

    // 生产等级
    public String getMachclass() {
        return machclass;
    }

    public void setMachclass(String machclass) {
        this.machclass = machclass;
    }

    // 有效
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 单据状态
    public String getStatecode() {
        return statecode;
    }

    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }

    // 生产工艺
    public String getTechnics() {
        return technics;
    }

    public void setTechnics(String technics) {
        this.technics = technics;
    }

    // 整料PCS
    public Integer getMat2pcs() {
        return mat2pcs;
    }

    public void setMat2pcs(Integer mat2pcs) {
        this.mat2pcs = mat2pcs;
    }

    // 基材厚度
    public String getMatthick() {
        return matthick;
    }

    public void setMatthick(String matthick) {
        this.matthick = matthick;
    }

    // 基材铜厚
    public String getMatcuthick() {
        return matcuthick;
    }

    public void setMatcuthick(String matcuthick) {
        this.matcuthick = matcuthick;
    }

    // 基材名称
    public String getMatname() {
        return matname;
    }

    public void setMatname(String matname) {
        this.matname = matname;
    }

    // 基材编码
    public String getMatcode() {
        return matcode;
    }

    public void setMatcode(String matcode) {
        this.matcode = matcode;
    }

    // 基材厂商
    public String getMatfactory() {
        return matfactory;
    }

    public void setMatfactory(String matfactory) {
        this.matfactory = matfactory;
    }

    // 基材颜色
    public String getMatcolor() {
        return matcolor;
    }

    public void setMatcolor(String matcolor) {
        this.matcolor = matcolor;
    }

    // 产品代码
    public String getProductcode() {
        return productcode;
    }

    public void setProductcode(String productcode) {
        this.productcode = productcode;
    }

    // 成品厚度
    public String getProductthick() {
        return productthick;
    }

    public void setProductthick(String productthick) {
        this.productthick = productthick;
    }

    // 成品铜厚
    public String getProductcuthick() {
        return productcuthick;
    }

    public void setProductcuthick(String productcuthick) {
        this.productcuthick = productcuthick;
    }

    // 成品重量
    public Double getProductweight() {
        return productweight;
    }

    public void setProductweight(Double productweight) {
        this.productweight = productweight;
    }

    // 印次
    public Integer getPrintlayer() {
        return printlayer;
    }

    public void setPrintlayer(Integer printlayer) {
        this.printlayer = printlayer;
    }

    // 生产利用率
    public Double getPnluserate() {
        return pnluserate;
    }

    public void setPnluserate(Double pnluserate) {
        this.pnluserate = pnluserate;
    }

    // 开料利用率
    public Double getCutuserate() {
        return cutuserate;
    }

    public void setCutuserate(Double cutuserate) {
        this.cutuserate = cutuserate;
    }

    // 摘要
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 审核员
    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }

    // 审核员id
    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }

    // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


    public List<MatSpecpcbitemPojo> getItem() {
        return item;
    }

    public void setItem(List<MatSpecpcbitemPojo> item) {
        this.item = item;
    }

    public List<manu_MatSpecpcbdrawPojo> getDraw() {
        return draw;
    }

    public void setDraw(List<manu_MatSpecpcbdrawPojo> draw) {
        this.draw = draw;
    }

    public List<manu_MatSpecpcbdrlPojo> getDrl() {
        return drl;
    }

    public void setDrl(List<manu_MatSpecpcbdrlPojo> drl) {
        this.drl = drl;
    }

    public String getGroupuid() {
        return groupuid;
    }

    public void setGroupuid(String groupuid) {
        this.groupuid = groupuid;
    }

    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    public String getAbbreviate() {
        return abbreviate;
    }

    public void setAbbreviate(String abbreviate) {
        this.abbreviate = abbreviate;
    }
}

