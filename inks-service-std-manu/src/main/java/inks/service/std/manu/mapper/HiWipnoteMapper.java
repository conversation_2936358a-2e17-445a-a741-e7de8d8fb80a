package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.HiWipnoteEntity;
import inks.service.std.manu.domain.pojo.HiWipnotePojo;
import inks.service.std.manu.domain.pojo.HiWipnoteitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * WIP记录(HiWipnote)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-16 16:19:16
 */
@Mapper
public interface HiWipnoteMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    HiWipnotePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<HiWipnoteitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<HiWipnotePojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param hiWipnoteEntity 实例对象
     * @return 影响行数
     */
    int insert(HiWipnoteEntity hiWipnoteEntity);

    
    /**
     * 修改数据
     *
     * @param hiWipnoteEntity 实例对象
     * @return 影响行数
     */
    int update(HiWipnoteEntity hiWipnoteEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param hiWipnotePojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(HiWipnotePojo hiWipnotePojo);


    // 4个NowToHi方法
    int copyNowToHi(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("onlineIds") List<String> onlineIds, @Param("tid") String tid);

    int copyItemNowToHi(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("onlineIds") List<String> onlineIds,@Param("tid") String tid);

    int deleteNow(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("onlineIds") List<String> onlineIds,@Param("tid") String tid);

    int deleteItemNow(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("onlineIds") List<String> onlineIds,@Param("tid") String tid);


    // 4个HiToNow方法
    int copyHiToNow(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("ids") List<String> ids, @Param("tid") String tid);

    int copyItemHiToNow(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("ids") List<String> ids, @Param("tid") String tid);

    int deleteHi(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("ids") List<String> ids, @Param("tid") String tid);

    int deleteItemHi(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("ids") List<String> ids, @Param("tid") String tid);

    List<String> getOnlineNowWipNoteIds(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("tid") String tid);

    List<LinkedHashMap<String, Object>> getTableColumns(String tablename);

    List<String> getAllMigratableNowIds(String startdate, String enddate, List<String> onlineIds, String tid);

    int copyNowToHiByIds(List<String> ids, String tid);

    int copyItemNowToHiByIds(List<String> ids, String tid);

    int deleteNowByIds(List<String> ids, String tid);

    int deleteItemNowByIds(List<String> ids, String tid);

    List<String> getAllMigratableHiIds(String startdate, String enddate, String tid);
    
    int copyHiToNowByIds(List<String> ids, String tid);

    int copyItemHiToNowByIds(List<String> ids, String tid);

    int deleteHiByIds(List<String> ids, String tid);

    int deleteItemHiByIds(List<String> ids, String tid);

}

