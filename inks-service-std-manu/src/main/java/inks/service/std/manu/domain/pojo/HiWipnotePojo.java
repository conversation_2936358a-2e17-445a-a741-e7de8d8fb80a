package inks.service.std.manu.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * WIP记录(HiWipnote)实体类
 *
 * <AUTHOR>
 * @since 2023-10-16 16:19:16
 */
public class HiWipnotePojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = 750688404426930438L;
    // id
    @Excel(name = "id")
    private String id;
    // 单据编码
    @Excel(name = "单据编码")
    private String refno;
    // 单据日期
    @Excel(name = "单据日期")
    private Date billdate;
    // 生产类型
    @Excel(name = "生产类型")
    private String worktype;
    // 生产车间id
    @Excel(name = "生产车间id")
    private String workshopid;
    // 生产车间
    @Excel(name = "生产车间")
    private String workshop;
    // 货品id
    @Excel(name = "货品id")
    private String goodsid;
    // 计划完工
    @Excel(name = "计划完工")
    private Date plandate;
    // 数量
    @Excel(name = "数量")
    private Double quantity;
    // 总投数
    @Excel(name = "总投数")
    private Double wkpcsqty;
    // 投料Sec
    @Excel(name = "投料Sec")
    private Double wksecqty;
    // 总报废
    @Excel(name = "总报废")
    private Double mrbpcsqty;
    // 报废Sec
    @Excel(name = "报废Sec")
    private Double mrbsecqty;
    // 补单
    @Excel(name = "补单")
    private Integer supplement;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 状态
    @Excel(name = "状态")
    private String statecode;
    // 状态日期
    @Excel(name = "状态日期")
    private Date statedate;
    // 当前工序id
    @Excel(name = "当前工序id")
    private String wkwpid;
    // 当前工序编码
    @Excel(name = "当前工序编码")
    private String wkwpcode;
    // 当前工序名称
    @Excel(name = "当前工序名称")
    private String wkwpname;
    // 当前行号
    @Excel(name = "当前行号")
    private Integer wkrownum;
    // 客户
    @Excel(name = "客户")
    private String customer;
    // 客户PO
    @Excel(name = "客户PO")
    private String custpo;
    // 销售单号
    @Excel(name = "销售单号")
    private String machuid;
    // 销售子项id
    @Excel(name = "销售子项id")
    private String machitemid;
    // 销售客户id
    @Excel(name = "销售客户id")
    private String machgroupid;
    // 主计划号
    @Excel(name = "主计划号")
    private String mainplanuid;
    // 主计划Itemid
    @Excel(name = "主计划Itemid")
    private String mainplanitemid;
    // 加工单号
    @Excel(name = "加工单号")
    private String workuid;
     // 加工单RefNo
     @Excel(name = "加工单RefNo")
    private String workrefno;
     // 加工单行号
     @Excel(name = "加工单行号")
    private Integer workrownum;
    // 加工单Itemid
    @Excel(name = "加工单Itemid")
    private String workitemid;
    // 分单工序id
    @Excel(name = "分单工序id")
    private String substwpid;
    // 分单工序编码
    @Excel(name = "分单工序编码")
    private String substwpcode;
    // 分单工序名称
    @Excel(name = "分单工序名称")
    private String substwpname;
    // 合单工序id
    @Excel(name = "合单工序id")
    private String subendwpid;
    // 合单工序编码
    @Excel(name = "合单工序编码")
    private String subendwpcode;
    // 合单工序名称
    @Excel(name = "合单工序名称")
    private String subendwpname;
    // 分单编码
    @Excel(name = "分单编码")
    private String subuid;
    // 开工日期
    @Excel(name = "开工日期")
    private Date workdate;
    // 完工工序id
    @Excel(name = "完工工序id")
    private String compwpid;
    // 完工工序编码
    @Excel(name = "完工工序编码")
    private String compwpcode;
    // 完工工序名称
    @Excel(name = "完工工序名称")
    private String compwpname;
    // 完工Pcs
    @Excel(name = "完工Pcs")
    private Double comppcsqty;
    // Wip分管表
    @Excel(name = "Wip分管表")
    private String wipgroupid;
    // 摘要
    @Excel(name = "摘要")
    private String summary;
    // SPU属性
    @Excel(name = "SPU属性")
    private String attributejson;
    // spu文本
    @Excel(name = "spu文本")
    private String attributestr;
    // 主料Code
    @Excel(name = "主料Code")
    private String matcode;
    // 主料已领
    @Excel(name = "主料已领")
    private Integer matused;
    // 当前规格JSON
    @Excel(name = "当前规格JSON")
    private String wkspecjson;
    // item行数
    @Excel(name = "item行数")
    private Integer itemcount;
    // 完成行数
    @Excel(name = "完成行数")
    private Integer finishcount;
    // 打印次数
    @Excel(name = "打印次数")
    private Integer printcount;
     // 颜色等级
     @Excel(name = "颜色等级")
    private String colorlevel;
    // 长
    @Excel(name = "长")
    private Double sizex;
    // 宽
    @Excel(name = "宽")
    private Double sizey;
    // 厚
    @Excel(name = "厚")
    private Double sizez;
    // 关闭
    @Excel(name = "关闭")
    private Integer closed;
    // 作废制表id
    @Excel(name = "作废制表id")
    private String disannullisterid;
    // 作废制表
    @Excel(name = "作废制表")
    private String disannullister;
    // 作废日期
    @Excel(name = "作废日期")
    private Date disannuldate;
    // 作废
    @Excel(name = "作废")
    private Integer disannulmark;
    // 是否合并1被合并2合并为
    @Excel(name = "是否合并1被合并2合并为")
    private Integer mergemark;
    // 来源:0=其他
    @Excel(name = "来源:0其他，1销售订单，2加工单")
    private Integer sourcetype;
     // 子表Json
     @Excel(name = "子表Json")
    private String itemjson;
     // 是否隔离
     @Excel(name = "是否隔离")
    private Integer isolation;
     // 指数
     @Excel(name = "指数")
    private Integer exponent;
     // 总报工数
     @Excel(name = "总报工数")
    private Double jobpcsqty;
     // 报工数Sec
     @Excel(name = "报工数Sec")
    private Double jobsecqty;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<HiWipnoteitemPojo> item;

    // 编码
    private String groupuid;
    // 名称
    private String groupname;
    // 缩写
    private String abbreviate;
    // 缩写
    private String grouplevel;
    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 单据编码
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 单据日期
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }



    // 生产类型
    public String getWorktype() {
        return worktype;
    }

    public void setWorktype(String worktype) {
        this.worktype = worktype;
    }

    // 生产车间id
    public String getWorkshopid() {
        return workshopid;
    }

    public void setWorkshopid(String workshopid) {
        this.workshopid = workshopid;
    }

    // 生产车间
    public String getWorkshop() {
        return workshop;
    }

    public void setWorkshop(String workshop) {
        this.workshop = workshop;
    }

    // 货品id
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 计划完工
    public Date getPlandate() {
        return plandate;
    }

    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }

    // 数量
    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    // 总投数
    public Double getWkpcsqty() {
        return wkpcsqty;
    }

    public void setWkpcsqty(Double wkpcsqty) {
        this.wkpcsqty = wkpcsqty;
    }

    // 投料Sec
    public Double getWksecqty() {
        return wksecqty;
    }

    public void setWksecqty(Double wksecqty) {
        this.wksecqty = wksecqty;
    }

    // 总报废
    public Double getMrbpcsqty() {
        return mrbpcsqty;
    }

    public void setMrbpcsqty(Double mrbpcsqty) {
        this.mrbpcsqty = mrbpcsqty;
    }

    // 报废Sec
    public Double getMrbsecqty() {
        return mrbsecqty;
    }

    public void setMrbsecqty(Double mrbsecqty) {
        this.mrbsecqty = mrbsecqty;
    }

    // 补单
    public Integer getSupplement() {
        return supplement;
    }

    public void setSupplement(Integer supplement) {
        this.supplement = supplement;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 状态
    public String getStatecode() {
        return statecode;
    }

    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }

    // 状态日期
    public Date getStatedate() {
        return statedate;
    }

    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }

    // 当前工序id
    public String getWkwpid() {
        return wkwpid;
    }

    public void setWkwpid(String wkwpid) {
        this.wkwpid = wkwpid;
    }

    // 当前工序编码
    public String getWkwpcode() {
        return wkwpcode;
    }

    public void setWkwpcode(String wkwpcode) {
        this.wkwpcode = wkwpcode;
    }

    // 当前工序名称
    public String getWkwpname() {
        return wkwpname;
    }

    public void setWkwpname(String wkwpname) {
        this.wkwpname = wkwpname;
    }

    // 当前行号
    public Integer getWkrownum() {
        return wkrownum;
    }

    public void setWkrownum(Integer wkrownum) {
        this.wkrownum = wkrownum;
    }

    // 客户
    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    // 客户PO
    public String getCustpo() {
        return custpo;
    }

    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }

    // 销售单号
    public String getMachuid() {
        return machuid;
    }

    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }

    // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }

    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }

    // 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }

    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }

    // 主计划号
    public String getMainplanuid() {
        return mainplanuid;
    }

    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }

    // 主计划Itemid
    public String getMainplanitemid() {
        return mainplanitemid;
    }

    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }

    // 加工单号
    public String getWorkuid() {
        return workuid;
    }

    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }

  // 加工单RefNo
    public String getWorkrefno() {
        return workrefno;
    }

    public void setWorkrefno(String workrefno) {
        this.workrefno = workrefno;
    }

  // 加工单行号
    public Integer getWorkrownum() {
        return workrownum;
    }

    public void setWorkrownum(Integer workrownum) {
        this.workrownum = workrownum;
    }

    // 加工单Itemid
    public String getWorkitemid() {
        return workitemid;
    }

    public void setWorkitemid(String workitemid) {
        this.workitemid = workitemid;
    }

    // 分单工序id
    public String getSubstwpid() {
        return substwpid;
    }

    public void setSubstwpid(String substwpid) {
        this.substwpid = substwpid;
    }

    // 分单工序编码
    public String getSubstwpcode() {
        return substwpcode;
    }

    public void setSubstwpcode(String substwpcode) {
        this.substwpcode = substwpcode;
    }

    // 分单工序名称
    public String getSubstwpname() {
        return substwpname;
    }

    public void setSubstwpname(String substwpname) {
        this.substwpname = substwpname;
    }

    // 合单工序id
    public String getSubendwpid() {
        return subendwpid;
    }

    public void setSubendwpid(String subendwpid) {
        this.subendwpid = subendwpid;
    }

    // 合单工序编码
    public String getSubendwpcode() {
        return subendwpcode;
    }

    public void setSubendwpcode(String subendwpcode) {
        this.subendwpcode = subendwpcode;
    }

    // 合单工序名称
    public String getSubendwpname() {
        return subendwpname;
    }

    public void setSubendwpname(String subendwpname) {
        this.subendwpname = subendwpname;
    }

    // 分单编码
    public String getSubuid() {
        return subuid;
    }

    public void setSubuid(String subuid) {
        this.subuid = subuid;
    }

    // 开工日期
    public Date getWorkdate() {
        return workdate;
    }

    public void setWorkdate(Date workdate) {
        this.workdate = workdate;
    }

    // 完工工序id
    public String getCompwpid() {
        return compwpid;
    }

    public void setCompwpid(String compwpid) {
        this.compwpid = compwpid;
    }

    // 完工工序编码
    public String getCompwpcode() {
        return compwpcode;
    }

    public void setCompwpcode(String compwpcode) {
        this.compwpcode = compwpcode;
    }

    // 完工工序名称
    public String getCompwpname() {
        return compwpname;
    }

    public void setCompwpname(String compwpname) {
        this.compwpname = compwpname;
    }

    // 完工Pcs
    public Double getComppcsqty() {
        return comppcsqty;
    }

    public void setComppcsqty(Double comppcsqty) {
        this.comppcsqty = comppcsqty;
    }

    // Wip分管表
    public String getWipgroupid() {
        return wipgroupid;
    }

    public void setWipgroupid(String wipgroupid) {
        this.wipgroupid = wipgroupid;
    }

    // 摘要
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    // SPU属性
    public String getAttributejson() {
        return attributejson;
    }

    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }

  // SPU文本
    public String getAttributestr() {
        return attributestr;
    }

    public void setAttributestr(String attributestr) {
        this.attributestr = attributestr;
    }

    // 主料Code
    public String getMatcode() {
        return matcode;
    }

    public void setMatcode(String matcode) {
        this.matcode = matcode;
    }

    // 主料已领
    public Integer getMatused() {
        return matused;
    }

    public void setMatused(Integer matused) {
        this.matused = matused;
    }

    // 当前规格JSON
    public String getWkspecjson() {
        return wkspecjson;
    }

    public void setWkspecjson(String wkspecjson) {
        this.wkspecjson = wkspecjson;
    }

    // item行数
    public Integer getItemcount() {
        return itemcount;
    }

    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }

    // 完成行数
    public Integer getFinishcount() {
        return finishcount;
    }

    public void setFinishcount(Integer finishcount) {
        this.finishcount = finishcount;
    }

    // 打印次数
    public Integer getPrintcount() {
        return printcount;
    }

    public void setPrintcount(Integer printcount) {
        this.printcount = printcount;
    }

    // 颜色等级
    public String getColorlevel() {
        return colorlevel;
    }

    public void setColorlevel(String colorlevel) {
        this.colorlevel = colorlevel;
    }

    // 长
    public Double getSizex() {
        return sizex;
    }

    public void setSizex(Double sizex) {
        this.sizex = sizex;
    }

    // 宽
    public Double getSizey() {
        return sizey;
    }

    public void setSizey(Double sizey) {
        this.sizey = sizey;
    }

    // 厚
    public Double getSizez() {
        return sizez;
    }

    public void setSizez(Double sizez) {
        this.sizez = sizez;
    }

    // 关闭
    public Integer getClosed() {
        return closed;
    }

    public void setClosed(Integer closed) {
        this.closed = closed;
    }

    // 作废制表id
    public String getDisannullisterid() {
        return disannullisterid;
    }

    public void setDisannullisterid(String disannullisterid) {
        this.disannullisterid = disannullisterid;
    }

    // 作废制表
    public String getDisannullister() {
        return disannullister;
    }

    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }

    // 作废日期
    public Date getDisannuldate() {
        return disannuldate;
    }

    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }

    // 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }

    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }

    // 是否合并1被合并2合并为
    public Integer getMergemark() {
        return mergemark;
    }

    public void setMergemark(Integer mergemark) {
        this.mergemark = mergemark;
    }

    // 来源:0其他，1销售订单，2加工单
    public Integer getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }

  // 子表Json
    public String getItemjson() {
        return itemjson;
    }

    public void setItemjson(String itemjson) {
        this.itemjson = itemjson;
    }

  // 是否隔离
    public Integer getIsolation() {
        return isolation;
    }

    public void setIsolation(Integer isolation) {
        this.isolation = isolation;
    }

  // 指数
    public Integer getExponent() {
        return exponent;
    }

    public void setExponent(Integer exponent) {
        this.exponent = exponent;
    }

  // 总报工数
    public Double getJobpcsqty() {
        return jobpcsqty;
    }

    public void setJobpcsqty(Double jobpcsqty) {
        this.jobpcsqty = jobpcsqty;
    }

  // 报工数Sec
    public Double getJobsecqty() {
        return jobsecqty;
    }

    public void setJobsecqty(Double jobsecqty) {
        this.jobsecqty = jobsecqty;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


    public List<HiWipnoteitemPojo> getItem() {
        return item;
    }

    public void setItem(List<HiWipnoteitemPojo> item) {
        this.item = item;
    }

    public String getGroupuid() {
        return groupuid;
    }

    public void setGroupuid(String groupuid) {
        this.groupuid = groupuid;
    }

    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    public String getAbbreviate() {
        return abbreviate;
    }

    public void setAbbreviate(String abbreviate) {
        this.abbreviate = abbreviate;
    }

    public String getGrouplevel() {
        return grouplevel;
    }

    public void setGrouplevel(String grouplevel) {
        this.grouplevel = grouplevel;
    }
}

