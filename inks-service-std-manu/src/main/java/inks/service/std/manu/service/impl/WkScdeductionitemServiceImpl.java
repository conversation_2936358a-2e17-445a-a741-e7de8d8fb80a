package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkScdeductionitemEntity;
import inks.service.std.manu.domain.pojo.WkScdeductionitemPojo;
import inks.service.std.manu.mapper.WkScdeductionitemMapper;
import inks.service.std.manu.service.WkScdeductionitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 委制扣款Item(WkScdeductionitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-12 08:46:42
 */
@Service("wkScdeductionitemService")
public class WkScdeductionitemServiceImpl implements WkScdeductionitemService {
    @Resource
    private WkScdeductionitemMapper wkScdeductionitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkScdeductionitemPojo getEntity(String key, String tid) {
        return this.wkScdeductionitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkScdeductionitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkScdeductionitemPojo> lst = wkScdeductionitemMapper.getPageList(queryParam);
            PageInfo<WkScdeductionitemPojo> pageInfo = new PageInfo<WkScdeductionitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkScdeductionitemPojo> getList(String Pid, String tid) {
        try {
            List<WkScdeductionitemPojo> lst = wkScdeductionitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkScdeductionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkScdeductionitemPojo insert(WkScdeductionitemPojo wkScdeductionitemPojo) {
        //初始化item的NULL
        WkScdeductionitemPojo itempojo = this.clearNull(wkScdeductionitemPojo);
        WkScdeductionitemEntity wkScdeductionitemEntity = new WkScdeductionitemEntity();
        BeanUtils.copyProperties(itempojo, wkScdeductionitemEntity);
        //生成雪花id
        wkScdeductionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkScdeductionitemEntity.setRevision(1);  //乐观锁
        this.wkScdeductionitemMapper.insert(wkScdeductionitemEntity);
        return this.getEntity(wkScdeductionitemEntity.getId(), wkScdeductionitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkScdeductionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkScdeductionitemPojo update(WkScdeductionitemPojo wkScdeductionitemPojo) {
        WkScdeductionitemEntity wkScdeductionitemEntity = new WkScdeductionitemEntity();
        BeanUtils.copyProperties(wkScdeductionitemPojo, wkScdeductionitemEntity);
        this.wkScdeductionitemMapper.update(wkScdeductionitemEntity);
        return this.getEntity(wkScdeductionitemEntity.getId(), wkScdeductionitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkScdeductionitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkScdeductionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkScdeductionitemPojo clearNull(WkScdeductionitemPojo wkScdeductionitemPojo) {
        //初始化NULL字段
        if (wkScdeductionitemPojo.getPid() == null) wkScdeductionitemPojo.setPid("");
        if (wkScdeductionitemPojo.getGoodsid() == null) wkScdeductionitemPojo.setGoodsid("");
        if (wkScdeductionitemPojo.getItemcode() == null) wkScdeductionitemPojo.setItemcode("");
        if (wkScdeductionitemPojo.getItemname() == null) wkScdeductionitemPojo.setItemname("");
        if (wkScdeductionitemPojo.getItemspec() == null) wkScdeductionitemPojo.setItemspec("");
        if (wkScdeductionitemPojo.getItemunit() == null) wkScdeductionitemPojo.setItemunit("");
        if (wkScdeductionitemPojo.getQuantity() == null) wkScdeductionitemPojo.setQuantity(0D);
        if (wkScdeductionitemPojo.getTaxprice() == null) wkScdeductionitemPojo.setTaxprice(0D);
        if (wkScdeductionitemPojo.getTaxamount() == null) wkScdeductionitemPojo.setTaxamount(0D);
        if (wkScdeductionitemPojo.getTaxtotal() == null) wkScdeductionitemPojo.setTaxtotal(0D);
        if (wkScdeductionitemPojo.getItemtaxrate() == null) wkScdeductionitemPojo.setItemtaxrate(0);
        if (wkScdeductionitemPojo.getPrice() == null) wkScdeductionitemPojo.setPrice(0D);
        if (wkScdeductionitemPojo.getAmount() == null) wkScdeductionitemPojo.setAmount(0D);
        if (wkScdeductionitemPojo.getRemark() == null) wkScdeductionitemPojo.setRemark("");
        if (wkScdeductionitemPojo.getCiteuid() == null) wkScdeductionitemPojo.setCiteuid("");
        if (wkScdeductionitemPojo.getCiteitemid() == null) wkScdeductionitemPojo.setCiteitemid("");
        if (wkScdeductionitemPojo.getOrderuid() == null) wkScdeductionitemPojo.setOrderuid("");
        if (wkScdeductionitemPojo.getOrderitemid() == null) wkScdeductionitemPojo.setOrderitemid("");
        if (wkScdeductionitemPojo.getCustpo() == null) wkScdeductionitemPojo.setCustpo("");
        if (wkScdeductionitemPojo.getRownum() == null) wkScdeductionitemPojo.setRownum(0);
        if (wkScdeductionitemPojo.getInvoqty() == null) wkScdeductionitemPojo.setInvoqty(0D);
        if (wkScdeductionitemPojo.getInvoclosed() == null) wkScdeductionitemPojo.setInvoclosed(0);
        if (wkScdeductionitemPojo.getDisannulmark() == null) wkScdeductionitemPojo.setDisannulmark(0);
        if (wkScdeductionitemPojo.getDisannullisterid() == null) wkScdeductionitemPojo.setDisannullisterid("");
        if (wkScdeductionitemPojo.getDisannullister() == null) wkScdeductionitemPojo.setDisannullister("");
        if (wkScdeductionitemPojo.getDisannuldate() == null) wkScdeductionitemPojo.setDisannuldate(new Date());
        if (wkScdeductionitemPojo.getCustom1() == null) wkScdeductionitemPojo.setCustom1("");
        if (wkScdeductionitemPojo.getCustom2() == null) wkScdeductionitemPojo.setCustom2("");
        if (wkScdeductionitemPojo.getCustom3() == null) wkScdeductionitemPojo.setCustom3("");
        if (wkScdeductionitemPojo.getCustom4() == null) wkScdeductionitemPojo.setCustom4("");
        if (wkScdeductionitemPojo.getCustom5() == null) wkScdeductionitemPojo.setCustom5("");
        if (wkScdeductionitemPojo.getCustom6() == null) wkScdeductionitemPojo.setCustom6("");
        if (wkScdeductionitemPojo.getCustom7() == null) wkScdeductionitemPojo.setCustom7("");
        if (wkScdeductionitemPojo.getCustom8() == null) wkScdeductionitemPojo.setCustom8("");
        if (wkScdeductionitemPojo.getCustom9() == null) wkScdeductionitemPojo.setCustom9("");
        if (wkScdeductionitemPojo.getCustom10() == null) wkScdeductionitemPojo.setCustom10("");
        if (wkScdeductionitemPojo.getTenantid() == null) wkScdeductionitemPojo.setTenantid("");
        if (wkScdeductionitemPojo.getRevision() == null) wkScdeductionitemPojo.setRevision(0);
        return wkScdeductionitemPojo;
    }
}
