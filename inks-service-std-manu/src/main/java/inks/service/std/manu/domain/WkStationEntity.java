package inks.service.std.manu.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 工位(WkStation)实体类
 *
 * <AUTHOR>
 * @since 2024-10-29 15:58:39
 */
public class WkStationEntity implements Serializable {
    private static final long serialVersionUID = -27474374017753000L;
     // id
    private String id;
     // 工段id
    private String sectionid;
     // 编码
    private String statcode;
     // 名称
    private String statname;
     // 类型
    private String stattype;
     // 工位描述
    private String statdesc;
     // 状态刷新H
    private Integer staterefresh;
     // 顺序
    private Integer rownum;
     // 备注
    private String summary;
     // 有效标识
    private Integer enabledmark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 删除标识
    private Integer deletemark;
     // 删除人员id
    private String deletelisterid;
     // 删除人员
    private String deletelister;
     // 删除日期
    private Date deletedate;
     // 禁止生产
    private Integer disablewip;
     // 产值工时
    private Double loadtime;
     // 产值数量
    private Double loadqty;
     // 产值金额
    private Double loadamt;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 工段id
    public String getSectionid() {
        return sectionid;
    }
    
    public void setSectionid(String sectionid) {
        this.sectionid = sectionid;
    }
        
   // 编码
    public String getStatcode() {
        return statcode;
    }
    
    public void setStatcode(String statcode) {
        this.statcode = statcode;
    }
        
   // 名称
    public String getStatname() {
        return statname;
    }
    
    public void setStatname(String statname) {
        this.statname = statname;
    }
        
   // 类型
    public String getStattype() {
        return stattype;
    }
    
    public void setStattype(String stattype) {
        this.stattype = stattype;
    }
        
   // 工位描述
    public String getStatdesc() {
        return statdesc;
    }
    
    public void setStatdesc(String statdesc) {
        this.statdesc = statdesc;
    }
        
   // 状态刷新H
    public Integer getStaterefresh() {
        return staterefresh;
    }
    
    public void setStaterefresh(Integer staterefresh) {
        this.staterefresh = staterefresh;
    }
        
   // 顺序
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
   // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 删除标识
    public Integer getDeletemark() {
        return deletemark;
    }
    
    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }
        
   // 删除人员id
    public String getDeletelisterid() {
        return deletelisterid;
    }
    
    public void setDeletelisterid(String deletelisterid) {
        this.deletelisterid = deletelisterid;
    }
        
   // 删除人员
    public String getDeletelister() {
        return deletelister;
    }
    
    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }
        
   // 删除日期
    public Date getDeletedate() {
        return deletedate;
    }
    
    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }
        
   // 禁止生产
    public Integer getDisablewip() {
        return disablewip;
    }
    
    public void setDisablewip(Integer disablewip) {
        this.disablewip = disablewip;
    }
        
   // 产值工时
    public Double getLoadtime() {
        return loadtime;
    }
    
    public void setLoadtime(Double loadtime) {
        this.loadtime = loadtime;
    }
        
   // 产值数量
    public Double getLoadqty() {
        return loadqty;
    }
    
    public void setLoadqty(Double loadqty) {
        this.loadqty = loadqty;
    }
        
   // 产值金额
    public Double getLoadamt() {
        return loadamt;
    }
    
    public void setLoadamt(Double loadamt) {
        this.loadamt = loadamt;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

