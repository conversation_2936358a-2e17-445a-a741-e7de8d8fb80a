package inks.service.std.manu.domain.pojo;

import com.alibaba.fastjson.JSONArray;

import java.io.Serializable;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 阶梯工价的Spus :根据前端传来的SpuJson转为List<WkSteppriceSpusPojo>
 * SpuJson格式为：[{"key":"spuchang","name":"长","value":"1000"},{"key":"spuhou","name":"厚","value":"10"}]
 *
 * <AUTHOR>
 * @since 2023-06-28 09:01:41
 */
public class WkSteppriceSpusPojo implements Serializable {
    public static void main(String[] args) {
        String spuJson = "[{\"key\":\"spuchang\",\"name\":\"长\",\"value\":\"1000\"},{\"key\":\"spuhou\",\"name\":\"厚\",\"value\":\"10\"}]";
        String spuJson2 = "[{\"key\":\"spuhou\",\"name\":\"厚\",\"value\":\"10\"},{\"key\":\"spuchang\",\"name\":\"长\",\"value\":\"1000\"}]";
        String spuJson3 = "[{\"key\":\"spuhou\",\"name\":\"厚\",\"startvalue\":\"10\",\"endvalue\":\"20\"},{\"key\":\"spuchang\",\"name\":\"长\",\"startvalue\":\"1000\",\"endvalue\":\"2000\"}]";
        List<WkSteppriceSpusPojo> spusList = JSONArray.parseArray(spuJson3, WkSteppriceSpusPojo.class);
        // 对 spusList 进行排序，按照 【key 的升序排列】
        spusList.sort(Comparator.comparing(WkSteppriceSpusPojo::getKey));
        //打印wkSteppriceSpusPojos里的每个字段
        for (WkSteppriceSpusPojo spu : spusList) {
            System.out.println("Key: " + spu.getKey());
            System.out.println("Name: " + spu.getName());
            System.out.println("StartValue: " + spu.getStartval());
            System.out.println("EndValue: " + spu.getEndval());
            System.out.println();
        }
        // Spujson转换为SpuValue【拼接Spujson里的key和value】
        // [{"key":"spuchang","name":"长","value":"1000"},{"key":"spuhou","name":"厚","value":"10"}] 转为 spuchang:1000,spuhou:10
        String spuValue = spusList.stream()
                .map(spu -> spu.getKey() + ":" + spu.getStartval()+"-"+spu.getEndval())
                .collect(Collectors.joining(","));
        System.out.println("spuValue = " + spuValue);
    }
    private static final long serialVersionUID = 569771107645243223L;
    //key，name,value

    private String key;

    private String name;

    private String startval;

    private String endval;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStartval() {
        return startval;
    }

    public void setStartval(String startval) {
        this.startval = startval;
    }

    public String getEndval() {
        return endval;
    }

    public void setEndval(String endval) {
        this.endval = endval;
    }
}

