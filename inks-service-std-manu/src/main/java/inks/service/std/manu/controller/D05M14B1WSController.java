package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkWscarryoverPojo;
import inks.service.std.manu.domain.pojo.WkWscarryoveritemPojo;
import inks.service.std.manu.service.WkWscarryoverService;
import inks.service.std.manu.service.WkWscarryoveritemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 车间结转(Wk_WsCarryover)表控制层
 *
 * <AUTHOR>
 * @since 2022-06-09 10:53:17
 */
@RestController
@RequestMapping("D05M14B1WS")
@Api(tags = "D05M14B1WS:车间结转")
public class D05M14B1WSController extends WkWscarryoverController {
    private final String moduleCode = "D05M14B1WS";
    /**
     * 服务对象
     */
    @Resource
    private WkWscarryoverService wkWscarryoverService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkWscarryoveritemService wkWscarryoveritemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    @ApiOperation(value = " 新增车间结转", notes = "新增车间结转", produces = "application/json")
    @RequestMapping(value = "/createCarry", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WsCarryover.Add")
    public R<WkWscarryoverPojo> createCarry(@RequestBody String json) {
        try {
            WkWscarryoverPojo matCarryoverPojo = JSONArray.parseObject(json, WkWscarryoverPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Wk_WsCarryover", null, loginUser.getTenantid());
            matCarryoverPojo.setRefno(refno);
            matCarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
            matCarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matCarryoverPojo.setCreatedate(new Date());   // 创建时间
            matCarryoverPojo.setLister(loginUser.getRealname());   // 制表
            matCarryoverPojo.setListerid(loginUser.getUserid());    // 制表id
            matCarryoverPojo.setModifydate(new Date());   //修改时间
            matCarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            WkWscarryoverPojo carry = this.wkWscarryoverService.createCarry(matCarryoverPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok(carry);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询Item", notes = "按条件分页查询Item", produces = "application/json")
    @RequestMapping(value = "/getItemPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WsCarryover.List")
    public R<PageInfo<WkWscarryoveritemPojo>> getItemPageList(@RequestBody String json, String key) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WsCarryoverItem.RowNum");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Wk_WsCarryoverItem.Pid='" + key + "'");
            return R.ok(this.wkWscarryoveritemService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getInitPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WsCarryover.List")
    public R<PageInfo<WkWscarryoverPojo>> getInitPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WsCarryover.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Wk_WsCarryover.BillType='期初建账'");
            return R.ok(this.wkWscarryoverService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WsCarryover.List")
    public R<PageInfo<WkWscarryoverPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WsCarryover.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWscarryoverService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
