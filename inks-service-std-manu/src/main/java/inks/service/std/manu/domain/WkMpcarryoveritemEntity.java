package inks.service.std.manu.domain;

import java.io.Serializable;

/**
 * 结转子表(WkMpcarryoveritem)Entity
 *
 * <AUTHOR>
 * @since 2022-06-09 09:43:09
 */
public class WkMpcarryoveritemEntity implements Serializable {
    private static final long serialVersionUID = 162953785127793650L;
          // id
         private String id;
          // Pid
         private String pid;
          // 商品ID
         private String goodsid;
          // 产品编码
         private String itemcode;
          // 产品名称
         private String itemname;
          // 产品规格
         private String itemspec;
          // 产品单位
         private String itemunit;
          // 期初数量
         private Double openqty;
          // 期初金额
         private Double openamount;
          // 入账数量
         private Double inqty;
          // 入账金额
         private Double inamount;
          // 出账数量
         private Double outqty;
          // 出账金额
         private Double outamount;
          // 期末数量
         private Double closeqty;
          // 期末金额
         private Double closeamount;
          // Skuid(备用)
         private String skuid;
          // 属性Josn
         private String attributejson;
          // 行号
         private Integer rownum;
          // 自定义1
         private String custom1;
          // 自定义2
         private String custom2;
          // 自定义3
         private String custom3;
          // 自定义4
         private String custom4;
          // 自定义5
         private String custom5;
          // 自定义6
         private String custom6;
          // 自定义7
         private String custom7;
          // 自定义8
         private String custom8;
          // 自定义9
         private String custom9;
          // 自定义10
         private String custom10;
          // 租户id
         private String tenantid;
          // 乐观锁
         private Integer revision;

    // id
      public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
    // Pid
      public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
    // 商品ID
      public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
    // 产品编码
      public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
    // 产品名称
      public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
    // 产品规格
      public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
    // 产品单位
      public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
    // 期初数量
      public Double getOpenqty() {
        return openqty;
    }
    
    public void setOpenqty(Double openqty) {
        this.openqty = openqty;
    }
        
    // 期初金额
      public Double getOpenamount() {
        return openamount;
    }
    
    public void setOpenamount(Double openamount) {
        this.openamount = openamount;
    }
        
    // 入账数量
      public Double getInqty() {
        return inqty;
    }
    
    public void setInqty(Double inqty) {
        this.inqty = inqty;
    }
        
    // 入账金额
      public Double getInamount() {
        return inamount;
    }
    
    public void setInamount(Double inamount) {
        this.inamount = inamount;
    }
        
    // 出账数量
      public Double getOutqty() {
        return outqty;
    }
    
    public void setOutqty(Double outqty) {
        this.outqty = outqty;
    }
        
    // 出账金额
      public Double getOutamount() {
        return outamount;
    }
    
    public void setOutamount(Double outamount) {
        this.outamount = outamount;
    }
        
    // 期末数量
      public Double getCloseqty() {
        return closeqty;
    }
    
    public void setCloseqty(Double closeqty) {
        this.closeqty = closeqty;
    }
        
    // 期末金额
      public Double getCloseamount() {
        return closeamount;
    }
    
    public void setCloseamount(Double closeamount) {
        this.closeamount = closeamount;
    }
        
    // Skuid(备用)
      public String getSkuid() {
        return skuid;
    }
    
    public void setSkuid(String skuid) {
        this.skuid = skuid;
    }
        
    // 属性Josn
      public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
    // 行号
      public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
    // 自定义1
      public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
    // 自定义2
      public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
    // 自定义3
      public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
    // 自定义4
      public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
    // 自定义5
      public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
    // 自定义6
      public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
    // 自定义7
      public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
    // 自定义8
      public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
    // 自定义9
      public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
    // 自定义10
      public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
    // 租户id
      public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
    // 乐观锁
      public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

