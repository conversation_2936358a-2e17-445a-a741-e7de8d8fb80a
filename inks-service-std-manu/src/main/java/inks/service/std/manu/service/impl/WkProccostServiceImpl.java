package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.manu.domain.WkProccostEntity;
import inks.service.std.manu.domain.pojo.WkProccostPojo;
import inks.service.std.manu.mapper.WkProccostMapper;
import inks.service.std.manu.mapper.WkProcessMapper;
import inks.service.std.manu.service.WkProccostService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 工序成本(WkProccost)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-25 14:02:32
 */
@Service("wkProccostService")
public class WkProccostServiceImpl implements WkProccostService {
    @Resource
    private WkProccostMapper wkProccostMapper;
    @Resource
    private WkProcessMapper wkProcessMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkProccostPojo getEntity(String key, String tid) {
        return this.wkProccostMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkProccostPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProccostPojo> lst = wkProccostMapper.getPageList(queryParam);
            PageInfo<WkProccostPojo> pageInfo = new PageInfo<WkProccostPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkProccostPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProccostPojo insert(WkProccostPojo wkProccostPojo) {
        //初始化NULL字段
        if (wkProccostPojo.getUnitprice() == null) wkProccostPojo.setUnitprice(0D);
        if (wkProccostPojo.getCreateby() == null) wkProccostPojo.setCreateby("");
        if (wkProccostPojo.getCreatebyid() == null) wkProccostPojo.setCreatebyid("");
        if (wkProccostPojo.getCreatedate() == null) wkProccostPojo.setCreatedate(new Date());
        if (wkProccostPojo.getLister() == null) wkProccostPojo.setLister("");
        if (wkProccostPojo.getListerid() == null) wkProccostPojo.setListerid("");
        if (wkProccostPojo.getModifydate() == null) wkProccostPojo.setModifydate(new Date());
        if (wkProccostPojo.getCustom1() == null) wkProccostPojo.setCustom1("");
        if (wkProccostPojo.getCustom2() == null) wkProccostPojo.setCustom2("");
        if (wkProccostPojo.getCustom3() == null) wkProccostPojo.setCustom3("");
        if (wkProccostPojo.getCustom4() == null) wkProccostPojo.setCustom4("");
        if (wkProccostPojo.getCustom5() == null) wkProccostPojo.setCustom5("");
        if (wkProccostPojo.getCustom6() == null) wkProccostPojo.setCustom6("");
        if (wkProccostPojo.getCustom7() == null) wkProccostPojo.setCustom7("");
        if (wkProccostPojo.getCustom8() == null) wkProccostPojo.setCustom8("");
        if (wkProccostPojo.getCustom9() == null) wkProccostPojo.setCustom9("");
        if (wkProccostPojo.getCustom10() == null) wkProccostPojo.setCustom10("");
        if (wkProccostPojo.getTenantid() == null) wkProccostPojo.setTenantid("");
        if (wkProccostPojo.getTenantname() == null) wkProccostPojo.setTenantname("");
        if (wkProccostPojo.getRevision() == null) wkProccostPojo.setRevision(0);
        WkProccostEntity wkProccostEntity = new WkProccostEntity();
        BeanUtils.copyProperties(wkProccostPojo, wkProccostEntity);
        wkProccostEntity.setRevision(1);  //乐观锁
        this.wkProccostMapper.insert(wkProccostEntity);
        return this.getEntity(wkProccostEntity.getWpid(), wkProccostEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkProccostPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProccostPojo update(WkProccostPojo wkProccostPojo) {
        WkProccostEntity wkProccostEntity = new WkProccostEntity();
        BeanUtils.copyProperties(wkProccostPojo, wkProccostEntity);
        this.wkProccostMapper.update(wkProccostEntity);
        return this.getEntity(wkProccostEntity.getWpid(), wkProccostEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkProccostMapper.delete(key, tid);
    }

    @Override
    public List<WkProccostPojo> getAllList(String tid) {
        try {
            List<WkProccostPojo> lst = wkProccostMapper.getAllList(tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public List<WkProccostPojo> updateList(List<WkProccostPojo> wkProccostList, LoginUser loginUser) {
        for (WkProccostPojo wkProccostPojo : wkProccostList) {
            wkProccostPojo.setTenantid(loginUser.getTenantid());
            wkProccostPojo.setModifydate(new Date());
            wkProccostPojo.setLister(loginUser.getRealname());
            wkProccostPojo.setListerid(loginUser.getUserid());
            // 如果数据库为空，插入成本表
            WkProccostPojo proccostPojoDB = getEntity(wkProccostPojo.getWpid(), loginUser.getTenantid());
            if (proccostPojoDB == null) {
                wkProccostPojo.setCreateby(loginUser.getRealname());
                wkProccostPojo.setCreatebyid(loginUser.getUserid());
                // 如果为空，新增
                this.insert(wkProccostPojo);
            } //如果不为空且价格改变，更新成本表
            else if (Double.compare(proccostPojoDB.getUnitprice(), wkProccostPojo.getUnitprice() == null ? 0D : wkProccostPojo.getUnitprice()) != 0) {
                this.update(wkProccostPojo);
            }
        }
        return getAllList(loginUser.getTenantid());
    }
}
