package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipnoteitemEntity;
import inks.service.std.manu.domain.pojo.WkWipnoteitemPojo;
import inks.service.std.manu.mapper.WkWipnoteitemMapper;
import inks.service.std.manu.service.WkWipnoteitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Wip记录子表(WkWipnoteitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-02 10:49:31
 */
@Service("wkWipnoteitemService")
public class WkWipnoteitemServiceImpl implements WkWipnoteitemService {
    @Resource
    private WkWipnoteitemMapper wkWipnoteitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipnoteitemPojo getEntity(String key, String tid) {
        return this.wkWipnoteitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipnoteitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipnoteitemPojo> lst = wkWipnoteitemMapper.getPageList(queryParam);
            PageInfo<WkWipnoteitemPojo> pageInfo = new PageInfo<WkWipnoteitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkWipnoteitemPojo> getList(String Pid, String tid) {
        try {
            List<WkWipnoteitemPojo> lst = wkWipnoteitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkWipnoteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipnoteitemPojo insert(WkWipnoteitemPojo wkWipnoteitemPojo) {
        //初始化item的NULL
        WkWipnoteitemPojo itempojo = this.clearNull(wkWipnoteitemPojo);
        WkWipnoteitemEntity wkWipnoteitemEntity = new WkWipnoteitemEntity();
        BeanUtils.copyProperties(itempojo, wkWipnoteitemEntity);

        wkWipnoteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipnoteitemEntity.setRevision(1);  //乐观锁
        this.wkWipnoteitemMapper.insert(wkWipnoteitemEntity);
        return this.getEntity(wkWipnoteitemEntity.getId(), wkWipnoteitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipnoteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipnoteitemPojo update(WkWipnoteitemPojo wkWipnoteitemPojo) {
        WkWipnoteitemEntity wkWipnoteitemEntity = new WkWipnoteitemEntity();
        BeanUtils.copyProperties(wkWipnoteitemPojo, wkWipnoteitemEntity);
        this.wkWipnoteitemMapper.update(wkWipnoteitemEntity);
        return this.getEntity(wkWipnoteitemEntity.getId(), wkWipnoteitemEntity.getTenantid());
    }

    @Override
    public int update3Field(WkWipnoteitemPojo wkWipnoteitemPojo) {
//        只update3个字段: outpcsqty, rownum, modifydate
        return this.wkWipnoteitemMapper.update3Field(wkWipnoteitemPojo);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWipnoteitemMapper.delete(key, tid);
    }

    @Override
    public int batchDeleteByIds(List<String> deletedIds) {
        return this.wkWipnoteitemMapper.batchDeleteByIds(deletedIds);
    }

    /**
     * 修改数据
     *
     * @param wkWipnoteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipnoteitemPojo clearNull(WkWipnoteitemPojo wkWipnoteitemPojo) {
        //初始化NULL字段
        if (wkWipnoteitemPojo.getPid() == null) wkWipnoteitemPojo.setPid("");
        if (wkWipnoteitemPojo.getWpid() == null) wkWipnoteitemPojo.setWpid("");
        if (wkWipnoteitemPojo.getWpcode() == null) wkWipnoteitemPojo.setWpcode("");
        if (wkWipnoteitemPojo.getWpname() == null) wkWipnoteitemPojo.setWpname("");
        if (wkWipnoteitemPojo.getRownum() == null) wkWipnoteitemPojo.setRownum(0);
        if (wkWipnoteitemPojo.getPlandate() == null) wkWipnoteitemPojo.setPlandate(new Date());
        if (wkWipnoteitemPojo.getRemark() == null) wkWipnoteitemPojo.setRemark("");
        if (wkWipnoteitemPojo.getInpcsqty() == null) wkWipnoteitemPojo.setInpcsqty(0D);
        if (wkWipnoteitemPojo.getInsecqty() == null) wkWipnoteitemPojo.setInsecqty(0D);
        if (wkWipnoteitemPojo.getOutpcsqty() == null) wkWipnoteitemPojo.setOutpcsqty(0D);
        if (wkWipnoteitemPojo.getOutsecqty() == null) wkWipnoteitemPojo.setOutsecqty(0D);
        if (wkWipnoteitemPojo.getMrbpcsqty() == null) wkWipnoteitemPojo.setMrbpcsqty(0D);
        if (wkWipnoteitemPojo.getMrbsecqty() == null) wkWipnoteitemPojo.setMrbsecqty(0D);
        if (wkWipnoteitemPojo.getComppcsqty() == null) wkWipnoteitemPojo.setComppcsqty(0D);
        if (wkWipnoteitemPojo.getCompsecqty() == null) wkWipnoteitemPojo.setCompsecqty(0D);
        if (wkWipnoteitemPojo.getSubqty() == null) wkWipnoteitemPojo.setSubqty(0D);
        if (wkWipnoteitemPojo.getSubunit() == null) wkWipnoteitemPojo.setSubunit("");
//        生产WIP新增时，开始日期和接受日期默认为空
//        if (wkWipnoteitemPojo.getStartdate() == null) wkWipnoteitemPojo.setStartdate(new Date());
//        if (wkWipnoteitemPojo.getEnddate() == null) wkWipnoteitemPojo.setEnddate(new Date());
        if (wkWipnoteitemPojo.getItemworker() == null) wkWipnoteitemPojo.setItemworker("");
        if (wkWipnoteitemPojo.getEpibolepcsqty() == null) wkWipnoteitemPojo.setEpibolepcsqty(0D);
        if (wkWipnoteitemPojo.getEpibolesecqty() == null) wkWipnoteitemPojo.setEpibolesecqty(0D);
        if (wkWipnoteitemPojo.getLastwp() == null) wkWipnoteitemPojo.setLastwp(0);
        if (wkWipnoteitemPojo.getSpecjson() == null) wkWipnoteitemPojo.setSpecjson("");
        if (wkWipnoteitemPojo.getSpecpackjson() == null) wkWipnoteitemPojo.setSpecpackjson("");
        if (wkWipnoteitemPojo.getWorkparam() == null) wkWipnoteitemPojo.setWorkparam("");
        if (wkWipnoteitemPojo.getLister() == null) wkWipnoteitemPojo.setLister("");
        if (wkWipnoteitemPojo.getCreatedate() == null) wkWipnoteitemPojo.setCreatedate(new Date());
        if (wkWipnoteitemPojo.getModifydate() == null) wkWipnoteitemPojo.setModifydate(new Date());
        if (wkWipnoteitemPojo.getStartplan() == null) wkWipnoteitemPojo.setStartplan(new Date());
        if (wkWipnoteitemPojo.getInspid() == null) wkWipnoteitemPojo.setInspid("");
        if (wkWipnoteitemPojo.getInspuid() == null) wkWipnoteitemPojo.setInspuid("");
        if (wkWipnoteitemPojo.getInspresult() == null) wkWipnoteitemPojo.setInspresult(0);
        if (wkWipnoteitemPojo.getDisablein() == null) wkWipnoteitemPojo.setDisablein(0);
        if (wkWipnoteitemPojo.getDisableout() == null) wkWipnoteitemPojo.setDisableout(0);
        if (wkWipnoteitemPojo.getInworker() == null) wkWipnoteitemPojo.setInworker("");
     if(wkWipnoteitemPojo.getOutworker()==null) wkWipnoteitemPojo.setOutworker("");
     if(wkWipnoteitemPojo.getTasksqty()==null) wkWipnoteitemPojo.setTasksqty(0D);
        if (wkWipnoteitemPojo.getCustom1() == null) wkWipnoteitemPojo.setCustom1("");
        if (wkWipnoteitemPojo.getCustom2() == null) wkWipnoteitemPojo.setCustom2("");
        if (wkWipnoteitemPojo.getCustom3() == null) wkWipnoteitemPojo.setCustom3("");
        if (wkWipnoteitemPojo.getCustom4() == null) wkWipnoteitemPojo.setCustom4("");
        if (wkWipnoteitemPojo.getCustom5() == null) wkWipnoteitemPojo.setCustom5("");
        if (wkWipnoteitemPojo.getCustom6() == null) wkWipnoteitemPojo.setCustom6("");
        if (wkWipnoteitemPojo.getCustom7() == null) wkWipnoteitemPojo.setCustom7("");
        if (wkWipnoteitemPojo.getCustom8() == null) wkWipnoteitemPojo.setCustom8("");
        if (wkWipnoteitemPojo.getCustom9() == null) wkWipnoteitemPojo.setCustom9("");
        if (wkWipnoteitemPojo.getCustom10() == null) wkWipnoteitemPojo.setCustom10("");
        if (wkWipnoteitemPojo.getTenantid() == null) wkWipnoteitemPojo.setTenantid("");
        if (wkWipnoteitemPojo.getRevision() == null) wkWipnoteitemPojo.setRevision(0);
        return wkWipnoteitemPojo;
    }
}
