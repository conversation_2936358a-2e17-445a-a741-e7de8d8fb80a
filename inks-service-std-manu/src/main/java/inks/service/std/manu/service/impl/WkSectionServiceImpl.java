package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSectionEntity;
import inks.service.std.manu.domain.WkSectionitemEntity;
import inks.service.std.manu.domain.pojo.WkSectionPojo;
import inks.service.std.manu.domain.pojo.WkSectionitemPojo;
import inks.service.std.manu.domain.pojo.WkSectionitemdetailPojo;
import inks.service.std.manu.mapper.WkSectionMapper;
import inks.service.std.manu.mapper.WkSectionitemMapper;
import inks.service.std.manu.mapper.WkStationMapper;
import inks.service.std.manu.service.WkSectionService;
import inks.service.std.manu.service.WkSectionitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 生产工段(WkSection)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-28 11:21:20
 */
@Service("wkSectionService")
public class WkSectionServiceImpl implements WkSectionService {
    @Resource
    private WkSectionMapper wkSectionMapper;

    @Resource
    private WkSectionitemMapper wkSectionitemMapper;


    @Resource
    private WkSectionitemService wkSectionitemService;
    @Resource
    private WkStationMapper wkStationMapper;

    @Override
    public WkSectionPojo getEntity(String key, String tid) {
        return this.wkSectionMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<WkSectionitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSectionitemdetailPojo> lst = wkSectionMapper.getPageList(queryParam);
            PageInfo<WkSectionitemdetailPojo> pageInfo = new PageInfo<WkSectionitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public WkSectionPojo getBillEntity(String key, String tid) {
       try {
           //读取主表
           WkSectionPojo wkSectionPojo = this.wkSectionMapper.getEntity(key,tid);
           //读取子表
           wkSectionPojo.setItem(wkSectionitemMapper.getList(wkSectionPojo.getId(),tid));
           return wkSectionPojo;
       }catch (Exception e){
          throw new BaseBusinessException(e.getMessage());
       }
    }


    @Override
    public PageInfo<WkSectionPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSectionPojo> lst = wkSectionMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(WkSectionPojo item : lst){
                item.setItem(wkSectionitemMapper.getList(item.getId(), tid));
            }
            PageInfo<WkSectionPojo> pageInfo = new PageInfo<WkSectionPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<WkSectionPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSectionPojo> lst = wkSectionMapper.getPageTh(queryParam);
            PageInfo<WkSectionPojo> pageInfo = new PageInfo<WkSectionPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public WkSectionPojo insert(WkSectionPojo wkSectionPojo) {
    String tid = wkSectionPojo.getTenantid();
        //初始化NULL字段
        cleanNull(wkSectionPojo);
         //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkSectionEntity wkSectionEntity = new WkSectionEntity();
        BeanUtils.copyProperties(wkSectionPojo,wkSectionEntity);

        //设置id和新建日期
        wkSectionEntity.setId(id);
        wkSectionEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkSectionMapper.insert(wkSectionEntity);
        //Item子表处理
        List<WkSectionitemPojo> lst = wkSectionPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(WkSectionitemPojo item : lst){
               //初始化item的NULL
               WkSectionitemPojo itemPojo =this.wkSectionitemService.clearNull(item);
               WkSectionitemEntity wkSectionitemEntity = new WkSectionitemEntity();
               BeanUtils.copyProperties(itemPojo,wkSectionitemEntity);
               //设置id和Pid
               wkSectionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               wkSectionitemEntity.setPid(id);
               wkSectionitemEntity.setTenantid(tid);
               wkSectionitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.wkSectionitemMapper.insert(wkSectionitemEntity);
            }

        }
        //返回Bill实例
        return this.getBillEntity(wkSectionEntity.getId(),tid);
    }


    @Override
    @Transactional
    public WkSectionPojo update(WkSectionPojo wkSectionPojo) {
        String tid = wkSectionPojo.getTenantid();
        //主表更改
        WkSectionEntity wkSectionEntity = new WkSectionEntity();
        BeanUtils.copyProperties(wkSectionPojo,wkSectionEntity);
        this.wkSectionMapper.update(wkSectionEntity);
        if (wkSectionPojo.getItem() != null) {
        //Item子表处理
        List<WkSectionitemPojo> lst = wkSectionPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =wkSectionMapper.getDelItemIds(wkSectionPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for (String delId : lstDelIds) {
             this.wkSectionitemMapper.delete(delId, tid);
            }
        }
        if (lst != null){
            //循环每个item子表
            for(WkSectionitemPojo item : lst){
               WkSectionitemEntity wkSectionitemEntity = new WkSectionitemEntity();
               if ("".equals(item.getId()) || item.getId() == null){
                //初始化item的NULL
               WkSectionitemPojo itemPojo =this.wkSectionitemService.clearNull(item);
               BeanUtils.copyProperties(itemPojo,wkSectionitemEntity);
               //设置id和Pid
               wkSectionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               wkSectionitemEntity.setPid(wkSectionEntity.getId());  // 主表 id
               wkSectionitemEntity.setTenantid(tid);   // 租户id
               wkSectionitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.wkSectionitemMapper.insert(wkSectionitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(item,wkSectionitemEntity);
                wkSectionitemEntity.setTenantid(tid);
               this.wkSectionitemMapper.update(wkSectionitemEntity);
               }
            }
        }
        }
        //返回Bill实例
        return this.getBillEntity(wkSectionEntity.getId(),tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
        List<String> lstcite = this.wkSectionMapper.getItemCiteBillName(key, tid);
        if (!lstcite.isEmpty()) {
            throw new BaseBusinessException("禁止删除,被以下单据引用:" + lstcite);
        }

       WkSectionPojo wkSectionPojo =  this.getBillEntity(key,tid);
        //Item子表处理
        List<WkSectionitemPojo> lst = wkSectionPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(WkSectionitemPojo item : lst){
              this.wkSectionitemMapper.delete(item.getId(),tid);
            }
        }
        return this.wkSectionMapper.delete(key,tid) ;
    }




    private static void cleanNull(WkSectionPojo wkSectionPojo) {
        if(wkSectionPojo.getSecttype()==null) wkSectionPojo.setSecttype("");
        if(wkSectionPojo.getSectcode()==null) wkSectionPojo.setSectcode("");
        if(wkSectionPojo.getSectname()==null) wkSectionPojo.setSectname("");
        if(wkSectionPojo.getFlowdesc()==null) wkSectionPojo.setFlowdesc("");
        if(wkSectionPojo.getSummary()==null) wkSectionPojo.setSummary("");
        if(wkSectionPojo.getEnabledmark()==null) wkSectionPojo.setEnabledmark(0);
        if(wkSectionPojo.getRownum()==null) wkSectionPojo.setRownum(0);
        if(wkSectionPojo.getCreateby()==null) wkSectionPojo.setCreateby("");
        if(wkSectionPojo.getCreatebyid()==null) wkSectionPojo.setCreatebyid("");
        if(wkSectionPojo.getCreatedate()==null) wkSectionPojo.setCreatedate(new Date());
        if(wkSectionPojo.getLister()==null) wkSectionPojo.setLister("");
        if(wkSectionPojo.getListerid()==null) wkSectionPojo.setListerid("");
        if(wkSectionPojo.getModifydate()==null) wkSectionPojo.setModifydate(new Date());
        if(wkSectionPojo.getItemcount()==null) wkSectionPojo.setItemcount(0);
        if(wkSectionPojo.getCustom1()==null) wkSectionPojo.setCustom1("");
        if(wkSectionPojo.getCustom2()==null) wkSectionPojo.setCustom2("");
        if(wkSectionPojo.getCustom3()==null) wkSectionPojo.setCustom3("");
        if(wkSectionPojo.getCustom4()==null) wkSectionPojo.setCustom4("");
        if(wkSectionPojo.getCustom5()==null) wkSectionPojo.setCustom5("");
        if(wkSectionPojo.getCustom6()==null) wkSectionPojo.setCustom6("");
        if(wkSectionPojo.getCustom7()==null) wkSectionPojo.setCustom7("");
        if(wkSectionPojo.getCustom8()==null) wkSectionPojo.setCustom8("");
        if(wkSectionPojo.getCustom9()==null) wkSectionPojo.setCustom9("");
        if(wkSectionPojo.getCustom10()==null) wkSectionPojo.setCustom10("");
        if(wkSectionPojo.getTenantid()==null) wkSectionPojo.setTenantid("");
        if(wkSectionPojo.getTenantname()==null) wkSectionPojo.setTenantname("");
        if(wkSectionPojo.getRevision()==null) wkSectionPojo.setRevision(0);
   }


   //获取所有生产工段及关联工位，若传入key工段id,则只返回该工段及关联工位
    @Override
    public List<WkSectionPojo> getSectionsAndStations(String key,String tid) {
        List<WkSectionPojo> lstSections = wkSectionMapper.getListAll(key,tid);
        for (WkSectionPojo SectionDB : lstSections) {
            SectionDB.setStations(wkStationMapper.getListBySectid(SectionDB.getId(),tid));
        }
        return lstSections;
    }
}
