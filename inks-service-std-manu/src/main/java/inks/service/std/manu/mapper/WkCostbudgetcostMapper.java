package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkCostbudgetcostEntity;
import inks.service.std.manu.domain.pojo.WkCostbudgetcostPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生成费用(WkCostbudgetcost)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-03 19:16:51
 */
 @Mapper
public interface WkCostbudgetcostMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCostbudgetcostPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkCostbudgetcostPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkCostbudgetcostPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkCostbudgetcostEntity 实例对象
     * @return 影响行数
     */
    int insert(WkCostbudgetcostEntity wkCostbudgetcostEntity);

    
    /**
     * 修改数据
     *
     * @param wkCostbudgetcostEntity 实例对象
     * @return 影响行数
     */
    int update(WkCostbudgetcostEntity wkCostbudgetcostEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

