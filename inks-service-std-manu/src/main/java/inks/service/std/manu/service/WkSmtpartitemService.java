package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSmtpartitemPojo;

import java.util.List;

/**
 * 上料表项目(WkSmtpartitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-12 14:04:21
 */
public interface WkSmtpartitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSmtpartitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSmtpartitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSmtpartitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkSmtpartitemPojo 实例对象
     * @return 实例对象
     */
    WkSmtpartitemPojo insert(WkSmtpartitemPojo wkSmtpartitemPojo);

    /**
     * 修改数据
     *
     * @param wkSmtpartitempojo 实例对象
     * @return 实例对象
     */
    WkSmtpartitemPojo update(WkSmtpartitemPojo wkSmtpartitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkSmtpartitempojo 实例对象
     * @return 实例对象
     */
    WkSmtpartitemPojo clearNull(WkSmtpartitemPojo wkSmtpartitempojo);
}
