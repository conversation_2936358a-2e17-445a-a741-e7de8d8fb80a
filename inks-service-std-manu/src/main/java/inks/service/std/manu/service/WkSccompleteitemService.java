package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSccompleteitemPojo;

import java.util.List;

/**
 * 验收项目(WkSccompleteitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-09 12:50:36
 */
public interface WkSccompleteitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccompleteitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSccompleteitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSccompleteitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkSccompleteitemPojo 实例对象
     * @return 实例对象
     */
    WkSccompleteitemPojo insert(WkSccompleteitemPojo wkSccompleteitemPojo);

    /**
     * 修改数据
     *
     * @param wkSccompleteitempojo 实例对象
     * @return 实例对象
     */
    WkSccompleteitemPojo update(WkSccompleteitemPojo wkSccompleteitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkSccompleteitempojo 实例对象
     * @return 实例对象
     */
    WkSccompleteitemPojo clearNull(WkSccompleteitemPojo wkSccompleteitempojo);
}
