package inks.service.std.manu.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipfinishPojo;
import inks.service.std.manu.domain.pojo.WkWipfinishitemdetailPojo;
import com.github.pagehelper.PageInfo;

/**
 * 完工表(WkWipfinish)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-06 14:33:54
 */
public interface WkWipfinishService {

    WkWipfinishPojo getEntity(String key,String tid);

    PageInfo<WkWipfinishitemdetailPojo> getPageList(QueryParam queryParam);

    WkWipfinishPojo getBillEntity(String key,String tid);

    PageInfo<WkWipfinishPojo> getBillList(QueryParam queryParam);

    PageInfo<WkWipfinishPojo> getPageTh(QueryParam queryParam);

    WkWipfinishPojo insert(WkWipfinishPojo wkWipfinishPojo);

    WkWipfinishPojo update(WkWipfinishPojo wkWipfinishpojo);

    int delete(String key,String tid);

}
