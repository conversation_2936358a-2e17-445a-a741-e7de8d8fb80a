package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSectionitemPojo;
import inks.service.std.manu.domain.WkSectionitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 生产工段工序子表(WkSectionitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-28 11:21:43
 */
 @Mapper
public interface WkSectionitemMapper {

    WkSectionitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<WkSectionitemPojo> getPageList(QueryParam queryParam);

    List<WkSectionitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(WkSectionitemEntity wkSectionitemEntity);

    int update(WkSectionitemEntity wkSectionitemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

}

