package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSccompletematPojo;

import java.util.List;

/**
 * 验收物料(WkSccompletemat)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-09 12:50:37
 */
public interface WkSccompletematService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccompletematPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSccompletematPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSccompletematPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkSccompletematPojo 实例对象
     * @return 实例对象
     */
    WkSccompletematPojo insert(WkSccompletematPojo wkSccompletematPojo);

    /**
     * 修改数据
     *
     * @param wkSccompletematpojo 实例对象
     * @return 实例对象
     */
    WkSccompletematPojo update(WkSccompletematPojo wkSccompletematpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkSccompletematpojo 实例对象
     * @return 实例对象
     */
    WkSccompletematPojo clearNull(WkSccompletematPojo wkSccompletematpojo);
}
