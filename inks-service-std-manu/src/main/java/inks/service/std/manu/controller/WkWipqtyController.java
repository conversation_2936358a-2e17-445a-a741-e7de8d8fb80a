package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkWipqtyPojo;
import inks.service.std.manu.service.WkWipqtyService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 生产过数(Wk_WipQty)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:51:53
 */

public class WkWipqtyController {
    /**
     * 服务对象
     */
    @Resource
    private WkWipqtyService wkWipqtyService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    @Resource
    private UtilsFeignService utilsFeignService;
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取生产过数详细信息", notes = "获取生产过数详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<WkWipqtyPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkWipqtyService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询 wpid为工序 dir=in 入组 out 出组", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<PageInfo<WkWipqtyPojo>> getPageList(@RequestBody String json, String wpid, String dir) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQty.CreateDate");

            String qpfilter = "";
            if (wpid != null) {
                qpfilter += " and Wk_WipQty.wpid='" + wpid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWipqtyService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增生产过数", notes = "新增生产过数", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.Add")
    public R<WkWipqtyPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkWipqtyPojo wkWipqtyPojo = JSONArray.parseObject(json, WkWipqtyPojo.class);
            wkWipqtyPojo.setCreateby(loginUser.getRealname());   // 创建者
            wkWipqtyPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkWipqtyPojo.setCreatedate(new Date());   // 创建时间
            wkWipqtyPojo.setLister(loginUser.getRealname());   // 制表
            wkWipqtyPojo.setListerid(loginUser.getUserid());    // 制表id  
            wkWipqtyPojo.setModifydate(new Date());   //修改时间
            wkWipqtyPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.wkWipqtyService.insert(wkWipqtyPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改生产过数", notes = "修改生产过数", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.Edit")
    public R<WkWipqtyPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkWipqtyPojo wkWipqtyPojo = JSONArray.parseObject(json, WkWipqtyPojo.class);
            wkWipqtyPojo.setLister(loginUser.getRealname());   // 制表
            wkWipqtyPojo.setListerid(loginUser.getUserid());    // 制表id  
            wkWipqtyPojo.setTenantid(loginUser.getTenantid());   //租户id
            wkWipqtyPojo.setModifydate(new Date());   //修改时间
//            wkWipqtyPojo.setAssessor(""); // 审核员
//            wkWipqtyPojo.setAssessorid(""); // 审核员id
//            wkWipqtyPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.wkWipqtyService.update(wkWipqtyPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除生产过数", notes = "删除生产过数", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipQty.Delete")
    @OperLog(title = "删除生产过数")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkWipqtyPojo deleteWipQtyPojo = this.wkWipqtyService.delete(key, loginUser.getTenantid());
            // 返回workuid，wpname，direction，goodsuid，goodsname，pcsqty，id，refno
            String operLog = "workuid:" + deleteWipQtyPojo.getWpid() + ",wpname:" + deleteWipQtyPojo.getWpname() + ",direction:" + deleteWipQtyPojo.getDirection() + ",goodsuid:" + deleteWipQtyPojo.getGoodsuid() + ",goodsname:" + deleteWipQtyPojo.getGoodsname() + ",pcsqty:" + deleteWipQtyPojo.getPcsqty() + ",id:" + deleteWipQtyPojo.getId() + ",refno:" + deleteWipQtyPojo.getRefno();
            return R.ok(1, operLog);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipQty.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        WkWipqtyPojo wkWipqtyPojo = this.wkWipqtyService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(wkWipqtyPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印销售订单明细报表", notes = "打印销售订单明细报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printPageList(@RequestBody String json, String groupid, String ptid, String wpid, String dir) throws IOException, JRException {

        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Wk_WipQty.WkDate");
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Wk_WipQty.MachGroupid='" + groupid + "'";
        }

        if (wpid != null) {
            qpfilter += " and Wk_WipQty.wpid='" + wpid + "'";
        }
        if (dir != null) {
            if (dir.equals("in")) {
                qpfilter += " and Wk_WipQty.direction='入组'";
            } else {
                qpfilter += " and Wk_WipQty.direction='出组'";
            }
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<WkWipqtyPojo> lst = this.wkWipqtyService.getPageList(queryParam).getList();

        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }

        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkWipqtyPojo wkWipnoteitemPojo = new WkWipqtyPojo();
                    lst.add(wkWipnoteitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "分页云打印订单明细", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    public R<String> printWebPageList(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis, String wpid, String dir) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQty.WkDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Wk_WipQty.MachGroupid='" + groupid + "'";
            }
            if (wpid != null) {
                qpfilter += " and Wk_WipQty.wpid='" + wpid + "'";
            }
            if (dir != null) {
                if (dir.equals("in")) {
                    qpfilter += " and Wk_WipQty.direction='入组'";
                } else {
                    qpfilter += " and Wk_WipQty.direction='出组'";
                }
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<WkWipqtyPojo> lstitem = this.wkWipqtyService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "过数明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "批量打印单据,传入ids", notes = "json={KEY,KEY},ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBatchBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.Print")
    public void printBatchBill(@RequestBody String json, String ptid) throws IOException, JRException {
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            String content;
            if (reportsPojo != null) {
                content = reportsPojo.getRptdata();
            } else {
                throw new BaseBusinessException("未找到报表");
            }
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");

            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            //数据填充
            JasperPrint printAll = new JasperPrint();
            for (int a = 0; a < lstkeys.size(); a++) {
                String key = lstkeys.get(a);
                //=========获取单据表头信息========
                WkWipqtyPojo pojo = this.wkWipqtyService.getEntity(key, loginUser.getTenantid());
                if (pojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
                //表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(pojo);
                // 加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
//                // 判定是否需要追行
//                if (reportsPojo.getPagerow() > 0) {
//                    int index = 0;
//                    // 取行余数
//                    index = pojo.getItem().size() % reportsPojo.getPagerow();
//                    if (index > 0) {
//                        // 补全空白行
//                        for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
//                            WkWipqtyitemPojo itemPojo = new WkWipqtyitemPojo();
//                            pojo.getItem().add(itemPojo);
//                        }
//                    }
//                }
//
//                // 带属性List转为Map  EricRen 20220427
//                List<Map<String, Object>> lst = BeanUtils.attrcostListToMaps(pojo.getItem());
//                //item转数据源
//                JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);


                //报表生成
                InputStream stream = new ByteArrayInputStream(content.getBytes());
                //编译报表
                JasperReport jasperReport = JasperCompileManager.compileReport(stream);

                //数据填充
                JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
                if (a == 0) {
                    printAll = print;
                } else {
                    List<JRPrintPage> pages = print.getPages();
                    for (JRPrintPage page : pages) {
                        printAll.addPage(page);
                    }
                }
            }
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(printAll, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "批量云打印WkWipqty单据(ids)", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printBatchWebBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.Print")
    public R<String> printBatchWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            for (String key : lstkeys) {
                //=========获取单据表头信息========
                WkWipqtyPojo wkWipqtyPojo = this.wkWipqtyService.getEntity(key, loginUser.getTenantid());
                if (wkWipqtyPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }

                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(wkWipqtyPojo);

                // 获取单据表头.加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
//                        //=========获取单据Item信息========
//                        List<WkWipqtyitemPojo> lstitem = this.wkWipqtyitemService.getList(key, loginUser.getTenantid());
//                        // 单据Item. 带属性List转为Map  EricRen 20220427
//                        List<Map<String, Object>> lst = BeanUtils.attrcostListToMaps(lstitem);
                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
//                        maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);
                ptRefNoMain += wkWipqtyPojo.getRefno() + ",";

            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "WkWipqty：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

