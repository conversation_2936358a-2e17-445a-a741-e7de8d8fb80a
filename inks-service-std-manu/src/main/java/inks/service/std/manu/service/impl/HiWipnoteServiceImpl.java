package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.service.std.manu.domain.HiWipnoteEntity;
import inks.service.std.manu.domain.HiWipnoteitemEntity;
import inks.service.std.manu.domain.constant.MyConstant;
import inks.service.std.manu.domain.pojo.HiWipnotePojo;
import inks.service.std.manu.domain.pojo.HiWipnoteitemPojo;
import inks.service.std.manu.domain.pojo.HiWipnoteitemdetailPojo;
import inks.service.std.manu.mapper.HiWipnoteMapper;
import inks.service.std.manu.mapper.HiWipnoteitemMapper;
import inks.service.std.manu.service.HiWipnoteService;
import inks.service.std.manu.service.HiWipnoteitemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * WIP记录(HiWipnote)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-16 16:19:17
 */
@Service("hiWipnoteService")
public class HiWipnoteServiceImpl implements HiWipnoteService {
    @Resource
    private HiWipnoteMapper hiWipnoteMapper;

    @Resource
    private HiWipnoteitemMapper hiWipnoteitemMapper;
    private final static Logger log = LoggerFactory.getLogger(HiWipnoteServiceImpl.class);

    /**
     * 服务对象Item
     */
    @Resource
    private HiWipnoteitemService hiWipnoteitemService;

    @Resource
    private RedisService redisService;
    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public HiWipnotePojo getEntity(String key, String tid) {
        return this.hiWipnoteMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<HiWipnoteitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<HiWipnoteitemdetailPojo> lst = hiWipnoteMapper.getPageList(queryParam);
            PageInfo<HiWipnoteitemdetailPojo> pageInfo = new PageInfo<HiWipnoteitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public HiWipnotePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            HiWipnotePojo hiWipnotePojo = this.hiWipnoteMapper.getEntity(key, tid);
            //读取子表
            hiWipnotePojo.setItem(hiWipnoteitemMapper.getList(hiWipnotePojo.getId(), hiWipnotePojo.getTenantid()));
            return hiWipnotePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<HiWipnotePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<HiWipnotePojo> lst = hiWipnoteMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(hiWipnoteitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<HiWipnotePojo> pageInfo = new PageInfo<HiWipnotePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<HiWipnotePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<HiWipnotePojo> lst = hiWipnoteMapper.getPageTh(queryParam);
            PageInfo<HiWipnotePojo> pageInfo = new PageInfo<HiWipnotePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param hiWipnotePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public HiWipnotePojo insert(HiWipnotePojo hiWipnotePojo) {
//初始化NULL字段
        if (hiWipnotePojo.getRefno() == null) hiWipnotePojo.setRefno("");
        if (hiWipnotePojo.getBilldate() == null) hiWipnotePojo.setBilldate(new Date());
        if (hiWipnotePojo.getWorktype() == null) hiWipnotePojo.setWorktype("");
        if (hiWipnotePojo.getWorkshopid() == null) hiWipnotePojo.setWorkshopid("");
        if (hiWipnotePojo.getWorkshop() == null) hiWipnotePojo.setWorkshop("");
        if (hiWipnotePojo.getGoodsid() == null) hiWipnotePojo.setGoodsid("");
        if (hiWipnotePojo.getPlandate() == null) hiWipnotePojo.setPlandate(new Date());
        if (hiWipnotePojo.getQuantity() == null) hiWipnotePojo.setQuantity(0D);
        if (hiWipnotePojo.getWkpcsqty() == null) hiWipnotePojo.setWkpcsqty(0D);
        if (hiWipnotePojo.getWksecqty() == null) hiWipnotePojo.setWksecqty(0D);
        if (hiWipnotePojo.getMrbpcsqty() == null) hiWipnotePojo.setMrbpcsqty(0D);
        if (hiWipnotePojo.getMrbsecqty() == null) hiWipnotePojo.setMrbsecqty(0D);
        if (hiWipnotePojo.getSupplement() == null) hiWipnotePojo.setSupplement(0);
        if (hiWipnotePojo.getCreateby() == null) hiWipnotePojo.setCreateby("");
        if (hiWipnotePojo.getCreatebyid() == null) hiWipnotePojo.setCreatebyid("");
        if (hiWipnotePojo.getCreatedate() == null) hiWipnotePojo.setCreatedate(new Date());
        if (hiWipnotePojo.getLister() == null) hiWipnotePojo.setLister("");
        if (hiWipnotePojo.getListerid() == null) hiWipnotePojo.setListerid("");
        if (hiWipnotePojo.getModifydate() == null) hiWipnotePojo.setModifydate(new Date());
        if (hiWipnotePojo.getStatecode() == null) hiWipnotePojo.setStatecode("");
        if (hiWipnotePojo.getStatedate() == null) hiWipnotePojo.setStatedate(new Date());
        if (hiWipnotePojo.getWkwpid() == null) hiWipnotePojo.setWkwpid("");
        if (hiWipnotePojo.getWkwpcode() == null) hiWipnotePojo.setWkwpcode("");
        if (hiWipnotePojo.getWkwpname() == null) hiWipnotePojo.setWkwpname("");
        if (hiWipnotePojo.getWkrownum() == null) hiWipnotePojo.setWkrownum(0);
        if (hiWipnotePojo.getCustomer() == null) hiWipnotePojo.setCustomer("");
        if (hiWipnotePojo.getCustpo() == null) hiWipnotePojo.setCustpo("");
        if (hiWipnotePojo.getMachuid() == null) hiWipnotePojo.setMachuid("");
        if (hiWipnotePojo.getMachitemid() == null) hiWipnotePojo.setMachitemid("");
        if (hiWipnotePojo.getMachgroupid() == null) hiWipnotePojo.setMachgroupid("");
        if (hiWipnotePojo.getMainplanuid() == null) hiWipnotePojo.setMainplanuid("");
        if (hiWipnotePojo.getMainplanitemid() == null) hiWipnotePojo.setMainplanitemid("");
        if (hiWipnotePojo.getWorkuid() == null) hiWipnotePojo.setWorkuid("");
        if (hiWipnotePojo.getWorkrefno() == null) hiWipnotePojo.setWorkrefno("");
        if (hiWipnotePojo.getWorkrownum() == null) hiWipnotePojo.setWorkrownum(0);
        if (hiWipnotePojo.getWorkitemid() == null) hiWipnotePojo.setWorkitemid("");
        if (hiWipnotePojo.getSubstwpid() == null) hiWipnotePojo.setSubstwpid("");
        if (hiWipnotePojo.getSubstwpcode() == null) hiWipnotePojo.setSubstwpcode("");
        if (hiWipnotePojo.getSubstwpname() == null) hiWipnotePojo.setSubstwpname("");
        if (hiWipnotePojo.getSubendwpid() == null) hiWipnotePojo.setSubendwpid("");
        if (hiWipnotePojo.getSubendwpcode() == null) hiWipnotePojo.setSubendwpcode("");
        if (hiWipnotePojo.getSubendwpname() == null) hiWipnotePojo.setSubendwpname("");
        if (hiWipnotePojo.getSubuid() == null) hiWipnotePojo.setSubuid("");
        if (hiWipnotePojo.getWorkdate() == null) hiWipnotePojo.setWorkdate(new Date());
        if (hiWipnotePojo.getCompwpid() == null) hiWipnotePojo.setCompwpid("");
        if (hiWipnotePojo.getCompwpcode() == null) hiWipnotePojo.setCompwpcode("");
        if (hiWipnotePojo.getCompwpname() == null) hiWipnotePojo.setCompwpname("");
        if (hiWipnotePojo.getComppcsqty() == null) hiWipnotePojo.setComppcsqty(0D);
        if (hiWipnotePojo.getWipgroupid() == null) hiWipnotePojo.setWipgroupid("");
        if (hiWipnotePojo.getSummary() == null) hiWipnotePojo.setSummary("");
        if (hiWipnotePojo.getAttributejson() == null) hiWipnotePojo.setAttributejson("");
        if (hiWipnotePojo.getAttributestr() == null) hiWipnotePojo.setAttributestr("");
        if (hiWipnotePojo.getMatcode() == null) hiWipnotePojo.setMatcode("");
        if (hiWipnotePojo.getMatused() == null) hiWipnotePojo.setMatused(0);
        if (hiWipnotePojo.getWkspecjson() == null) hiWipnotePojo.setWkspecjson("");
        if (hiWipnotePojo.getItemcount() == null) hiWipnotePojo.setItemcount(0);
        if (hiWipnotePojo.getFinishcount() == null) hiWipnotePojo.setFinishcount(0);
        if (hiWipnotePojo.getPrintcount() == null) hiWipnotePojo.setPrintcount(0);
        if (hiWipnotePojo.getColorlevel() == null) hiWipnotePojo.setColorlevel("");
        if (hiWipnotePojo.getSizex() == null) hiWipnotePojo.setSizex(0D);
        if (hiWipnotePojo.getSizey() == null) hiWipnotePojo.setSizey(0D);
        if (hiWipnotePojo.getSizez() == null) hiWipnotePojo.setSizez(0D);
        if (hiWipnotePojo.getClosed() == null) hiWipnotePojo.setClosed(0);
        if (hiWipnotePojo.getDisannullisterid() == null) hiWipnotePojo.setDisannullisterid("");
        if (hiWipnotePojo.getDisannullister() == null) hiWipnotePojo.setDisannullister("");
        if (hiWipnotePojo.getDisannuldate() == null) hiWipnotePojo.setDisannuldate(new Date());
        if (hiWipnotePojo.getDisannulmark() == null) hiWipnotePojo.setDisannulmark(0);
        if (hiWipnotePojo.getMergemark() == null) hiWipnotePojo.setMergemark(0);
        if (hiWipnotePojo.getSourcetype() == null) hiWipnotePojo.setSourcetype(0);
//        if (hiWipnotePojo.getItemjson() == null) hiWipnotePojo.setItemjson("");
//        if (hiWipnotePojo.getIsolation() == null) hiWipnotePojo.setIsolation(0);
        if (hiWipnotePojo.getExponent() == null) hiWipnotePojo.setExponent(0);
        if (hiWipnotePojo.getJobpcsqty() == null) hiWipnotePojo.setJobpcsqty(0D);
        if (hiWipnotePojo.getJobsecqty() == null) hiWipnotePojo.setJobsecqty(0D);
        if (hiWipnotePojo.getCustom1() == null) hiWipnotePojo.setCustom1("");
        if (hiWipnotePojo.getCustom2() == null) hiWipnotePojo.setCustom2("");
        if (hiWipnotePojo.getCustom3() == null) hiWipnotePojo.setCustom3("");
        if (hiWipnotePojo.getCustom4() == null) hiWipnotePojo.setCustom4("");
        if (hiWipnotePojo.getCustom5() == null) hiWipnotePojo.setCustom5("");
        if (hiWipnotePojo.getCustom6() == null) hiWipnotePojo.setCustom6("");
        if (hiWipnotePojo.getCustom7() == null) hiWipnotePojo.setCustom7("");
        if (hiWipnotePojo.getCustom8() == null) hiWipnotePojo.setCustom8("");
        if (hiWipnotePojo.getCustom9() == null) hiWipnotePojo.setCustom9("");
        if (hiWipnotePojo.getCustom10() == null) hiWipnotePojo.setCustom10("");
        if (hiWipnotePojo.getTenantid() == null) hiWipnotePojo.setTenantid("");
        if (hiWipnotePojo.getTenantname() == null) hiWipnotePojo.setTenantname("");
        if (hiWipnotePojo.getRevision() == null) hiWipnotePojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        HiWipnoteEntity hiWipnoteEntity = new HiWipnoteEntity();
        BeanUtils.copyProperties(hiWipnotePojo, hiWipnoteEntity);

        //设置id和新建日期
        hiWipnoteEntity.setId(id);
        hiWipnoteEntity.setRevision(1);  //乐观锁
        //插入主表
        this.hiWipnoteMapper.insert(hiWipnoteEntity);
        //Item子表处理
        List<HiWipnoteitemPojo> lst = hiWipnotePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                HiWipnoteitemPojo itemPojo = this.hiWipnoteitemService.clearNull(lst.get(i));
                HiWipnoteitemEntity hiWipnoteitemEntity = new HiWipnoteitemEntity();
                BeanUtils.copyProperties(itemPojo, hiWipnoteitemEntity);
                //设置id和Pid
                hiWipnoteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                hiWipnoteitemEntity.setPid(id);
                hiWipnoteitemEntity.setTenantid(hiWipnotePojo.getTenantid());
                hiWipnoteitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.hiWipnoteitemMapper.insert(hiWipnoteitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(hiWipnoteEntity.getId(), hiWipnoteEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param hiWipnotePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public HiWipnotePojo update(HiWipnotePojo hiWipnotePojo) {
        //主表更改
        HiWipnoteEntity hiWipnoteEntity = new HiWipnoteEntity();
        BeanUtils.copyProperties(hiWipnotePojo, hiWipnoteEntity);
        this.hiWipnoteMapper.update(hiWipnoteEntity);
        if (hiWipnotePojo.getItem() != null) {
            //Item子表处理
            List<HiWipnoteitemPojo> lst = hiWipnotePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = hiWipnoteMapper.getDelItemIds(hiWipnotePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.hiWipnoteitemMapper.delete(lstDelId, hiWipnoteEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (HiWipnoteitemPojo hiWipnoteitemPojo : lst) {
                    HiWipnoteitemEntity hiWipnoteitemEntity = new HiWipnoteitemEntity();
                    if ("".equals(hiWipnoteitemPojo.getId()) || hiWipnoteitemPojo.getId() == null) {
                        //初始化item的NULL
                        HiWipnoteitemPojo itemPojo = this.hiWipnoteitemService.clearNull(hiWipnoteitemPojo);
                        BeanUtils.copyProperties(itemPojo, hiWipnoteitemEntity);
                        //设置id和Pid
                        hiWipnoteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        hiWipnoteitemEntity.setPid(hiWipnoteEntity.getId());  // 主表 id
                        hiWipnoteitemEntity.setTenantid(hiWipnotePojo.getTenantid());   // 租户id
                        hiWipnoteitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.hiWipnoteitemMapper.insert(hiWipnoteitemEntity);
                    } else {
                        BeanUtils.copyProperties(hiWipnoteitemPojo, hiWipnoteitemEntity);
                        hiWipnoteitemEntity.setTenantid(hiWipnotePojo.getTenantid());
                        this.hiWipnoteitemMapper.update(hiWipnoteitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(hiWipnoteEntity.getId(), hiWipnoteEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        HiWipnotePojo hiWipnotePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<HiWipnoteitemPojo> lst = hiWipnotePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (HiWipnoteitemPojo hiWipnoteitemPojo : lst) {
                this.hiWipnoteitemMapper.delete(hiWipnoteitemPojo.getId(), tid);
            }
        }
        return this.hiWipnoteMapper.delete(key, tid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String moveNowToHi(String startdate, String enddate, String tid) {
        // 方法加锁
        String lockKey = "wip_moveNowToHi_lock:" + tid;
        Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 20, TimeUnit.MINUTES);
        if (!Boolean.TRUE.equals(locked)) {
            throw new BaseBusinessException("已有迁移任务正在执行，请稍后再试");
        }
        Map<String, Object> progress = new HashMap<>();
        try {
            // 添加表结构验证：在迁移前检查源表和目标表的字段名、类型及顺序是否一致
            validateTableStructure("Wk_WipNote", "Hi_WipNote");
            validateTableStructure("Wk_WipNoteItem", "Hi_WipNoteItem");

            List<String> onlineIds = hiWipnoteMapper.getOnlineNowWipNoteIds(startdate, enddate, tid);
            List<String> allIds = hiWipnoteMapper.getAllMigratableNowIds(startdate, enddate, onlineIds, tid);

            // 记录迁移进度到 Redis
            progress.put("total", allIds.size());
            progress.put("finish", 0);
            redisService.setCacheObject(MyConstant.WIP_NOW_TO_HI + tid, progress, 20L, TimeUnit.SECONDS);

            // 设置批处理大小：每次迁移100条WIP主表及关联的X条子表数据
            int batchSize = 100;
            int mainCount = 0, itemCount = 0;
            for (int i = 0; i < allIds.size(); i += batchSize) {
                int end = Math.min(i + batchSize, allIds.size());
                List<String> batch = allIds.subList(i, end);

                int m1 = hiWipnoteMapper.copyNowToHiByIds(batch, tid);
                int it1 = hiWipnoteMapper.copyItemNowToHiByIds(batch, tid);
                int m2 = hiWipnoteMapper.deleteNowByIds(batch, tid);
                int it2 = hiWipnoteMapper.deleteItemNowByIds(batch, tid);

                if (m1 != m2 || it1 != it2) {
                    throw new BaseBusinessException("主/子表批次迁移条数不一致，触发回滚");
                }

                mainCount += m1;
                itemCount += it1;
                progress.put("finish", end);
                progress.put("msg", String.format("迁移进度：%d/%d, 总共迁移主表 %d 条，子表 %d 条", end, allIds.size(), mainCount, itemCount));
                redisService.setCacheObject(MyConstant.WIP_NOW_TO_HI + tid, progress, 20L, TimeUnit.SECONDS);

                log.info("迁移进度：{}/{}，本批次主表{}条、子表{}条", end, allIds.size(), m1, it1);
            }

            return String.format("成功迁移主表 %d 条，子表 %d 条", mainCount, itemCount);

        } catch (Exception ex) {
            // 记录错误信息到 Redis
            // 可以存完整 StackTrace，也可以存简要的 ex.getMessage()
            progress.put("error", ex.getMessage());
            redisService.setCacheObject(MyConstant.WIP_NOW_TO_HI + tid, progress, 20L, TimeUnit.SECONDS);
            // 继续抛出，触发事务回滚
            throw ex;
        } finally {
            // 释放方法锁
            redisTemplate.delete(lockKey);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String moveHiToNow(String startdate, String enddate, List<String> ids, String tid) {
        // 方法加锁
        String lockKey = "wip_moveHiToNow_lock:" + tid;
        Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 20, TimeUnit.MINUTES);
        if (!Boolean.TRUE.equals(locked)) {
            throw new BaseBusinessException("已有迁移任务正在执行，请稍后再试");
        }
        Map<String, Object> progress = new HashMap<>();
        String progressKey = null;
        try {
            // 添加表结构验证：在迁移前检查源表和目标表的字段名、类型及顺序是否一致
            validateTableStructure("Wk_WipNote", "Hi_WipNote");
            validateTableStructure("Wk_WipNoteItem", "Hi_WipNoteItem");

            // 如果未指定IDs，则获取符合时间范围的所有ID
            if (ids == null || ids.isEmpty()) {
                ids = hiWipnoteMapper.getAllMigratableHiIds(startdate, enddate, tid);
            }

            // 记录迁移进度到 Redis
            progressKey = MyConstant.WIP_HI_TO_NOW + tid;
            progress.put("total", ids.size());
            progress.put("finish", 0);
            redisService.setCacheObject(progressKey, progress, 20L, TimeUnit.SECONDS);

            // 设置批处理大小：每次迁移100条WIP主表及关联的X条子表数据
            int batchSize = 100;
            int totalMain = 0, totalItem = 0;

            for (int i = 0; i < ids.size(); i += batchSize) {
                int end = Math.min(i + batchSize, ids.size());
                List<String> batchIds = ids.subList(i, end);

                // 复制数据到 Now 表
                int m1 = hiWipnoteMapper.copyHiToNowByIds(batchIds, tid);
                int it1 = hiWipnoteMapper.copyItemHiToNowByIds(batchIds, tid);
                // 从 Hi 表删除已迁移数据
                int m2 = hiWipnoteMapper.deleteHiByIds(batchIds, tid);
                int it2 = hiWipnoteMapper.deleteItemHiByIds(batchIds, tid);

                // 校验本批次主/子表迁移条数是否一致
                if (m1 != m2 || it1 != it2) {
                    throw new BaseBusinessException("主/子表批次迁移条数不一致，触发回滚");
                }

                totalMain += m1;
                totalItem += it1;
                // 更新进度到 Redis
                progress.put("finish", end);
                progress.put("msg", String.format("迁移进度：%d/%d, 总共迁移主表 %d 条，子表 %d 条", end, ids.size(), totalMain, totalItem));
                redisService.setCacheObject(progressKey, progress, 20L, TimeUnit.SECONDS);

                log.info("迁移进度：{}/{}，本批次主表{}条、子表{}条", end, ids.size(), m1, it1);

            }

            return String.format("成功迁移主表 %d 条，子表 %d 条", totalMain, totalItem);

        } catch (Exception ex) {
            // 记录错误信息到 Redis
            progress.put("error", ex.getMessage());
            redisService.setCacheObject(progressKey, progress, 20L, TimeUnit.SECONDS);
            // 继续抛出，触发事务回滚
            throw ex;
        } finally {
            // 释放方法锁
            redisTemplate.delete(lockKey);
        }
    }

    // 添加表结构验证：在迁移前检查源表和目标表的字段名、类型及顺序是否一致
    @Override
    public void validateTableStructure(String sourceTable, String targetTable) {
        List<LinkedHashMap<String, Object>> sourceColumns = hiWipnoteMapper.getTableColumns(sourceTable);
        List<LinkedHashMap<String, Object>> targetColumns = hiWipnoteMapper.getTableColumns(targetTable);

        if (sourceColumns.size() != targetColumns.size()) {
            throw new BaseBusinessException(String.format("表结构不一致：%s 和 %s 的字段数量不同", sourceTable, targetTable));
        }

        for (int i = 0; i < sourceColumns.size(); i++) {
            LinkedHashMap<String, Object> sourceCol = sourceColumns.get(i);
            LinkedHashMap<String, Object> targetCol = targetColumns.get(i);

            String sourceName = ((String) sourceCol.get("COLUMN_NAME")).toUpperCase();
            String targetName = ((String) targetCol.get("COLUMN_NAME")).toUpperCase();
            String sourceType = ((String) sourceCol.get("DATA_TYPE")).toUpperCase();
            String targetType = ((String) targetCol.get("DATA_TYPE")).toUpperCase();

            if (!sourceName.equals(targetName) || !sourceType.equals(targetType)) {
                throw new BaseBusinessException(String.format(
                        "表结构不一致：字段名或类型不匹配。源表 %s 第%d个字段 %s(%s)，目标表 %s 第%d个字段 %s(%s)",
                        sourceTable, i + 1, sourceName, sourceType,
                        targetTable, i + 1, targetName, targetType
                ));
            }
        }
        log.info("校验通过,表结构一致：{} 和 {}", sourceTable, targetTable);
    }
}
