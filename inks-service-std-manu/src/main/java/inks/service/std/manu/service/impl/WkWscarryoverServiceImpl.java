package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.std.manu.domain.WkWscarryoverEntity;
import inks.service.std.manu.domain.WkWscarryoveritemEntity;
import inks.service.std.manu.domain.pojo.WkWscarryoverPojo;
import inks.service.std.manu.domain.pojo.WkWscarryoveritemPojo;
import inks.service.std.manu.domain.pojo.WkWscarryoveritemdetailPojo;
import inks.service.std.manu.mapper.WkWscarryoverMapper;
import inks.service.std.manu.mapper.WkWscarryoveritemMapper;
import inks.service.std.manu.service.WkWscarryoverService;
import inks.service.std.manu.service.WkWscarryoveritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 车间结转(WkWscarryover)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-09 10:53:17
 */
@Service("wkWscarryoverService")
public class WkWscarryoverServiceImpl implements WkWscarryoverService {
    @Resource
    private WkWscarryoverMapper wkWscarryoverMapper;

    @Resource
    private WkWscarryoveritemMapper wkWscarryoveritemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkWscarryoveritemService wkWscarryoveritemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWscarryoverPojo getEntity(String key, String tid) {
        return this.wkWscarryoverMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWscarryoveritemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWscarryoveritemdetailPojo> lst = wkWscarryoverMapper.getPageList(queryParam);
            PageInfo<WkWscarryoveritemdetailPojo> pageInfo = new PageInfo<WkWscarryoveritemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWscarryoverPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkWscarryoverPojo wkWscarryoverPojo = this.wkWscarryoverMapper.getEntity(key, tid);
            //读取子表
            wkWscarryoverPojo.setItem(wkWscarryoveritemMapper.getList(wkWscarryoverPojo.getId(), wkWscarryoverPojo.getTenantid()));
            return wkWscarryoverPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWscarryoverPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWscarryoverPojo> lst = wkWscarryoverMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkWscarryoveritemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkWscarryoverPojo> pageInfo = new PageInfo<WkWscarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWscarryoverPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWscarryoverPojo> lst = wkWscarryoverMapper.getPageTh(queryParam);
            PageInfo<WkWscarryoverPojo> pageInfo = new PageInfo<WkWscarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWscarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWscarryoverPojo insert(WkWscarryoverPojo wkWscarryoverPojo) {
//初始化NULL字段
        if (wkWscarryoverPojo.getRefno() == null) wkWscarryoverPojo.setRefno("");
        if (wkWscarryoverPojo.getBilltype() == null) wkWscarryoverPojo.setBilltype("");
        if (wkWscarryoverPojo.getBilldate() == null) wkWscarryoverPojo.setBilldate(new Date());
        if (wkWscarryoverPojo.getBilltitle() == null) wkWscarryoverPojo.setBilltitle("");
        if (wkWscarryoverPojo.getGroupid() == null) wkWscarryoverPojo.setGroupid("");
        if (wkWscarryoverPojo.getTradercode() == null) wkWscarryoverPojo.setTradercode("");
        if (wkWscarryoverPojo.getTradername() == null) wkWscarryoverPojo.setTradername("");
        if (wkWscarryoverPojo.getCarryyear() == null) wkWscarryoverPojo.setCarryyear(0);
        if (wkWscarryoverPojo.getCarrymonth() == null) wkWscarryoverPojo.setCarrymonth(0);
        if (wkWscarryoverPojo.getStartdate() == null) wkWscarryoverPojo.setStartdate(new Date());
        if (wkWscarryoverPojo.getEnddate() == null) wkWscarryoverPojo.setEnddate(new Date());
        if (wkWscarryoverPojo.getOperator() == null) wkWscarryoverPojo.setOperator("");
        if (wkWscarryoverPojo.getOperatorid() == null) wkWscarryoverPojo.setOperatorid("");
        if (wkWscarryoverPojo.getSummary() == null) wkWscarryoverPojo.setSummary("");
        if (wkWscarryoverPojo.getCreateby() == null) wkWscarryoverPojo.setCreateby("");
        if (wkWscarryoverPojo.getCreatebyid() == null) wkWscarryoverPojo.setCreatebyid("");
        if (wkWscarryoverPojo.getCreatedate() == null) wkWscarryoverPojo.setCreatedate(new Date());
        if (wkWscarryoverPojo.getLister() == null) wkWscarryoverPojo.setLister("");
        if (wkWscarryoverPojo.getListerid() == null) wkWscarryoverPojo.setListerid("");
        if (wkWscarryoverPojo.getModifydate() == null) wkWscarryoverPojo.setModifydate(new Date());
        if (wkWscarryoverPojo.getBillopenamount() == null) wkWscarryoverPojo.setBillopenamount(0D);
        if (wkWscarryoverPojo.getBillinamount() == null) wkWscarryoverPojo.setBillinamount(0D);
        if (wkWscarryoverPojo.getBilloutamount() == null) wkWscarryoverPojo.setBilloutamount(0D);
        if (wkWscarryoverPojo.getBillcloseamount() == null) wkWscarryoverPojo.setBillcloseamount(0D);
        if (wkWscarryoverPojo.getItemcount() == null) wkWscarryoverPojo.setItemcount(0);
        if (wkWscarryoverPojo.getPrintcount() == null) wkWscarryoverPojo.setPrintcount(0);
        if (wkWscarryoverPojo.getCustom1() == null) wkWscarryoverPojo.setCustom1("");
        if (wkWscarryoverPojo.getCustom2() == null) wkWscarryoverPojo.setCustom2("");
        if (wkWscarryoverPojo.getCustom3() == null) wkWscarryoverPojo.setCustom3("");
        if (wkWscarryoverPojo.getCustom4() == null) wkWscarryoverPojo.setCustom4("");
        if (wkWscarryoverPojo.getCustom5() == null) wkWscarryoverPojo.setCustom5("");
        if (wkWscarryoverPojo.getCustom6() == null) wkWscarryoverPojo.setCustom6("");
        if (wkWscarryoverPojo.getCustom7() == null) wkWscarryoverPojo.setCustom7("");
        if (wkWscarryoverPojo.getCustom8() == null) wkWscarryoverPojo.setCustom8("");
        if (wkWscarryoverPojo.getCustom9() == null) wkWscarryoverPojo.setCustom9("");
        if (wkWscarryoverPojo.getCustom10() == null) wkWscarryoverPojo.setCustom10("");
        if (wkWscarryoverPojo.getTenantid() == null) wkWscarryoverPojo.setTenantid("");
        if (wkWscarryoverPojo.getTenantname() == null) wkWscarryoverPojo.setTenantname("");
        if (wkWscarryoverPojo.getRevision() == null) wkWscarryoverPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkWscarryoverEntity wkWscarryoverEntity = new WkWscarryoverEntity();
        BeanUtils.copyProperties(wkWscarryoverPojo, wkWscarryoverEntity);
        //设置id和新建日期
        wkWscarryoverEntity.setId(id);
        wkWscarryoverEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkWscarryoverMapper.insert(wkWscarryoverEntity);
        //Item子表处理
        List<WkWscarryoveritemPojo> lst = wkWscarryoverPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkWscarryoveritemPojo itemPojo = this.wkWscarryoveritemService.clearNull(lst.get(i));
                WkWscarryoveritemEntity wkWscarryoveritemEntity = new WkWscarryoveritemEntity();
                BeanUtils.copyProperties(itemPojo, wkWscarryoveritemEntity);
                //设置id和Pid
                wkWscarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWscarryoveritemEntity.setPid(id);
                wkWscarryoveritemEntity.setTenantid(wkWscarryoverPojo.getTenantid());
                wkWscarryoveritemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkWscarryoveritemMapper.insert(wkWscarryoveritemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWscarryoverEntity.getId(), wkWscarryoverEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWscarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWscarryoverPojo update(WkWscarryoverPojo wkWscarryoverPojo) {
        //主表更改
        WkWscarryoverEntity wkWscarryoverEntity = new WkWscarryoverEntity();
        BeanUtils.copyProperties(wkWscarryoverPojo, wkWscarryoverEntity);
        this.wkWscarryoverMapper.update(wkWscarryoverEntity);
        if (wkWscarryoverPojo.getItem() != null) {
            //Item子表处理
            List<WkWscarryoveritemPojo> lst = wkWscarryoverPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkWscarryoverMapper.getDelItemIds(wkWscarryoverPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkWscarryoveritemMapper.delete(lstDelIds.get(i), wkWscarryoverEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    WkWscarryoveritemEntity wkWscarryoveritemEntity = new WkWscarryoveritemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        WkWscarryoveritemPojo itemPojo = this.wkWscarryoveritemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, wkWscarryoveritemEntity);
                        //设置id和Pid
                        wkWscarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkWscarryoveritemEntity.setPid(wkWscarryoverEntity.getId());  // 主表 id
                        wkWscarryoveritemEntity.setTenantid(wkWscarryoverPojo.getTenantid());   // 租户id
                        wkWscarryoveritemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkWscarryoveritemMapper.insert(wkWscarryoveritemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkWscarryoveritemEntity);
                        wkWscarryoveritemEntity.setTenantid(wkWscarryoverPojo.getTenantid());
                        this.wkWscarryoveritemMapper.update(wkWscarryoveritemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWscarryoverEntity.getId(), wkWscarryoverEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkWscarryoverPojo wkWscarryoverPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkWscarryoveritemPojo> lst = wkWscarryoverPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkWscarryoveritemPojo wkWscarryoveritemPojo : lst) {
                this.wkWscarryoveritemMapper.delete(wkWscarryoveritemPojo.getId(), tid);
            }
        }
        this.wkWscarryoverMapper.delete(key, tid);
        return wkWscarryoverPojo.getRefno();
    }


    /**
     * 新增数据
     *
     * @param wkWscarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWscarryoverPojo createCarry(WkWscarryoverPojo wkWscarryoverPojo) {

        // 查询当前客户之前的销售账单
        QueryParam queryParam = new QueryParam();
        queryParam.setFilterstr(" and Wk_WsCarryover.Groupid='" + wkWscarryoverPojo.getGroupid() + "'");
        queryParam.setOrderBy("Wk_WsCarryover.EndDate");
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1);
        queryParam.setTenantid(wkWscarryoverPojo.getTenantid());
        List<WkWscarryoverPojo> lstCarry = this.wkWscarryoverMapper.getPageTh(queryParam);
        if (lstCarry.size() == 0) {
            throw new RuntimeException("请先初始化车间结转");
        }

        // 查询上个结转表内容
        List<WkWscarryoveritemPojo> lstOpen = this.wkWscarryoveritemMapper.getList(lstCarry.get(0).getId(), wkWscarryoverPojo.getTenantid());

        // =============本期出入库汇总 =====================
        // 初始化时间
        Date dtStart = lstCarry.get(0).getEnddate();
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(wkWscarryoverPojo.getEnddate()) + " 23:59:59");
        wkWscarryoverPojo.setStartdate(lstCarry.get(0).getEnddate());

        queryParam = new QueryParam();
        queryParam.setFilterstr(" and Wk_Worksheet.Groupid='" + wkWscarryoverPojo.getGroupid() + "'");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setTenantid(wkWscarryoverPojo.getTenantid());
        List<WkWscarryoveritemPojo> lstWs = this.wkWscarryoverMapper.getGoodsWsList(queryParam);

        queryParam = new QueryParam();
        queryParam.setFilterstr(" and Mat_Access.Groupid='" + wkWscarryoverPojo.getGroupid() + "'");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setTenantid(wkWscarryoverPojo.getTenantid());
        List<WkWscarryoveritemPojo> lstAcce = this.wkWscarryoverMapper.getGoodsAcceList(queryParam);
        //  ==========开始拼接 进项==============
        int rowNum = 1;
        List<WkWscarryoveritemPojo> lstNew = new ArrayList<>();
        for (WkWscarryoveritemPojo openPojo : lstOpen) {
            WkWscarryoveritemPojo newPojo = new WkWscarryoveritemPojo();
            newPojo.setGoodsid(openPojo.getGoodsid());
            newPojo.setOpenqty(openPojo.getCloseqty());
            newPojo.setOpenamount(openPojo.getCloseamount());
            if (lstWs.size() > 0) {
                for (WkWscarryoveritemPojo mpPojo : lstWs) {
                    if (openPojo.getGoodsid().equals(mpPojo.getGoodsid())) {
                        newPojo.setInqty(mpPojo.getInqty());
                        newPojo.setInamount(mpPojo.getInamount());
                        lstWs.remove(mpPojo);
                        break;
                    }
                }
            } else {
                newPojo.setInqty(0D);
                newPojo.setInamount(0D);
            }
            if (lstAcce.size() > 0) {
                for (WkWscarryoveritemPojo accePojo : lstAcce) {
                    if (openPojo.getGoodsid().equals(accePojo.getGoodsid()) || newPojo.getGoodsid().equals(accePojo.getGoodsid())) {
                        newPojo.setOutqty(accePojo.getOutqty());
                        newPojo.setOutamount(accePojo.getOutamount());
                        lstAcce.remove(accePojo);
                        break;
                    }
                }
            } else {
                newPojo.setOutqty(0D);
                newPojo.setOutamount(0D);
            }

            // 三项非空，添加到列表
            if (newPojo.getOpenqty() != 0D || newPojo.getInqty() != 0D || newPojo.getOutqty() != 0D) {
                newPojo.setOutqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setOutamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }

        // =========本期新增================
        if (lstWs.size() > 0) {
            for (WkWscarryoveritemPojo mpPojo : lstWs) {
                WkWscarryoveritemPojo newPojo = new WkWscarryoveritemPojo();
                newPojo.setGoodsid(mpPojo.getGoodsid());
                newPojo.setOpenqty(0D);
                newPojo.setOpenamount(0D);
                newPojo.setInqty(mpPojo.getInqty());
                newPojo.setInamount(mpPojo.getInamount());
                if (lstAcce.size() > 0) {
                    for (WkWscarryoveritemPojo accePojo : lstAcce) {
                        if (newPojo.getGoodsid().equals(accePojo.getGoodsid())) {
                            newPojo.setOutqty(accePojo.getOutqty());
                            newPojo.setOutamount(accePojo.getOutamount());
                            lstAcce.remove(accePojo);
                            break;
                        }
                    }
                } else {
                    newPojo.setOutqty(0D);
                    newPojo.setOutamount(0D);
                }
                /// 添加到列表
                newPojo.setOutqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setOutamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }
        if (lstAcce.size() > 0) {
            for (WkWscarryoveritemPojo accePojo : lstAcce) {
                WkWscarryoveritemPojo newPojo = new WkWscarryoveritemPojo();
                newPojo.setGoodsid(accePojo.getGoodsid());
                newPojo.setOpenqty(0D);
                newPojo.setOpenamount(0D);
                newPojo.setInqty(0D);
                newPojo.setInamount(0D);
                newPojo.setOutqty(accePojo.getOutqty());
                newPojo.setOutamount(accePojo.getOutamount());
                /// 添加到列表
                newPojo.setOutqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setOutamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }
        //  =======插入数据========
        wkWscarryoverPojo.setItem(lstNew);
        wkWscarryoverPojo.setItemcount(lstNew.size());
        wkWscarryoverPojo = insert(wkWscarryoverPojo);

        //返回Bill实例
        return this.getEntity(wkWscarryoverPojo.getId(), wkWscarryoverPojo.getTenantid());

    }


}
