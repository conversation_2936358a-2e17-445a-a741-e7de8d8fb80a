package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.security.config.InksConfigThreadLocal;
import inks.service.std.manu.domain.WkWorksheetEntity;
import inks.service.std.manu.domain.WkWorksheetitemEntity;
import inks.service.std.manu.domain.WkWorksheetmatEntity;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.mapper.*;
import inks.service.std.manu.service.WkWorksheetService;
import inks.service.std.manu.service.WkWorksheetitemService;
import inks.service.std.manu.service.WkWorksheetmatService;
import inks.service.std.manu.service.WkWorksheetmatmergeService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 厂制工单(WkWorksheet)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-16 07:57:50
 */
@Service("wkWorksheetService")
public class WkWorksheetServiceImpl implements WkWorksheetService {
    @Resource
    private WkWorksheetMapper wkWorksheetMapper;

    @Resource
    private WkMrpitemMapper wkMrpitemMapper;

    @Resource
    private WkWorksheetitemMapper wkWorksheetitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkWorksheetitemService wkWorksheetitemService;
    @Resource
    private WkWorksheetmatmergeService wkWorksheetmatMergeService;
    @Resource
    private WkMrpMapper wkMrpMapper;

    @Resource
    private WkWorksheetmatMapper wkWorksheetmatMapper;
    @Resource
    private WkWorksheetmergeMapper wkWorksheetmergeMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private WkWorksheetmatService wkWorksheetmatService;


    @Resource
    private manu_SyncMapper manuSyncMapper;
    @Autowired
    private SystemFeignService systemFeignService;
    @Qualifier("wkWorksheetmatmergeMapper")
    @Autowired
    private WkWorksheetmatmergeMapper wkWorksheetmatmergeMapper;

    // 将生产加工单mat子表转换为merge子表(相同goodsid+flowcode的合并,累加数量、ItemCount、MachUids是逗号分隔的字符串)
    private static List<WkWorksheetmatmergePojo> sheetMatListToMatMergeList(List<WkWorksheetmatPojo> lst) {
        // 创建一个HashMap，键是goodsid+flowcode，值是WkWorksheetmatmergePojo对象
        Map<String, WkWorksheetmatmergePojo> goodsidFlowcode_Merge_Map = new HashMap<>();
        // 创建一个HashMap，键是goodsid+flowcode，值是Set<String>，用于存储每个goodsid+flowcode对应的machuid集合
        Map<String, Set<String>> goodsidFlowcode_MachuidSet_Map = new HashMap<>();

        // 遍历原始item列表
        for (WkWorksheetmatPojo matItem : lst) {
            // 使用goodsid和flowcode生成一个唯一的键
            String goodsidFlowcode_key = matItem.getGoodsid() + "-" + matItem.getFlowcode();

            // 检查这个键是否已经在映射中存在
            if (goodsidFlowcode_Merge_Map.containsKey(goodsidFlowcode_key)) {
                // 如果存在，更新映射merge中的2个值:数量、ItemCount
                WkWorksheetmatmergePojo mergeMat = goodsidFlowcode_Merge_Map.get(goodsidFlowcode_key);
                mergeMat.setQuantity(mergeMat.getQuantity() + matItem.getQuantity());
                mergeMat.setItemcount(mergeMat.getItemcount() + 1); // 合并了几行item

                // 添加machuid到Set中
                goodsidFlowcode_MachuidSet_Map.get(goodsidFlowcode_key).add(matItem.getMachuid());
            } else {
                // 如果不存在，创建一个新的WkWorksheetmatmergePojo对象并添加到映射中
                WkWorksheetmatmergePojo mergeItem = new WkWorksheetmatmergePojo();
                BeanUtils.copyProperties(matItem, mergeItem);
                mergeItem.setItemcount(1); // 合并了几行item
                goodsidFlowcode_Merge_Map.put(goodsidFlowcode_key, mergeItem);

                // 创建一个新的Set<String>并添加到映射中
                Set<String> machuidSet = new HashSet<>();
                machuidSet.add(matItem.getMachuid());
                goodsidFlowcode_MachuidSet_Map.put(goodsidFlowcode_key, machuidSet);
            }
        }

        // 将映射的值转换为一个列表(只有id都还没有)
        List<WkWorksheetmatmergePojo> mergedListNew = new ArrayList<>(goodsidFlowcode_Merge_Map.values());

        // 将machuids Set转换为以逗号分隔的字符串
        for (WkWorksheetmatmergePojo mergeItem : mergedListNew) {
            String goodsidFlowcode_key = mergeItem.getGoodsid() + "-" + mergeItem.getFlowcode();
            mergeItem.setMachuid(String.join(",", goodsidFlowcode_MachuidSet_Map.get(goodsidFlowcode_key)));
        }

        return mergedListNew;
    }



//    // 将采购计划item子表转换为merge子表(相同goodsid+machuid的合并,累加数量、ItemCount machuids以逗号分隔的字符串)
//    private static List<WkWorksheetmatmergePojo> sheetMatListToMatMergeList22222222222(List<WkWorksheetmatPojo> lst) {
//        // 创建一个HashMap，键是goodsid，值是BuyPlanmergePojo对象
//        Map<String, WkWorksheetmatmergePojo> goodsid_Merge_Map = new HashMap<>();
//        // 创建一个HashMap，键是goodsid，值是Set<String>，用于存储每个goodsid对应的machuid集合
//        Map<String, Set<String>> goodsid_MachuidSet_Map = new HashMap<>();
//        // 遍历原始item列表
//        for (WkWorksheetmatPojo matItem : lst) {
//            // 使用goodsid生成一个唯一的键
//            String key = matItem.getGoodsid();
//            // 检查这个键是否已经在映射中存在
//            if (goodsid_Merge_Map.containsKey(key)) {
//                // 如果存在，更新映射merge中的2个值:数量、ItemCount
//                WkWorksheetmatmergePojo mergeMat = goodsid_Merge_Map.get(key);
//                mergeMat.setQuantity(mergeMat.getQuantity() + matItem.getQuantity());
//                mergeMat.setItemcount(mergeMat.getItemcount() + 1);//合并了几行item
//                // 添加machuid到Set中
//                goodsid_MachuidSet_Map.get(key).add(matItem.getMachuid());
//            } else {
//                // 如果不存在，创建一个新的BuyPlanmergePojo对象并添加到映射中
//                WkWorksheetmatmergePojo mergeItem = new WkWorksheetmatmergePojo();
//                BeanUtils.copyProperties(matItem, mergeItem);
//                mergeItem.setItemcount(1);//合并了几行item
//                goodsid_Merge_Map.put(key, mergeItem);
//                // 创建一个新的Set<String>并添加到映射中
//                Set<String> machuidSet = new HashSet<>();
//                machuidSet.add(matItem.getMachuid());
//                goodsid_MachuidSet_Map.put(key, machuidSet);
//            }
//        }
//        // 将映射的值转换为一个列表 (只有id还都没有)
//        List<WkWorksheetmatmergePojo> mergedListNew = new ArrayList<>(goodsid_Merge_Map.values());
//        // 将machuids Set转换为以逗号分隔的字符串
//        for (WkWorksheetmatmergePojo mergeItem : mergedListNew) {
//            String key = mergeItem.getGoodsid();
//            mergeItem.setMachuid(String.join(",", goodsid_MachuidSet_Map.get(key)));
//        }
//        return mergedListNew;
//    }

    private static void cleanNull(WkWorksheetPojo wkWorksheetPojo) {
        if (wkWorksheetPojo.getRefno() == null) wkWorksheetPojo.setRefno("");
        if (wkWorksheetPojo.getBilltype() == null) wkWorksheetPojo.setBilltype("");
        if (wkWorksheetPojo.getBilldate() == null) wkWorksheetPojo.setBilldate(new Date());
        if (wkWorksheetPojo.getBilltitle() == null) wkWorksheetPojo.setBilltitle("");
        if (wkWorksheetPojo.getOperator() == null) wkWorksheetPojo.setOperator("");
        if (wkWorksheetPojo.getGroupid() == null) wkWorksheetPojo.setGroupid("");
        if (wkWorksheetPojo.getSummary() == null) wkWorksheetPojo.setSummary("");
        if (wkWorksheetPojo.getCreateby() == null) wkWorksheetPojo.setCreateby("");
        if (wkWorksheetPojo.getCreatebyid() == null) wkWorksheetPojo.setCreatebyid("");
        if (wkWorksheetPojo.getCreatedate() == null) wkWorksheetPojo.setCreatedate(new Date());
        if (wkWorksheetPojo.getLister() == null) wkWorksheetPojo.setLister("");
        if (wkWorksheetPojo.getListerid() == null) wkWorksheetPojo.setListerid("");
        if (wkWorksheetPojo.getModifydate() == null) wkWorksheetPojo.setModifydate(new Date());
        if (wkWorksheetPojo.getAssessor() == null) wkWorksheetPojo.setAssessor("");
        if (wkWorksheetPojo.getAssessorid() == null) wkWorksheetPojo.setAssessorid("");
        if (wkWorksheetPojo.getAssessdate() == null) wkWorksheetPojo.setAssessdate(new Date());
        if (wkWorksheetPojo.getBillstatecode() == null) wkWorksheetPojo.setBillstatecode("");
        if (wkWorksheetPojo.getBillstatedate() == null) wkWorksheetPojo.setBillstatedate(new Date());
        if (wkWorksheetPojo.getBillstartdate() == null) wkWorksheetPojo.setBillstartdate(new Date());
        if (wkWorksheetPojo.getBillplandate() == null) wkWorksheetPojo.setBillplandate(new Date());
        if (wkWorksheetPojo.getBillplanhour() == null) wkWorksheetPojo.setBillplanhour(0D);
        if (wkWorksheetPojo.getBillfinishhour() == null) wkWorksheetPojo.setBillfinishhour(0D);
        if (wkWorksheetPojo.getItemcount() == null) wkWorksheetPojo.setItemcount(wkWorksheetPojo.getItem().size());
        if (wkWorksheetPojo.getWipcount() == null) wkWorksheetPojo.setWipcount(0);
        if (wkWorksheetPojo.getDisannulcount() == null) wkWorksheetPojo.setDisannulcount(0);
        if (wkWorksheetPojo.getFinishcount() == null) wkWorksheetPojo.setFinishcount(0);
        if (wkWorksheetPojo.getPrintcount() == null) wkWorksheetPojo.setPrintcount(0);
        if (wkWorksheetPojo.getMergecount() == null) wkWorksheetPojo.setMergecount(0);
        if (wkWorksheetPojo.getBillwkwpid() == null) wkWorksheetPojo.setBillwkwpid("");
        if (wkWorksheetPojo.getBillwkwpcode() == null) wkWorksheetPojo.setBillwkwpcode("");
        if (wkWorksheetPojo.getBillwkwpname() == null) wkWorksheetPojo.setBillwkwpname("");
        if (wkWorksheetPojo.getOaflowmark() == null) wkWorksheetPojo.setOaflowmark(0);
        if(wkWorksheetPojo.getMatmergecount()==null) wkWorksheetPojo.setMatmergecount(0);
        if(wkWorksheetPojo.getBillquantity()==null) wkWorksheetPojo.setBillquantity(0D);
        if(wkWorksheetPojo.getBillwkpcsqty()==null) wkWorksheetPojo.setBillwkpcsqty(0D);
        if(wkWorksheetPojo.getBillwksecqty()==null) wkWorksheetPojo.setBillwksecqty(0D);
        if (wkWorksheetPojo.getCustom1() == null) wkWorksheetPojo.setCustom1("");
        if (wkWorksheetPojo.getCustom2() == null) wkWorksheetPojo.setCustom2("");
        if (wkWorksheetPojo.getCustom3() == null) wkWorksheetPojo.setCustom3("");
        if (wkWorksheetPojo.getCustom4() == null) wkWorksheetPojo.setCustom4("");
        if (wkWorksheetPojo.getCustom5() == null) wkWorksheetPojo.setCustom5("");
        if (wkWorksheetPojo.getCustom6() == null) wkWorksheetPojo.setCustom6("");
        if (wkWorksheetPojo.getCustom7() == null) wkWorksheetPojo.setCustom7("");
        if (wkWorksheetPojo.getCustom8() == null) wkWorksheetPojo.setCustom8("");
        if (wkWorksheetPojo.getCustom9() == null) wkWorksheetPojo.setCustom9("");
        if (wkWorksheetPojo.getCustom10() == null) wkWorksheetPojo.setCustom10("");
        if (wkWorksheetPojo.getTenantid() == null) wkWorksheetPojo.setTenantid("");
        if (wkWorksheetPojo.getTenantname() == null) wkWorksheetPojo.setTenantname("");
        if (wkWorksheetPojo.getRevision() == null) wkWorksheetPojo.setRevision(0);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWorksheetPojo getEntity(String key, String tid) {
        return this.wkWorksheetMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWorksheetitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWorksheetitemdetailPojo> lst = wkWorksheetMapper.getPageList(queryParam);
            PageInfo<WkWorksheetitemdetailPojo> pageInfo = new PageInfo<WkWorksheetitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWorksheetPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetMapper.getEntity(key, tid);
            //读取子表
            wkWorksheetPojo.setItem(wkWorksheetitemMapper.getList(wkWorksheetPojo.getId(), wkWorksheetPojo.getTenantid()));
            //读取mat
            wkWorksheetPojo.setMat(wkWorksheetmatMapper.getList(wkWorksheetPojo.getId(), wkWorksheetPojo.getTenantid()));
            return wkWorksheetPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public WkWorksheetPojo getMergeBillEntity(String key, String tenantid) {
        try {
            //读取主表
            WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetMapper.getEntity(key, tenantid);
            //读取子表
            wkWorksheetPojo.setItem(wkWorksheetitemMapper.getList(wkWorksheetPojo.getId(), tenantid));
            //读取mat
            wkWorksheetPojo.setMat(wkWorksheetmatMapper.getList(wkWorksheetPojo.getId(), tenantid));
            //读取matmerge
            wkWorksheetPojo.setMerge(wkWorksheetmatmergeMapper.getList(wkWorksheetPojo.getId(), tenantid));
            return wkWorksheetPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<WkWorksheetPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWorksheetPojo> lst = wkWorksheetMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (WkWorksheetPojo wkWorksheetPojo : lst) {
                wkWorksheetPojo.setItem(wkWorksheetitemMapper.getList(wkWorksheetPojo.getId(), wkWorksheetPojo.getTenantid()));
            }
            //循环设置每个主表对象的mat子表
            for (WkWorksheetPojo wkWorksheetPojo : lst) {
                wkWorksheetPojo.setMat(wkWorksheetmatMapper.getList(wkWorksheetPojo.getId(), wkWorksheetPojo.getTenantid()));
            }
            PageInfo<WkWorksheetPojo> pageInfo = new PageInfo<WkWorksheetPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWorksheetPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWorksheetPojo> lst = wkWorksheetMapper.getPageTh(queryParam);
            PageInfo<WkWorksheetPojo> pageInfo = new PageInfo<WkWorksheetPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWorksheetPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public WkWorksheetPojo insert(WkWorksheetPojo wkWorksheetPojo, String token) {
        String tid = wkWorksheetPojo.getTenantid();
        //初始化NULL字段
        cleanNull(wkWorksheetPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkWorksheetEntity wkWorksheetEntity = new WkWorksheetEntity();
        BeanUtils.copyProperties(wkWorksheetPojo, wkWorksheetEntity);
        //设置id和新建日期
        wkWorksheetEntity.setId(id);
        wkWorksheetEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkWorksheetMapper.insert(wkWorksheetEntity);
        //Item子表处理
        List<WkWorksheetitemPojo> lst = wkWorksheetPojo.getItem();
        // 存储item表的RowCode和雪花id键值对 在生成item.id时存入,在设置mat.itemid时取出
        HashMap<String, String> rowCode_idMap = new HashMap<>();
        if (lst != null) {
            //循环每个item子表
            for (WkWorksheetitemPojo wkWorksheetitemPojo : lst) {
                //初始化item的NULL
                WkWorksheetitemPojo itemPojo = this.wkWorksheetitemService.clearNull(wkWorksheetitemPojo);
                WkWorksheetitemEntity wkWorksheetitemEntity = new WkWorksheetitemEntity();
                BeanUtils.copyProperties(itemPojo, wkWorksheetitemEntity);
                //设置id和Pid
                wkWorksheetitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                // 存入RowCode和雪花id键值对
                rowCode_idMap.put(itemPojo.getRowcode(), wkWorksheetitemEntity.getId());
                wkWorksheetitemEntity.setPid(id);
                wkWorksheetitemEntity.setTenantid(tid);
                wkWorksheetitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkWorksheetitemMapper.insert(wkWorksheetitemEntity);

                // 如果是MRP需求
                if ("MRP需求".equals(wkWorksheetPojo.getBilltype())) {
                    int mrpqtydec = 0;// MRP数量小数位
                    this.wkWorksheetMapper.updateMrpWsFinish(itemPojo.getMrpitemid(), itemPojo.getMrpuid(), tid);
                    WkMrpitemPojo wkMrpitemPojo = this.wkMrpitemMapper.getEntity(itemPojo.getMrpitemid(), tid);
                    if (wkMrpitemPojo != null && wkMrpitemPojo.getWkwsqty() + wkMrpitemPojo.getWkscqty() > wkMrpitemPojo.getNeedqty()) {
                        double wkqty = wkMrpitemPojo.getWkwsqty() + wkMrpitemPojo.getWkscqty();
                        throw new RuntimeException(wkWorksheetitemPojo.getGoodsuid() + ":生产总数" + wkqty + "大于需求数" + wkMrpitemPojo.getNeedqty());
                    }
                    // 刷新MRP完工数
                    this.wkMrpMapper.updateFinishCount(itemPojo.getMrpitemid(), tid);
                    // 生成物料表
                    if (wkMrpitemPojo != null) {
                        List<WkMrpitemPojo> lstMrpitem = this.wkMrpMapper.getItemListByParentid(wkMrpitemPojo.getId(), tid);
                        for (int k = 0; k < lstMrpitem.size(); k++) {
                            WkMrpitemPojo mrpitemPojo = lstMrpitem.get(k);
                            WkWorksheetmatPojo worksheetmatPojo = new WkWorksheetmatPojo();
                            worksheetmatPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                            worksheetmatPojo.setPid(id);
                            worksheetmatPojo.setItemid(wkWorksheetitemEntity.getId());
                            worksheetmatPojo.setGoodsid(mrpitemPojo.getGoodsid());
                            worksheetmatPojo.setMainqty(mrpitemPojo.getMainqty());
                            worksheetmatPojo.setSubqty(mrpitemPojo.getSubqty());
                            worksheetmatPojo.setLossrate(mrpitemPojo.getLossrate());
                            double matqty = wkWorksheetitemPojo.getQuantity() / worksheetmatPojo.getMainqty() * worksheetmatPojo.getSubqty() * (1 + worksheetmatPojo.getLossrate() / 100);
                            // 保留小数位0位,向上取整 向上进位 保留小数位数  double needQty = 6.123456D;MRP数量小数位 0-7.0  1-6.2  2-6.13  3-6.124
                            matqty = Math.ceil(matqty * Math.pow(10, mrpqtydec)) / Math.pow(10, mrpqtydec);
                            worksheetmatPojo.setQuantity(matqty);
                            worksheetmatPojo.setFinishqty(0D);
                            worksheetmatPojo.setMrpuid(wkWorksheetitemPojo.getMrpuid());
                            worksheetmatPojo.setMrpitemid(mrpitemPojo.getId());
                            worksheetmatPojo.setRownum(k);
                            worksheetmatPojo.setClosed(0);
                            worksheetmatPojo.setTenantid(tid);
                            worksheetmatPojo.setRevision(1);
                            worksheetmatPojo.setFlowcode(mrpitemPojo.getFlowcode());  //TODO 这里需要手动赋值

                            this.wkWorksheetmatService.insert(worksheetmatPojo);
                        }

                    }
                }
                // 同步销售订单生产数量
                if ("销售订单".equals(wkWorksheetPojo.getBilltype())) {
                    if (!"".equals(wkWorksheetitemPojo.getMachitemid()) && wkWorksheetitemPojo.getMachitemid() != null) {
                        this.wkWorksheetMapper.updateMachWKQuantity(wkWorksheetitemPojo.getMachitemid(), tid);
                    }
                }
                // 同步生产主计划的startqty	开单数量
                if ("生产主计划".equals(wkWorksheetPojo.getBilltype())||"MRP需求".equals(wkWorksheetPojo.getBilltype())) {
                    this.wkWorksheetMapper.updateMainPlanStartQty(wkWorksheetitemPojo.getMainplanitemid(), tid);
                }
            }
        }

        // 手工加工单
        if (!"MRP需求".equals(wkWorksheetPojo.getBilltype())) {

            //Item子表处理
            List<WkWorksheetmatPojo> lstmat = wkWorksheetPojo.getMat();
            if (lstmat != null) {
                //循环每个item子表
                for (WkWorksheetmatPojo wkWorksheetmatPojo : lstmat) {
                    //初始化item的NULL
                    WkWorksheetmatPojo matPojo = this.wkWorksheetmatService.clearNull(wkWorksheetmatPojo);
                    WkWorksheetmatEntity wkWorksheetmatEntity = new WkWorksheetmatEntity();
                    BeanUtils.copyProperties(matPojo, wkWorksheetmatEntity);
                    //设置id和Pid
                    wkWorksheetmatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    wkWorksheetmatEntity.setPid(id);
                    wkWorksheetmatEntity.setTenantid(tid);
                    wkWorksheetmatEntity.setRevision(1);  //乐观锁
                    // 取出:设置mat.itemid
                    wkWorksheetmatEntity.setItemid(rowCode_idMap.get(matPojo.getItemrowcode()));
                    //插入子表
                    this.wkWorksheetmatMapper.insert(wkWorksheetmatEntity);
                }

            }
        }

        // billType是MRP需求时：
        // 是否创建WorksheetMatMerge合并子表?
        // 读取指定系统参数 是否进行mat子表合并创建merge 为"true"时合并
        //String matmerge = systemFeignService.getConfigValue("module.manu.sheetmatmerge", tid, token).getData();
        String matmerge = InksConfigThreadLocal.getConfig("module.manu.sheetmatmerge");
        if ("true".equals(matmerge)) {
            // 从数据库查出matList:保证有id、machuid
            List<WkWorksheetmatPojo> matItemListDB = wkWorksheetmatMapper.getList(id, tid);
            // 将生产加工单mat子表转换为merge子表(相同goodsid+flowcode的合并,累加数量、ItemCount、MachUids是逗号分隔的字符串)
            List<WkWorksheetmatmergePojo> mergedListNew = sheetMatListToMatMergeList(matItemListDB);

            // 创建一个映射，键是goodsid，值是List<WkWorksheetmatPojo>
            Map<String, List<WkWorksheetmatPojo>> goodsid_ItemList_Map = matItemListDB.stream()
                    .collect(Collectors.groupingBy(WkWorksheetmatPojo::getGoodsid));
            //循环每个matMerge子表插入,插入后反写Wk_Worksheetmat.Mergeid
            for (WkWorksheetmatmergePojo mergePojo : mergedListNew) {
                mergePojo.setPid(id);
                WkWorksheetmatmergePojo insert = this.wkWorksheetmatMergeService.insert(mergePojo);
                String mergeidInsert = insert.getId();
                // 取出原始 WkWorksheetmatPojo 对象并设置 mergeid
                String key = mergePojo.getGoodsid();
                List<WkWorksheetmatPojo> matitems = goodsid_ItemList_Map.get(key);
                for (WkWorksheetmatPojo matItem : matitems) {
                    //更新Wk_Worksheetmat.Mergeid '合并行id'
                    this.wkWorksheetmatMapper.updateMergeid(matItem.getId(), mergeidInsert, tid);
                }
            }
            //更新合并行数 100条matitem合并为3条merge,则取3
            this.wkWorksheetMapper.updateMatMergeCount(id, mergedListNew.size(), tid);
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(WkWorksheetitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkWsRemQty(goodsid, tid);
        });
        //返回Bill实例
        return this.getBillEntity(wkWorksheetEntity.getId(), wkWorksheetEntity.getTenantid());
    }

    /**
     * 修改数据
     *
     * @param wkWorksheetPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWorksheetPojo update(WkWorksheetPojo wkWorksheetPojo, String token) {
        String tid = wkWorksheetPojo.getTenantid();
        //主表更改
        WkWorksheetEntity wkWorksheetEntity = new WkWorksheetEntity();
        BeanUtils.copyProperties(wkWorksheetPojo, wkWorksheetEntity);
        this.wkWorksheetMapper.update(wkWorksheetEntity);
        // 存储item表的RowCode和雪花id键值对 在生成item.id时存入,在设置mat.itemid时取出
        HashMap<String, String> rowCode_idMap = new HashMap<>();
        // 所有去重的goodsid Set集合
        Set<String> goodsidSet = new HashSet<>();
        if (wkWorksheetPojo.getItem() != null) {
            //Item子表处理
            List<WkWorksheetitemPojo> lst = wkWorksheetPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkWorksheetMapper.getDelItemIds(wkWorksheetPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    WkWorksheetitemPojo dbPojo = this.wkWorksheetitemMapper.getEntity(lstDelIds.get(i), wkWorksheetEntity.getTenantid());
                    goodsidSet.add(dbPojo.getGoodsid());
                    this.wkWorksheetitemMapper.delete(lstDelIds.get(i), wkWorksheetEntity.getTenantid());
                    // 如果是MRP需求
                    if ("MRP需求".equals(wkWorksheetPojo.getBilltype())) {
                        this.wkWorksheetMapper.updateMrpWsFinish(dbPojo.getMrpitemid(), dbPojo.getMrpuid(), tid);
                        // 刷新MRP完工数
                        this.wkMrpMapper.updateFinishCount(dbPojo.getMrpitemid(), tid);
                        // 删除Mat清单
                        this.wkWorksheetmatMapper.deleteByItemid(dbPojo.getId(), tid);
                    }
                    // 同步销售订单生产数量
                    if ("销售订单".equals(wkWorksheetPojo.getBilltype())) {
                        if (!"".equals(dbPojo.getMachitemid()) && dbPojo.getMachitemid() != null) {
                            this.wkWorksheetMapper.updateMachWKQuantity(dbPojo.getMachitemid(), tid);
                        }
                    }
                    // 同步生产主计划的startqty	开单数量
                    if ("生产主计划".equals(wkWorksheetPojo.getBilltype())||"MRP需求".equals(wkWorksheetPojo.getBilltype())) {
                        this.wkWorksheetMapper.updateMainPlanStartQty(lst.get(i).getMainplanitemid(), tid);
                    }
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (WkWorksheetitemPojo wkWorksheetitemPojo : lst) {
                    goodsidSet.add(wkWorksheetitemPojo.getGoodsid());
                    String itemId = wkWorksheetitemPojo.getId();
                    WkWorksheetitemEntity wkWorksheetitemEntity = new WkWorksheetitemEntity();
                    if ("".equals(itemId) || itemId == null) {
                        //初始化item的NULL
                        WkWorksheetitemPojo itemPojo = this.wkWorksheetitemService.clearNull(wkWorksheetitemPojo);
                        BeanUtils.copyProperties(itemPojo, wkWorksheetitemEntity);
                        //设置id和Pid
                        wkWorksheetitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        // 存入RowCode和雪花id键值对
                        rowCode_idMap.put(itemPojo.getRowcode(), wkWorksheetitemEntity.getId());
                        wkWorksheetitemEntity.setPid(wkWorksheetEntity.getId());  // 主表 id
                        wkWorksheetitemEntity.setTenantid(tid);   // 租户id
                        wkWorksheetitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkWorksheetitemMapper.insert(wkWorksheetitemEntity);
                    } else {
                        BeanUtils.copyProperties(wkWorksheetitemPojo, wkWorksheetitemEntity);
                        // 存入RowCode和雪花id键值对
                        rowCode_idMap.put(wkWorksheetitemPojo.getRowcode(), itemId);
                        wkWorksheetitemEntity.setTenantid(tid);
                        this.wkWorksheetitemMapper.update(wkWorksheetitemEntity);
                    }
                    // 如果是MRP需求
                    if ("MRP需求".equals(wkWorksheetPojo.getBilltype())) {
                        int mrpqtydec = 0;// MRP数量小数位
                        double orgQty = 0D;
                        if (StringUtils.isNotBlank(itemId)) {
                            WkWorksheetitemPojo entity = wkWorksheetitemMapper.getEntity(itemId, tid);
                            orgQty = (entity != null) ? entity.getQuantity() : 0D;
                        }
                        this.wkWorksheetMapper.updateMrpWsFinish(wkWorksheetitemPojo.getMrpitemid(), wkWorksheetitemPojo.getMrpuid(), tid);
                        WkMrpitemPojo wkMrpitemPojo = this.wkMrpitemMapper.getEntity(wkWorksheetitemPojo.getMrpitemid(), tid);
                        if (wkMrpitemPojo != null) {
                            double wkQty = wkMrpitemPojo.getWkwsqty() + wkMrpitemPojo.getWkscqty() - orgQty;
                            double needQty = wkMrpitemPojo.getNeedqty();
                            if (wkQty > needQty) {
                                throw new RuntimeException(wkWorksheetitemPojo.getGoodsuid() + ":生产总数" + wkQty + "大于需求数" + needQty);
                            }
                        }


                        // 刷新MRP完工数
                        this.wkMrpMapper.updateFinishCount(wkWorksheetitemPojo.getMrpitemid(), tid);
                        // 更新物料表
                        if (wkMrpitemPojo != null) {
                            List<WkWorksheetmatPojo> lstMrpmat = this.wkWorksheetmatMapper.getListByItemid(wkMrpitemPojo.getId(), tid);
                            for (WkWorksheetmatPojo wkWorksheetmatPojo : lstMrpmat) {
                                WkWorksheetmatEntity matEntity = new WkWorksheetmatEntity();
                                BeanUtils.copyProperties(wkWorksheetmatPojo, matEntity);
                                Double matqty = wkWorksheetitemPojo.getQuantity() / matEntity.getMainqty() * matEntity.getSubqty() * (1 + matEntity.getLossrate() / 100);
                                // 保留小数位0位,向上取整 向上进位 保留小数位数  double needQty = 6.123456D;MRP数量小数位 0-7.0  1-6.2  2-6.13  3-6.124
                                matqty = Math.ceil(matqty * Math.pow(10, mrpqtydec)) / Math.pow(10, mrpqtydec);
                                matEntity.setQuantity(matqty);
                                matEntity.setRevision(matEntity.getRevision() + 1);
                                this.wkWorksheetmatMapper.update(matEntity);
                            }
                        }


                    }
                    // 同步销售订单生产数量
                    if ("销售订单".equals(wkWorksheetPojo.getBilltype())) {
                        if (!"".equals(wkWorksheetitemPojo.getMachitemid()) && wkWorksheetitemPojo.getMachitemid() != null) {
                            this.wkWorksheetMapper.updateMachWKQuantity(wkWorksheetitemPojo.getMachitemid(), tid);
                        }
                    }
                    // 同步生产主计划的startqty	开单数量
                    if ("生产主计划".equals(wkWorksheetPojo.getBilltype())||"MRP需求".equals(wkWorksheetPojo.getBilltype())) {
                        this.wkWorksheetMapper.updateMainPlanStartQty(wkWorksheetitemPojo.getMainplanitemid(), tid);
                    }
                }
            }
        }
        // 手工加工单
        if (!"MRP需求".equals(wkWorksheetPojo.getBilltype()) && wkWorksheetPojo.getMat() != null) {

            //Mat子表处理
            List<WkWorksheetmatPojo> matList = wkWorksheetPojo.getMat();
            //获取被删除的Mat
            List<String> lstDelIds = wkWorksheetMapper.getDelMatIds(wkWorksheetPojo);
            if (lstDelIds != null) {
                //循环每个删除Mat子表
                for (String lstDelId : lstDelIds) {
                    this.wkWorksheetmatMapper.delete(lstDelId, wkWorksheetEntity.getTenantid());
                }
            }
            if (matList != null) {
                //循环每个Mat子表
                for (WkWorksheetmatPojo wkWorksheetmatPojo : matList) {
                    WkWorksheetmatEntity wkWorksheetmatEntity = new WkWorksheetmatEntity();
                    if ("".equals(wkWorksheetmatPojo.getId()) || wkWorksheetmatPojo.getId() == null) {
                        //初始化Mat的NULL
                        WkWorksheetmatPojo matPojo = this.wkWorksheetmatService.clearNull(wkWorksheetmatPojo);
                        BeanUtils.copyProperties(matPojo, wkWorksheetmatEntity);
                        //设置id和Pid
                        wkWorksheetmatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkWorksheetmatEntity.setPid(wkWorksheetEntity.getId());  // 主表 id
                        wkWorksheetmatEntity.setTenantid(tid);   // 租户id
                        wkWorksheetmatEntity.setRevision(1);  // 乐观锁
                        // 取出:设置mat.itemid
                        wkWorksheetmatEntity.setItemid(rowCode_idMap.get(matPojo.getItemrowcode()));
                        //插入子表
                        this.wkWorksheetmatMapper.insert(wkWorksheetmatEntity);
                    } else {
                        BeanUtils.copyProperties(wkWorksheetmatPojo, wkWorksheetmatEntity);
                        wkWorksheetmatEntity.setTenantid(tid);
                        // 取出:设置mat.itemid
                        wkWorksheetmatEntity.setItemid(rowCode_idMap.get(wkWorksheetmatPojo.getItemrowcode()));
                        this.wkWorksheetmatMapper.update(wkWorksheetmatEntity);
                    }
                }
            }


        }
        // billType是MRP需求时：
        // 是否创建WorksheetMatMerge合并子表?
        // 读取指定系统参数 是否进行mat子表合并创建merge 为"true"时合并
        syncMergeSheetMat(wkWorksheetPojo.getId(), token, tid);
        // 同步货品数量 SQL替代MQ
        goodsidSet.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkWsRemQty(goodsid, tid);
        });
        //返回Bill实例
        return this.getBillEntity(wkWorksheetEntity.getId(), wkWorksheetEntity.getTenantid());
    }

    // 用于生成加工单mat表update或者在mrp重拉refreshItemList时进行mat合并表的刷新
    private void syncMergeSheetMat(String sheetid, String token, String tid) {
        //String matmerge = systemFeignService.getConfigValue("module.manu.sheetmatmerge", tid, token).getData();
        String matmerge = InksConfigThreadLocal.getConfig("module.manu.sheetmatmerge");
        if ("true".equals(matmerge)) {
            // 从数据库查出matList:保证有id、machuid
            List<WkWorksheetmatPojo> matListDB = wkWorksheetmatMapper.getList(sheetid, tid);
            // 将生产加工单mat子表转换为merge子表(相同goodsid+flowcode的合并,累加数量、ItemCount、MachUids是逗号分隔的字符串)
            List<WkWorksheetmatmergePojo> mergedListNew = sheetMatListToMatMergeList(matListDB);
            // 获取数据库中的merge子表List
            List<WkWorksheetmatmergePojo> mergeListDB = wkWorksheetmatmergeMapper.getList(sheetid, tid);
            // 将数据库中的列表转换为映射 键是 goodsid+flowcode，值是 WkWorksheetmatmergePojo 对象
            Map<String, WkWorksheetmatmergePojo> mergeMapDB = new HashMap<>();
            for (WkWorksheetmatmergePojo mergeDB : mergeListDB) {
                String keyDB = mergeDB.getGoodsid() + "-" + mergeDB.getFlowcode();
                mergeMapDB.put(keyDB, mergeDB);
            }

            // 创建一个映射，键是 goodsid+flowcode，值是 WkWorksheetmatPojo 对象的列表
            Map<String, List<WkWorksheetmatPojo>> goodsidFlowcode_ItemList_Map = matListDB.stream()
                    .collect(Collectors.groupingBy(item -> item.getGoodsid() + "-" + item.getFlowcode()));

            // 遍历新的列表
            for (WkWorksheetmatmergePojo mergeNew : mergedListNew) {
                String keyNew = mergeNew.getGoodsid() + "-" + mergeNew.getFlowcode();
                if (mergeMapDB.containsKey(keyNew)) {
                    // 如果键在数据库中的映射中存在，更新对象
                    WkWorksheetmatmergePojo mergePojoDB = mergeMapDB.get(keyNew);
                    mergeNew.setId(mergePojoDB.getId());
                    wkWorksheetmatMergeService.update(mergeNew);
                } else {
                    // 如果键在数据库中的映射中不存在，插入新对象
                    WkWorksheetmatmergePojo insert = wkWorksheetmatMergeService.insert(mergeNew);
                    mergeNew.setId(insert.getId());
                }

                // 取出原始 WkWorksheetmatPojo 对象并设置 mergeid
                List<WkWorksheetmatPojo> itemList = goodsidFlowcode_ItemList_Map.get(keyNew);
                for (WkWorksheetmatPojo item : itemList) {
                    // 更新Wk_WorksheetItem.Mergeid '合并行id'
                    this.wkWorksheetmatMapper.updateMergeid(item.getId(), mergeNew.getId(), tid);
                }
                // 从数据库中的映射中移除键
                mergeMapDB.remove(keyNew);
            }
            // 数据库中的映射中剩下的键就是需要删除的对象
            for (WkWorksheetmatmergePojo mergeDelete : mergeMapDB.values()) {
                wkWorksheetmatmergeMapper.delete(mergeDelete.getId(), tid);
            }
            //更新合并行数 100条item合并为3条merge,则取3
            this.wkWorksheetMapper.updateMatMergeCount(sheetid, mergedListNew.size(), tid);
        }
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid, String token) {
        WkWorksheetPojo wkWorksheetPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkWorksheetitemPojo> lst = wkWorksheetPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkWorksheetitemPojo wkWorksheetitemPojo : lst) {
                // 如果是合并前的原始加工单子表,禁止删除
                if (wkWorksheetitemPojo.getMergemark() != null && wkWorksheetitemPojo.getMergemark() == 1) {
                    throw new RuntimeException("已转合并单，禁止删除");
                }
                // 如果是合并单
                if (wkWorksheetitemPojo.getMergemark() != null && wkWorksheetitemPojo.getMergemark() == 2) {
                    // 拿到原始单
                    List<WkWorksheetmergePojo> mergeList = wkWorksheetmergeMapper.getMergeListByItemid(wkWorksheetitemPojo.getId(), tid);
                    for (WkWorksheetmergePojo worksheetmergePojo : mergeList) {
                        // 更新此合并单合并前原始的子项的Mergemark=0
                        wkWorksheetitemMapper.updateMergemark0(worksheetmergePojo.getWorkitemid(), tid);
                        WkWorksheetitemPojo wkWorksheetitemOrg = wkWorksheetitemMapper.getEntity(worksheetmergePojo.getWorkitemid(), tid);
                        // 更新合并前原始的加工单主表的已合并行数
                        wkWorksheetMapper.updateMergeCount(wkWorksheetitemOrg.getPid(), tid);
                    }
                }
                if (wkWorksheetitemPojo.getFinishqty() > 0) {
                    throw new RuntimeException(wkWorksheetitemPojo.getGoodsuid() + ":已有生产入库" + wkWorksheetitemPojo.getFinishqty());
                }
                this.wkWorksheetitemMapper.delete(wkWorksheetitemPojo.getId(), tid);
                // 如果是MRP需求
                if ("MRP需求".equals(wkWorksheetPojo.getBilltype())) {
                    this.wkWorksheetMapper.updateMrpWsFinish(wkWorksheetitemPojo.getMrpitemid(), wkWorksheetitemPojo.getMrpuid(), wkWorksheetPojo.getTenantid());
                    // 刷新MRP完工数
                    this.wkMrpMapper.updateFinishCount(wkWorksheetitemPojo.getMrpitemid(), wkWorksheetPojo.getTenantid());
                    // 删除Mat清单
                    this.wkWorksheetmatMapper.deleteByItemid(wkWorksheetitemPojo.getId(), wkWorksheetPojo.getTenantid());
                }
                // 同步销售订单生产数量
                if ("销售订单".equals(wkWorksheetPojo.getBilltype())) {
                    if (!"".equals(wkWorksheetitemPojo.getMachitemid()) && wkWorksheetitemPojo.getMachitemid() != null) {
                        this.wkWorksheetMapper.updateMachWKQuantity(wkWorksheetitemPojo.getMachitemid(), wkWorksheetPojo.getTenantid());
                    }
                }
                // 同步生产主计划的startqty	开单数量
                if ("生产主计划".equals(wkWorksheetPojo.getBilltype())||"MRP需求".equals(wkWorksheetPojo.getBilltype())) {
                    this.wkWorksheetMapper.updateMainPlanStartQty(wkWorksheetitemPojo.getMainplanitemid(), tid);
                }
            }
        }

        //Mat子表处理
        List<WkWorksheetmatPojo> lstmat = wkWorksheetPojo.getMat();
        if (lstmat != null) {
            //循环每个删除item子表
            for (WkWorksheetmatPojo wkWorksheetmatPojo : lstmat) {
                if (wkWorksheetmatPojo.getFinishqty() > 0) {
                    throw new RuntimeException(wkWorksheetmatPojo.getGoodsuid() + ":已有申领记录" + wkWorksheetmatPojo.getFinishqty());
                }
                this.wkWorksheetitemMapper.delete(wkWorksheetmatPojo.getId(), tid);
            }
        }
        // merge子表处理
        // 是否创建WorksheetMatMerge合并子表?
        // 读取指定系统参数 是否进行mat子表合并创建merge 为"true"时合并
        //String matmerge = systemFeignService.getConfigValue("module.manu.sheetmatmerge", tid, token).getData();
        String matmerge = InksConfigThreadLocal.getConfig("module.manu.sheetmatmerge");
        if ("true".equals(matmerge)) {
            wkWorksheetmatmergeMapper.deleteAllByPid(key, tid);
        }
        this.wkWorksheetMapper.delete(key, tid);

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(WkWorksheetitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkWsRemQty(goodsid, tid);
        });
        return wkWorksheetPojo.getRefno();
    }

    /**
     * 审核数据
     *
     * @param wkWorksheetPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWorksheetPojo approval(WkWorksheetPojo wkWorksheetPojo) {
        //主表更改
        WkWorksheetEntity wkWorksheetEntity = new WkWorksheetEntity();
        BeanUtils.copyProperties(wkWorksheetPojo, wkWorksheetEntity);
        this.wkWorksheetMapper.approval(wkWorksheetEntity);
        //返回Bill实例
        return this.getBillEntity(wkWorksheetEntity.getId(), wkWorksheetEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWorksheetPojo disannul(List<WkWorksheetitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            WkWorksheetitemPojo Pojo = lst.get(i);
            WkWorksheetitemPojo dbPojo = this.wkWorksheetitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (dbPojo.getDisannulmark() != type) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getWipused() == 1 || dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    WkWorksheetitemEntity entity = new WkWorksheetitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(tid);
                    this.wkWorksheetitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(WkWorksheetitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkWsRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.wkWorksheetMapper.updateDisannulCount(Pid, tid);
            //主表更改
            WkWorksheetEntity wkWorksheetEntity = new WkWorksheetEntity();
            wkWorksheetEntity.setId(Pid);
            wkWorksheetEntity.setLister(loginUser.getRealname());
            wkWorksheetEntity.setListerid(loginUser.getUserid());
            wkWorksheetEntity.setModifydate(new Date());
            wkWorksheetEntity.setTenantid(tid);
            this.wkWorksheetMapper.update(wkWorksheetEntity);
            //返回Bill实例
            return this.getBillEntity(wkWorksheetEntity.getId(), wkWorksheetEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWorksheetPojo closed(List<WkWorksheetitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            WkWorksheetitemPojo Pojo = lst.get(i);
            WkWorksheetitemPojo dbPojo = this.wkWorksheetitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (dbPojo.getClosed() != type) {
                    if (Pid.equals("")) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    WkWorksheetitemEntity entity = new WkWorksheetitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(tid);
                    this.wkWorksheetitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(WkWorksheetitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkWsRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.wkWorksheetMapper.updateFinishCount(Pid, tid);
            //主表更改
            WkWorksheetEntity wkWorksheetEntity = new WkWorksheetEntity();
            wkWorksheetEntity.setId(Pid);
            wkWorksheetEntity.setLister(loginUser.getRealname());
            wkWorksheetEntity.setListerid(loginUser.getUserid());
            wkWorksheetEntity.setModifydate(new Date());
            wkWorksheetEntity.setTenantid(tid);
            this.wkWorksheetMapper.update(wkWorksheetEntity);
            //返回Bill实例
            return this.getBillEntity(wkWorksheetEntity.getId(), wkWorksheetEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWorksheetPojo getBillEntityByRefNo(String key, String tid) {
        try {
            //读取主表
            WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetMapper.getEntityByRefNo(key, tid);
            //读取子表
            wkWorksheetPojo.setItem(wkWorksheetitemMapper.getList(wkWorksheetPojo.getId(), wkWorksheetPojo.getTenantid()));
            return wkWorksheetPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public WkWorksheetPojo getBillEntityByItemid(String workitemid, String tid) {
        try {
            //读取主表
            WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetMapper.getEntityByItemid(workitemid, tid);
            //读取子表
            wkWorksheetPojo.setItem(wkWorksheetitemMapper.getList(wkWorksheetPojo.getId(), wkWorksheetPojo.getTenantid()));
            return wkWorksheetPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getMachitemWKqty(String machitemid, String tid) {
        return this.wkWorksheetMapper.getMachitemWKqty(machitemid, tid);
    }

    @Override
    public int updateMergeCount(String pid, String tid) {
        return this.wkWorksheetMapper.updateMergeCount(pid, tid);
    }

    @Override
    // 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.wkWorksheetMapper.getItemCiteBillName(key, pid, tid);
    }

    @Override
    public void updatePrintcount(WkWorksheetPojo billPrintPojo) {
        this.wkWorksheetMapper.updatePrintcount(billPrintPojo);
    }

    @Override
    public WkWorksheetitemdetailPojo getItemDetailEntity(String key, String tenantid) {
        return this.wkWorksheetMapper.getItemDetailEntity(key, tenantid);
    }

    @Override
    public List<MatSpecpcbitemPojo> getMatSpecpcbitemByGoodsid(String goodsid, String tenantid) {
        return this.wkWorksheetMapper.getMatSpecpcbitemByGoodsid(goodsid, tenantid);
    }

    @Override
    public Map<String, Object> getMatSpecpcbByGoodsid(String goodsid, String tenantid) {
        return this.wkWorksheetMapper.getMatSpecpcbByGoodsid(goodsid, tenantid);
    }

    @Override
    public WkMrpPojo contrastItemList(String key, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        // 最终返回，记录增删改的结果 WkWorkSheetMatPojo.BomMark=1表示新增，2修改，-1删除
        WkMrpPojo wkMrpPojoResult = new WkMrpPojo();
        List<WkMrpitemPojo> lstMrpItemResult = new ArrayList<>();
        // 新: 改动的MrpItems
        List<WkMrpitemPojo> lstMrpItemDB = this.wkMrpitemMapper.getListBySheetid(key, tid);
        // 旧: 当前单据的所有Mat
        List<WkWorksheetmatPojo> lstSheetMat = this.wkWorksheetmatMapper.getList(key, tid);
        if (CollectionUtils.isNotEmpty(lstSheetMat)) {
            wkMrpPojoResult.setRefno(lstSheetMat.get(0).getMrpuid());
            wkMrpPojoResult.setCustom1(lstSheetMat.get(0).getPid()); //临时把mat的Pid存放在mrp的Custom1中,仅用于在refreshItemList方法设置mat的pid
        }
        // 创建一个Map以便于根据goodsid和mrpitemid查找WkMrpitemPojo
        Map<String, WkWorksheetmatPojo> sheetMatMapDB = new HashMap<>();
        for (WkWorksheetmatPojo item : lstSheetMat) {
            // 以goodsid+mrpitemid作为 对比"键"
            sheetMatMapDB.put(item.getGoodsid() + item.getMrpitemid(), item);
        }

        for (WkMrpitemPojo mrpitem : lstMrpItemDB) {
            String mrpitemKey = mrpitem.getGoodsid() + mrpitem.getId();// 对比"键"
            // 查询当前行在旧数据中是否存在
            WkWorksheetmatPojo sheetmat = sheetMatMapDB.get(mrpitemKey);
            // 存在:
            if (sheetmat != null) {
                // 从Map中移除，以便后续处理不存在于lstSheetMat中的项
                sheetMatMapDB.remove(mrpitemKey);
                // 新需求数量和原需求量对比,如果没有变化,则不返回
                if (Objects.equals(mrpitem.getBomqty(), sheetmat.getQuantity())) {
                    continue; // 跳过当前循环的剩余部分，进入下一次循环
                }
                // 有变化，进行mrp新需求量和mat已申领的对比
                mrpitem.setBommark(2); // 设置BomMark为2，表示修改
                mrpitem.setMatreqqty(sheetmat.getFinishqty());// 设置mat已申领数量
                mrpitem.setCustom2(sheetmat.getId());// 临时设置mat的id，仅用于在refreshItemList方法设置mat的id
                lstMrpItemResult.add(mrpitem);

            } else {
                // lstSheetMat有，但lstMrpItemDB没有的，设置为新增
                mrpitem.setBommark(1); // 设置BomMark为1，表示新增
                lstMrpItemResult.add(mrpitem);
            }
        }

        // 处理剩余的lstSheetMat项，即lstMrpItemDB中没有的
        if (!sheetMatMapDB.isEmpty()) {
            for (WkWorksheetmatPojo remainingItem : sheetMatMapDB.values()) {
                WkMrpitemPojo mrpitem = new WkMrpitemPojo();
                // 赋值
                BeanUtils.copyProperties(remainingItem, mrpitem);
                mrpitem.setMatreqqty(remainingItem.getFinishqty());// 设置mat已申领数量
                mrpitem.setBommark(-1); // 设置BomMark为-1，表示删除
                mrpitem.setCustom2(remainingItem.getId());// 临时设置mat的id，仅用于在refreshItemList方法设置mat的id
                lstMrpItemResult.add(mrpitem);
            }
        }
        wkMrpPojoResult.setItem(lstMrpItemResult);
        return wkMrpPojoResult;
    }

    @Override
    public Map<String, Object> refreshItemList(String key, String cmd, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        WkMrpPojo wkMrpPojo = this.contrastItemList(key, loginUser);
        String sheetid = wkMrpPojo.getCustom1();// contrastItemList方法中: 临时把mat的Pid存放在mrp的Custom1中,仅用于在refreshItemList方法设置mat的pid
        List<WkMrpitemPojo> wkMrpItemList = wkMrpPojo.getItem();
        int rowNum = 1; //更新行数
        for (WkMrpitemPojo mrpItem : wkMrpItemList) {
            Integer bomMark = mrpItem.getBommark();
            if (bomMark == 1 && cmd.contains("c")) {
                WkWorksheetmatPojo sheetMat = new WkWorksheetmatPojo();
                BeanUtils.copyProperties(mrpItem, sheetMat);
                sheetMat.setPid(sheetid);
                sheetMat.setMrpuid(wkMrpPojo.getRefno());
                sheetMat.setMrpitemid(mrpItem.getId());
                String sheetItemId = wkWorksheetitemMapper.getIdByMrpitemid(mrpItem.getItemparentid(), tid);
                sheetMat.setItemid(sheetItemId);
                sheetMat.setQuantity(mrpItem.getBomqty());
                sheetMat.setFinishqty(0D);
                this.wkWorksheetmatService.insert(sheetMat);
                rowNum++;
            } else if (bomMark == 2 && cmd.contains("u")) {
                WkWorksheetmatPojo sheetMat = new WkWorksheetmatPojo();
                sheetMat.setId(mrpItem.getCustom2());
                sheetMat.setTenantid(tid);
                sheetMat.setQuantity(mrpItem.getBomqty());
                sheetMat.setSubqty(mrpItem.getSubqty());
                sheetMat.setMainqty(mrpItem.getMainqty());
                sheetMat.setLossrate(mrpItem.getLossrate());
                sheetMat.setFlowcode(mrpItem.getFlowcode());
                this.wkWorksheetmatService.update(sheetMat);
                rowNum++;
            } else if (bomMark == -1 && cmd.contains("d")) {
                this.wkWorksheetmatMapper.delete(mrpItem.getCustom2(), tid);
                rowNum++;
            }
        }

        // billType是MRP需求时：
        // 是否创建WorksheetMatMerge合并子表?
        // 读取指定系统参数 是否进行mat子表合并创建merge 为"true"时合并
        syncMergeSheetMat(sheetid, loginUser.getToken(), tid);

        Map<String, Object> map = new HashMap<>();
        map.put("num", rowNum);
        if (rowNum > 0) {
            map.put("list", this.wkWorksheetmatMapper.getList(key, tid));
        } else {
            map.put("list", null);
        }
        //读取子表
        return map;
    }

    @Override
    public double getQuantityByMachitemid(String machitemid, String tenantid) {
        return this.wkWorksheetMapper.getQuantityByMachitemid(machitemid, tenantid);
    }

    @Override
    public String getIdByRefNo(String refno, String tenantid) {
        return this.wkWorksheetMapper.getIdByRefNo(refno, tenantid);
    }
}
