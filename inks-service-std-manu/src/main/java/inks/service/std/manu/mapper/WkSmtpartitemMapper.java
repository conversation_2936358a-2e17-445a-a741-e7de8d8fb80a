package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSmtpartitemEntity;
import inks.service.std.manu.domain.pojo.WkSmtpartitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 上料表项目(WkSmtpartitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-12 14:04:20
 */
 @Mapper
public interface WkSmtpartitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSmtpartitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSmtpartitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSmtpartitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkSmtpartitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSmtpartitemEntity wkSmtpartitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkSmtpartitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkSmtpartitemEntity wkSmtpartitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

