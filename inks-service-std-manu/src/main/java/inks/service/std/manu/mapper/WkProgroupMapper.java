package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkProgroupEntity;
import inks.service.std.manu.domain.pojo.WkProgroupPojo;
import inks.service.std.manu.domain.pojo.WkProgroupitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产制程(WkProgroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:43:17
 */
@Mapper
public interface WkProgroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProgroupPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkProgroupitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkProgroupPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param wkProgroupEntity 实例对象
     * @return 影响行数
     */
    int insert(WkProgroupEntity wkProgroupEntity);

    
    /**
     * 修改数据
     *
     * @param wkProgroupEntity 实例对象
     * @return 影响行数
     */
    int update(WkProgroupEntity wkProgroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param wkProgroupPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(WkProgroupPojo wkProgroupPojo);
                                                                                                                             }

