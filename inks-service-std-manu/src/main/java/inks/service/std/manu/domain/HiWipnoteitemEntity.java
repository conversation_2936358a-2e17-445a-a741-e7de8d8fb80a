package inks.service.std.manu.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * Wip记录子表(HiWipnoteitem)Entity
 *
 * <AUTHOR>
 * @since 2023-10-16 16:20:25
 */
public class HiWipnoteitemEntity implements Serializable {
    private static final long serialVersionUID = -64110996788931961L;
         private String id;
          // Pid
         private String pid;
          // 工序id
         private String wpid;
          // 工序编码
         private String wpcode;
          // 工序名称
         private String wpname;
          // 生产顺序
         private Integer rownum;
          // 计划完工
         private Date plandate;
          // 备注
         private String remark;
          // 入组pcs数
         private Double inpcsqty;
          // 入组Sec数
         private Double insecqty;
          // 出组pcs数
         private Double outpcsqty;
          // 出组Sec数
         private Double outsecqty;
          // 报废pcs数
         private Double mrbpcsqty;
          // 报废Sec数
         private Double mrbsecqty;
          // 完工Pcs数
         private Double comppcsqty;
          // 完工Sec数
         private Double compsecqty;
          // 辅助数量
         private Double subqty;
          // 辅助单位
         private String subunit;
          // 开始日期
         private Date startdate;
          // 结束日期
         private Date enddate;
          // 工人
         private String itemworker;
          // 委外Pcs数
         private Double epibolepcsqty;
          // 委外Sec数
         private Double epibolesecqty;
          // 最后工序
         private Integer lastwp;
          // 当前规格JSON
         private String specjson;
          // 包装规格JSON
         private String specpackjson;
          // 工作参数
         private String workparam;
          // 制表
         private String lister;
          // 新建日期
         private Date createdate;
          // 修改日期
         private Date modifydate;
          // 计划开始
         private Date startplan;
          // 检验单id
         private String inspid;
          // 检验单号
         private String inspuid;
          // 1Pass2Fail3Pending
         private Integer inspresult;
          // 禁止入组
         private Integer disablein;
          // 禁止出组
         private Integer disableout;
          // 入组操作人
         private String inworker;
          // 出组操作人
         private String outworker;
          // 自定义1
         private String custom1;
          // 自定义2
         private String custom2;
          // 自定义3
         private String custom3;
          // 自定义4
         private String custom4;
          // 自定义5
         private String custom5;
          // 自定义6
         private String custom6;
          // 自定义7
         private String custom7;
          // 自定义8
         private String custom8;
          // 自定义9
         private String custom9;
          // 自定义10
         private String custom10;
          // 租户id
         private String tenantid;
          // 乐观锁
         private Integer revision;

 
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // Pid
  
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
    // 工序id
  
    public String getWpid() {
        return wpid;
    }

    public void setWpid(String wpid) {
        this.wpid = wpid;
    }
    // 工序编码
  
    public String getWpcode() {
        return wpcode;
    }

    public void setWpcode(String wpcode) {
        this.wpcode = wpcode;
    }
    // 工序名称
  
    public String getWpname() {
        return wpname;
    }

    public void setWpname(String wpname) {
        this.wpname = wpname;
    }
    // 生产顺序
  
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 计划完工
  
    public Date getPlandate() {
        return plandate;
    }

    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }
    // 备注
  
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 入组pcs数
  
    public Double getInpcsqty() {
        return inpcsqty;
    }

    public void setInpcsqty(Double inpcsqty) {
        this.inpcsqty = inpcsqty;
    }
    // 入组Sec数
  
    public Double getInsecqty() {
        return insecqty;
    }

    public void setInsecqty(Double insecqty) {
        this.insecqty = insecqty;
    }
    // 出组pcs数
  
    public Double getOutpcsqty() {
        return outpcsqty;
    }

    public void setOutpcsqty(Double outpcsqty) {
        this.outpcsqty = outpcsqty;
    }
    // 出组Sec数
  
    public Double getOutsecqty() {
        return outsecqty;
    }

    public void setOutsecqty(Double outsecqty) {
        this.outsecqty = outsecqty;
    }
    // 报废pcs数
  
    public Double getMrbpcsqty() {
        return mrbpcsqty;
    }

    public void setMrbpcsqty(Double mrbpcsqty) {
        this.mrbpcsqty = mrbpcsqty;
    }
    // 报废Sec数
  
    public Double getMrbsecqty() {
        return mrbsecqty;
    }

    public void setMrbsecqty(Double mrbsecqty) {
        this.mrbsecqty = mrbsecqty;
    }
    // 完工Pcs数
  
    public Double getComppcsqty() {
        return comppcsqty;
    }

    public void setComppcsqty(Double comppcsqty) {
        this.comppcsqty = comppcsqty;
    }
    // 完工Sec数
  
    public Double getCompsecqty() {
        return compsecqty;
    }

    public void setCompsecqty(Double compsecqty) {
        this.compsecqty = compsecqty;
    }
    // 辅助数量
  
    public Double getSubqty() {
        return subqty;
    }

    public void setSubqty(Double subqty) {
        this.subqty = subqty;
    }
    // 辅助单位
  
    public String getSubunit() {
        return subunit;
    }

    public void setSubunit(String subunit) {
        this.subunit = subunit;
    }
    // 开始日期
  
    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }
    // 结束日期
  
    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }
    // 工人
  
    public String getItemworker() {
        return itemworker;
    }

    public void setItemworker(String itemworker) {
        this.itemworker = itemworker;
    }
    // 委外Pcs数
  
    public Double getEpibolepcsqty() {
        return epibolepcsqty;
    }

    public void setEpibolepcsqty(Double epibolepcsqty) {
        this.epibolepcsqty = epibolepcsqty;
    }
    // 委外Sec数
  
    public Double getEpibolesecqty() {
        return epibolesecqty;
    }

    public void setEpibolesecqty(Double epibolesecqty) {
        this.epibolesecqty = epibolesecqty;
    }
    // 最后工序
  
    public Integer getLastwp() {
        return lastwp;
    }

    public void setLastwp(Integer lastwp) {
        this.lastwp = lastwp;
    }
    // 当前规格JSON
  
    public String getSpecjson() {
        return specjson;
    }

    public void setSpecjson(String specjson) {
        this.specjson = specjson;
    }
    // 包装规格JSON
  
    public String getSpecpackjson() {
        return specpackjson;
    }

    public void setSpecpackjson(String specpackjson) {
        this.specpackjson = specpackjson;
    }
    // 工作参数
  
    public String getWorkparam() {
        return workparam;
    }

    public void setWorkparam(String workparam) {
        this.workparam = workparam;
    }
    // 制表
  
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
    // 新建日期
  
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
    // 修改日期
  
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
    // 计划开始
  
    public Date getStartplan() {
        return startplan;
    }

    public void setStartplan(Date startplan) {
        this.startplan = startplan;
    }
    // 检验单id
  
    public String getInspid() {
        return inspid;
    }

    public void setInspid(String inspid) {
        this.inspid = inspid;
    }
    // 检验单号
  
    public String getInspuid() {
        return inspuid;
    }

    public void setInspuid(String inspuid) {
        this.inspuid = inspuid;
    }
    // 1Pass2Fail3Pending
  
    public Integer getInspresult() {
        return inspresult;
    }

    public void setInspresult(Integer inspresult) {
        this.inspresult = inspresult;
    }
    // 禁止入组
  
    public Integer getDisablein() {
        return disablein;
    }

    public void setDisablein(Integer disablein) {
        this.disablein = disablein;
    }
    // 禁止出组
  
    public Integer getDisableout() {
        return disableout;
    }

    public void setDisableout(Integer disableout) {
        this.disableout = disableout;
    }
    // 入组操作人
  
    public String getInworker() {
        return inworker;
    }

    public void setInworker(String inworker) {
        this.inworker = inworker;
    }
    // 出组操作人
  
    public String getOutworker() {
        return outworker;
    }

    public void setOutworker(String outworker) {
        this.outworker = outworker;
    }
    // 自定义1
  
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2
  
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3
  
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4
  
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5
  
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 自定义6
  
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
    // 自定义7
  
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
    // 自定义8
  
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
    // 自定义9
  
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
    // 自定义10
  
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
    // 租户id
  
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
    // 乐观锁
  
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

