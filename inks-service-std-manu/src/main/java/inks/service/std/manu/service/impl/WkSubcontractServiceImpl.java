package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSubcontractEntity;
import inks.service.std.manu.domain.WkSubcontractitemEntity;
import inks.service.std.manu.domain.WkSubcontractmatEntity;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.mapper.*;
import inks.service.std.manu.service.WkSubcontractService;
import inks.service.std.manu.service.WkSubcontractitemService;
import inks.service.std.manu.service.WkSubcontractmatService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 委制单据(WkSubcontract)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-16 08:18:47
 */
@Service("wkSubcontractService")
public class WkSubcontractServiceImpl implements WkSubcontractService {
    @Resource
    private WkSubcontractMapper wkSubcontractMapper;

    @Resource
    private WkSubcontractitemMapper wkSubcontractitemMapper;
    @Resource
    private TransactionTemplate transactionTemplate;
    /**
     * 服务对象Item
     */
    @Resource
    private WkSubcontractitemService wkSubcontractitemService;

    /**
     * 服务对象Item
     */
    @Resource
    private WkSubcontractmatService wkSubcontractmatService;

    @Resource
    private WkMrpitemMapper wkMrpitemMapper;

    @Resource
    private WkMrpMapper wkMrpMapper;

    @Resource
    private WkSubcontractmatMapper wkSubcontractmatMapper;

    @Resource
    private manu_SyncMapper manuSyncMapper;

    // 设置主表.未税金额,税额,含税金额=子表累加
    private static void calculateAmount(List<WkSubcontractitemPojo> lst, WkSubcontractEntity wkSubcontractEntity) {
        double billTaxAmount = 0.0;
        double billAmount = 0.0;
        double billTaxTotal = 0.0;
        for (WkSubcontractitemPojo item : lst) {
            billTaxAmount += Optional.ofNullable(item.getTaxamount()).orElse(0.0);
            billAmount += Optional.ofNullable(item.getAmount()).orElse(0.0);
            billTaxTotal += Optional.ofNullable(item.getTaxtotal()).orElse(0.0);
        }
        wkSubcontractEntity.setBilltaxamount(billTaxAmount);
        wkSubcontractEntity.setBillamount(billAmount);
        wkSubcontractEntity.setBilltaxtotal(billTaxTotal);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSubcontractPojo getEntity(String key, String tid) {
        return this.wkSubcontractMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSubcontractitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSubcontractitemdetailPojo> lst = wkSubcontractMapper.getPageList(queryParam);
            PageInfo<WkSubcontractitemdetailPojo> pageInfo = new PageInfo<WkSubcontractitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSubcontractPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkSubcontractPojo wkSubcontractPojo = this.wkSubcontractMapper.getEntity(key, tid);
            //读取子表
            wkSubcontractPojo.setItem(wkSubcontractitemMapper.getList(wkSubcontractPojo.getId(), wkSubcontractPojo.getTenantid()));
            //读取物料子表
            wkSubcontractPojo.setMat(wkSubcontractmatMapper.getList(wkSubcontractPojo.getId(), wkSubcontractPojo.getTenantid()));
            return wkSubcontractPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSubcontractPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSubcontractPojo> lst = wkSubcontractMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkSubcontractitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkSubcontractPojo> pageInfo = new PageInfo<WkSubcontractPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSubcontractPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSubcontractPojo> lst = wkSubcontractMapper.getPageTh(queryParam);
            PageInfo<WkSubcontractPojo> pageInfo = new PageInfo<WkSubcontractPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkSubcontractPojo 实例对象
     * @return 实例对象
     */
    @Override
//    @Transactional
    public WkSubcontractPojo insert(WkSubcontractPojo wkSubcontractPojo) {
//初始化NULL字段
        if (wkSubcontractPojo.getRefno() == null) wkSubcontractPojo.setRefno("");
        if (wkSubcontractPojo.getBilltype() == null) wkSubcontractPojo.setBilltype("");
        if (wkSubcontractPojo.getBilldate() == null) wkSubcontractPojo.setBilldate(new Date());
        if (wkSubcontractPojo.getBilltitle() == null) wkSubcontractPojo.setBilltitle("");
        if (wkSubcontractPojo.getTaxrate() == null) wkSubcontractPojo.setTaxrate(0);
        if (wkSubcontractPojo.getOperator() == null) wkSubcontractPojo.setOperator("");
        if (wkSubcontractPojo.getGroupid() == null) wkSubcontractPojo.setGroupid("");
        if (wkSubcontractPojo.getSummary() == null) wkSubcontractPojo.setSummary("");
        if (wkSubcontractPojo.getCreateby() == null) wkSubcontractPojo.setCreateby("");
        if (wkSubcontractPojo.getCreatebyid() == null) wkSubcontractPojo.setCreatebyid("");
        if (wkSubcontractPojo.getCreatedate() == null) wkSubcontractPojo.setCreatedate(new Date());
        if (wkSubcontractPojo.getLister() == null) wkSubcontractPojo.setLister("");
        if (wkSubcontractPojo.getListerid() == null) wkSubcontractPojo.setListerid("");
        if (wkSubcontractPojo.getModifydate() == null) wkSubcontractPojo.setModifydate(new Date());
        if (wkSubcontractPojo.getAssessor() == null) wkSubcontractPojo.setAssessor("");
        if (wkSubcontractPojo.getAssessorid() == null) wkSubcontractPojo.setAssessorid("");
        if (wkSubcontractPojo.getAssessdate() == null) wkSubcontractPojo.setAssessdate(new Date());
        if (wkSubcontractPojo.getBillstatecode() == null) wkSubcontractPojo.setBillstatecode("");
        if (wkSubcontractPojo.getBillstatedate() == null) wkSubcontractPojo.setBillstatedate(new Date());
        if (wkSubcontractPojo.getBillstartdate() == null) wkSubcontractPojo.setBillstartdate(new Date());
        if (wkSubcontractPojo.getBillplandate() == null) wkSubcontractPojo.setBillplandate(new Date());
        if (wkSubcontractPojo.getBilltaxamount() == null) wkSubcontractPojo.setBilltaxamount(0D);
        if (wkSubcontractPojo.getBilltaxtotal() == null) wkSubcontractPojo.setBilltaxtotal(0D);
        if (wkSubcontractPojo.getBillamount() == null) wkSubcontractPojo.setBillamount(0D);
        if (wkSubcontractPojo.getItemcount() == null) wkSubcontractPojo.setItemcount(0);
        if (wkSubcontractPojo.getDisannulcount() == null) wkSubcontractPojo.setDisannulcount(0);
        if (wkSubcontractPojo.getFinishcount() == null) wkSubcontractPojo.setFinishcount(0);
        if (wkSubcontractPojo.getPrepayments()== null) wkSubcontractPojo.setPrepayments(0D);
        if (wkSubcontractPojo.getPrintcount() == null) wkSubcontractPojo.setPrintcount(0);
        if (wkSubcontractPojo.getOaflowmark() == null) wkSubcontractPojo.setOaflowmark(0);
        if(wkSubcontractPojo.getArrivaladd()==null) wkSubcontractPojo.setArrivaladd("");
        if(wkSubcontractPojo.getPayment()==null) wkSubcontractPojo.setPayment("");
        if (wkSubcontractPojo.getCustom1() == null) wkSubcontractPojo.setCustom1("");
        if (wkSubcontractPojo.getCustom2() == null) wkSubcontractPojo.setCustom2("");
        if (wkSubcontractPojo.getCustom3() == null) wkSubcontractPojo.setCustom3("");
        if (wkSubcontractPojo.getCustom4() == null) wkSubcontractPojo.setCustom4("");
        if (wkSubcontractPojo.getCustom5() == null) wkSubcontractPojo.setCustom5("");
        if (wkSubcontractPojo.getCustom6() == null) wkSubcontractPojo.setCustom6("");
        if (wkSubcontractPojo.getCustom7() == null) wkSubcontractPojo.setCustom7("");
        if (wkSubcontractPojo.getCustom8() == null) wkSubcontractPojo.setCustom8("");
        if (wkSubcontractPojo.getCustom9() == null) wkSubcontractPojo.setCustom9("");
        if (wkSubcontractPojo.getCustom10() == null) wkSubcontractPojo.setCustom10("");
        String tid = wkSubcontractPojo.getTenantid();
        if (tid == null) wkSubcontractPojo.setTenantid("");
        if (wkSubcontractPojo.getTenantname() == null) wkSubcontractPojo.setTenantname("");
        if (wkSubcontractPojo.getRevision() == null) wkSubcontractPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkSubcontractEntity wkSubcontractEntity = new WkSubcontractEntity();
        BeanUtils.copyProperties(wkSubcontractPojo, wkSubcontractEntity);
        //设置id和新建日期
        wkSubcontractEntity.setId(id);
        wkSubcontractEntity.setRevision(1);  //乐观锁

        List<WkSubcontractitemPojo> lst = wkSubcontractPojo.getItem();
        // 设置主表.未税金额,税额,含税金额=子表累加
        calculateAmount(lst, wkSubcontractEntity);
        // 在需要的地方设置事务隔离级别为READ_COMMITTED
        transactionTemplate.setIsolationLevel(Isolation.READ_COMMITTED.value());
        transactionTemplate.execute((status) -> {
            //插入主表
            this.wkSubcontractMapper.insert(wkSubcontractEntity);
            //Item子表处理
            //循环每个item子表
            for (WkSubcontractitemPojo wkSubcontractitemPojo : lst) {
                //初始化item的NULL
                WkSubcontractitemPojo itemPojo = this.wkSubcontractitemService.clearNull(wkSubcontractitemPojo);
                WkSubcontractitemEntity wkSubcontractitemEntity = new WkSubcontractitemEntity();
                BeanUtils.copyProperties(itemPojo, wkSubcontractitemEntity);
                //设置id和Pid
                wkSubcontractitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkSubcontractitemEntity.setPid(id);
                wkSubcontractitemEntity.setTenantid(tid);
                wkSubcontractitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkSubcontractitemMapper.insert(wkSubcontractitemEntity);
                // 如果是MRP需求
                if ("MRP需求".equals(wkSubcontractPojo.getBilltype())) {
                    this.wkSubcontractMapper.updateMrpScFinish(itemPojo.getMrpitemid(), itemPojo.getMrpuid(), tid);
                    WkMrpitemPojo wkMrpitemPojo = this.wkMrpitemMapper.getEntity(itemPojo.getMrpitemid(), tid);
                    if (wkMrpitemPojo != null && wkMrpitemPojo.getWkwsqty() + wkMrpitemPojo.getWkscqty() > wkMrpitemPojo.getNeedqty()) {
                        double wkqty = wkMrpitemPojo.getWkwsqty() + wkMrpitemPojo.getWkscqty();
                        throw new RuntimeException(wkSubcontractitemPojo.getGoodsuid() + ":生产总数" + wkqty + "大于需求数" + wkMrpitemPojo.getNeedqty());
                    }
                    // 刷新MRP完工数
                    this.wkMrpMapper.updateFinishCount(itemPojo.getMrpitemid(), tid);
                    // 生成物料表
                    if (wkMrpitemPojo != null) {
                        List<WkMrpitemPojo> lstMrpitem = this.wkMrpMapper.getItemListByParentid(wkMrpitemPojo.getId(), tid);
                        for (int k = 0; k < lstMrpitem.size(); k++) {
                            WkMrpitemPojo mrpitemPojo = lstMrpitem.get(k);
                            WkSubcontractmatEntity matEntity = new WkSubcontractmatEntity();
                            matEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                            matEntity.setPid(id);
                            matEntity.setItemid(wkSubcontractitemEntity.getId());
                            matEntity.setGoodsid(mrpitemPojo.getGoodsid());
                            matEntity.setMainqty(mrpitemPojo.getMainqty());
                            matEntity.setSubqty(mrpitemPojo.getSubqty());
                            matEntity.setLossrate(mrpitemPojo.getLossrate());
                            Double matqty = wkSubcontractitemPojo.getQuantity() / matEntity.getMainqty() * matEntity.getSubqty() * (1 + matEntity.getLossrate() / 100);
                            matEntity.setQuantity(matqty);
                            matEntity.setFinishqty(0D);
                            matEntity.setMrpuid(wkSubcontractitemPojo.getMrpuid());
                            matEntity.setMrpitemid(wkSubcontractitemPojo.getMrpitemid());
                            matEntity.setRownum(k);
                            matEntity.setClosed(0);
                            matEntity.setTenantid(tid);
                            matEntity.setRevision(1);
                            this.wkSubcontractmatMapper.insert(matEntity);
                        }
                    }
                }
            }

            // 手工加工单
            if (!"MRP需求".equals(wkSubcontractPojo.getBilltype())) {

                //Item子表处理
                List<WkSubcontractmatPojo> lstmat = wkSubcontractPojo.getMat();
                if (lstmat != null) {
                    //循环每个item子表
                    for (WkSubcontractmatPojo wkSubcontractmatPojo : lstmat) {
                        //初始化item的NULL
                        WkSubcontractmatPojo matPojo = this.wkSubcontractmatService.clearNull(wkSubcontractmatPojo);
                        WkSubcontractmatEntity wkSubcontractmatEntity = new WkSubcontractmatEntity();
                        BeanUtils.copyProperties(matPojo, wkSubcontractmatEntity);
                        //设置id和Pid
                        wkSubcontractmatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                        wkSubcontractmatEntity.setPid(id);
                        wkSubcontractmatEntity.setTenantid(tid);
                        wkSubcontractmatEntity.setRevision(1);  //乐观锁
                        //插入子表
                        this.wkSubcontractmatMapper.insert(wkSubcontractmatEntity);
                    }
                }
            }
            //goodsid去重后 同步货品数量 MQ生产者  nanno 20230222
            Set<String> goodsidLstSet = lst.stream()
                    .map(WkSubcontractitemPojo::getGoodsid)
                    .collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                manuSyncMapper.updateGoodsWkScRemQty(goodsid, tid);
            });
            return Boolean.TRUE;
        });


        //返回Bill实例
        return this.getBillEntity(wkSubcontractEntity.getId(), wkSubcontractEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSubcontractPojo 实例对象
     * @return 实例对象
     */
    @Override
//    @Transactional
    public WkSubcontractPojo update(WkSubcontractPojo wkSubcontractPojo) {
        String tid = wkSubcontractPojo.getTenantid();
        // 收集删除的所有goodsid
        Set<String> goodsIdSetDelete = new HashSet<>();
        //主表更改
        WkSubcontractEntity wkSubcontractEntity = new WkSubcontractEntity();
        BeanUtils.copyProperties(wkSubcontractPojo, wkSubcontractEntity);
        List<WkSubcontractitemPojo> lst = wkSubcontractPojo.getItem();
        if (lst == null) throw new RuntimeException("单据内容不能为空");
        // 设置主表.未税金额,税额,含税金额=子表累加
        calculateAmount(lst, wkSubcontractEntity);
        // 在需要的地方设置事务隔离级别为READ_COMMITTED
        transactionTemplate.setIsolationLevel(Isolation.READ_COMMITTED.value());
        transactionTemplate.execute((status) -> {
            this.wkSubcontractMapper.update(wkSubcontractEntity);
            {
                //Item子表处理
                //获取被删除的Item
                List<String> lstDelIds = wkSubcontractMapper.getDelItemIds(wkSubcontractPojo);
                if (lstDelIds != null) {
                    //循环每个删除item子表
                    for (String lstDelId : lstDelIds) {
                        WkSubcontractitemPojo dbPojo = this.wkSubcontractitemMapper.getEntity(lstDelId, wkSubcontractEntity.getTenantid());
                        this.wkSubcontractitemMapper.delete(lstDelId, wkSubcontractEntity.getTenantid());
                        // 如果是MRP需求
                        if ("MRP需求".equals(wkSubcontractPojo.getBilltype())) {
                            this.wkSubcontractMapper.updateMrpScFinish(dbPojo.getMrpitemid(), dbPojo.getMrpuid(), tid);
                            // 刷新MRP完工数
                            this.wkMrpMapper.updateFinishCount(dbPojo.getMrpitemid(), tid);
                            // 删除Mat清单
                            this.wkSubcontractmatMapper.deleteByItemid(dbPojo.getId(), tid);
                        }
                        goodsIdSetDelete.add(dbPojo.getGoodsid());

                    }
                }
                //循环每个item子表
                for (WkSubcontractitemPojo wkSubcontractitemPojo : lst) {
                    String itemId = wkSubcontractitemPojo.getId();
                    WkSubcontractitemEntity wkSubcontractitemEntity = new WkSubcontractitemEntity();
                    if ("".equals(itemId) || itemId == null) {
                        //初始化item的NULL
                        WkSubcontractitemPojo itemPojo = this.wkSubcontractitemService.clearNull(wkSubcontractitemPojo);
                        BeanUtils.copyProperties(itemPojo, wkSubcontractitemEntity);
                        //设置id和Pid
                        wkSubcontractitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkSubcontractitemEntity.setPid(wkSubcontractEntity.getId());  // 主表 id
                        wkSubcontractitemEntity.setTenantid(tid);   // 租户id
                        wkSubcontractitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkSubcontractitemMapper.insert(wkSubcontractitemEntity);
                    } else {
                        BeanUtils.copyProperties(wkSubcontractitemPojo, wkSubcontractitemEntity);
                        wkSubcontractitemEntity.setTenantid(tid);
                        this.wkSubcontractitemMapper.update(wkSubcontractitemEntity);
                    }

                    // 如果是MRP需求
                    if ("MRP需求".equals(wkSubcontractPojo.getBilltype())) {
                        double orgQty = 0D;
                        if (StringUtils.isNotBlank(itemId)) {
                            WkSubcontractitemPojo entity = wkSubcontractitemMapper.getEntity(itemId, tid);
                            orgQty = (entity != null) ? entity.getQuantity() : 0D;
                        }
                        this.wkSubcontractMapper.updateMrpScFinish(wkSubcontractitemPojo.getMrpitemid(), wkSubcontractitemPojo.getMrpuid(), tid);
                        WkMrpitemPojo wkMrpitemPojo = this.wkMrpitemMapper.getEntity(wkSubcontractitemPojo.getMrpitemid(), tid);
                        if (wkMrpitemPojo != null) {
                            double wkqty = wkMrpitemPojo.getWkwsqty() + wkMrpitemPojo.getWkscqty() - orgQty;
                            double needqty = wkMrpitemPojo.getNeedqty();
                            if (wkqty > needqty) {
                                throw new RuntimeException(wkSubcontractitemPojo.getGoodsuid() + ":生产总数" + wkqty + "大于需求数" + needqty);
                            }
                        }
                        // 刷新MRP完工数
                        this.wkMrpMapper.updateFinishCount(wkSubcontractitemPojo.getMrpitemid(), tid);
                        // 更新物料表
                        if (wkMrpitemPojo != null) {
                            List<WkSubcontractmatPojo> lstMrpmat = this.wkSubcontractmatMapper.getListByItemid(wkMrpitemPojo.getId(), tid);
                            for (WkSubcontractmatPojo wkSubcontractmatPojo : lstMrpmat) {
                                WkSubcontractmatEntity matEntity = new WkSubcontractmatEntity();
                                BeanUtils.copyProperties(wkSubcontractmatPojo, matEntity);
                                Double matqty = wkSubcontractitemPojo.getQuantity() / matEntity.getMainqty() * matEntity.getSubqty() * (1 + matEntity.getLossrate() / 100);
                                matEntity.setQuantity(matqty);
                                matEntity.setRevision(matEntity.getRevision() + 1);
                                this.wkSubcontractmatMapper.update(matEntity);
                            }
                        }
                    }
                }
            }
            // 手工加工单
            if (!"MRP需求".equals(wkSubcontractPojo.getBilltype()) && wkSubcontractPojo.getMat() != null) {

                //Item子表处理
                List<WkSubcontractmatPojo> lstMat = wkSubcontractPojo.getMat();
                //获取被删除的Item
                List<String> lstDelIds = wkSubcontractMapper.getDelMatIds(wkSubcontractPojo);
                if (lstDelIds != null) {
                    //循环每个删除item子表
                    for (String lstDelId : lstDelIds) {
                        WkSubcontractmatPojo dbPojo = this.wkSubcontractmatMapper.getEntity(lstDelId, wkSubcontractEntity.getTenantid());
                        this.wkSubcontractmatMapper.delete(lstDelId, wkSubcontractEntity.getTenantid());
                    }
                }
                if (lstMat != null) {
                    //循环每个item子表
                    for (WkSubcontractmatPojo wkSubcontractmatPojo : lstMat) {
                        WkSubcontractmatEntity wkSubcontractmatEntity = new WkSubcontractmatEntity();
                        if ("".equals(wkSubcontractmatPojo.getId()) || wkSubcontractmatPojo.getId() == null) {
                            //初始化item的NULL
                            WkSubcontractmatPojo matPojo = this.wkSubcontractmatService.clearNull(wkSubcontractmatPojo);
                            BeanUtils.copyProperties(matPojo, wkSubcontractmatEntity);
                            //设置id和Pid
                            wkSubcontractmatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                            wkSubcontractmatEntity.setPid(wkSubcontractEntity.getId());  // 主表 id
                            wkSubcontractmatEntity.setTenantid(tid);   // 租户id
                            wkSubcontractmatEntity.setRevision(1);  // 乐观锁
                            //插入子表
                            this.wkSubcontractmatMapper.insert(wkSubcontractmatEntity);
                        } else {
                            BeanUtils.copyProperties(wkSubcontractmatPojo, wkSubcontractmatEntity);
                            wkSubcontractmatEntity.setTenantid(tid);
                            this.wkSubcontractmatMapper.update(wkSubcontractmatEntity);
                        }
                    }
                }

            }
            // 收集去重的所有goodsid
            Set<String> goodsIdSetFront = lst.stream()
                    .map(WkSubcontractitemPojo::getGoodsid)
                    .collect(Collectors.toSet());
            goodsIdSetDelete.addAll(goodsIdSetFront);//所有goodsid
            // 同步货品数量 SQL替代MQ
            goodsIdSetDelete.forEach(goodsid -> {
                manuSyncMapper.updateGoodsWkScRemQty(goodsid, tid);
            });
            return Boolean.TRUE;
        });

        //返回Bill实例
        return this.getBillEntity(wkSubcontractEntity.getId(), wkSubcontractEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
//    @Transactional
    public String delete(String key, String tid) {
        WkSubcontractPojo wkSubcontractPojo = this.getBillEntity(key, tid);
        List<WkSubcontractitemPojo> lst = wkSubcontractPojo.getItem();
        // 在需要的地方设置事务隔离级别为READ_COMMITTED
        transactionTemplate.setIsolationLevel(Isolation.READ_COMMITTED.value());
        transactionTemplate.execute((status) -> {
            //Item子表处理
            if (lst != null) {
                //循环每个删除item子表
                for (WkSubcontractitemPojo wkSubcontractitemPojo : lst) {
                    if (wkSubcontractitemPojo.getFinishqty() > 0) {
                        throw new RuntimeException(wkSubcontractitemPojo.getGoodsuid() + ":已有加工入库" + wkSubcontractitemPojo.getFinishqty());
                    }
                    this.wkSubcontractitemMapper.delete(wkSubcontractitemPojo.getId(), tid);
                    // 如果是MRP需求
                    if ("MRP需求".equals(wkSubcontractPojo.getBilltype())) {
                        this.wkSubcontractMapper.updateMrpScFinish(wkSubcontractitemPojo.getMrpitemid(), wkSubcontractitemPojo.getMrpuid(), wkSubcontractPojo.getTenantid());
                        // 刷新MRP完工数
                        this.wkMrpMapper.updateFinishCount(wkSubcontractitemPojo.getMrpitemid(), wkSubcontractPojo.getTenantid());
                        // 删除Mat清单
                        this.wkSubcontractmatMapper.deleteByItemid(wkSubcontractitemPojo.getId(), wkSubcontractPojo.getTenantid());
                    }
                }
            }
            //Mat子表处理
            List<WkSubcontractmatPojo> lstmat = wkSubcontractPojo.getMat();
            if (lstmat != null) {
                //循环每个删除item子表
                for (WkSubcontractmatPojo wkSubcontractmatPojo : lstmat) {
                    if (wkSubcontractmatPojo.getFinishqty() > 0) {
                        throw new RuntimeException(wkSubcontractmatPojo.getGoodsuid() + ":已有申领记录" + wkSubcontractmatPojo.getFinishqty());
                    }
                    this.wkSubcontractitemMapper.delete(wkSubcontractmatPojo.getId(), tid);
                }
            }
            this.wkSubcontractMapper.delete(key, tid);
            // 收集去重的所有goodsid
            Set<String> goodsIdSetDelete = lst.stream()
                    .map(WkSubcontractitemPojo::getGoodsid)
                    .collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsIdSetDelete.forEach(goodsid -> {
                manuSyncMapper.updateGoodsWkScRemQty(goodsid, tid);
            });
            return Boolean.TRUE;
        });

        return wkSubcontractPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkSubcontractPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSubcontractPojo approval(WkSubcontractPojo wkSubcontractPojo) {
        //主表更改
        WkSubcontractEntity wkSubcontractEntity = new WkSubcontractEntity();
        BeanUtils.copyProperties(wkSubcontractPojo, wkSubcontractEntity);
        this.wkSubcontractMapper.approval(wkSubcontractEntity);
        //返回Bill实例
        return this.getBillEntity(wkSubcontractEntity.getId(), wkSubcontractEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSubcontractPojo disannul(List<WkSubcontractitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            WkSubcontractitemPojo Pojo = lst.get(i);
            WkSubcontractitemPojo dbPojo = this.wkSubcontractitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    WkSubcontractitemEntity entity = new WkSubcontractitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(tid);
                    this.wkSubcontractitemMapper.update(entity);
                    disNum++;

                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        // 收集去重的所有goodsid
        Set<String> goodsIdSetDelete = lst.stream()
                .map(WkSubcontractitemPojo::getGoodsid)
                .collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsIdSetDelete.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkScRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.wkSubcontractMapper.updateDisannulCount(Pid, tid);
            //主表更改
            WkSubcontractEntity wkSubcontractEntity = new WkSubcontractEntity();
            wkSubcontractEntity.setId(Pid);
            wkSubcontractEntity.setLister(loginUser.getRealname());
            wkSubcontractEntity.setListerid(loginUser.getUserid());
            wkSubcontractEntity.setModifydate(new Date());
            wkSubcontractEntity.setTenantid(tid);
            this.wkSubcontractMapper.update(wkSubcontractEntity);
            //返回Bill实例
            return this.getBillEntity(wkSubcontractEntity.getId(), wkSubcontractEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSubcontractPojo closed(List<WkSubcontractitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            WkSubcontractitemPojo Pojo = lst.get(i);
            WkSubcontractitemPojo dbPojo = this.wkSubcontractitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    WkSubcontractitemEntity entity = new WkSubcontractitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(tid);
                    this.wkSubcontractitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        // 收集去重的所有goodsid
        Set<String> goodsIdSetDelete = lst.stream()
                .map(WkSubcontractitemPojo::getGoodsid)
                .collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsIdSetDelete.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkScRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.wkSubcontractMapper.updateFinishCount(Pid, tid);
            //主表更改
            WkSubcontractEntity wkSubcontractEntity = new WkSubcontractEntity();
            wkSubcontractEntity.setId(Pid);
            wkSubcontractEntity.setLister(loginUser.getRealname());
            wkSubcontractEntity.setListerid(loginUser.getUserid());
            wkSubcontractEntity.setModifydate(new Date());
            wkSubcontractEntity.setTenantid(tid);
            this.wkSubcontractMapper.update(wkSubcontractEntity);
            //返回Bill实例
            return this.getBillEntity(wkSubcontractEntity.getId(), wkSubcontractEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    // 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.wkSubcontractMapper.getItemCiteBillName(key, pid, tid);
    }

    @Override
    public void updatePrintcount(WkSubcontractPojo billPrintPojo) {
        this.wkSubcontractMapper.updatePrintcount(billPrintPojo);
    }
}
