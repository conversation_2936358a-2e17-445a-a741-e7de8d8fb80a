package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWorksheetmatmergeEntity;
import inks.service.std.manu.domain.pojo.WkWorksheetmatmergePojo;
import inks.service.std.manu.mapper.WkWorksheetmatmergeMapper;
import inks.service.std.manu.service.WkWorksheetmatmergeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 厂制物料合并表(WkWorksheetmatmerge)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-11 16:07:47
 */
@Service("wkWorksheetmatmergeService")
public class WkWorksheetmatmergeServiceImpl implements WkWorksheetmatmergeService {
    @Resource
    private WkWorksheetmatmergeMapper wkWorksheetmatmergeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWorksheetmatmergePojo getEntity(String key, String tid) {
        return this.wkWorksheetmatmergeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWorksheetmatmergePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWorksheetmatmergePojo> lst = wkWorksheetmatmergeMapper.getPageList(queryParam);
            PageInfo<WkWorksheetmatmergePojo> pageInfo = new PageInfo<WkWorksheetmatmergePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkWorksheetmatmergePojo> getList(String Pid, String tid) {
        try {
            List<WkWorksheetmatmergePojo> lst = wkWorksheetmatmergeMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkWorksheetmatmergePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetmatmergePojo insert(WkWorksheetmatmergePojo wkWorksheetmatmergePojo) {
        //初始化item的NULL
        WkWorksheetmatmergePojo itempojo = this.clearNull(wkWorksheetmatmergePojo);
        WkWorksheetmatmergeEntity wkWorksheetmatmergeEntity = new WkWorksheetmatmergeEntity();
        BeanUtils.copyProperties(itempojo, wkWorksheetmatmergeEntity);
        //生成雪花id
        wkWorksheetmatmergeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWorksheetmatmergeEntity.setRevision(1);  //乐观锁
        this.wkWorksheetmatmergeMapper.insert(wkWorksheetmatmergeEntity);
        return this.getEntity(wkWorksheetmatmergeEntity.getId(), wkWorksheetmatmergeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWorksheetmatmergePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetmatmergePojo update(WkWorksheetmatmergePojo wkWorksheetmatmergePojo) {
        WkWorksheetmatmergeEntity wkWorksheetmatmergeEntity = new WkWorksheetmatmergeEntity();
        BeanUtils.copyProperties(wkWorksheetmatmergePojo, wkWorksheetmatmergeEntity);
        this.wkWorksheetmatmergeMapper.update(wkWorksheetmatmergeEntity);
        return this.getEntity(wkWorksheetmatmergeEntity.getId(), wkWorksheetmatmergeEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWorksheetmatmergeMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkWorksheetmatmergePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetmatmergePojo clearNull(WkWorksheetmatmergePojo wkWorksheetmatmergePojo) {
        //初始化NULL字段
        if (wkWorksheetmatmergePojo.getPid() == null) wkWorksheetmatmergePojo.setPid("");
        if (wkWorksheetmatmergePojo.getItemid() == null) wkWorksheetmatmergePojo.setItemid("");
        if (wkWorksheetmatmergePojo.getGoodsid() == null) wkWorksheetmatmergePojo.setGoodsid("");
        if (wkWorksheetmatmergePojo.getQuantity() == null) wkWorksheetmatmergePojo.setQuantity(0D);
        if (wkWorksheetmatmergePojo.getFinishqty() == null) wkWorksheetmatmergePojo.setFinishqty(0D);
        if (wkWorksheetmatmergePojo.getMrpuid() == null) wkWorksheetmatmergePojo.setMrpuid("");
        if (wkWorksheetmatmergePojo.getMrpitemid() == null) wkWorksheetmatmergePojo.setMrpitemid("");
        if (wkWorksheetmatmergePojo.getSubqty() == null) wkWorksheetmatmergePojo.setSubqty(0D);
        if (wkWorksheetmatmergePojo.getMainqty() == null) wkWorksheetmatmergePojo.setMainqty(0D);
        if (wkWorksheetmatmergePojo.getLossrate() == null) wkWorksheetmatmergePojo.setLossrate(0D);
        if (wkWorksheetmatmergePojo.getBomid() == null) wkWorksheetmatmergePojo.setBomid("");
        if (wkWorksheetmatmergePojo.getBomtype() == null) wkWorksheetmatmergePojo.setBomtype(0);
        if (wkWorksheetmatmergePojo.getBomitemid() == null) wkWorksheetmatmergePojo.setBomitemid("");
        if (wkWorksheetmatmergePojo.getItemrowcode() == null) wkWorksheetmatmergePojo.setItemrowcode("");
        if (wkWorksheetmatmergePojo.getRownum() == null) wkWorksheetmatmergePojo.setRownum(0);
        if (wkWorksheetmatmergePojo.getClosed() == null) wkWorksheetmatmergePojo.setClosed(0);
        if (wkWorksheetmatmergePojo.getBomqty() == null) wkWorksheetmatmergePojo.setBomqty(0D);
        if (wkWorksheetmatmergePojo.getAvaiqty() == null) wkWorksheetmatmergePojo.setAvaiqty(0D);
        if (wkWorksheetmatmergePojo.getNeedqty() == null) wkWorksheetmatmergePojo.setNeedqty(0D);
        if (wkWorksheetmatmergePojo.getStoplanqty() == null) wkWorksheetmatmergePojo.setStoplanqty(0D);
        if (wkWorksheetmatmergePojo.getRealqty() == null) wkWorksheetmatmergePojo.setRealqty(0D);
        if (wkWorksheetmatmergePojo.getFlowcode() == null) wkWorksheetmatmergePojo.setFlowcode("");
        if (wkWorksheetmatmergePojo.getAttributejson() == null) wkWorksheetmatmergePojo.setAttributejson("");
        if (wkWorksheetmatmergePojo.getItemcount() == null) wkWorksheetmatmergePojo.setItemcount(0);
        if (wkWorksheetmatmergePojo.getMachuid() == null) wkWorksheetmatmergePojo.setMachuid("");
        if (wkWorksheetmatmergePojo.getCustom1() == null) wkWorksheetmatmergePojo.setCustom1("");
        if (wkWorksheetmatmergePojo.getCustom2() == null) wkWorksheetmatmergePojo.setCustom2("");
        if (wkWorksheetmatmergePojo.getCustom3() == null) wkWorksheetmatmergePojo.setCustom3("");
        if (wkWorksheetmatmergePojo.getCustom4() == null) wkWorksheetmatmergePojo.setCustom4("");
        if (wkWorksheetmatmergePojo.getCustom5() == null) wkWorksheetmatmergePojo.setCustom5("");
        if (wkWorksheetmatmergePojo.getCustom6() == null) wkWorksheetmatmergePojo.setCustom6("");
        if (wkWorksheetmatmergePojo.getCustom7() == null) wkWorksheetmatmergePojo.setCustom7("");
        if (wkWorksheetmatmergePojo.getCustom8() == null) wkWorksheetmatmergePojo.setCustom8("");
        if (wkWorksheetmatmergePojo.getCustom9() == null) wkWorksheetmatmergePojo.setCustom9("");
        if (wkWorksheetmatmergePojo.getCustom10() == null) wkWorksheetmatmergePojo.setCustom10("");
        if (wkWorksheetmatmergePojo.getTenantid() == null) wkWorksheetmatmergePojo.setTenantid("");
        if (wkWorksheetmatmergePojo.getRevision() == null) wkWorksheetmatmergePojo.setRevision(0);
        return wkWorksheetmatmergePojo;
    }
}
