package inks.service.std.manu.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWiptasksPojo;
import inks.service.std.manu.domain.pojo.WkWiptasksitemPojo;
import inks.service.std.manu.domain.pojo.WkWiptasksitemdetailPojo;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * 派工单(WkWiptasks)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-06 14:33:54
 */
public interface WkWiptasksService {

    WkWiptasksPojo getEntity(String key,String tid);

    PageInfo<WkWiptasksitemdetailPojo> getPageList(QueryParam queryParam);

    WkWiptasksPojo getBillEntity(String key,String tid);

    PageInfo<WkWiptasksPojo> getBillList(QueryParam queryParam);

    PageInfo<WkWiptasksPojo> getPageTh(QueryParam queryParam);

    WkWiptasksPojo insert(WkWiptasksPojo wkWiptasksPojo);

    WkWiptasksPojo update(WkWiptasksPojo wkWiptaskspojo);

    int delete(String key,String tid);


     WkWiptasksPojo approval(WkWiptasksPojo wkWiptasksPojo);

    PageInfo<Map<String, Object>> getPageListByMach(QueryParam queryParam);

    PageInfo<Map<String, Object>> getOnlinePageListByWip(QueryParam queryParam);

    WkWiptasksPojo disannul(List<WkWiptasksitemPojo> lst, Integer type, LoginUser loginUser);

    WkWiptasksPojo closed(List<WkWiptasksitemPojo> lst, Integer type, LoginUser loginUser);

}
