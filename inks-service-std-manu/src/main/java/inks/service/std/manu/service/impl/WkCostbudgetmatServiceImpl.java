package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkCostbudgetmatEntity;
import inks.service.std.manu.domain.pojo.WkCostbudgetmatPojo;
import inks.service.std.manu.mapper.WkCostbudgetmatMapper;
import inks.service.std.manu.service.WkCostbudgetmatService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 成本物料(WkCostbudgetmat)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-03 19:17:02
 */
@Service("wkCostbudgetmatService")
public class WkCostbudgetmatServiceImpl implements WkCostbudgetmatService {
    @Resource
    private WkCostbudgetmatMapper wkCostbudgetmatMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkCostbudgetmatPojo getEntity(String key, String tid) {
        return this.wkCostbudgetmatMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCostbudgetmatPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCostbudgetmatPojo> lst = wkCostbudgetmatMapper.getPageList(queryParam);
            PageInfo<WkCostbudgetmatPojo> pageInfo = new PageInfo<WkCostbudgetmatPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkCostbudgetmatPojo> getList(String Pid, String tid) {
        try {
            List<WkCostbudgetmatPojo> lst = wkCostbudgetmatMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkCostbudgetmatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCostbudgetmatPojo insert(WkCostbudgetmatPojo wkCostbudgetmatPojo) {
        //初始化item的NULL
        WkCostbudgetmatPojo itempojo = this.clearNull(wkCostbudgetmatPojo);
        WkCostbudgetmatEntity wkCostbudgetmatEntity = new WkCostbudgetmatEntity();
        BeanUtils.copyProperties(itempojo, wkCostbudgetmatEntity);

        wkCostbudgetmatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkCostbudgetmatEntity.setRevision(1);  //乐观锁
        this.wkCostbudgetmatMapper.insert(wkCostbudgetmatEntity);
        return this.getEntity(wkCostbudgetmatEntity.getId(), wkCostbudgetmatEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkCostbudgetmatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCostbudgetmatPojo update(WkCostbudgetmatPojo wkCostbudgetmatPojo) {
        WkCostbudgetmatEntity wkCostbudgetmatEntity = new WkCostbudgetmatEntity();
        BeanUtils.copyProperties(wkCostbudgetmatPojo, wkCostbudgetmatEntity);
        this.wkCostbudgetmatMapper.update(wkCostbudgetmatEntity);
        return this.getEntity(wkCostbudgetmatEntity.getId(), wkCostbudgetmatEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkCostbudgetmatMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkCostbudgetmatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCostbudgetmatPojo clearNull(WkCostbudgetmatPojo wkCostbudgetmatPojo) {
        //初始化NULL字段
        if (wkCostbudgetmatPojo.getPid() == null) wkCostbudgetmatPojo.setPid("");
        if (wkCostbudgetmatPojo.getItemid() == null) wkCostbudgetmatPojo.setItemid("");
        if (wkCostbudgetmatPojo.getGoodsid() == null) wkCostbudgetmatPojo.setGoodsid("");
        if (wkCostbudgetmatPojo.getItemcode() == null) wkCostbudgetmatPojo.setItemcode("");
        if (wkCostbudgetmatPojo.getItemname() == null) wkCostbudgetmatPojo.setItemname("");
        if (wkCostbudgetmatPojo.getItemspec() == null) wkCostbudgetmatPojo.setItemspec("");
        if (wkCostbudgetmatPojo.getItemunit() == null) wkCostbudgetmatPojo.setItemunit("");
        if (wkCostbudgetmatPojo.getQuantity() == null) wkCostbudgetmatPojo.setQuantity(0D);
        if (wkCostbudgetmatPojo.getTaxprice() == null) wkCostbudgetmatPojo.setTaxprice(0D);
        if (wkCostbudgetmatPojo.getTaxamount() == null) wkCostbudgetmatPojo.setTaxamount(0D);
        if (wkCostbudgetmatPojo.getPrice() == null) wkCostbudgetmatPojo.setPrice(0D);
        if (wkCostbudgetmatPojo.getAmount() == null) wkCostbudgetmatPojo.setAmount(0D);
        if (wkCostbudgetmatPojo.getTaxtotal() == null) wkCostbudgetmatPojo.setTaxtotal(0D);
        if (wkCostbudgetmatPojo.getItemtaxrate() == null) wkCostbudgetmatPojo.setItemtaxrate(0);
        if (wkCostbudgetmatPojo.getRownum() == null) wkCostbudgetmatPojo.setRownum(0);
        if (wkCostbudgetmatPojo.getRemark() == null) wkCostbudgetmatPojo.setRemark("");
        if (wkCostbudgetmatPojo.getCustom1() == null) wkCostbudgetmatPojo.setCustom1("");
        if (wkCostbudgetmatPojo.getCustom2() == null) wkCostbudgetmatPojo.setCustom2("");
        if (wkCostbudgetmatPojo.getCustom3() == null) wkCostbudgetmatPojo.setCustom3("");
        if (wkCostbudgetmatPojo.getCustom4() == null) wkCostbudgetmatPojo.setCustom4("");
        if (wkCostbudgetmatPojo.getCustom5() == null) wkCostbudgetmatPojo.setCustom5("");
        if (wkCostbudgetmatPojo.getCustom6() == null) wkCostbudgetmatPojo.setCustom6("");
        if (wkCostbudgetmatPojo.getCustom7() == null) wkCostbudgetmatPojo.setCustom7("");
        if (wkCostbudgetmatPojo.getCustom8() == null) wkCostbudgetmatPojo.setCustom8("");
        if (wkCostbudgetmatPojo.getCustom9() == null) wkCostbudgetmatPojo.setCustom9("");
        if (wkCostbudgetmatPojo.getCustom10() == null) wkCostbudgetmatPojo.setCustom10("");
        if (wkCostbudgetmatPojo.getTenantid() == null) wkCostbudgetmatPojo.setTenantid("");
        if (wkCostbudgetmatPojo.getRevision() == null) wkCostbudgetmatPojo.setRevision(0);
        return wkCostbudgetmatPojo;
    }
}
