package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipfinishitemPojo;
import inks.service.std.manu.domain.WkWipfinishitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 完工单子表(WkWipfinishitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-06 14:34:14
 */
 @Mapper
public interface WkWipfinishitemMapper {

    WkWipfinishitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<WkWipfinishitemPojo> getPageList(QueryParam queryParam);

    List<WkWipfinishitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(WkWipfinishitemEntity wkWipfinishitemEntity);

    int update(WkWipfinishitemEntity wkWipfinishitemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

}

