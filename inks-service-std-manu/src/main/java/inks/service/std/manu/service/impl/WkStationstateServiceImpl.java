package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkStationstateEntity;
import inks.service.std.manu.domain.pojo.WkStationstatePojo;
import inks.service.std.manu.mapper.WkStationstateMapper;
import inks.service.std.manu.service.WkStationstateService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 工位状态(WkStationstate)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-17 15:34:48
 */
@Service("wkStationstateService")
public class WkStationstateServiceImpl implements WkStationstateService {
    @Resource
    private WkStationstateMapper wkStationstateMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkStationstatePojo getEntity(String key, String tid) {
        return this.wkStationstateMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkStationstatePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkStationstatePojo> lst = wkStationstateMapper.getPageList(queryParam);
            PageInfo<WkStationstatePojo> pageInfo = new PageInfo<WkStationstatePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkStationstatePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkStationstatePojo insert(WkStationstatePojo wkStationstatePojo) {
        //初始化NULL字段
        if (wkStationstatePojo.getBilltype() == null) wkStationstatePojo.setBilltype("");
        if (wkStationstatePojo.getBilldate() == null) wkStationstatePojo.setBilldate(new Date());
        if (wkStationstatePojo.getBilltitle() == null) wkStationstatePojo.setBilltitle("");
        if (wkStationstatePojo.getOperator() == null) wkStationstatePojo.setOperator("");
        if (wkStationstatePojo.getOperatorid() == null) wkStationstatePojo.setOperatorid("");
        if (wkStationstatePojo.getWpid() == null) wkStationstatePojo.setWpid("");
        if (wkStationstatePojo.getWpcode() == null) wkStationstatePojo.setWpcode("");
        if (wkStationstatePojo.getWpname() == null) wkStationstatePojo.setWpname("");
        if (wkStationstatePojo.getStatid() == null) wkStationstatePojo.setStatid("");
        if (wkStationstatePojo.getStatcode() == null) wkStationstatePojo.setStatcode("");
        if (wkStationstatePojo.getStatname() == null) wkStationstatePojo.setStatname("");
        if (wkStationstatePojo.getStaterefresh() == null) wkStationstatePojo.setStaterefresh(0);
        if (wkStationstatePojo.getEnddate() == null) wkStationstatePojo.setEnddate(new Date());
        if (wkStationstatePojo.getStatejson() == null) wkStationstatePojo.setStatejson("");
        if (wkStationstatePojo.getCreateby() == null) wkStationstatePojo.setCreateby("");
        if (wkStationstatePojo.getCreatebyid() == null) wkStationstatePojo.setCreatebyid("");
        if (wkStationstatePojo.getCreatedate() == null) wkStationstatePojo.setCreatedate(new Date());
        if (wkStationstatePojo.getLister() == null) wkStationstatePojo.setLister("");
        if (wkStationstatePojo.getListerid() == null) wkStationstatePojo.setListerid("");
        if (wkStationstatePojo.getModifydate() == null) wkStationstatePojo.setModifydate(new Date());
        if (wkStationstatePojo.getCustom1() == null) wkStationstatePojo.setCustom1("");
        if (wkStationstatePojo.getCustom2() == null) wkStationstatePojo.setCustom2("");
        if (wkStationstatePojo.getCustom3() == null) wkStationstatePojo.setCustom3("");
        if (wkStationstatePojo.getCustom4() == null) wkStationstatePojo.setCustom4("");
        if (wkStationstatePojo.getCustom5() == null) wkStationstatePojo.setCustom5("");
        if (wkStationstatePojo.getCustom6() == null) wkStationstatePojo.setCustom6("");
        if (wkStationstatePojo.getCustom7() == null) wkStationstatePojo.setCustom7("");
        if (wkStationstatePojo.getCustom8() == null) wkStationstatePojo.setCustom8("");
        if (wkStationstatePojo.getCustom9() == null) wkStationstatePojo.setCustom9("");
        if (wkStationstatePojo.getCustom10() == null) wkStationstatePojo.setCustom10("");
        if (wkStationstatePojo.getTenantid() == null) wkStationstatePojo.setTenantid("");
        if (wkStationstatePojo.getRevision() == null) wkStationstatePojo.setRevision(0);
        if (wkStationstatePojo.getTenantname() == null) wkStationstatePojo.setTenantname("");
        WkStationstateEntity wkStationstateEntity = new WkStationstateEntity();
        BeanUtils.copyProperties(wkStationstatePojo, wkStationstateEntity);
        //生成雪花id
        wkStationstateEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkStationstateEntity.setRevision(1);  //乐观锁
        this.wkStationstateMapper.insert(wkStationstateEntity);
        return this.getEntity(wkStationstateEntity.getId(), wkStationstateEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkStationstatePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkStationstatePojo update(WkStationstatePojo wkStationstatePojo) {
        WkStationstateEntity wkStationstateEntity = new WkStationstateEntity();
        BeanUtils.copyProperties(wkStationstatePojo, wkStationstateEntity);
        this.wkStationstateMapper.update(wkStationstateEntity);
        return this.getEntity(wkStationstateEntity.getId(), wkStationstateEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkStationstateMapper.delete(key, tid);
    }


}
