package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipnoteitemPojo;

import java.util.List;

/**
 * Wip记录子表(WkWipnoteitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-26 10:08:24
 */
public interface WkWipnoteitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipnoteitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipnoteitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipnoteitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkWipnoteitemPojo 实例对象
     * @return 实例对象
     */
    WkWipnoteitemPojo insert(WkWipnoteitemPojo wkWipnoteitemPojo);

    /**
     * 修改数据
     *
     * @param wkWipnoteitempojo 实例对象
     * @return 实例对象
     */
    WkWipnoteitemPojo update(WkWipnoteitemPojo wkWipnoteitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkWipnoteitempojo 实例对象
     * @return 实例对象
     */
    WkWipnoteitemPojo clearNull(WkWipnoteitemPojo wkWipnoteitempojo);

    int update3Field(WkWipnoteitemPojo wkWipnoteitemPojo);

    int batchDeleteByIds(List<String> deletedIds);
}
