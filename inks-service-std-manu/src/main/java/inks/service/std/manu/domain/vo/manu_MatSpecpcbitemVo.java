package inks.service.std.manu.domain.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * 缩小版的 PCB工艺项目(MatSpecpcbitem)Pojo
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:57
 */
public class manu_MatSpecpcbitemVo implements Serializable {
    private static final long serialVersionUID = 517403608621521615L;

    // 编码
    @Excel(name = "编码")
    private String wpcode;
    // 工序
    @Excel(name = "工序")
    private String wpname;
    // 描述
    @Excel(name = "描述")
    private String description;
    // 流程编码
    @Excel(name = "流程编码")
    private String flowcode;
    // 工具编码
    @Excel(name = "工具编码")
    private String toolscode;
    // 备注
    @Excel(name = "备注")
    private String remark;


    public String getWpcode() {
        return wpcode;
    }

    public void setWpcode(String wpcode) {
        this.wpcode = wpcode;
    }

    public String getWpname() {
        return wpname;
    }

    public void setWpname(String wpname) {
        this.wpname = wpname;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFlowcode() {
        return flowcode;
    }

    public void setFlowcode(String flowcode) {
        this.flowcode = flowcode;
    }

    public String getToolscode() {
        return toolscode;
    }

    public void setToolscode(String toolscode) {
        this.toolscode = toolscode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

