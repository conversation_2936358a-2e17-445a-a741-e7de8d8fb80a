package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipqtyrolesEntity;
import inks.service.std.manu.domain.WkWipqtyrolesitemEntity;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesPojo;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesitemPojo;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesitemdetailPojo;
import inks.service.std.manu.mapper.WkWipqtyrolesMapper;
import inks.service.std.manu.mapper.WkWipqtyrolesitemMapper;
import inks.service.std.manu.service.WkWipqtyrolesService;
import inks.service.std.manu.service.WkWipqtyrolesitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 过数角色(WkWipqtyroles)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-14 20:53:28
 */
@Service("wkWipqtyrolesService")
public class WkWipqtyrolesServiceImpl implements WkWipqtyrolesService {


    @Resource
    private WkWipqtyrolesMapper wkWipqtyrolesMapper;

    @Resource
    private WkWipqtyrolesitemMapper wkWipqtyrolesitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkWipqtyrolesitemService wkWipqtyrolesitemService;


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipqtyrolesPojo getEntity(String key, String tid) {
        return this.wkWipqtyrolesMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipqtyrolesitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtyrolesitemdetailPojo> lst = wkWipqtyrolesMapper.getPageList(queryParam);
            PageInfo<WkWipqtyrolesitemdetailPojo> pageInfo = new PageInfo<WkWipqtyrolesitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipqtyrolesPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkWipqtyrolesPojo wkWipqtyrolesPojo = this.wkWipqtyrolesMapper.getEntity(key, tid);
            //读取子表
            wkWipqtyrolesPojo.setItem(wkWipqtyrolesitemMapper.getList(wkWipqtyrolesPojo.getId(), wkWipqtyrolesPojo.getTenantid()));
            return wkWipqtyrolesPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipqtyrolesPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtyrolesPojo> lst = wkWipqtyrolesMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkWipqtyrolesitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkWipqtyrolesPojo> pageInfo = new PageInfo<WkWipqtyrolesPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipqtyrolesPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtyrolesPojo> lst = wkWipqtyrolesMapper.getPageTh(queryParam);
            PageInfo<WkWipqtyrolesPojo> pageInfo = new PageInfo<WkWipqtyrolesPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWipqtyrolesPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipqtyrolesPojo insert(WkWipqtyrolesPojo wkWipqtyrolesPojo) {
//初始化NULL字段
        if (wkWipqtyrolesPojo.getUserid() == null) wkWipqtyrolesPojo.setUserid("");
        if (wkWipqtyrolesPojo.getUsername() == null) wkWipqtyrolesPojo.setUsername("");
        if (wkWipqtyrolesPojo.getRealname() == null) wkWipqtyrolesPojo.setRealname("");
        if (wkWipqtyrolesPojo.getRownum() == null) wkWipqtyrolesPojo.setRownum(0);
        if (wkWipqtyrolesPojo.getSummary() == null) wkWipqtyrolesPojo.setSummary("");
        if (wkWipqtyrolesPojo.getCreateby() == null) wkWipqtyrolesPojo.setCreateby("");
        if (wkWipqtyrolesPojo.getCreatebyid() == null) wkWipqtyrolesPojo.setCreatebyid("");
        if (wkWipqtyrolesPojo.getCreatedate() == null) wkWipqtyrolesPojo.setCreatedate(new Date());
        if (wkWipqtyrolesPojo.getLister() == null) wkWipqtyrolesPojo.setLister("");
        if (wkWipqtyrolesPojo.getListerid() == null) wkWipqtyrolesPojo.setListerid("");
        if (wkWipqtyrolesPojo.getModifydate() == null) wkWipqtyrolesPojo.setModifydate(new Date());
        if (wkWipqtyrolesPojo.getCustom1() == null) wkWipqtyrolesPojo.setCustom1("");
        if (wkWipqtyrolesPojo.getCustom2() == null) wkWipqtyrolesPojo.setCustom2("");
        if (wkWipqtyrolesPojo.getCustom3() == null) wkWipqtyrolesPojo.setCustom3("");
        if (wkWipqtyrolesPojo.getCustom4() == null) wkWipqtyrolesPojo.setCustom4("");
        if (wkWipqtyrolesPojo.getCustom5() == null) wkWipqtyrolesPojo.setCustom5("");
        if (wkWipqtyrolesPojo.getCustom6() == null) wkWipqtyrolesPojo.setCustom6("");
        if (wkWipqtyrolesPojo.getCustom7() == null) wkWipqtyrolesPojo.setCustom7("");
        if (wkWipqtyrolesPojo.getCustom8() == null) wkWipqtyrolesPojo.setCustom8("");
        if (wkWipqtyrolesPojo.getCustom9() == null) wkWipqtyrolesPojo.setCustom9("");
        if (wkWipqtyrolesPojo.getCustom10() == null) wkWipqtyrolesPojo.setCustom10("");
        if (wkWipqtyrolesPojo.getTenantid() == null) wkWipqtyrolesPojo.setTenantid("");
        if (wkWipqtyrolesPojo.getRevision() == null) wkWipqtyrolesPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkWipqtyrolesEntity wkWipqtyrolesEntity = new WkWipqtyrolesEntity();
        BeanUtils.copyProperties(wkWipqtyrolesPojo, wkWipqtyrolesEntity);
        //设置id和新建日期
        wkWipqtyrolesEntity.setId(id);
        wkWipqtyrolesEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkWipqtyrolesMapper.insert(wkWipqtyrolesEntity);
        //Item子表处理
        List<WkWipqtyrolesitemPojo> lst = wkWipqtyrolesPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkWipqtyrolesitemPojo itemPojo = this.wkWipqtyrolesitemService.clearNull(lst.get(i));
                WkWipqtyrolesitemEntity wkWipqtyrolesitemEntity = new WkWipqtyrolesitemEntity();
                BeanUtils.copyProperties(itemPojo, wkWipqtyrolesitemEntity);
                //设置id和Pid
                wkWipqtyrolesitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipqtyrolesitemEntity.setPid(id);
                wkWipqtyrolesitemEntity.setTenantid(wkWipqtyrolesPojo.getTenantid());
                wkWipqtyrolesitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkWipqtyrolesitemMapper.insert(wkWipqtyrolesitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipqtyrolesEntity.getId(), wkWipqtyrolesEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipqtyrolesPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipqtyrolesPojo update(WkWipqtyrolesPojo wkWipqtyrolesPojo) {
        //主表更改
        WkWipqtyrolesEntity wkWipqtyrolesEntity = new WkWipqtyrolesEntity();
        BeanUtils.copyProperties(wkWipqtyrolesPojo, wkWipqtyrolesEntity);
        this.wkWipqtyrolesMapper.update(wkWipqtyrolesEntity);
        //Item子表处理
        List<WkWipqtyrolesitemPojo> lst = wkWipqtyrolesPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = wkWipqtyrolesMapper.getDelItemIds(wkWipqtyrolesPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.wkWipqtyrolesitemMapper.delete(lstDelIds.get(i), wkWipqtyrolesEntity.getTenantid());
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                WkWipqtyrolesitemEntity wkWipqtyrolesitemEntity = new WkWipqtyrolesitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    WkWipqtyrolesitemPojo itemPojo = this.wkWipqtyrolesitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, wkWipqtyrolesitemEntity);
                    //设置id和Pid
                    wkWipqtyrolesitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    wkWipqtyrolesitemEntity.setPid(wkWipqtyrolesEntity.getId());  // 主表 id
                    wkWipqtyrolesitemEntity.setTenantid(wkWipqtyrolesPojo.getTenantid());   // 租户id
                    wkWipqtyrolesitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.wkWipqtyrolesitemMapper.insert(wkWipqtyrolesitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), wkWipqtyrolesitemEntity);
                    wkWipqtyrolesitemEntity.setTenantid(wkWipqtyrolesPojo.getTenantid());
                    this.wkWipqtyrolesitemMapper.update(wkWipqtyrolesitemEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipqtyrolesEntity.getId(), wkWipqtyrolesEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        WkWipqtyrolesPojo wkWipqtyrolesPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkWipqtyrolesitemPojo> lst = wkWipqtyrolesPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.wkWipqtyrolesitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.wkWipqtyrolesMapper.delete(key, tid);
    }


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipqtyrolesPojo getBillEntityByUserid(String key, String tid) {
        try {
            //读取主表
            WkWipqtyrolesPojo wkWipqtyrolesPojo = this.wkWipqtyrolesMapper.getEntityByUserid(key, tid);
            //读取子表
            if (wkWipqtyrolesPojo != null)
                wkWipqtyrolesPojo.setItem(wkWipqtyrolesitemMapper.getList(wkWipqtyrolesPojo.getId(), wkWipqtyrolesPojo.getTenantid()));
            return wkWipqtyrolesPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<WkWipqtyrolesPojo> getListByWpid(String key, String tenantid) {
        return this.wkWipqtyrolesMapper.getListByWpid(key, tenantid);
    }
}
