package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkScdeductionitemdetailPojo;
import inks.service.std.manu.service.WkScdeductionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 委制扣款(Wk_ScDeduction)表控制层
 *
 * <AUTHOR>
 * @since 2024-03-12 08:46:28
 */
@RestController
@RequestMapping("D05M03B1De")
@Api(tags = "D05M03B1De:委制扣款")
public class D05M03B1DeController extends WkScdeductionController {

    @Resource
    private TokenService tokenService;
    @Resource
    private WkScdeductionService wkScdeductionService;

    @ApiOperation(value = "委制扣款未开票(Wk_ScDeduction)按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineInvoPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ScDeduction.List")
    public R<PageInfo<WkScdeductionitemdetailPojo>> getOnlineInvoPageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_ScDeduction.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and ABS(Wk_ScDeductionItem.InvoQty)<ABS(Wk_ScDeductionItem.Quantity)";
//            qpfilter += " and Wk_ScDeductionItem.Closed=0 and Wk_ScDeductionItem.InvoClosed=0";
            qpfilter += " and Wk_ScDeductionItem.InvoClosed=0";
            qpfilter += " and Wk_ScDeduction.Assessorid<>''";
            if (groupid != null) {
                qpfilter += " and Wk_ScDeduction.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkScdeductionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "委制扣款未开票(Wk_ScDeduction)按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ScDeduction.List")
    public R<PageInfo<WkScdeductionitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_ScDeduction.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            qpfilter += " and Wk_ScDeduction.DisannulMark=0 and Wk_ScDeduction.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and (Wk_WipNoteItem.inpcsqty=0 or Wk_WipNoteItem.inpcsqty>Wk_WipNoteItem.outpcsqty+Wk_WipNoteItem.mrbpcsqty)";

            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkScdeductionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
