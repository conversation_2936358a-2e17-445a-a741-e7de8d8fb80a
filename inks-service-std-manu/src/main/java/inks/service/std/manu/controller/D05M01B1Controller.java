package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.InksConfig;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.domain.vo.*;
import inks.service.std.manu.mapper.WkMainplanitemMapper;
import inks.service.std.manu.mapper.WkWorksheetMapper;
import inks.service.std.manu.mapper.WkWorksheetmatMapper;
import inks.service.std.manu.service.WkWipnoteService;
import inks.service.std.manu.service.WkWorksheetService;
import inks.service.std.manu.service.WkWorksheetitemService;
import inks.service.std.manu.service.WkWorksheetmatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 厂制工单(Wk_Worksheet)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 15:25:54
 */
@RestController
@RequestMapping("D05M01B1")
@Api(tags = "D05M01B1:厂制单据：MRP需求")
public class D05M01B1Controller extends WkWorksheetController {
    @Resource
    private WkWipnoteService wkWipnoteService;
    /**
     * 服务对象
     */
    @Resource
    private WkWorksheetService wkWorksheetService;

    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 服务对象
     */
    @Resource
    private WkWorksheetmatService wkWorksheetmatService;
    @Resource
    private WkWorksheetmatMapper wkWorksheetmatMapper;
    @Resource
    private WkWorksheetitemService wkWorksheetitemService;
    @Resource
    private RedisService redisService;
    @Resource
    private UtilsFeignService utilsFeignService;
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private WkMainplanitemMapper wkMainplanitemMapper;
    @Resource
    private WkWorksheetMapper wkWorksheetMapper;
    @Resource
    @Qualifier("manu_threadPoolExecutor")
    private Executor threadPoolExecutor;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<PageInfo<WkWorksheetitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWorksheetService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<PageInfo<WkWorksheetitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid, Integer appl) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += " and Wk_WorksheetItem.FinishQty+Wk_WorksheetItem.MrbQty<Wk_WorksheetItem.WkPcsQty";
            qpfilter += " and Wk_WorksheetItem.DisannulMark=0 and Wk_WorksheetItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Wk_WorksheetItem.MergeMark!=1 ";  // 被合并的不再展示
            if (groupid != null) {
                qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
            }
            // 审批通过的
            if (appl != null && appl == 1) {
                qpfilter += " and Wk_Worksheet.Assessor<>''";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWorksheetService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineMatPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<PageInfo<WkWorksheetitemdetailPojo>> getOnlineMatPageList(@RequestBody String json, String groupid, Integer appl) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            //20241129 注掉qpfilter += " and Wk_WorksheetItem.FinishQty<Wk_WorksheetItem.Quantity";
            qpfilter += " and Wk_WorksheetItem.DisannulMark=0 and Wk_WorksheetItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Wk_WorksheetItem.MergeMark!=1 ";  // 被合并的不再展示
            qpfilter += " and Wk_WorksheetItem.MatUsed=0 and Wk_WorksheetItem.MatCode!=''";//物料未被占用的

            if (groupid != null) {
                qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
            }
            // 审批通过的
            if (appl != null && appl == 1) {
                qpfilter += " and Wk_Worksheet.Assessor<>''";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWorksheetService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineWipPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<PageInfo<WkWorksheetitemdetailPojo>> getOnlineWipPageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "and Wk_WorksheetItem.WipUsed=0";
            qpfilter += " and Wk_WorksheetItem.FinishQty+Wk_WorksheetItem.MrbQty<Wk_WorksheetItem.WkPcsQty";
            qpfilter += " and Wk_WorksheetItem.DisannulMark=0 and Wk_WorksheetItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Mat_Goods.GoodsState = '成品' ";
            if (groupid != null) {
                qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
            }
            // 审批通过的
            qpfilter += " and Wk_Worksheet.Assessor<>''";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWorksheetService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<PageInfo<WkWorksheetPojo>> getPageTh(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWorksheetService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageThByMrpitemid", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<PageInfo<WkWorksheetPojo>> getPageThByMrpitemid(@RequestBody String json, String groupid, String mrpitemid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and (Wk_Worksheet.id in (select Pid from Wk_WorksheetItem where Wk_WorksheetItem.MrpItemid='" + mrpitemid + "'))";
            if (groupid != null) {
                qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWorksheetService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<PageInfo<WkWorksheetPojo>> getOnlinePageTh(@RequestBody String json, String groupid, Integer appl) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += " and Wk_Worksheet.FinishCount<Wk_Worksheet.ItemCount";
            if (groupid != null) {
                qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
            }
            // 审批通过的
            if (appl != null && appl == 1) {
                qpfilter += " and Wk_Worksheet.Assessor<>''";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWorksheetService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "Feign获取厂制工单详细信息ByRefno", notes = "Feign获取厂制工单详细信息ByRefno", produces = "application/json")
    @RequestMapping(value = "/getMapBillEntityByRefNo", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<Map<String, Object>> getMapBillEntityByRefNo(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetService.getBillEntityByRefNo(key, loginUser.getTenantid());
            Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetPojo);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按查询结余物料", notes = "按查询结余物料", produces = "application/json")
    @RequestMapping(value = "/getMatList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<List<WkWorksheetmatPojo>> getMatList(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkWorksheetmatService.getList(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = "合并加工单子项物料", notes = "合并加工单子项", produces = "application/json")
    @RequestMapping(value = "/mergeItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Merge")
    public R<WkWorksheetitemdetailPojo> mergeItem(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<WkWorksheetitemdetailPojo> lstitem = JSONArray.parseArray(json, WkWorksheetitemdetailPojo.class);
            wkWorksheetmatService.mergeItem(lstitem, loginUser);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<List < WkWorksheetmatPojo>>
     * @Description 根据合并后的加工单子表的id查询Merge表记录
     * <AUTHOR>
     * @param[1] key 合并后的加工单子表的id
     * @time 2023/4/18 12:44
     */
    @ApiOperation(value = "根据合并后的加工单子表的id查询Merge表记录", notes = "按查询结余物料", produces = "application/json")
    @RequestMapping(value = "/getMergeListByItemid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<List<WkWorksheetmergePojo>> getMergeListByItemid(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkWorksheetmatService.getMergeListByItemid(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据Wk_WorksheetItem.id集合获取对应的Wk_WorksheetMat对象集合", notes = "按查询结余物料", produces = "application/json")
    @RequestMapping(value = "/getMatListByItemIds", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<List<WkWorksheetmatPojo>> getMatListByItemIds(@RequestBody String json) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<String> itemIdList = JSONArray.parseArray(json, String.class);
            return R.ok(this.wkWorksheetmatService.getMatListByItemIds(itemIdList, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据Wk_WorksheetMat.itemid+goodsid获取对应的Wk_WorksheetMat.id", notes = "按查询结余物料", produces = "application/json")
    @RequestMapping(value = "/getMatIdByGoodsIdAndItemId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<String> getMatIdByGoodsIdAndItemId(String goodsid, String itemid) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWorksheetmatMapper.getMatIdByGoodsIdAndItemId(goodsid, itemid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "重拉BOM表，对比WkWorkSheetMat和MrpItem(仅做对比 不update)", notes = "", produces = "application/json")
    @RequestMapping(value = "/contrastItemList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Edit")
    public R<WkMrpPojo> contrastItemList(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWorksheetService.contrastItemList(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "重拉BOM表，对比后刷新WkWorkSheetMat(update了WkWorkSheetMat表),cmd='cud' create update delete不传默认cud", notes = "重拉BOM表，刷新WkWorkSheetMat,key=worksheet.id,输出Map.num为更新数，Map.list为新内容", produces = "application/json")
    @RequestMapping(value = "/refreshItemList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Edit")
    @InksConfig
    public R<Map<String, Object>> refreshItemList(String key, String cmd) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            if (StringUtils.isBlank(cmd)) {
                cmd = "cud";
            }
            return R.ok(this.wkWorksheetService.refreshItemList(key, cmd, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printMergeListByItemid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public void printMergeListByItemid(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息 合并后的加工单子表
        WkWorksheetitemdetailPojo wkWorksheetitemdetailPojo = wkWorksheetitemService.getEntityDetail(key, loginUser.getTenantid());
        //获取单据信息 合并后的加工单子表
        List<WkWorksheetmergePojo> mergeList = wkWorksheetmatService.getMergeListByItemid(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetitemdetailPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = mergeList.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkWorksheetmergePojo wkWorksheetmergePojo = new WkWorksheetmergePojo();
                    mergeList.add(wkWorksheetmergePojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(mergeList);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
    //
    //@Resource
    //private wkWorksheetMapper wkWorksheetMapper;

    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebMergeListByItemid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebMergeListByItemid(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            WkWorksheetitemdetailPojo wkWorksheetitemdetailPojo = wkWorksheetitemService.getEntityDetail(key, loginUser.getTenantid());
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetitemdetailPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<WkWorksheetmergePojo> lstitem = wkWorksheetmatService.getMergeListByItemid(key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);
            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd HH:mm:ss");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "合并加工单");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限

            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "云打印MI工单打印(传入加工单子表id,加工单detailPojo作为主表,Goodsid对应Specid查出工序List<Mat_SpecPcbItem>)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebDetailAndMi", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebDetailAndMi(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //获取单据信息
            String tid = loginUser.getTenantid();
            WkWorksheetitemdetailPojo worksheetitemdetailPojo = this.wkWorksheetService.getItemDetailEntity(key, tid);
            String goodsid = worksheetitemdetailPojo.getGoodsid();

//            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
//            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
//            if (r.getCode() == 200) {
//                Map<String, String> tencfg = r.getData();
//                String printapproved = tencfg.get("system.bill.printapproved");
//                if (printapproved != null && printapproved.equals("true") && worksheetitemdetailPojo.getAssessor().equals("")) {
//                    throw new BaseBusinessException("请先审核单据");
//                }
//            } else {
//                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
//            }
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(worksheetitemdetailPojo);
            // 表头的map还要添加Goodsid对应的Pcb工艺主表Mat_SpecPcb对象信息
            Map<String, Object> matSpecPcbMap = wkWorksheetService.getMatSpecpcbByGoodsid(goodsid, tid);
            map.putAll(matSpecPcbMap);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<MatSpecpcbitemPojo> lstitem = this.wkWorksheetService.getMatSpecpcbitemByGoodsid(goodsid, tid);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd HH:mm:ss");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "生产加工单" + worksheetitemdetailPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            mapPrint.put("data", ptJson);   //  打印数据
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "批量云打印MI工单打印(传入加工单子表id,加工单detailPojo作为主表,Goodsid对应Specid查出工序List<Mat_SpecPcbItem>)", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBatchDetailAndMi", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebBatchDetailAndMi(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();

            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            for (String key : lstkeys) {
                //=========获取单据表头信息========
                WkWorksheetitemdetailPojo worksheetitemdetailPojo = this.wkWorksheetService.getItemDetailEntity(key, loginUser.getTenantid());
                //额外差个销售订单子表数量
                String machitemid = worksheetitemdetailPojo.getMachitemid();
                if (StringUtils.isNotBlank(machitemid)) {
                    double quantity = wkWorksheetService.getQuantityByMachitemid(machitemid, loginUser.getTenantid());
                    worksheetitemdetailPojo.setMachquantity(quantity);
                }
                String goodsid = worksheetitemdetailPojo.getGoodsid();


                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(worksheetitemdetailPojo);
                // 表头的map还要添加Goodsid对应的Pcb工艺主表Mat_SpecPcb对象信息
                Map<String, Object> matSpecPcbMap = wkWorksheetService.getMatSpecpcbByGoodsid(goodsid, tid);
                map.putAll(matSpecPcbMap);
                // 获取单据表头.加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
                //=========获取单据Item信息========
                List<MatSpecpcbitemPojo> lstitem = this.wkWorksheetService.getMatSpecpcbitemByGoodsid(worksheetitemdetailPojo.getGoodsid(), loginUser.getTenantid());
                // 单据Item. 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = BeanUtils.attrcostListToMaps(lstitem);
                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
                maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd HH:mm:ss");
                lstptJson.add(ptJson);
                ptRefNoMain += worksheetitemdetailPojo.getRefno() + ",";
//                // 刷入打印Num++
//                WkWorksheetPojo billPrintPojo = new WkWorksheetPojo();
//                billPrintPojo.setId(worksheetitemdetailPojo.getId());
//                billPrintPojo.setPrintcount(worksheetitemdetailPojo.getPrintcount() + 1);
//                billPrintPojo.setTenantid(worksheetitemdetailPojo.getTenantid());
//                this.wkWorksheetService.updatePrintcount(billPrintPojo);
            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "WkWorksheet：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebItemMi", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebItemMi(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            //=========获取单据表头信息========
            WkWorksheetitemdetailPojo worksheetitemdetailPojo = this.wkWorksheetitemService.getEntityDetail(key, tid);
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(worksheetitemdetailPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // goodsid,MainPlanItemid
            String goodsid = worksheetitemdetailPojo.getGoodsid();
            String mainPlanItemid = worksheetitemdetailPojo.getMainplanitemid();
            //=========获取单据Item信息========
            //Item》》Mi中的item.
            //Drl》》Mi中的Drl.
            //Draw》》Mi中的Draw.
            //Ecl》》goodsid》Eclcode，查Mat_Ecn,List;
            //MpWs 》》 MainPlanItemid,查 Wk_worksheet, 返回日期、renfo\goods*4\quantity\WkQty;
            manu_MatSpecpcbPojo specpcb = wkWorksheetMapper.getEntityByGoodsid_Specpcb(goodsid, tid);
            if (specpcb.getAssessor().isEmpty()) {
                return R.fail("MI未审核");
            }
            List<manu_MatSpecpcbitemVo> specpcbItemList = wkWorksheetMapper.getListVO_Specpcbitem(specpcb.getId(), tid);
            List<manu_MatSpecpcbdrlPojo> specpcbDrlList = wkWorksheetMapper.getList_Specpcbdrl(specpcb.getId(), tid);
            List<manu_MatSpecpcbdrawPojo> specpcbdrawList = wkWorksheetMapper.getList_Specpcbdraw(specpcb.getId(), tid);
            List<manu_MatEcnPojo> ecnList = wkWorksheetMapper.getListByGoodsid_Ecn(goodsid, tid);
            WkMainplanitemPojo mainPlanItem = wkMainplanitemMapper.getEntity(mainPlanItemid, tid);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            //map下面追加Mi的制表，审核信息
            map.put("milister", specpcb.getLister());
            map.put("mimodifydate", specpcb.getModifydate());
            map.put("miassessor", specpcb.getAssessor());
            map.put("miassessdate", specpcb.getAssessdate());
            if (specpcbdrawList.size() >= 3) {
                //主map中增加pnlimg\cutimg\vcutimg； 清空drawList的drawimage
                for (manu_MatSpecpcbdrawPojo drawPojo : specpcbdrawList) {
                    String type = drawPojo.getDrawtype();
                    String img = stripBase64Header(drawPojo.getDrawimage());
                    if ("Pnl".equals(type)) {
                        map.put("pnlimg", img);
                    } else if ("Cut".equals(type)) {
                        map.put("cutimg", img);
                    } else if ("VCut".equals(type)) {
                        map.put("vcutimg", img);
                    }
                    // 清空drawimage
                    drawPojo.setDrawimage("");
                }
            }

            mapreport.put("Master", map);
            mapreport.put("Item", attrListToMaps(specpcbItemList));
            mapreport.put("Drl", attrListToMaps(specpcbDrlList));
            mapreport.put("Draw", attrListToMaps(specpcbdrawList));
            mapreport.put("Ecl", attrListToMaps(ecnList));
            mapreport.put("MpWs", BeanUtils.beanToMap(mainPlanItem));
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("xml", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd HH:mm:ss");
            // logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            // 打印命令
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            //mapPrint.put("msg", "预收单" + worksheetitemPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            mapPrint.put("data", ptJson);   //  打印数据
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //如果字符串包含 "base64,"，只保留其后面的部分；否则原样返回（包括 null 也要兼容）。
    public static String stripBase64Header(String src) {
        if (src == null) return null;
        int idx = src.indexOf("base64,");
        if (idx != -1) {
            return src.substring(idx + 7);
        }
        return src;
    }

    @ApiOperation(value = "云打印报表 MI工单打印 ", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printwebDetailAndPcbMi", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printwebDetailAndPcbMi(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();

            //=========获取单据表头信息 ================Batch从这里开始复制========
            WkWorksheetitemdetailPojo wkWorksheetitemdetailPojo = this.wkWorksheetitemService.getEntityDetail(key, tid);
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetitemdetailPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // goodsid,sheetid,machitemid
            String goodsid = wkWorksheetitemdetailPojo.getGoodsid();
            String sheetid = wkWorksheetitemdetailPojo.getPid();
            String machitemid = wkWorksheetitemdetailPojo.getMachitemid();
            String mainplanitemid = wkWorksheetitemdetailPojo.getMainplanitemid();

            //=========获取单据Item信息========
            //Item》》Mi中的item.
            //Drl》》Mi中的Drl.
            //Draw》》Mi中的Draw.
            //Ecl》》goodsid》Eclcode，查Mat_Ecn,List;
            //WsItem 》》 传入加工单子表的所有同级Wk_worksheetitem
            manu_MatSpecpcbPojo specpcb = wkWorksheetMapper.getEntityByGoodsid_Specpcb(goodsid, tid);
            if (specpcb.getAssessor().isEmpty()) {
                return R.fail("MI未审核");
            }
            List<manu_MatSpecpcbitemVo> specpcbItemList = wkWorksheetMapper.getListVO_Specpcbitem(specpcb.getId(), tid);
            List<manu_MatSpecpcbdrlVo> specpcbDrlList = wkWorksheetMapper.getList_SpecpcbdrlVo(specpcb.getId(), tid);
            List<manu_MatSpecpcbdrawVo> specpcbdrawList = wkWorksheetMapper.getList_SpecpcbdrawVo(specpcb.getId(), tid);
            List<manu_MatEcnPojo> ecnList = wkWorksheetMapper.getListByGoodsid_Ecn(goodsid, tid);
            List<WkWorksheetitemPojo> worksheetitemList = wkWorksheetitemService.getList(sheetid, tid);
            // 销售订单
            double machqty = 0;
            String machtype = "";
            String machremark = "";
            String machsummary = "";
            if (StringUtils.isNotBlank(machitemid)) {
                Map<String, Object> machMap = wkWorksheetMapper.getQtyBymachitemid(machitemid, tid);
                BigDecimal quantity = (BigDecimal) machMap.get("Quantity");
                machqty = quantity.doubleValue();
                machtype = (String) machMap.get("BillType");
                machremark = (String) machMap.get("Remark");
                machsummary = (String) machMap.get("Summary");
            }
            //生产主计划
            double planstartqty = 0;
            if (StringUtils.isNotBlank(mainplanitemid)) {
                Map<String, Object> planMap = wkWorksheetMapper.getQtyByplanitemid(mainplanitemid, tid);
                BigDecimal startQty = (BigDecimal) planMap.get("StartQty");
                planstartqty = startQty.doubleValue();
            }
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            //map下面追加Mi的制表，审核信息
            map.put("milister", specpcb.getLister());
            map.put("mimodifydate", specpcb.getModifydate());
            map.put("miassessor", specpcb.getAssessor());
            map.put("miassessdate", specpcb.getAssessdate());
            map.put("migroupuid", specpcb.getGroupuid());
            map.put("misummary", specpcb.getSummary());
            map.put("goodsclass", specpcb.getGoodsclass());
            map.put("surface", specpcb.getSurface());
            map.put("material", specpcb.getMaterial());
            map.put("matname", specpcb.getMatname());
            // 追加销售订单数量
            map.put("machqty", machqty);
            map.put("machtype", machtype);
            map.put("machremark", machremark);
            map.put("machsummary", machsummary);
            // 追加生产主计划
            map.put("planstartqty", planstartqty);
            // 加工单行数
            map.put("itemcount", worksheetitemList.size());
            if (specpcbdrawList.size() >= 3) {
                //主map中增加pnlimg\cutimg\vcutimg； 清空drawList的drawimage
                for (manu_MatSpecpcbdrawVo drawPojo : specpcbdrawList) {
                    String type = drawPojo.getDrawtype();
                    String img = stripBase64Header(drawPojo.getDrawimage());
                    if ("Pnl".equals(type)) {
                        map.put("pnlimg", img);
                    } else if ("Cut".equals(type)) {
                        map.put("cutimg", img);
                    } else if ("VCut".equals(type)) {
                        map.put("vcutimg", img);
                    }
                    // 清空drawimage
                    drawPojo.setDrawimage("");
                }
            }
            mapreport.put("Master", map);
            mapreport.put("Item", attrListToMaps(specpcbItemList));
            mapreport.put("Drl", attrListToMaps(specpcbDrlList));
            mapreport.put("Draw", attrListToMaps(specpcbdrawList));
            mapreport.put("Ecl", attrListToMaps(ecnList));
            // mapreport.put("WsItem", attrListToMaps(worksheetitemList));
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            //Map<String, Object> mapdata = new LinkedHashMap<>();
            //mapdata.put("xml", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapreport, "yyyy-MM-dd HH:mm:ss");
            // logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            // 打印命令
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            //mapPrint.put("msg", "预收单" + worksheetitemPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "批量云打印MI工单打印(传入加工单子表id,加工单detailPojo作为主表,Goodsid对应Specid查出工序List<Mat_SpecPcbItem>)", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBatchDetailAndPcbMi", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebBatchDetailAndPcbMi(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> sheetitemids = JSONArray.parseArray(json, String.class);
            // 从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();

            // 最终拼接出的打印JSON
            List<String> lstptJson = Collections.synchronizedList(new ArrayList<>());
            String ptRefNoMain = "";

            // 确保顺序：用于存储结果的线程安全Map，键为sheetitemid对应的索引，值为生成的JSON
            ConcurrentHashMap<Integer, String> jsonMap = new ConcurrentHashMap<>();
            // 使用CountDownLatch等待线程池任务完成
            CountDownLatch latch = new CountDownLatch(sheetitemids.size());

            for (int i = 0; i < sheetitemids.size(); i++) {
                String sheetitemid = sheetitemids.get(i);
                int index = i; // 保存索引
                // 提交任务到线程池
                threadPoolExecutor.execute(() -> {
                    try {
                        // =========获取单据表头信息 ================Batch从这里开始复制========
                        WkWorksheetitemdetailPojo wkWorksheetitemdetailPojo = this.wkWorksheetitemService.getEntityDetail(sheetitemid, tid);
                        // 获取单据表头.表头转MAP
                        Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetitemdetailPojo);
                        // 获取单据表头.加入公司信息
                        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

                        // goodsid,sheetid,machitemid,mainplanitemid
                        String goodsid = wkWorksheetitemdetailPojo.getGoodsid();
                        String sheetid = wkWorksheetitemdetailPojo.getPid();
                        String machitemid = wkWorksheetitemdetailPojo.getMachitemid();
                        String mainplanitemid = wkWorksheetitemdetailPojo.getMainplanitemid();
                        // =========获取单据Item信息========
                        // Item》》Mi中的item.
                        // Drl》》Mi中的Drl.
                        // Draw》》Mi中的Draw.
                        // Ecl》》goodsid》Eclcode，查Mat_Ecn,List;
                        // WsItem 》》 传入加工单子表的所有同级Wk_worksheetitem
                        manu_MatSpecpcbPojo specpcb = wkWorksheetMapper.getEntityByGoodsid_Specpcb(goodsid, tid);
                        List<manu_MatSpecpcbitemVo> specpcbItemList = wkWorksheetMapper.getListVO_Specpcbitem(specpcb.getId(), tid);
                        List<manu_MatSpecpcbdrlVo> specpcbDrlList = wkWorksheetMapper.getList_SpecpcbdrlVo(specpcb.getId(), tid);
                        List<manu_MatSpecpcbdrawVo> specpcbdrawList = wkWorksheetMapper.getList_SpecpcbdrawVo(specpcb.getId(), tid);
                        List<manu_MatEcnPojo> ecnList = wkWorksheetMapper.getListByGoodsid_Ecn(goodsid, tid);
                        List<WkWorksheetitemPojo> worksheetitemList = wkWorksheetitemService.getList(sheetid, tid);
                        // 销售订单
                        double machqty = 0;
                        String machtype = "";
                        String machremark = "";
                        String machsummary = "";
                        if (StringUtils.isNotBlank(machitemid)) {
                            Map<String, Object> machMap = wkWorksheetMapper.getQtyBymachitemid(machitemid, tid);
                            BigDecimal quantity = (BigDecimal) machMap.get("Quantity");
                            machqty = quantity.doubleValue();
                            machtype = (String) machMap.get("BillType");
                            machremark = (String) machMap.get("Remark");
                            machsummary = (String) machMap.get("Summary");
                        }
                        // 生产主计划
                        double planstartqty = 0;
                        if (StringUtils.isNotBlank(mainplanitemid)) {
                            Map<String, Object> planMap = wkWorksheetMapper.getQtyByplanitemid(mainplanitemid, tid);
                            BigDecimal startQty = (BigDecimal) planMap.get("StartQty");
                            planstartqty = startQty.doubleValue();
                        }

                        // === 整理report=xml+grparam=====
                        Map<String, Object> mapreport = new LinkedHashMap<>();
                        // map下面追加Mi的制表，审核信息
                        map.put("milister", specpcb.getLister());
                        map.put("mimodifydate", specpcb.getModifydate());
                        map.put("miassessor", specpcb.getAssessor());
                        map.put("miassessdate", specpcb.getAssessdate());
                        map.put("migroupuid", specpcb.getGroupuid());
                        map.put("misummary", specpcb.getSummary());
                        map.put("goodsclass", specpcb.getGoodsclass());
                        map.put("surface", specpcb.getSurface());
                        map.put("material", specpcb.getMaterial());
                        map.put("matname", specpcb.getMatname());
                        // 追加销售订单数量
                        map.put("machqty", machqty);
                        map.put("machtype", machtype);
                        map.put("machremark", machremark);
                        map.put("machsummary", machsummary);
                        // 追加生产主计划
                        map.put("planstartqty", planstartqty);
                        // 加工单行数
                        map.put("itemcount", worksheetitemList.size());
                        if (specpcbdrawList.size() >= 3) {
                            //主map中增加pnlimg\cutimg\vcutimg； 清空drawList的drawimage
                            for (manu_MatSpecpcbdrawVo drawPojo : specpcbdrawList) {
                                String type = drawPojo.getDrawtype();
                                String img = stripBase64Header(drawPojo.getDrawimage());
                                if ("Pnl".equals(type)) {
                                    map.put("pnlimg", img);
                                } else if ("Cut".equals(type)) {
                                    map.put("cutimg", img);
                                } else if ("VCut".equals(type)) {
                                    map.put("vcutimg", img);
                                }
                                // 清空drawimage
                                drawPojo.setDrawimage("");
                            }
                        }
                        mapreport.put("Master", map);
                        mapreport.put("Item", attrListToMaps(specpcbItemList));
                        mapreport.put("Drl", attrListToMaps(specpcbDrlList));
                        mapreport.put("Draw", attrListToMaps(specpcbdrawList));
                        mapreport.put("Ecl", attrListToMaps(ecnList));
                        // mapreport.put("WsItem", attrListToMaps(worksheetitemList));

                        // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                        String ptJson = JSONObject.toJSONStringWithDateFormat(mapreport, "yyyy-MM-dd HH:mm:ss");
                        jsonMap.put(index, ptJson); // 按索引存储JSON结果
                    } catch (Exception e) {
                        // 记录异常日志
                        e.printStackTrace();
                    } finally {
                        latch.countDown();
                    }
                });
            }

            // 等待所有线程完成
            latch.await();

            // 按顺序整理lstptJson
            for (int i = 0; i < sheetitemids.size(); i++) {
                lstptJson.add(jsonMap.get(i)); // 按照索引提取结果
            }

            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "WkWorksheet：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   // 打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   // 打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   // 打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail("打印异常：" + e.getMessage());
        }
    }


    @ApiOperation(value = "通过wipid 云打印报表MI工单打印", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printwebDetailAndPcbMiByWipid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printwebDetailAndPcbMiByWipid(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();

            //=========获取单据表头信息 ================Batch从这里开始复制========
            WkWipnotePojo wipnotePojo = wkWipnoteService.getEntity(key, tid);
            // 加工单
            String sheetItemid = wipnotePojo.getWorkitemid();
            WkWorksheetitemdetailPojo wkWorksheetitemdetailPojo = this.wkWorksheetitemService.getEntityDetail(sheetItemid, tid);
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetitemdetailPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // goodsid,sheetid,machitemid
            String goodsid = wkWorksheetitemdetailPojo.getGoodsid();
            String sheetid = wkWorksheetitemdetailPojo.getPid();
            String machitemid = wkWorksheetitemdetailPojo.getMachitemid();
            String mainplanitemid = wkWorksheetitemdetailPojo.getMainplanitemid();

            //=========获取单据Item信息========
            //Item》》Mi中的item.
            //Drl》》Mi中的Drl.
            //Draw》》Mi中的Draw.
            //Ecl》》goodsid》Eclcode，查Mat_Ecn,List;
            //WsItem 》》 传入加工单子表的所有同级Wk_worksheetitem
            manu_MatSpecpcbPojo specpcb = wkWorksheetMapper.getEntityByGoodsid_Specpcb(goodsid, tid);
            if (specpcb.getAssessor().isEmpty()) {
                return R.fail("MI未审核");
            }
            List<manu_MatSpecpcbitemVo> specpcbItemList = wkWorksheetMapper.getListVO_Specpcbitem(specpcb.getId(), tid);
            List<manu_MatSpecpcbdrlVo> specpcbDrlList = wkWorksheetMapper.getList_SpecpcbdrlVo(specpcb.getId(), tid);
            List<manu_MatSpecpcbdrawVo> specpcbdrawList = wkWorksheetMapper.getList_SpecpcbdrawVo(specpcb.getId(), tid);
            List<manu_MatEcnPojo> ecnList = wkWorksheetMapper.getListByGoodsid_Ecn(goodsid, tid);
            List<WkWorksheetitemPojo> worksheetitemList = wkWorksheetitemService.getList(sheetid, tid);
            // 销售订单
            double machqty = 0;
            String machtype = "";
            String machremark = "";
            String machsummary = "";
            if (StringUtils.isNotBlank(machitemid)) {
                Map<String, Object> machMap = wkWorksheetMapper.getQtyBymachitemid(machitemid, tid);
                BigDecimal quantity = (BigDecimal) machMap.get("Quantity");
                machqty = quantity.doubleValue();
                machtype = (String) machMap.get("BillType");
                machremark = (String) machMap.get("Remark");
                machsummary = (String) machMap.get("Summary");
            }
            //生产主计划
            double planstartqty = 0;
            if (StringUtils.isNotBlank(mainplanitemid)) {
                Map<String, Object> planMap = wkWorksheetMapper.getQtyByplanitemid(mainplanitemid, tid);
                BigDecimal startQty = (BigDecimal) planMap.get("StartQty");
                planstartqty = startQty.doubleValue();
            }
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            //map下面追加Mi的制表，审核信息
            map.put("milister", specpcb.getLister());
            map.put("mimodifydate", specpcb.getModifydate());
            map.put("miassessor", specpcb.getAssessor());
            map.put("miassessdate", specpcb.getAssessdate());
            map.put("migroupuid", specpcb.getGroupuid());
            map.put("misummary", specpcb.getSummary());
            map.put("goodsclass", specpcb.getGoodsclass());
            map.put("surface", specpcb.getSurface());
            map.put("material", specpcb.getMaterial());
            map.put("matname", specpcb.getMatname());
            // 追加销售订单数量
            map.put("machqty", machqty);
            map.put("machtype", machtype);
            map.put("machremark", machremark);
            map.put("machsummary", machsummary);
            // 追加生产主计划
            map.put("planstartqty", planstartqty);
            // 加工单行数
            map.put("itemcount", worksheetitemList.size());

            // printwebDetailAndPcbMiByWipid方法额外添加的属性 来自wip的字段信息
            map.put("wipworkuid", wipnotePojo.getWorkuid());
            map.put("wipplandate", wipnotePojo.getPlandate());
            map.put("wipquantity", wipnotePojo.getQuantity());
            map.put("wipwkpcsqty", wipnotePojo.getWkpcsqty());
            map.put("wipwksecqty", wipnotePojo.getWksecqty());
            map.put("wipworkshop", wipnotePojo.getWorkshop());
            map.put("wiplister", wipnotePojo.getLister());
            map.put("wipmodifydate", wipnotePojo.getModifydate());
            map.put("wipsummary", wipnotePojo.getSummary());
            map.put("wipsubuid", wipnotePojo.getSubuid());
            if (specpcbdrawList.size() >= 3) {
                //主map中增加pnlimg\cutimg\vcutimg； 清空drawList的drawimage
                for (manu_MatSpecpcbdrawVo drawPojo : specpcbdrawList) {
                    String type = drawPojo.getDrawtype();
                    String img = stripBase64Header(drawPojo.getDrawimage());
                    if ("Pnl".equals(type)) {
                        map.put("pnlimg", img);
                    } else if ("Cut".equals(type)) {
                        map.put("cutimg", img);
                    } else if ("VCut".equals(type)) {
                        map.put("vcutimg", img);
                    }
                    // 清空drawimage
                    drawPojo.setDrawimage("");
                }
            }
            mapreport.put("Master", map);
            mapreport.put("Item", attrListToMaps(specpcbItemList));
            mapreport.put("Drl", attrListToMaps(specpcbDrlList));
            mapreport.put("Draw", attrListToMaps(specpcbdrawList));
            mapreport.put("Ecl", attrListToMaps(ecnList));
            // mapreport.put("WsItem", attrListToMaps(worksheetitemList));
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            // Map<String, Object> mapdata = new LinkedHashMap<>();
            //  mapdata.put("xml", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapreport, "yyyy-MM-dd HH:mm:ss");
            // logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            // 打印命令
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            //mapPrint.put("msg", "预收单" + worksheetitemPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "通过wipids 批量云打印MI工单打印", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBatchDetailAndPcbMiByWipid", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebBatchDetailAndPcbMiByWipid(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> wipids = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();

            // 最终拼接出的打印JSON
            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";

            for (String wipid : wipids) {
                //=========获取单据表头信息 ================Batch从这里开始复制========
                WkWipnotePojo wipnotePojo = wkWipnoteService.getEntity(wipid, tid);
                // 加工单
                String sheetItemid = wipnotePojo.getWorkitemid();
                WkWorksheetitemdetailPojo wkWorksheetitemdetailPojo = this.wkWorksheetitemService.getEntityDetail(sheetItemid, tid);
                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetitemdetailPojo);
                // 获取单据表头.加入公司信息
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

                // goodsid,sheetid,machitemid
                String goodsid = wkWorksheetitemdetailPojo.getGoodsid();
                String sheetid = wkWorksheetitemdetailPojo.getPid();
                String machitemid = wkWorksheetitemdetailPojo.getMachitemid();
                String mainplanitemid = wkWorksheetitemdetailPojo.getMainplanitemid();

                //=========获取单据Item信息========
                //Item》》Mi中的item.
                //Drl》》Mi中的Drl.
                //Draw》》Mi中的Draw.
                //Ecl》》goodsid》Eclcode，查Mat_Ecn,List;
                //WsItem 》》 传入加工单子表的所有同级Wk_worksheetitem
                manu_MatSpecpcbPojo specpcb = wkWorksheetMapper.getEntityByGoodsid_Specpcb(goodsid, tid);
                List<manu_MatSpecpcbitemVo> specpcbItemList = wkWorksheetMapper.getListVO_Specpcbitem(specpcb.getId(), tid);
                List<manu_MatSpecpcbdrlVo> specpcbDrlList = wkWorksheetMapper.getList_SpecpcbdrlVo(specpcb.getId(), tid);
                List<manu_MatSpecpcbdrawVo> specpcbdrawList = wkWorksheetMapper.getList_SpecpcbdrawVo(specpcb.getId(), tid);
                List<manu_MatEcnPojo> ecnList = wkWorksheetMapper.getListByGoodsid_Ecn(goodsid, tid);
                List<WkWorksheetitemPojo> worksheetitemList = wkWorksheetitemService.getList(sheetid, tid);
                // 销售订单
                double machqty = 0;
                String machtype = "";
                String machremark = "";
                String machsummary = "";
                if (StringUtils.isNotBlank(machitemid)) {
                    Map<String, Object> machMap = wkWorksheetMapper.getQtyBymachitemid(machitemid, tid);
                    BigDecimal quantity = (BigDecimal) machMap.get("Quantity");
                    machqty = quantity.doubleValue();
                    machtype = (String) machMap.get("BillType");
                    machremark = (String) machMap.get("Remark");
                    machsummary = (String) machMap.get("Summary");
                }
                //生产主计划
                double planstartqty = 0;
                if (StringUtils.isNotBlank(mainplanitemid)) {
                    Map<String, Object> planMap = wkWorksheetMapper.getQtyByplanitemid(mainplanitemid, tid);
                    BigDecimal startQty = (BigDecimal) planMap.get("StartQty");
                    planstartqty = startQty.doubleValue();
                }


                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                //map下面追加Mi的制表，审核信息
                map.put("milister", specpcb.getLister());
                map.put("mimodifydate", specpcb.getModifydate());
                map.put("miassessor", specpcb.getAssessor());
                map.put("miassessdate", specpcb.getAssessdate());
                map.put("migroupuid", specpcb.getGroupuid());
                map.put("misummary", specpcb.getSummary());
                map.put("goodsclass", specpcb.getGoodsclass());
                map.put("surface", specpcb.getSurface());
                map.put("material", specpcb.getMaterial());
                map.put("matname", specpcb.getMatname());
                // 追加销售订单数量
                map.put("machqty", machqty);
                map.put("machtype", machtype);
                map.put("machremark", machremark);
                map.put("machsummary", machsummary);
                // 追加生产主计划
                map.put("planstartqty", planstartqty);
                // 加工单行数
                map.put("itemcount", worksheetitemList.size());

                // printwebDetailAndPcbMiByWipid方法额外添加的属性 来自wip的字段信息
                map.put("wipworkuid", wipnotePojo.getWorkuid());
                map.put("wipplandate", wipnotePojo.getPlandate());
                map.put("wipquantity", wipnotePojo.getQuantity());
                map.put("wipwkpcsqty", wipnotePojo.getWkpcsqty());
                map.put("wipwksecqty", wipnotePojo.getWksecqty());
                map.put("wipworkshop", wipnotePojo.getWorkshop());
                map.put("wiplister", wipnotePojo.getLister());
                map.put("wipmodifydate", wipnotePojo.getModifydate());
                map.put("wipsummary", wipnotePojo.getSummary());
                map.put("wipsubuid", wipnotePojo.getSubuid());
                if (specpcbdrawList.size() >= 3) {
                    //主map中增加pnlimg\cutimg\vcutimg； 清空drawList的drawimage
                    for (manu_MatSpecpcbdrawVo drawPojo : specpcbdrawList) {
                        String type = drawPojo.getDrawtype();
                        String img = stripBase64Header(drawPojo.getDrawimage());
                        if ("Pnl".equals(type)) {
                            map.put("pnlimg", img);
                        } else if ("Cut".equals(type)) {
                            map.put("cutimg", img);
                        } else if ("VCut".equals(type)) {
                            map.put("vcutimg", img);
                        }
                        // 清空drawimage
                        drawPojo.setDrawimage("");
                    }
                }
                mapreport.put("Master", map);
                mapreport.put("Item", attrListToMaps(specpcbItemList));
                mapreport.put("Drl", attrListToMaps(specpcbDrlList));
                mapreport.put("Draw", attrListToMaps(specpcbdrawList));
                mapreport.put("Ecl", attrListToMaps(ecnList));
                // mapreport.put("WsItem", attrListToMaps(worksheetitemList));

                //mapreport.put("xml", mapreport);
                // mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                //Map<String, Object> mapdata = new LinkedHashMap<>();
                //mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapreport, "yyyy-MM-dd HH:mm:ss");
                lstptJson.add(ptJson);
            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "WkWorksheet：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
