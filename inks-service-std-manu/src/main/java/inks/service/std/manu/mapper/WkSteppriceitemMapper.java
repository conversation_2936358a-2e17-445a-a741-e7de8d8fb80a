package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSteppriceitemEntity;
import inks.service.std.manu.domain.pojo.WkSteppriceitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阶梯项目(WkSteppriceitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-26 16:21:33
 */
 @Mapper
public interface WkSteppriceitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSteppriceitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSteppriceitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSteppriceitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkSteppriceitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSteppriceitemEntity wkSteppriceitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkSteppriceitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkSteppriceitemEntity wkSteppriceitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int checkGoodsidWpidSpuvalue(@Param("goodsid")String goodsid, @Param("wpid")String wpid, @Param("spuValue")String spuValue, @Param("tid")String tenantid);

}

