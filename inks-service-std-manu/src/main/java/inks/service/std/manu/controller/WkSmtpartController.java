package inks.service.std.manu.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.ImportSmtYsmPojo;
import inks.service.std.manu.domain.pojo.WkSmtpartPojo;
import inks.service.std.manu.domain.pojo.WkSmtpartitemPojo;
import inks.service.std.manu.domain.pojo.WkSmtpartitemdetailPojo;
import inks.service.std.manu.service.WkSmtpartService;
import inks.service.std.manu.service.WkSmtpartitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * SMT上料表(WkSmtpart)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-12 13:59:10
 */

public class WkSmtpartController {
    private final String moduleCode = "D05M12B1";
    /**
     * 服务对象
     */
    @Resource
    private WkSmtpartService wkSmtpartService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkSmtpartitemService wkSmtpartitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private UtilsFeignService utilsFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取SMT上料表详细信息", notes = "获取SMT上料表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_SmtPart.List")
    public R<WkSmtpartPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkSmtpartService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_SmtPart.List")
    public R<PageInfo<WkSmtpartitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_SmtPart.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSmtpartService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取SMT上料表详细信息", notes = "获取SMT上料表详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_SmtPart.List")
    public R<WkSmtpartPojo> getBillEntity(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkSmtpartService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_SmtPart.List")
    public R<PageInfo<WkSmtpartPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_SmtPart.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkSmtpartService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_SmtPart.List")
    public R<PageInfo<WkSmtpartPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_SmtPart.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSmtpartService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增SMT上料表", notes = "新增SMT上料表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_SmtPart.Add")
    public R<WkSmtpartPojo> create(@RequestBody String json) {
        try {
            WkSmtpartPojo wkSmtpartPojo = JSONArray.parseObject(json, WkSmtpartPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Wk_SmtPart", null, loginUser.getTenantid());
            wkSmtpartPojo.setRefno(refno);

            wkSmtpartPojo.setCreateby(loginUser.getRealname());   // 创建者
            wkSmtpartPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkSmtpartPojo.setCreatedate(new Date());   // 创建时间
            wkSmtpartPojo.setLister(loginUser.getRealname());   // 制表
            wkSmtpartPojo.setListerid(loginUser.getUserid());    // 制表id            
            wkSmtpartPojo.setModifydate(new Date());   //修改时间
            wkSmtpartPojo.setTenantid(loginUser.getTenantid());   //租户id
            WkSmtpartPojo insert = this.wkSmtpartService.insert(wkSmtpartPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改SMT上料表", notes = "修改SMT上料表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_SmtPart.Edit")
    public R<WkSmtpartPojo> update(@RequestBody String json) {
        try {
            WkSmtpartPojo wkSmtpartPojo = JSONArray.parseObject(json, WkSmtpartPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            wkSmtpartPojo.setLister(loginUser.getRealname());   // 制表
            wkSmtpartPojo.setListerid(loginUser.getUserid());    // 制表id   
            wkSmtpartPojo.setModifydate(new Date());   //修改时间
            wkSmtpartPojo.setAssessor(""); //审核员
            wkSmtpartPojo.setAssessorid(""); //审核员
            wkSmtpartPojo.setAssessdate(new Date()); //审核时间
            wkSmtpartPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.wkSmtpartService.update(wkSmtpartPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除SMT上料表", notes = "删除SMT上料表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_SmtPart.Delete")
    @OperLog(title = "删除SMT上料表")
    public R<Integer> delete(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String refno = this.wkSmtpartService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增SMT上料表Item", notes = "新增SMT上料表Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_SmtPart.Add")
    public R<WkSmtpartitemPojo> createItem(@RequestBody String json) {
        try {
            WkSmtpartitemPojo wkSmtpartitemPojo = JSONArray.parseObject(json, WkSmtpartitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            wkSmtpartitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkSmtpartitemService.insert(wkSmtpartitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除SMT上料表Item", notes = "删除SMT上料表Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_SmtPart.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkSmtpartitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核SMT上料表", notes = "审核SMT上料表", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_SmtPart.Approval")
    public R<WkSmtpartPojo> approval(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            WkSmtpartPojo wkSmtpartPojo = this.wkSmtpartService.getEntity(key, loginUser.getTenantid());
            if (wkSmtpartPojo.getAssessor().equals("")) {
                wkSmtpartPojo.setAssessor(loginUser.getRealname()); //审核员
                wkSmtpartPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                wkSmtpartPojo.setAssessor(""); //审核员
                wkSmtpartPojo.setAssessorid(""); //审核员
            }
            wkSmtpartPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.wkSmtpartService.approval(wkSmtpartPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_SmtPart.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        WkSmtpartPojo wkSmtpartPojo = this.wkSmtpartService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(wkSmtpartPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = wkSmtpartPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkSmtpartitemPojo wkSmtpartitemPojo = new WkSmtpartitemPojo();
                    wkSmtpartPojo.getItem().add(wkSmtpartitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(wkSmtpartPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_SmtPart.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            WkSmtpartPojo wkSmtpartPojo = this.wkSmtpartService.getEntity(key, loginUser.getTenantid());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData(); //
                String printapproved = tencfg.get("system.bill.printapproved");
                if (printapproved != null && printapproved.equals("true") && wkSmtpartPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(wkSmtpartPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<WkSmtpartitemPojo> lstitem = this.wkSmtpartitemService.getList(key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = BeanUtils.attrcostListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "WkSmtpart" + wkSmtpartPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限

            // 刷入打印Num++

            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /*
     *
     * esay poi导入导出 时间2021-12-16 Eric
     *
     *
     *
     * */
    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<WkSmtpartitemPojo> list = new ArrayList<>();
            //创建表格
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("订单BOM", ""),
                    WkSmtpartitemPojo.class, list);
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "订单BOM模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导出数据", notes = "导出数据", produces = "application/json")
    @RequestMapping(value = "/exportList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_BomOrder.List")
    public void exportList(@RequestBody String json, HttpServletRequest request, HttpServletResponse response) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy())) {
                queryParam.setOrderBy("CreateDate");
            }
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("订单BOM", "订单BOM"),
                    WkSmtpartitemPojo.class, this.wkSmtpartitemService.getPageList(queryParam).getList());
            POIUtil.downloadWorkbook(workbook, request, response, "订单BOM信息");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<WkSmtpartitemPojo>> importExecl(String pid, MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<WkSmtpartitemPojo> list = POIUtil.importExcel(file.getInputStream(), WkSmtpartitemPojo.class, importParams);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importYSM", method = RequestMethod.POST)
    public R<List<ImportSmtYsmPojo>> importYSM(String devCode, String devNum, MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //去掉标题和表头
        ImportParams importParams = POIUtil.createImportParams(0, 1);
        //将表格数据写入List
        List<ImportSmtYsmPojo> list = POIUtil.importExcel(file.getInputStream(), ImportSmtYsmPojo.class, importParams);
        for (int i = 0; i < list.size(); i++) {
            //判断安装号码长度如果等于1那么编码为A00
            if ((list.get(i).getStationnum() != null || !"".equals(list.get(i).getStationnum()))
                    && list.get(i).getStationnum().toString().length() == 1) {
                list.get(i).setStationcode(devNum + "A00" + list.get(i).getStationnum());
            }
            //判断安装号码长度如果等于2那么编码为A0
            if ((list.get(i).getStationnum() != null || !"".equals(list.get(i).getStationnum()))
                    && list.get(i).getStationnum().toString().length() == 2) {
                list.get(i).setStationcode(devNum + "A0" + list.get(i).getStationnum());
            }
            //安装号码长度等于3那么编码为B
            if ((list.get(i).getStationnum() != null || !"".equals(list.get(i).getStationnum()))
                    && list.get(i).getStationnum().toString().length() == 3) {
                list.get(i).setStationcode(devNum + "B" + list.get(i).getStationnum());
            }
            //设置设备编码
            list.get(i).setDevcode(devCode);
            //设置数量
            list.get(i).setQuantity(list.get(i).getSingleqty());
        }
        return R.ok(list);
    }

}

