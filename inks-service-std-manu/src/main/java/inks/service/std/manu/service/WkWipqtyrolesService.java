package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesPojo;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesitemdetailPojo;

import java.util.List;

/**
 * 过数角色(WkWipqtyroles)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 20:53:28
 */
public interface WkWipqtyrolesService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtyrolesPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipqtyrolesitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtyrolesPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipqtyrolesPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipqtyrolesPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipqtyrolesPojo 实例对象
     * @return 实例对象
     */
    WkWipqtyrolesPojo insert(WkWipqtyrolesPojo wkWipqtyrolesPojo);

    /**
     * 修改数据
     *
     * @param wkWipqtyrolespojo 实例对象
     * @return 实例对象
     */
    WkWipqtyrolesPojo update(WkWipqtyrolesPojo wkWipqtyrolespojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtyrolesPojo getBillEntityByUserid(String key, String tid);

    List<WkWipqtyrolesPojo> getListByWpid(String key, String tenantid);
}
