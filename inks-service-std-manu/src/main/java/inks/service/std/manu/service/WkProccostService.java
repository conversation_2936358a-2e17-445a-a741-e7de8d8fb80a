package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkProccostPojo;

import java.util.List;

/**
 * 工序成本(WkProccost)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-25 14:02:32
 */
public interface WkProccostService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProccostPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkProccostPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkProccostPojo 实例对象
     * @return 实例对象
     */
    WkProccostPojo insert(WkProccostPojo wkProccostPojo);

    /**
     * 修改数据
     *
     * @param wkProccostpojo 实例对象
     * @return 实例对象
     */
    WkProccostPojo update(WkProccostPojo wkProccostpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    List<WkProccostPojo> getAllList(String tid);


    List<WkProccostPojo> updateList(List<WkProccostPojo> wkProccostList, LoginUser loginUser);
}
