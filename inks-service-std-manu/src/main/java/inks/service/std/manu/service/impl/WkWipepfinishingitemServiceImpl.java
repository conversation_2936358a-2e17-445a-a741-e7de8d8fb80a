package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipepfinishingitemEntity;
import inks.service.std.manu.domain.pojo.WkWipepfinishingitemPojo;
import inks.service.std.manu.mapper.WkWipepfinishingitemMapper;
import inks.service.std.manu.service.WkWipepfinishingitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 工序收货项目(WkWipepfinishingitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-04 11:13:55
 */
@Service("wkWipepfinishingitemService")
public class WkWipepfinishingitemServiceImpl implements WkWipepfinishingitemService {
    @Resource
    private WkWipepfinishingitemMapper wkWipepfinishingitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipepfinishingitemPojo getEntity(String key, String tid) {
        return this.wkWipepfinishingitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipepfinishingitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipepfinishingitemPojo> lst = wkWipepfinishingitemMapper.getPageList(queryParam);
            PageInfo<WkWipepfinishingitemPojo> pageInfo = new PageInfo<WkWipepfinishingitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkWipepfinishingitemPojo> getList(String Pid, String tid) {
        try {
            List<WkWipepfinishingitemPojo> lst = wkWipepfinishingitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkWipepfinishingitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipepfinishingitemPojo insert(WkWipepfinishingitemPojo wkWipepfinishingitemPojo) {
        //初始化item的NULL
        WkWipepfinishingitemPojo itempojo = this.clearNull(wkWipepfinishingitemPojo);
        WkWipepfinishingitemEntity wkWipepfinishingitemEntity = new WkWipepfinishingitemEntity();
        BeanUtils.copyProperties(itempojo, wkWipepfinishingitemEntity);

        wkWipepfinishingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipepfinishingitemEntity.setRevision(1);  //乐观锁
        this.wkWipepfinishingitemMapper.insert(wkWipepfinishingitemEntity);
        return this.getEntity(wkWipepfinishingitemEntity.getId(), wkWipepfinishingitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipepfinishingitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipepfinishingitemPojo update(WkWipepfinishingitemPojo wkWipepfinishingitemPojo) {
        WkWipepfinishingitemEntity wkWipepfinishingitemEntity = new WkWipepfinishingitemEntity();
        BeanUtils.copyProperties(wkWipepfinishingitemPojo, wkWipepfinishingitemEntity);
        this.wkWipepfinishingitemMapper.update(wkWipepfinishingitemEntity);
        return this.getEntity(wkWipepfinishingitemEntity.getId(), wkWipepfinishingitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWipepfinishingitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkWipepfinishingitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipepfinishingitemPojo clearNull(WkWipepfinishingitemPojo wkWipepfinishingitemPojo) {
        //初始化NULL字段
        if (wkWipepfinishingitemPojo.getPid() == null) wkWipepfinishingitemPojo.setPid("");
        if (wkWipepfinishingitemPojo.getGoodsid() == null) wkWipepfinishingitemPojo.setGoodsid("");
        if (wkWipepfinishingitemPojo.getQuantity() == null) wkWipepfinishingitemPojo.setQuantity(0D);
        if (wkWipepfinishingitemPojo.getMrbqty() == null) wkWipepfinishingitemPojo.setMrbqty(0D);
        if (wkWipepfinishingitemPojo.getSubitemid() == null) wkWipepfinishingitemPojo.setSubitemid("");
        if (wkWipepfinishingitemPojo.getSubuse() == null) wkWipepfinishingitemPojo.setSubuse("");
        if (wkWipepfinishingitemPojo.getSubunit() == null) wkWipepfinishingitemPojo.setSubunit("");
        if (wkWipepfinishingitemPojo.getSubqty() == null) wkWipepfinishingitemPojo.setSubqty(0D);
        if (wkWipepfinishingitemPojo.getTaxprice() == null) wkWipepfinishingitemPojo.setTaxprice(0D);
        if (wkWipepfinishingitemPojo.getTaxamount() == null) wkWipepfinishingitemPojo.setTaxamount(0D);
        if (wkWipepfinishingitemPojo.getTaxtotal() == null) wkWipepfinishingitemPojo.setTaxtotal(0D);
        if (wkWipepfinishingitemPojo.getPrice() == null) wkWipepfinishingitemPojo.setPrice(0D);
        if (wkWipepfinishingitemPojo.getAmount() == null) wkWipepfinishingitemPojo.setAmount(0D);
        if (wkWipepfinishingitemPojo.getItemtaxrate() == null) wkWipepfinishingitemPojo.setItemtaxrate(0);
        if (wkWipepfinishingitemPojo.getRemark() == null) wkWipepfinishingitemPojo.setRemark("");
        if (wkWipepfinishingitemPojo.getCiteuid() == null) wkWipepfinishingitemPojo.setCiteuid("");
        if (wkWipepfinishingitemPojo.getCiteitemid() == null) wkWipepfinishingitemPojo.setCiteitemid("");
        if (wkWipepfinishingitemPojo.getStatecode() == null) wkWipepfinishingitemPojo.setStatecode("");
        if (wkWipepfinishingitemPojo.getStatedate() == null) wkWipepfinishingitemPojo.setStatedate(new Date());
        if (wkWipepfinishingitemPojo.getInspected() == null) wkWipepfinishingitemPojo.setInspected(0);
        if (wkWipepfinishingitemPojo.getClosed() == null) wkWipepfinishingitemPojo.setClosed(0);
        if (wkWipepfinishingitemPojo.getRownum() == null) wkWipepfinishingitemPojo.setRownum(0);
        if (wkWipepfinishingitemPojo.getInvoqty() == null) wkWipepfinishingitemPojo.setInvoqty(0D);
        if (wkWipepfinishingitemPojo.getInvoclosed() == null) wkWipepfinishingitemPojo.setInvoclosed(0);
        if (wkWipepfinishingitemPojo.getVirtualitem() == null) wkWipepfinishingitemPojo.setVirtualitem(0);
        if (wkWipepfinishingitemPojo.getWipitemid() == null) wkWipepfinishingitemPojo.setWipitemid("");
        if (wkWipepfinishingitemPojo.getWsid() == null) wkWipepfinishingitemPojo.setWsid("");
        if (wkWipepfinishingitemPojo.getWsuid() == null) wkWipepfinishingitemPojo.setWsuid("");
        if (wkWipepfinishingitemPojo.getWpid() == null) wkWipepfinishingitemPojo.setWpid("");
        if (wkWipepfinishingitemPojo.getEndwpid() == null) wkWipepfinishingitemPojo.setEndwpid("");
        if (wkWipepfinishingitemPojo.getCustomer() == null) wkWipepfinishingitemPojo.setCustomer("");
        if (wkWipepfinishingitemPojo.getCustpo() == null) wkWipepfinishingitemPojo.setCustpo("");
        if (wkWipepfinishingitemPojo.getMachuid() == null) wkWipepfinishingitemPojo.setMachuid("");
        if (wkWipepfinishingitemPojo.getMachitemid() == null) wkWipepfinishingitemPojo.setMachitemid("");
        if (wkWipepfinishingitemPojo.getMainplanuid() == null) wkWipepfinishingitemPojo.setMainplanuid("");
        if (wkWipepfinishingitemPojo.getMainplanitemid() == null) wkWipepfinishingitemPojo.setMainplanitemid("");
        if (wkWipepfinishingitemPojo.getMachgroupid() == null) wkWipepfinishingitemPojo.setMachgroupid("");
        if (wkWipepfinishingitemPojo.getDisannulmark() == null) wkWipepfinishingitemPojo.setDisannulmark(0);
        if (wkWipepfinishingitemPojo.getDisannullisterid() == null) wkWipepfinishingitemPojo.setDisannullisterid("");
        if (wkWipepfinishingitemPojo.getDisannullister() == null) wkWipepfinishingitemPojo.setDisannullister("");
        if (wkWipepfinishingitemPojo.getDisannuldate() == null) wkWipepfinishingitemPojo.setDisannuldate(new Date());
        if (wkWipepfinishingitemPojo.getAttributejson() == null) wkWipepfinishingitemPojo.setAttributejson("");
        if (wkWipepfinishingitemPojo.getCustom1() == null) wkWipepfinishingitemPojo.setCustom1("");
        if (wkWipepfinishingitemPojo.getCustom2() == null) wkWipepfinishingitemPojo.setCustom2("");
        if (wkWipepfinishingitemPojo.getCustom3() == null) wkWipepfinishingitemPojo.setCustom3("");
        if (wkWipepfinishingitemPojo.getCustom4() == null) wkWipepfinishingitemPojo.setCustom4("");
        if (wkWipepfinishingitemPojo.getCustom5() == null) wkWipepfinishingitemPojo.setCustom5("");
        if (wkWipepfinishingitemPojo.getCustom6() == null) wkWipepfinishingitemPojo.setCustom6("");
        if (wkWipepfinishingitemPojo.getCustom7() == null) wkWipepfinishingitemPojo.setCustom7("");
        if (wkWipepfinishingitemPojo.getCustom8() == null) wkWipepfinishingitemPojo.setCustom8("");
        if (wkWipepfinishingitemPojo.getCustom9() == null) wkWipepfinishingitemPojo.setCustom9("");
        if (wkWipepfinishingitemPojo.getCustom10() == null) wkWipepfinishingitemPojo.setCustom10("");
        if (wkWipepfinishingitemPojo.getTenantid() == null) wkWipepfinishingitemPojo.setTenantid("");
        if (wkWipepfinishingitemPojo.getRevision() == null) wkWipepfinishingitemPojo.setRevision(0);
        return wkWipepfinishingitemPojo;
    }
}
