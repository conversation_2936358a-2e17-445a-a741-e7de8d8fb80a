package inks.service.std.manu.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * WIP合并(WkWipnotemerge)实体类
 *
 * <AUTHOR>
 * @since 2023-06-13 09:51:14
 */
public class WkWipnotemergeEntity implements Serializable {
    private static final long serialVersionUID = -39809157291244970L;
        private String id;
         // 合并后主表id
         private String mergeid;
         // 合并前主表id
         private String wipid;
         // 产品id
         private String goodsid;
         // 需求数量
         private Double quantity;
         // 加工单号
         private String workuid;
         // 加工单Itemid
         private String workitemid;
         // 主表标记
         private Integer mainmark;
         // 行号
         private Integer rownum;
         // 生产类型 订单 样品
         private String machtype;
         // 销售单号
         private String machuid;
         // 销售子项id
         private String machitemid;
         // 销售客户id
         private String machgroupid;
         // 客户
         private String customer;
         // 客户PO
         private String custpo;
         // 应用单号
         private String citeuid;
         // 应用子项id
         private String citeitemid;
         // 主计划单号
         private String mainplanuid;
         // 主计划子项id
         private String mainplanitemid;
         // 属性Josn
         private String attributejson;
         // 总投数
         private Double wkpcsqty;
         // 第二总投数
         private Double wksecqty;
         // 来源:0=其他 1销售订单 2加工单
         private Integer sourcetype;
        private String remark;
        private String createby;
        private String createbyid;
         // 新建日期
         private Date createdate;
        private String lister;
        private String listerid;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 自定义6
         private String custom6;
         // 自定义7
         private String custom7;
         // 自定义8
         private String custom8;
         // 自定义9
         private String custom9;
         // 自定义10
         private String custom10;
         // 租户id
         private String tenantid;
         // 租户名称
         private String tenantname;
         // 乐观锁
         private Integer revision;

    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 合并后主表id
    public String getMergeid() {
        return mergeid;
    }
    
    public void setMergeid(String mergeid) {
        this.mergeid = mergeid;
    }
        
// 合并前主表id
    public String getWipid() {
        return wipid;
    }
    
    public void setWipid(String wipid) {
        this.wipid = wipid;
    }
        
// 产品id
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
// 需求数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
// 加工单号
    public String getWorkuid() {
        return workuid;
    }
    
    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }
        
// 加工单Itemid
    public String getWorkitemid() {
        return workitemid;
    }
    
    public void setWorkitemid(String workitemid) {
        this.workitemid = workitemid;
    }
        
// 主表标记
    public Integer getMainmark() {
        return mainmark;
    }
    
    public void setMainmark(Integer mainmark) {
        this.mainmark = mainmark;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 生产类型 订单 样品
    public String getMachtype() {
        return machtype;
    }
    
    public void setMachtype(String machtype) {
        this.machtype = machtype;
    }
        
// 销售单号
    public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
// 销售子项id
    public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
// 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }
    
    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }
        
// 客户
    public String getCustomer() {
        return customer;
    }
    
    public void setCustomer(String customer) {
        this.customer = customer;
    }
        
// 客户PO
    public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
// 应用单号
    public String getCiteuid() {
        return citeuid;
    }
    
    public void setCiteuid(String citeuid) {
        this.citeuid = citeuid;
    }
        
// 应用子项id
    public String getCiteitemid() {
        return citeitemid;
    }
    
    public void setCiteitemid(String citeitemid) {
        this.citeitemid = citeitemid;
    }
        
// 主计划单号
    public String getMainplanuid() {
        return mainplanuid;
    }
    
    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }
        
// 主计划子项id
    public String getMainplanitemid() {
        return mainplanitemid;
    }
    
    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }
        
// 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
// 总投数
    public Double getWkpcsqty() {
        return wkpcsqty;
    }
    
    public void setWkpcsqty(Double wkpcsqty) {
        this.wkpcsqty = wkpcsqty;
    }
        
// 第二总投数
    public Double getWksecqty() {
        return wksecqty;
    }
    
    public void setWksecqty(Double wksecqty) {
        this.wksecqty = wksecqty;
    }
        
// 来源:0=其他 1销售订单 2加工单
    public Integer getSourcetype() {
        return sourcetype;
    }
    
    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }
        
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

