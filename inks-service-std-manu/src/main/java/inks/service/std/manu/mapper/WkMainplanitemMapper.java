package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkMainplanitemEntity;
import inks.service.std.manu.domain.pojo.WkMainplanitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 主计划项目(WkMainplanitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-26 10:04:30
 */
 @Mapper
public interface WkMainplanitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMainplanitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMainplanitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMainplanitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkMainplanitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkMainplanitemEntity wkMainplanitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkMainplanitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkMainplanitemEntity wkMainplanitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int syncBusMachItemMainPlanQty(@Param("machitemid") String machitemid, @Param("tid") String tid);

    int syncBusMachMainPlanCount(@Param("machitemid") String machitemid, @Param("tid") String tid);

    void syncMergeMarkInIds(List<String> mainPlanItemIds, int mergeMark, String tid);

    List<String> getMachItemidsInPlanItemids(List<String> mergeItemIds, String tid);
}

