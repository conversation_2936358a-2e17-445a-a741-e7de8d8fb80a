package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkMrpitemEntity;
import inks.service.std.manu.domain.pojo.WkMrpitemPojo;
import inks.service.std.manu.mapper.WkMrpMapper;
import inks.service.std.manu.mapper.WkMrpitemMapper;
import inks.service.std.manu.service.WkMrpitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * MRP项目(WkMrpitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-24 15:15:20
 */
@Service("wkMrpitemService")
public class WkMrpitemServiceImpl implements WkMrpitemService {
    @Resource
    private WkMrpitemMapper wkMrpitemMapper;
    @Resource
    private WkMrpMapper wkMrpMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMrpitemPojo getEntity(String key, String tid) {
        return this.wkMrpitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMrpitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMrpitemPojo> lst = wkMrpitemMapper.getPageList(queryParam);
            PageInfo<WkMrpitemPojo> pageInfo = new PageInfo<WkMrpitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkMrpitemPojo> getList(String Pid, String tid) {
        try {
            List<WkMrpitemPojo> lst = wkMrpitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkMrpitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMrpitemPojo insert(WkMrpitemPojo wkMrpitemPojo) {
        //初始化item的NULL
        WkMrpitemPojo itempojo = this.clearNull(wkMrpitemPojo);
        WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
        BeanUtils.copyProperties(itempojo, wkMrpitemEntity);

        wkMrpitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkMrpitemEntity.setRevision(1);  //乐观锁
        this.wkMrpitemMapper.insert(wkMrpitemEntity);
        return this.getEntity(wkMrpitemEntity.getId(), wkMrpitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkMrpitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMrpitemPojo update(WkMrpitemPojo wkMrpitemPojo) {
        WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
        BeanUtils.copyProperties(wkMrpitemPojo, wkMrpitemEntity);
        this.wkMrpitemMapper.update(wkMrpitemEntity);
        return this.getEntity(wkMrpitemEntity.getId(), wkMrpitemEntity.getTenantid());
    }

    @Override
    public String updateItemAttrCode(List<String> itemids, String attrcode, String tenantid) {
//        // 原本的attrCodeDB 属性 厂制/委制/外购/客供 校验是否已转到当前的单据(生产加工单Wk_WorkSheetItem
//        // 委外加工单Wk_SubcontractItem
//        // 采购订单Buy_OrderItem
//        // 客供订单MatCustsuppItem
//        String attrcodeDB = wkMrpitemMapper.getAttrCode(itemids.get(0), tenantid);
//        if (attrcodeDB.equals("厂制")) {
//            List<String> goodsUids = wkMrpitemMapper.checkWk_WorkSheetItem(itemids, tenantid);
//            if (CollectionUtils.isNotEmpty(goodsUids)) {
//                return "以下货品已转入生产加工单:" + goodsUids;
//            }
//        } else if (attrcodeDB.equals("委制")) {
//        } else if (attrcodeDB.equals("外购")) {
//        } else if (attrcodeDB.equals("客供")) {
//        }
        // 想要转到的attrcode
        int i = wkMrpitemMapper.updateItemAttrCode(itemids, attrcode, tenantid);
        //子表全部刷新后，刷新主表的5个Count
        this.wkMrpMapper.syncMrpCount(wkMrpitemMapper.getPid(itemids.get(0)), tenantid);
        return "更新了" + i + "条数据";
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkMrpitemMapper.delete(key, tid);
    }


    @Override
    public List<WkMrpitemPojo> getMrpItemListByMachItemids(List<String> machitems, String tenantid) {
        return wkMrpitemMapper.getMrpItemListByMachItemids(machitems, tenantid);
    }

    @Override
    public List<WkMrpitemPojo> getListInObjids(Set<String> objids, String tid) {
        return wkMrpitemMapper.getListInObjids(objids, tid);
    }

    /**
     * 修改数据
     *
     * @param wkMrpitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMrpitemPojo clearNull(WkMrpitemPojo wkMrpitemPojo) {
        //初始化NULL字段
        if (wkMrpitemPojo.getPid() == null) wkMrpitemPojo.setPid("");
        if (wkMrpitemPojo.getItemparentid() == null) wkMrpitemPojo.setItemparentid("");
        if (wkMrpitemPojo.getMrpobjid() == null) wkMrpitemPojo.setMrpobjid("");
        if (wkMrpitemPojo.getLevelnum() == null) wkMrpitemPojo.setLevelnum(0);
        if (wkMrpitemPojo.getLevelsymbol() == null) wkMrpitemPojo.setLevelsymbol("");
        if (wkMrpitemPojo.getGoodsid() == null) wkMrpitemPojo.setGoodsid("");
        if (wkMrpitemPojo.getItemcode() == null) wkMrpitemPojo.setItemcode("");
        if (wkMrpitemPojo.getItemname() == null) wkMrpitemPojo.setItemname("");
        if (wkMrpitemPojo.getItemspec() == null) wkMrpitemPojo.setItemspec("");
        if (wkMrpitemPojo.getItemunit() == null) wkMrpitemPojo.setItemunit("");
        if (wkMrpitemPojo.getBomid() == null) wkMrpitemPojo.setBomid("");
        if (wkMrpitemPojo.getBomtype() == null) wkMrpitemPojo.setBomtype("");
        if (wkMrpitemPojo.getBomitemid() == null) wkMrpitemPojo.setBomitemid("");
        if (wkMrpitemPojo.getSubqty() == null) wkMrpitemPojo.setSubqty(0D);
        if (wkMrpitemPojo.getMainqty() == null) wkMrpitemPojo.setMainqty(0D);
        if (wkMrpitemPojo.getLossrate() == null) wkMrpitemPojo.setLossrate(0D);
        if (wkMrpitemPojo.getAttrcode() == null) wkMrpitemPojo.setAttrcode("");
        if (wkMrpitemPojo.getFlowcode() == null) wkMrpitemPojo.setFlowcode("");
        if (wkMrpitemPojo.getBomqty() == null) wkMrpitemPojo.setBomqty(0D);
        if (wkMrpitemPojo.getStoqty() == null) wkMrpitemPojo.setStoqty(0D);
        if (wkMrpitemPojo.getSafestock() == null) wkMrpitemPojo.setSafestock(0D);
        if (wkMrpitemPojo.getNeedqty() == null) wkMrpitemPojo.setNeedqty(0D);
        if (wkMrpitemPojo.getRealqty() == null) wkMrpitemPojo.setRealqty(0D);
        if (wkMrpitemPojo.getWorkdate() == null) wkMrpitemPojo.setWorkdate(new Date());
        if (wkMrpitemPojo.getPlandate() == null) wkMrpitemPojo.setPlandate(new Date());
        if (wkMrpitemPojo.getRemark() == null) wkMrpitemPojo.setRemark("");
        if (wkMrpitemPojo.getEnabledmark() == null) wkMrpitemPojo.setEnabledmark(0);
        if (wkMrpitemPojo.getClosed() == null) wkMrpitemPojo.setClosed(0);
        if (wkMrpitemPojo.getRownum() == null) wkMrpitemPojo.setRownum(0);
        if (wkMrpitemPojo.getBuyplanqty() == null) wkMrpitemPojo.setBuyplanqty(0D);
        if (wkMrpitemPojo.getBuyorderqty() == null) wkMrpitemPojo.setBuyorderqty(0D);
        if (wkMrpitemPojo.getCustsuppqty() == null) wkMrpitemPojo.setCustsuppqty(0D);
        if (wkMrpitemPojo.getWkwsqty() == null) wkMrpitemPojo.setWkwsqty(0D);
        if (wkMrpitemPojo.getWkscqty() == null) wkMrpitemPojo.setWkscqty(0D);
        if (wkMrpitemPojo.getOtherqty() == null) wkMrpitemPojo.setOtherqty(0D);
        if (wkMrpitemPojo.getMatreqqty() == null) wkMrpitemPojo.setMatreqqty(0D);
        if (wkMrpitemPojo.getMatcompqty() == null) wkMrpitemPojo.setMatcompqty(0D);
        if (wkMrpitemPojo.getMativqty() == null) wkMrpitemPojo.setMativqty(0D);
        if (wkMrpitemPojo.getBuyremqty() == null) wkMrpitemPojo.setBuyremqty(0D);
        if (wkMrpitemPojo.getWkwsremqty() == null) wkMrpitemPojo.setWkwsremqty(0D);
        if (wkMrpitemPojo.getWkscremqty() == null) wkMrpitemPojo.setWkscremqty(0D);
        if (wkMrpitemPojo.getBusremqty() == null) wkMrpitemPojo.setBusremqty(0D);
        if (wkMrpitemPojo.getMrpremqty() == null) wkMrpitemPojo.setMrpremqty(0D);
        if (wkMrpitemPojo.getFreereqremqty() == null) wkMrpitemPojo.setFreereqremqty(0D);
        if (wkMrpitemPojo.getReqremqty() == null) wkMrpitemPojo.setReqremqty(0D);
        if (wkMrpitemPojo.getGroupid() == null) wkMrpitemPojo.setGroupid("");
        if (wkMrpitemPojo.getGroupname() == null) wkMrpitemPojo.setGroupname("");
        if (wkMrpitemPojo.getMatreqrtqty() == null) wkMrpitemPojo.setMatreqrtqty(0D);
        if (wkMrpitemPojo.getMatcomprtqty() == null) wkMrpitemPojo.setMatcomprtqty(0D);
        if (wkMrpitemPojo.getWkfinishqty() == null) wkMrpitemPojo.setWkfinishqty(0D);
        if (wkMrpitemPojo.getBuyfinishqty() == null) wkMrpitemPojo.setBuyfinishqty(0D);
        if (wkMrpitemPojo.getCustfinishqty() == null) wkMrpitemPojo.setCustfinishqty(0D);
        if (wkMrpitemPojo.getMachuid() == null) wkMrpitemPojo.setMachuid("");
        if (wkMrpitemPojo.getMachitemid() == null) wkMrpitemPojo.setMachitemid("");
        if (wkMrpitemPojo.getMachbatch() == null) wkMrpitemPojo.setMachbatch("");
        if (wkMrpitemPojo.getMachgroupid() == null) wkMrpitemPojo.setMachgroupid("");
        if (wkMrpitemPojo.getMainplanuid() == null) wkMrpitemPojo.setMainplanuid("");
        if (wkMrpitemPojo.getMainplanitemid() == null) wkMrpitemPojo.setMainplanitemid("");
        if (wkMrpitemPojo.getBomround() == null) wkMrpitemPojo.setBomround(0);
        if (wkMrpitemPojo.getBomdate() == null) wkMrpitemPojo.setBomdate(new Date());
        if (wkMrpitemPojo.getBommark() == null) wkMrpitemPojo.setBommark(0);
     if(wkMrpitemPojo.getAttributejson()==null) wkMrpitemPojo.setAttributejson("");
     if(wkMrpitemPojo.getGrossqty()==null) wkMrpitemPojo.setGrossqty(0D);
     if(wkMrpitemPojo.getMergemark()==null) wkMrpitemPojo.setMergemark(0);
     if(wkMrpitemPojo.getMattype()==null) wkMrpitemPojo.setMattype(0);
     if(wkMrpitemPojo.getCustom1()==null) wkMrpitemPojo.setCustom1("");
        if (wkMrpitemPojo.getCustom2() == null) wkMrpitemPojo.setCustom2("");
        if (wkMrpitemPojo.getCustom3() == null) wkMrpitemPojo.setCustom3("");
        if (wkMrpitemPojo.getCustom4() == null) wkMrpitemPojo.setCustom4("");
        if (wkMrpitemPojo.getCustom5() == null) wkMrpitemPojo.setCustom5("");
        if (wkMrpitemPojo.getCustom6() == null) wkMrpitemPojo.setCustom6("");
        if (wkMrpitemPojo.getCustom7() == null) wkMrpitemPojo.setCustom7("");
        if (wkMrpitemPojo.getCustom8() == null) wkMrpitemPojo.setCustom8("");
        if (wkMrpitemPojo.getCustom9() == null) wkMrpitemPojo.setCustom9("");
        if (wkMrpitemPojo.getCustom10() == null) wkMrpitemPojo.setCustom10("");
        if (wkMrpitemPojo.getTenantid() == null) wkMrpitemPojo.setTenantid("");
        if (wkMrpitemPojo.getTenantname() == null) wkMrpitemPojo.setTenantname("");
        if (wkMrpitemPojo.getRevision() == null) wkMrpitemPojo.setRevision(0);
        return wkMrpitemPojo;
    }
}
