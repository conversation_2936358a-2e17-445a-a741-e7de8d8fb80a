package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkScdeductionitemPojo;

import java.util.List;

/**
 * 委制扣款Item(WkScdeductionitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-12 08:46:42
 */
public interface WkScdeductionitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkScdeductionitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkScdeductionitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkScdeductionitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkScdeductionitemPojo 实例对象
     * @return 实例对象
     */
    WkScdeductionitemPojo insert(WkScdeductionitemPojo wkScdeductionitemPojo);

    /**
     * 修改数据
     *
     * @param wkScdeductionitempojo 实例对象
     * @return 实例对象
     */
    WkScdeductionitemPojo update(WkScdeductionitemPojo wkScdeductionitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkScdeductionitempojo 实例对象
     * @return 实例对象
     */
    WkScdeductionitemPojo clearNull(WkScdeductionitemPojo wkScdeductionitempojo);
}
