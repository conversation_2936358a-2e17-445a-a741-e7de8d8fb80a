package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkStationitemPojo;

import java.util.List;

/**
 * 工位员工(WkStationitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-22 12:36:34
 */
public interface WkStationitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStationitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkStationitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkStationitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkStationitemPojo 实例对象
     * @return 实例对象
     */
    WkStationitemPojo insert(WkStationitemPojo wkStationitemPojo);

    /**
     * 修改数据
     *
     * @param wkStationitempojo 实例对象
     * @return 实例对象
     */
    WkStationitemPojo update(WkStationitemPojo wkStationitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkStationitempojo 实例对象
     * @return 实例对象
     */
    WkStationitemPojo clearNull(WkStationitemPojo wkStationitempojo);
}
