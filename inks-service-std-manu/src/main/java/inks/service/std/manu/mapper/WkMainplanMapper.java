package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkMainplanEntity;
import inks.service.std.manu.domain.pojo.WkMainplanPojo;
import inks.service.std.manu.domain.pojo.WkMainplanitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产主计划(WkMainplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-14 15:21:38
 */
@Mapper
public interface WkMainplanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMainplanPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMainplanitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMainplanPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param wkMainplanEntity 实例对象
     * @return 影响行数
     */
    int insert(WkMainplanEntity wkMainplanEntity);

    
    /**
     * 修改数据
     *
     * @param wkMainplanEntity 实例对象
     * @return 影响行数
     */
    int update(WkMainplanEntity wkMainplanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param wkMainplanPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(WkMainplanPojo wkMainplanPojo);
                                                                          /**
     * 修改数据
     *
     * @param wkMainplanEntity 实例对象
     * @return 影响行数
     */
    int approval(WkMainplanEntity wkMainplanEntity);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid, @Param("tid") String tid);

    void updatePrintcount(WkMainplanPojo billPrintPojo);

    List<WkMainplanitemdetailPojo> getMainPlanItem(@Param("mainPlanItemIds") List<String> mainPlanItemIds, @Param("tid") String tid);


    void syncMachingItemWkMergeInIds(List<String> salveMachItemids, int wkMergeMark, String insertPlanItemid, String tid);
}

