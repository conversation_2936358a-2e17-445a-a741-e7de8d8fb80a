package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSubcontractEntity;
import inks.service.std.manu.domain.pojo.WkSubcontractPojo;
import inks.service.std.manu.domain.pojo.WkSubcontractitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 委制单据(WkSubcontract)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:36:28
 */
@Mapper
public interface WkSubcontractMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSubcontractPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSubcontractitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSubcontractPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSubcontractEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSubcontractEntity wkSubcontractEntity);


    /**
     * 修改数据
     *
     * @param wkSubcontractEntity 实例对象
     * @return 影响行数
     */
    int update(WkSubcontractEntity wkSubcontractEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkSubcontractPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkSubcontractPojo wkSubcontractPojo);
    /**
     * 查询 被删除的Item
     *
     * @param wkSubcontractPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelMatIds(WkSubcontractPojo wkSubcontractPojo);

    /**
     * 修改数据
     *
     * @param wkSubcontractEntity 实例对象
     * @return 影响行数
     */
    int approval(WkSubcontractEntity wkSubcontractEntity);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDisannulCount(@Param("key") String key, @Param("tid") String tid);


    /**
     * 修改完工记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMrpScFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid, @Param("tid") String tid);

    void updatePrintcount(WkSubcontractPojo billPrintPojo);
}

