package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkCostbudgetEntity;
import inks.service.std.manu.domain.WkCostbudgetcostEntity;
import inks.service.std.manu.domain.WkCostbudgetitemEntity;
import inks.service.std.manu.domain.WkCostbudgetmatEntity;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.mapper.WkCostbudgetMapper;
import inks.service.std.manu.mapper.WkCostbudgetcostMapper;
import inks.service.std.manu.mapper.WkCostbudgetitemMapper;
import inks.service.std.manu.mapper.WkCostbudgetmatMapper;
import inks.service.std.manu.service.WkCostbudgetService;
import inks.service.std.manu.service.WkCostbudgetcostService;
import inks.service.std.manu.service.WkCostbudgetitemService;
import inks.service.std.manu.service.WkCostbudgetmatService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 成本预测(WkCostbudget)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-03 19:16:26
 */
@Service("wkCostbudgetService")
public class WkCostbudgetServiceImpl implements WkCostbudgetService {
    @Resource
    private WkCostbudgetMapper wkCostbudgetMapper;

    @Resource
    private WkCostbudgetitemMapper wkCostbudgetitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkCostbudgetitemService wkCostbudgetitemService;

    @Resource
    private WkCostbudgetmatMapper wkCostbudgetmatMapper;

    @Resource
    private WkCostbudgetcostMapper wkCostbudgetcostMapper;

    /**
     * 服务对象mat
     */
    @Resource
    private WkCostbudgetmatService wkCostbudgetmatService;


    /**
     * 服务对象cost
     */
    @Resource
    private WkCostbudgetcostService wkCostbudgetcostService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkCostbudgetPojo getEntity(String key, String tid) {
        return this.wkCostbudgetMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCostbudgetitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCostbudgetitemdetailPojo> lst = wkCostbudgetMapper.getPageList(queryParam);
            PageInfo<WkCostbudgetitemdetailPojo> pageInfo = new PageInfo<WkCostbudgetitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkCostbudgetPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkCostbudgetPojo wkCostbudgetPojo = this.wkCostbudgetMapper.getEntity(key, tid);
            //读取子表
            wkCostbudgetPojo.setItem(wkCostbudgetitemMapper.getList(wkCostbudgetPojo.getId(), wkCostbudgetPojo.getTenantid()));
            //读取子表
            wkCostbudgetPojo.setMat(wkCostbudgetmatMapper.getList(wkCostbudgetPojo.getId(), wkCostbudgetPojo.getTenantid()));
            //读取子表
            wkCostbudgetPojo.setCost(wkCostbudgetcostMapper.getList(wkCostbudgetPojo.getId(), wkCostbudgetPojo.getTenantid()));
            return wkCostbudgetPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCostbudgetPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCostbudgetPojo> lst = wkCostbudgetMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkCostbudgetitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setMat(wkCostbudgetmatMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setCost(wkCostbudgetcostMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkCostbudgetPojo> pageInfo = new PageInfo<WkCostbudgetPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCostbudgetPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCostbudgetPojo> lst = wkCostbudgetMapper.getPageTh(queryParam);
            PageInfo<WkCostbudgetPojo> pageInfo = new PageInfo<WkCostbudgetPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkCostbudgetPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkCostbudgetPojo insert(WkCostbudgetPojo wkCostbudgetPojo) {
//初始化NULL字段
        if (wkCostbudgetPojo.getRefno() == null) wkCostbudgetPojo.setRefno("");
        if (wkCostbudgetPojo.getBilltype() == null) wkCostbudgetPojo.setBilltype("");
        if (wkCostbudgetPojo.getBilldate() == null) wkCostbudgetPojo.setBilldate(new Date());
        if (wkCostbudgetPojo.getBilltitle() == null) wkCostbudgetPojo.setBilltitle("");
        if (wkCostbudgetPojo.getOperator() == null) wkCostbudgetPojo.setOperator("");
        if (wkCostbudgetPojo.getGroupid() == null) wkCostbudgetPojo.setGroupid("");
        if (wkCostbudgetPojo.getSummary() == null) wkCostbudgetPojo.setSummary("");
        if (wkCostbudgetPojo.getCreateby() == null) wkCostbudgetPojo.setCreateby("");
        if (wkCostbudgetPojo.getCreatebyid() == null) wkCostbudgetPojo.setCreatebyid("");
        if (wkCostbudgetPojo.getCreatedate() == null) wkCostbudgetPojo.setCreatedate(new Date());
        if (wkCostbudgetPojo.getLister() == null) wkCostbudgetPojo.setLister("");
        if (wkCostbudgetPojo.getListerid() == null) wkCostbudgetPojo.setListerid("");
        if (wkCostbudgetPojo.getModifydate() == null) wkCostbudgetPojo.setModifydate(new Date());
        if (wkCostbudgetPojo.getAssessor() == null) wkCostbudgetPojo.setAssessor("");
        if (wkCostbudgetPojo.getAssessorid() == null) wkCostbudgetPojo.setAssessorid("");
        if (wkCostbudgetPojo.getAssessdate() == null) wkCostbudgetPojo.setAssessdate(new Date());
        if (wkCostbudgetPojo.getCustom1() == null) wkCostbudgetPojo.setCustom1("");
        if (wkCostbudgetPojo.getCustom2() == null) wkCostbudgetPojo.setCustom2("");
        if (wkCostbudgetPojo.getCustom3() == null) wkCostbudgetPojo.setCustom3("");
        if (wkCostbudgetPojo.getCustom4() == null) wkCostbudgetPojo.setCustom4("");
        if (wkCostbudgetPojo.getCustom5() == null) wkCostbudgetPojo.setCustom5("");
        if (wkCostbudgetPojo.getCustom6() == null) wkCostbudgetPojo.setCustom6("");
        if (wkCostbudgetPojo.getCustom7() == null) wkCostbudgetPojo.setCustom7("");
        if (wkCostbudgetPojo.getCustom8() == null) wkCostbudgetPojo.setCustom8("");
        if (wkCostbudgetPojo.getCustom9() == null) wkCostbudgetPojo.setCustom9("");
        if (wkCostbudgetPojo.getCustom10() == null) wkCostbudgetPojo.setCustom10("");
        if (wkCostbudgetPojo.getTenantid() == null) wkCostbudgetPojo.setTenantid("");
        if (wkCostbudgetPojo.getRevision() == null) wkCostbudgetPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkCostbudgetEntity wkCostbudgetEntity = new WkCostbudgetEntity();
        BeanUtils.copyProperties(wkCostbudgetPojo, wkCostbudgetEntity);
        //设置id和新建日期
        wkCostbudgetEntity.setId(id);
        wkCostbudgetEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkCostbudgetMapper.insert(wkCostbudgetEntity);
        //Item子表处理
        List<WkCostbudgetitemPojo> lst = wkCostbudgetPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkCostbudgetitemPojo itemPojo = this.wkCostbudgetitemService.clearNull(lst.get(i));
                WkCostbudgetitemEntity wkCostbudgetitemEntity = new WkCostbudgetitemEntity();
                BeanUtils.copyProperties(itemPojo, wkCostbudgetitemEntity);
                //设置id和Pid
                wkCostbudgetitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkCostbudgetitemEntity.setPid(id);
                wkCostbudgetitemEntity.setTenantid(wkCostbudgetPojo.getTenantid());
                wkCostbudgetitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkCostbudgetitemMapper.insert(wkCostbudgetitemEntity);
            }
        }

        //Mat子表处理
        List<WkCostbudgetmatPojo> lstMat = wkCostbudgetPojo.getMat();
        if (lstMat != null) {
            //循环每个item子表
            for (int i = 0; i < lstMat.size(); i++) {
                //初始化mat的NULL
                WkCostbudgetmatPojo matPojo = this.wkCostbudgetmatService.clearNull(lstMat.get(i));
                WkCostbudgetmatEntity wkCostbudgetmatEntity = new WkCostbudgetmatEntity();
                BeanUtils.copyProperties(matPojo, wkCostbudgetmatEntity);
                //设置id和Pid
                wkCostbudgetmatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkCostbudgetmatEntity.setPid(id);
                wkCostbudgetmatEntity.setTenantid(wkCostbudgetPojo.getTenantid());
                wkCostbudgetmatEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkCostbudgetmatMapper.insert(wkCostbudgetmatEntity);
            }
        }

        //Cost子表处理
        List<WkCostbudgetcostPojo> lstCost = wkCostbudgetPojo.getCost();
        if (lstCost != null) {
            //循环每个item子表
            for (int i = 0; i < lstCost.size(); i++) {
                //初始化cost的NULL
                WkCostbudgetcostPojo costPojo = this.wkCostbudgetcostService.clearNull(lstCost.get(i));
                WkCostbudgetcostEntity wkCostbudgetcostEntity = new WkCostbudgetcostEntity();
                BeanUtils.copyProperties(costPojo, wkCostbudgetcostEntity);
                //设置id和Pid
                wkCostbudgetcostEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkCostbudgetcostEntity.setPid(id);
                wkCostbudgetcostEntity.setTenantid(wkCostbudgetPojo.getTenantid());
                wkCostbudgetcostEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkCostbudgetcostMapper.insert(wkCostbudgetcostEntity);
            }
        }

        //返回Bill实例
        return this.getBillEntity(wkCostbudgetEntity.getId(), wkCostbudgetEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkCostbudgetPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkCostbudgetPojo update(WkCostbudgetPojo wkCostbudgetPojo) {
        //主表更改
        WkCostbudgetEntity wkCostbudgetEntity = new WkCostbudgetEntity();
        BeanUtils.copyProperties(wkCostbudgetPojo, wkCostbudgetEntity);
        this.wkCostbudgetMapper.update(wkCostbudgetEntity);

        if (wkCostbudgetPojo.getItem() != null) {
            //Item子表处理
            List<WkCostbudgetitemPojo> lst = wkCostbudgetPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkCostbudgetMapper.getDelItemIds(wkCostbudgetPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkCostbudgetitemMapper.delete(lstDelIds.get(i), wkCostbudgetEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    WkCostbudgetitemEntity wkCostbudgetitemEntity = new WkCostbudgetitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        WkCostbudgetitemPojo itemPojo = this.wkCostbudgetitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, wkCostbudgetitemEntity);
                        //设置id和Pid
                        wkCostbudgetitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkCostbudgetitemEntity.setPid(wkCostbudgetEntity.getId());  // 主表 id
                        wkCostbudgetitemEntity.setTenantid(wkCostbudgetPojo.getTenantid());   // 租户id
                        wkCostbudgetitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkCostbudgetitemMapper.insert(wkCostbudgetitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkCostbudgetitemEntity);
                        wkCostbudgetitemEntity.setTenantid(wkCostbudgetPojo.getTenantid());
                        this.wkCostbudgetitemMapper.update(wkCostbudgetitemEntity);
                    }
                }
            }
        }


        if (wkCostbudgetPojo.getMat() != null) {
            //Mat子表处理
            List<WkCostbudgetmatPojo> lst = wkCostbudgetPojo.getMat();
            //获取被删除的Mat
            List<String> lstDelIds = wkCostbudgetMapper.getDelMatIds(wkCostbudgetPojo);
            if (lstDelIds != null) {
                //循环每个删除mat子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkCostbudgetmatMapper.delete(lstDelIds.get(i), wkCostbudgetEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个mat子表
                for (int i = 0; i < lst.size(); i++) {
                    WkCostbudgetmatEntity wkCostbudgetmatEntity = new WkCostbudgetmatEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化mat的NULL
                        WkCostbudgetmatPojo matPojo = this.wkCostbudgetmatService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(matPojo, wkCostbudgetmatEntity);
                        //设置id和Pid
                        wkCostbudgetmatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // mat id
                        wkCostbudgetmatEntity.setPid(wkCostbudgetEntity.getId());  // 主表 id
                        wkCostbudgetmatEntity.setTenantid(wkCostbudgetPojo.getTenantid());   // 租户id
                        wkCostbudgetmatEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkCostbudgetmatMapper.insert(wkCostbudgetmatEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkCostbudgetmatEntity);
                        wkCostbudgetmatEntity.setTenantid(wkCostbudgetPojo.getTenantid());
                        this.wkCostbudgetmatMapper.update(wkCostbudgetmatEntity);
                    }
                }
            }
        }

        if (wkCostbudgetPojo.getCost() != null) {
            //Cost子表处理
            List<WkCostbudgetcostPojo> lst = wkCostbudgetPojo.getCost();
            //获取被删除的Cost
            List<String> lstDelIds = wkCostbudgetMapper.getDelCostIds(wkCostbudgetPojo);
            if (lstDelIds != null) {
                //循环每个删除cost子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkCostbudgetcostMapper.delete(lstDelIds.get(i), wkCostbudgetEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个cost子表
                for (int i = 0; i < lst.size(); i++) {
                    WkCostbudgetcostEntity wkCostbudgetcostEntity = new WkCostbudgetcostEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化cost的NULL
                        WkCostbudgetcostPojo costPojo = this.wkCostbudgetcostService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(costPojo, wkCostbudgetcostEntity);
                        //设置id和Pid
                        wkCostbudgetcostEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // cost id
                        wkCostbudgetcostEntity.setPid(wkCostbudgetEntity.getId());  // 主表 id
                        wkCostbudgetcostEntity.setTenantid(wkCostbudgetPojo.getTenantid());   // 租户id
                        wkCostbudgetcostEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkCostbudgetcostMapper.insert(wkCostbudgetcostEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkCostbudgetcostEntity);
                        wkCostbudgetcostEntity.setTenantid(wkCostbudgetPojo.getTenantid());
                        this.wkCostbudgetcostMapper.update(wkCostbudgetcostEntity);
                    }
                }
            }
        }


        //返回Bill实例
        return this.getBillEntity(wkCostbudgetEntity.getId(), wkCostbudgetEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkCostbudgetPojo wkCostbudgetPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkCostbudgetitemPojo> lst = wkCostbudgetPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkCostbudgetitemPojo wkCostbudgetitemPojo : lst) {
                this.wkCostbudgetitemMapper.delete(wkCostbudgetitemPojo.getId(), tid);
            }
        }

        //Mat子表处理
        List<WkCostbudgetmatPojo> lstMat = wkCostbudgetPojo.getMat();
        if (lstMat != null) {
            //循环每个删除mat子表
            for (WkCostbudgetmatPojo wkCostbudgetmatPojo : lstMat) {
                this.wkCostbudgetmatMapper.delete(wkCostbudgetmatPojo.getId(), tid);
            }
        }

        //Cost子表处理
        List<WkCostbudgetcostPojo> lstCost = wkCostbudgetPojo.getCost();
        if (lstCost != null) {
            //循环每个删除cost子表
            for (WkCostbudgetcostPojo wkCostbudgetcostPojo : lstCost) {
                this.wkCostbudgetcostMapper.delete(wkCostbudgetcostPojo.getId(), tid);
            }
        }
        this.wkCostbudgetMapper.delete(key, tid);
        return wkCostbudgetPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkCostbudgetPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkCostbudgetPojo approval(WkCostbudgetPojo wkCostbudgetPojo) {
        //主表更改
        WkCostbudgetEntity wkCostbudgetEntity = new WkCostbudgetEntity();
        BeanUtils.copyProperties(wkCostbudgetPojo, wkCostbudgetEntity);
        this.wkCostbudgetMapper.approval(wkCostbudgetEntity);
        //返回Bill实例
        return this.getBillEntity(wkCostbudgetEntity.getId(), wkCostbudgetEntity.getTenantid());
    }

}
