package inks.service.std.manu.utils;


import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

public class MyDateUtils {
    /**
     * @return LocalDateTime
     * @Description LocalDateTime提供了一系列的plusXxx()和minusXxx()方法来进行日期时间的加减操作
     * <AUTHOR>
     * @param[1] LocalDateTime 待累加的日期时间
     * @param[2] time
     * @param[3] type 累加单位，0代表小时，1代表分钟，2代表天,3代表秒
     * @time 2023/7/20 12:47
     */
    public static LocalDateTime addTime(LocalDateTime dateTime, long time, TimeUnit unit) {
        switch (unit) {
            case SECONDS:
                return dateTime.plusSeconds(time);
            case MINUTES:
                return dateTime.plusMinutes(time);
            case HOURS:
                return dateTime.plusHours(time);
            case DAYS:
                return dateTime.plusDays(time);
            case NANOSECONDS:
                return dateTime.plusNanos(time);
            case MICROSECONDS:
                return dateTime.plusNanos(TimeUnit.MICROSECONDS.toNanos(time));
            case MILLISECONDS:
                return dateTime.plusNanos(TimeUnit.MILLISECONDS.toNanos(time));
            default:
                // 如果unit不是以上任何一个，返回原始日期时间
                return dateTime;
        }
    }

    /**
     * @return LocalDateTime
     * @Description LocalDateTime提供了一系列的minusXxx()方法来进行日期时间的减操作
     * <AUTHOR>
     * @param[1] LocalDateTime 待减少的日期时间
     * @param[2] time
     * @param[3] type 减少单位，0代表小时，1代表分钟，2代表天,3代表秒
     * @time 2023/7/20 12:47
     */
    public static LocalDateTime subTime(LocalDateTime dateTime, long time, TimeUnit unit) {
        switch (unit) {
            case SECONDS:
                return dateTime.minusSeconds(time);
            case MINUTES:
                return dateTime.minusMinutes(time);
            case HOURS:
                return dateTime.minusHours(time);
            case DAYS:
                return dateTime.minusDays(time);
            case NANOSECONDS:
                return dateTime.minusNanos(time);
            case MICROSECONDS:
                return dateTime.minusNanos(TimeUnit.MICROSECONDS.toNanos(time));
            case MILLISECONDS:
                return dateTime.minusNanos(TimeUnit.MILLISECONDS.toNanos(time));
            default:
                // 如果unit不是以上任何一个，返回原始日期时间
                return dateTime;
        }
    }

    public static Date addTime(Date date, long time, TimeUnit unit) {
        // 将Date转换为LocalDateTime
        LocalDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        // 使用原有的逻辑
        LocalDateTime resultDateTime = addTime(dateTime, time, unit);

        // 将结果转回Date
        return Date.from(resultDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date subTime(Date date, long time, TimeUnit unit) {
        // 将Date转换为LocalDateTime
        LocalDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        // 使用原有的逻辑
        LocalDateTime resultDateTime = subTime(dateTime, time, unit);

        // 将结果转回Date
        return Date.from(resultDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }


    // 配合工序周期的int类型字段 CycleUnit: 0小时1分钟2天
    public static LocalDateTime addTime(LocalDateTime dateTime, int time, int type) {
        switch (type) {
            case 0:
                // 累加小时
                return dateTime.plusHours(time);
            case 1:
                // 累加分钟
                return dateTime.plusMinutes(time);
            case 2:
                // 累加天
                return dateTime.plusDays(time);
            case 3:
                // 累加秒
                return dateTime.plusSeconds(time);
            default:
                // 如果type不是0、1、2或3，返回原始日期时间
                return dateTime;
        }
    }
    public static Date addTime(Date date, int time, int type) {
        // 将Date转换为LocalDateTime
        LocalDateTime dateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        // 使用原有的逻辑
        LocalDateTime resultDateTime = addTime(dateTime, time, type);

        // 将结果转回Date
        return Date.from(resultDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }


    // 在给定日期基础上加指定分钟数
    public static Date addMinutesOnDate(Date date, int minutesToAdd) {
        // 将分钟数转换为毫秒数
        long millisecondsToAdd = (long) minutesToAdd * 60 * 1000;
        // 获取当前日期的毫秒数
        long currentTimeInMillis = date.getTime();
        // 在当前日期的基础上加上指定的毫秒数
        long newTimeInMillis = currentTimeInMillis + millisecondsToAdd;
        // 创建新的日期对象并返回
        return new Date(newTimeInMillis);
    }

    // 在给定日期基础上减去指定分钟数
    public static Date subMinutesOnDate(Date date, int minutesToSubtract) {
        long millisecondsToSubtract = (long) minutesToSubtract * 60 * 1000;
        long currentTimeInMillis = date.getTime();
        long newTimeInMillis = currentTimeInMillis - millisecondsToSubtract;
        return new Date(newTimeInMillis);
    }


    public static Date getCycleDate(String code) {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);//设置起时间
        String[] value = new String[2];
        for (int i = 0; i < code.length(); i++) {
            value[i] = code.charAt(i) + "";
            System.out.println(value[i]);
        }
        if (value[0].equals("Y")) {
            cal.add(Calendar.YEAR, Integer.valueOf(value[1]));
        }
        if (value[0].equals("M")) {
            cal.add(Calendar.MONTH, Integer.valueOf(value[1]));
        }
        if (value[0].equals("W")) {
            cal.add(Calendar.DATE, Integer.valueOf(value[1]) * 7);
        }
        return cal.getTime();
    }


}
