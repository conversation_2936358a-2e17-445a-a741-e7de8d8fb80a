package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipqtygroupEntity;
import inks.service.std.manu.domain.WkWipqtygroupitemEntity;
import inks.service.std.manu.domain.pojo.WkWipqtygroupPojo;
import inks.service.std.manu.domain.pojo.WkWipqtygroupitemPojo;
import inks.service.std.manu.domain.pojo.WkWipqtygroupitemdetailPojo;
import inks.service.std.manu.mapper.WkWipqtygroupMapper;
import inks.service.std.manu.mapper.WkWipqtygroupitemMapper;
import inks.service.std.manu.service.WkWipqtygroupService;
import inks.service.std.manu.service.WkWipqtygroupitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 过数小组(WkWipqtygroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-10 11:14:11
 */
@Service("wkWipqtygroupService")
public class WkWipqtygroupServiceImpl implements WkWipqtygroupService {
    @Resource
    private WkWipqtygroupMapper wkWipqtygroupMapper;

    @Resource
    private WkWipqtygroupitemMapper wkWipqtygroupitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkWipqtygroupitemService wkWipqtygroupitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipqtygroupPojo getEntity(String key, String tid) {
        return this.wkWipqtygroupMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipqtygroupitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtygroupitemdetailPojo> lst = wkWipqtygroupMapper.getPageList(queryParam);
            PageInfo<WkWipqtygroupitemdetailPojo> pageInfo = new PageInfo<WkWipqtygroupitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipqtygroupPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkWipqtygroupPojo wkWipqtygroupPojo = this.wkWipqtygroupMapper.getEntity(key, tid);
            //读取子表
            wkWipqtygroupPojo.setItem(wkWipqtygroupitemMapper.getList(wkWipqtygroupPojo.getId(), wkWipqtygroupPojo.getTenantid()));
            return wkWipqtygroupPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipqtygroupPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtygroupPojo> lst = wkWipqtygroupMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkWipqtygroupitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkWipqtygroupPojo> pageInfo = new PageInfo<WkWipqtygroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipqtygroupPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtygroupPojo> lst = wkWipqtygroupMapper.getPageTh(queryParam);
            PageInfo<WkWipqtygroupPojo> pageInfo = new PageInfo<WkWipqtygroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWipqtygroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipqtygroupPojo insert(WkWipqtygroupPojo wkWipqtygroupPojo) {
//初始化NULL字段
        if (wkWipqtygroupPojo.getWkcencode() == null) wkWipqtygroupPojo.setWkcencode("");
        if (wkWipqtygroupPojo.getWkcenname() == null) wkWipqtygroupPojo.setWkcenname("");
        if (wkWipqtygroupPojo.getRownum() == null) wkWipqtygroupPojo.setRownum(0);
        if (wkWipqtygroupPojo.getSummary() == null) wkWipqtygroupPojo.setSummary("");
        if (wkWipqtygroupPojo.getCreateby() == null) wkWipqtygroupPojo.setCreateby("");
        if (wkWipqtygroupPojo.getCreatebyid() == null) wkWipqtygroupPojo.setCreatebyid("");
        if (wkWipqtygroupPojo.getCreatedate() == null) wkWipqtygroupPojo.setCreatedate(new Date());
        if (wkWipqtygroupPojo.getLister() == null) wkWipqtygroupPojo.setLister("");
        if (wkWipqtygroupPojo.getListerid() == null) wkWipqtygroupPojo.setListerid("");
        if (wkWipqtygroupPojo.getModifydate() == null) wkWipqtygroupPojo.setModifydate(new Date());
        if (wkWipqtygroupPojo.getCustom1() == null) wkWipqtygroupPojo.setCustom1("");
        if (wkWipqtygroupPojo.getCustom2() == null) wkWipqtygroupPojo.setCustom2("");
        if (wkWipqtygroupPojo.getCustom3() == null) wkWipqtygroupPojo.setCustom3("");
        if (wkWipqtygroupPojo.getCustom4() == null) wkWipqtygroupPojo.setCustom4("");
        if (wkWipqtygroupPojo.getCustom5() == null) wkWipqtygroupPojo.setCustom5("");
        if (wkWipqtygroupPojo.getCustom6() == null) wkWipqtygroupPojo.setCustom6("");
        if (wkWipqtygroupPojo.getCustom7() == null) wkWipqtygroupPojo.setCustom7("");
        if (wkWipqtygroupPojo.getCustom8() == null) wkWipqtygroupPojo.setCustom8("");
        if (wkWipqtygroupPojo.getCustom9() == null) wkWipqtygroupPojo.setCustom9("");
        if (wkWipqtygroupPojo.getCustom10() == null) wkWipqtygroupPojo.setCustom10("");
        if (wkWipqtygroupPojo.getTenantid() == null) wkWipqtygroupPojo.setTenantid("");
        if (wkWipqtygroupPojo.getTenantname() == null) wkWipqtygroupPojo.setTenantname("");
        if (wkWipqtygroupPojo.getRevision() == null) wkWipqtygroupPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkWipqtygroupEntity wkWipqtygroupEntity = new WkWipqtygroupEntity();
        BeanUtils.copyProperties(wkWipqtygroupPojo, wkWipqtygroupEntity);

        //设置id和新建日期
        wkWipqtygroupEntity.setId(id);
        wkWipqtygroupEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkWipqtygroupMapper.insert(wkWipqtygroupEntity);
        //Item子表处理
        List<WkWipqtygroupitemPojo> lst = wkWipqtygroupPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkWipqtygroupitemPojo itemPojo = this.wkWipqtygroupitemService.clearNull(lst.get(i));
                WkWipqtygroupitemEntity wkWipqtygroupitemEntity = new WkWipqtygroupitemEntity();
                BeanUtils.copyProperties(itemPojo, wkWipqtygroupitemEntity);
                //设置id和Pid
                wkWipqtygroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipqtygroupitemEntity.setPid(id);
                wkWipqtygroupitemEntity.setTenantid(wkWipqtygroupPojo.getTenantid());
                wkWipqtygroupitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkWipqtygroupitemMapper.insert(wkWipqtygroupitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipqtygroupEntity.getId(), wkWipqtygroupEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipqtygroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipqtygroupPojo update(WkWipqtygroupPojo wkWipqtygroupPojo) {
        //主表更改
        WkWipqtygroupEntity wkWipqtygroupEntity = new WkWipqtygroupEntity();
        BeanUtils.copyProperties(wkWipqtygroupPojo, wkWipqtygroupEntity);
        this.wkWipqtygroupMapper.update(wkWipqtygroupEntity);
        if (wkWipqtygroupPojo.getItem() != null) {
            //Item子表处理
            List<WkWipqtygroupitemPojo> lst = wkWipqtygroupPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkWipqtygroupMapper.getDelItemIds(wkWipqtygroupPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkWipqtygroupitemMapper.delete(lstDelIds.get(i), wkWipqtygroupEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    WkWipqtygroupitemEntity wkWipqtygroupitemEntity = new WkWipqtygroupitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        WkWipqtygroupitemPojo itemPojo = this.wkWipqtygroupitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, wkWipqtygroupitemEntity);
                        //设置id和Pid
                        wkWipqtygroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkWipqtygroupitemEntity.setPid(wkWipqtygroupEntity.getId());  // 主表 id
                        wkWipqtygroupitemEntity.setTenantid(wkWipqtygroupPojo.getTenantid());   // 租户id
                        wkWipqtygroupitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkWipqtygroupitemMapper.insert(wkWipqtygroupitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkWipqtygroupitemEntity);
                        wkWipqtygroupitemEntity.setTenantid(wkWipqtygroupPojo.getTenantid());
                        this.wkWipqtygroupitemMapper.update(wkWipqtygroupitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipqtygroupEntity.getId(), wkWipqtygroupEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        WkWipqtygroupPojo wkWipqtygroupPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkWipqtygroupitemPojo> lst = wkWipqtygroupPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.wkWipqtygroupitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.wkWipqtygroupMapper.delete(key, tid);
    }


}
