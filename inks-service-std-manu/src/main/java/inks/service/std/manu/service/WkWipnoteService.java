package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipnotePojo;
import inks.service.std.manu.domain.pojo.WkWipnoteitemPojo;
import inks.service.std.manu.domain.pojo.WkWipnoteitemdetailPojo;

import java.util.List;

/**
 * WIP记录(WkWipnote)表服务接口
 *
 * <AUTHOR>
 * @since 2022-01-06 13:57:46
 */
public interface WkWipnoteService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipnotePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipnoteitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipnotePojo getBillEntity(String key, String tid);

    WkWipnotePojo getBillEntityByWorkUid(String workuid, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipnotePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipnotePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipnotePojo 实例对象
     * @return 实例对象
     */
    WkWipnotePojo insert(WkWipnotePojo wkWipnotePojo, Integer inFirst);

    /**
     * 修改数据
     *
     * @param wkWipnotepojo 实例对象
     * @return 实例对象
     */
    WkWipnotePojo update(WkWipnotePojo wkWipnotepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipnotePojo getEntityByWorkUid(String key, String tid);

    WkWipnotePojo getScmBillEntity(String key, String groupids, String tid);

    /**
     * 作废数据
     *
     * @param key 实例对象
     * @return 实例对象
     */
    WkWipnotePojo disannul(String key, Integer type, LoginUser loginUser);

    /**
     * 中止数据
     *
     * @param key 实例对象
     * @return 实例对象
     */
    String closed(List<String> ids, Integer type, LoginUser loginUser);

    WkWipnotePojo mergeItem(List<WkWipnotePojo> lstitem, int inFirst, String progroupid, LoginUser loginUser);

    void updatePrintcount(WkWipnotePojo billPrintPojo);

    List<WkWipnoteitemPojo> getSpecPcbItemListByGoodsid(String goodsid, String tenantid);

    PageInfo<WkWipnoteitemdetailPojo> getOnlinePageListBySummary(QueryParam queryParam);
}
