package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkScdeductionEntity;
import inks.service.std.manu.domain.WkScdeductionitemEntity;
import inks.service.std.manu.domain.pojo.WkScdeductionPojo;
import inks.service.std.manu.domain.pojo.WkScdeductionitemPojo;
import inks.service.std.manu.domain.pojo.WkScdeductionitemdetailPojo;
import inks.service.std.manu.mapper.WkScdeductionMapper;
import inks.service.std.manu.mapper.WkScdeductionitemMapper;
import inks.service.std.manu.service.WkScdeductionService;
import inks.service.std.manu.service.WkScdeductionitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 委制扣款(WkScdeduction)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-12 10:40:22
 */
@Service("wkScdeductionService")
public class WkScdeductionServiceImpl implements WkScdeductionService {
    @Resource
    private WkScdeductionMapper wkScdeductionMapper;

    @Resource
    private WkScdeductionitemMapper wkScdeductionitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkScdeductionitemService wkScdeductionitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkScdeductionPojo getEntity(String key, String tid) {
        return this.wkScdeductionMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkScdeductionitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkScdeductionitemdetailPojo> lst = wkScdeductionMapper.getPageList(queryParam);
            PageInfo<WkScdeductionitemdetailPojo> pageInfo = new PageInfo<WkScdeductionitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkScdeductionPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkScdeductionPojo wkScdeductionPojo = this.wkScdeductionMapper.getEntity(key, tid);
            //读取子表
            wkScdeductionPojo.setItem(wkScdeductionitemMapper.getList(wkScdeductionPojo.getId(), tid));
            return wkScdeductionPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkScdeductionPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkScdeductionPojo> lst = wkScdeductionMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (WkScdeductionPojo item : lst) {
                item.setItem(wkScdeductionitemMapper.getList(item.getId(), tid));
            }
            PageInfo<WkScdeductionPojo> pageInfo = new PageInfo<WkScdeductionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkScdeductionPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkScdeductionPojo> lst = wkScdeductionMapper.getPageTh(queryParam);
            PageInfo<WkScdeductionPojo> pageInfo = new PageInfo<WkScdeductionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkScdeductionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkScdeductionPojo insert(WkScdeductionPojo wkScdeductionPojo) {
        String tid = wkScdeductionPojo.getTenantid();
//初始化NULL字段
        if (wkScdeductionPojo.getRefno() == null) wkScdeductionPojo.setRefno("");
        if (wkScdeductionPojo.getBilltype() == null) wkScdeductionPojo.setBilltype("");
        if (wkScdeductionPojo.getBilltitle() == null) wkScdeductionPojo.setBilltitle("");
        if (wkScdeductionPojo.getBilldate() == null) wkScdeductionPojo.setBilldate(new Date());
        if (wkScdeductionPojo.getGroupid() == null) wkScdeductionPojo.setGroupid("");
        if (wkScdeductionPojo.getOperator() == null) wkScdeductionPojo.setOperator("");
        if (wkScdeductionPojo.getTaxrate() == null) wkScdeductionPojo.setTaxrate(0);
        if (wkScdeductionPojo.getBilltaxamount() == null) wkScdeductionPojo.setBilltaxamount(0D);
        if (wkScdeductionPojo.getBillamount() == null) wkScdeductionPojo.setBillamount(0D);
        if (wkScdeductionPojo.getBilltaxtotal() == null) wkScdeductionPojo.setBilltaxtotal(0D);
        if (wkScdeductionPojo.getSummary() == null) wkScdeductionPojo.setSummary("");
        if (wkScdeductionPojo.getCreateby() == null) wkScdeductionPojo.setCreateby("");
        if (wkScdeductionPojo.getCreatebyid() == null) wkScdeductionPojo.setCreatebyid("");
        if (wkScdeductionPojo.getCreatedate() == null) wkScdeductionPojo.setCreatedate(new Date());
        if (wkScdeductionPojo.getLister() == null) wkScdeductionPojo.setLister("");
        if (wkScdeductionPojo.getListerid() == null) wkScdeductionPojo.setListerid("");
        if (wkScdeductionPojo.getModifydate() == null) wkScdeductionPojo.setModifydate(new Date());
        if (wkScdeductionPojo.getAssessor() == null) wkScdeductionPojo.setAssessor("");
        if (wkScdeductionPojo.getAssessorid() == null) wkScdeductionPojo.setAssessorid("");
        if (wkScdeductionPojo.getAssessdate() == null) wkScdeductionPojo.setAssessdate(new Date());
        if (wkScdeductionPojo.getItemcount() == null)
            wkScdeductionPojo.setItemcount(wkScdeductionPojo.getItem().size());
        if (wkScdeductionPojo.getDisannulcount() == null) wkScdeductionPojo.setDisannulcount(0);
        if (wkScdeductionPojo.getFinishcount() == null) wkScdeductionPojo.setFinishcount(0);
        if (wkScdeductionPojo.getPrintcount() == null) wkScdeductionPojo.setPrintcount(0);
        if (wkScdeductionPojo.getCustom1() == null) wkScdeductionPojo.setCustom1("");
        if (wkScdeductionPojo.getCustom2() == null) wkScdeductionPojo.setCustom2("");
        if (wkScdeductionPojo.getCustom3() == null) wkScdeductionPojo.setCustom3("");
        if (wkScdeductionPojo.getCustom4() == null) wkScdeductionPojo.setCustom4("");
        if (wkScdeductionPojo.getCustom5() == null) wkScdeductionPojo.setCustom5("");
        if (wkScdeductionPojo.getCustom6() == null) wkScdeductionPojo.setCustom6("");
        if (wkScdeductionPojo.getCustom7() == null) wkScdeductionPojo.setCustom7("");
        if (wkScdeductionPojo.getCustom8() == null) wkScdeductionPojo.setCustom8("");
        if (wkScdeductionPojo.getCustom9() == null) wkScdeductionPojo.setCustom9("");
        if (wkScdeductionPojo.getCustom10() == null) wkScdeductionPojo.setCustom10("");
        if (wkScdeductionPojo.getDeptid() == null) wkScdeductionPojo.setDeptid("");
        if (wkScdeductionPojo.getTenantid() == null) wkScdeductionPojo.setTenantid("");
        if (wkScdeductionPojo.getTenantname() == null) wkScdeductionPojo.setTenantname("");
        if (wkScdeductionPojo.getRevision() == null) wkScdeductionPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkScdeductionEntity wkScdeductionEntity = new WkScdeductionEntity();
        BeanUtils.copyProperties(wkScdeductionPojo, wkScdeductionEntity);

        //设置id和新建日期
        wkScdeductionEntity.setId(id);
        wkScdeductionEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkScdeductionMapper.insert(wkScdeductionEntity);
        //Item子表处理
        List<WkScdeductionitemPojo> lst = wkScdeductionPojo.getItem();

        if (lst != null) {
            //循环每个item子表
            for (WkScdeductionitemPojo item : lst) {
                //初始化item的NULL
                WkScdeductionitemPojo itemPojo = this.wkScdeductionitemService.clearNull(item);
                WkScdeductionitemEntity wkScdeductionitemEntity = new WkScdeductionitemEntity();
                BeanUtils.copyProperties(itemPojo, wkScdeductionitemEntity);
                //设置id和Pid
                wkScdeductionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkScdeductionitemEntity.setPid(id);
                wkScdeductionitemEntity.setTenantid(tid);
                wkScdeductionitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkScdeductionitemMapper.insert(wkScdeductionitemEntity);
            }

        }
        //返回Bill实例
        return this.getBillEntity(wkScdeductionEntity.getId(), tid);

    }

    /**
     * 修改数据
     *
     * @param wkScdeductionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkScdeductionPojo update(WkScdeductionPojo wkScdeductionPojo) {
        String tid = wkScdeductionPojo.getTenantid();
        wkScdeductionPojo.setItemcount(wkScdeductionPojo.getItem().size());
        //主表更改
        WkScdeductionEntity wkScdeductionEntity = new WkScdeductionEntity();
        BeanUtils.copyProperties(wkScdeductionPojo, wkScdeductionEntity);
        this.wkScdeductionMapper.update(wkScdeductionEntity);
        if (wkScdeductionPojo.getItem() != null) {
            //Item子表处理
            List<WkScdeductionitemPojo> lst = wkScdeductionPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkScdeductionMapper.getDelItemIds(wkScdeductionPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String delId : lstDelIds) {
                    this.wkScdeductionitemMapper.delete(delId, tid);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (WkScdeductionitemPojo item : lst) {
                    WkScdeductionitemEntity wkScdeductionitemEntity = new WkScdeductionitemEntity();
                    if ("".equals(item.getId()) || item.getId() == null) {
                        //初始化item的NULL
                        WkScdeductionitemPojo itemPojo = this.wkScdeductionitemService.clearNull(item);
                        BeanUtils.copyProperties(itemPojo, wkScdeductionitemEntity);
                        //设置id和Pid
                        wkScdeductionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkScdeductionitemEntity.setPid(wkScdeductionEntity.getId());  // 主表 id
                        wkScdeductionitemEntity.setTenantid(tid);   // 租户id
                        wkScdeductionitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkScdeductionitemMapper.insert(wkScdeductionitemEntity);
                    } else {
                        BeanUtils.copyProperties(item, wkScdeductionitemEntity);
                        wkScdeductionitemEntity.setTenantid(tid);
                        this.wkScdeductionitemMapper.update(wkScdeductionitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkScdeductionEntity.getId(), tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkScdeductionPojo wkScdeductionPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkScdeductionitemPojo> lst = wkScdeductionPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkScdeductionitemPojo item : lst) {
                this.wkScdeductionitemMapper.delete(item.getId(), tid);
            }
        }
        this.wkScdeductionMapper.delete(key, tid);
        return wkScdeductionPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkScdeductionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkScdeductionPojo approval(WkScdeductionPojo wkScdeductionPojo) {
        String tid = wkScdeductionPojo.getTenantid();
        //主表更改
        WkScdeductionEntity wkScdeductionEntity = new WkScdeductionEntity();
        BeanUtils.copyProperties(wkScdeductionPojo, wkScdeductionEntity);
        this.wkScdeductionMapper.approval(wkScdeductionEntity);
        //返回Bill实例
        return this.getBillEntity(wkScdeductionEntity.getId(), tid);
    }

}
