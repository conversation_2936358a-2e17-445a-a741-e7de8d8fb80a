package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSccarryoveritemPojo;

import java.util.List;

/**
 * 结转子表(WkSccarryoveritem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-09 14:05:01
 */
public interface WkSccarryoveritemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccarryoveritemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSccarryoveritemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSccarryoveritemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkSccarryoveritemPojo 实例对象
     * @return 实例对象
     */
    WkSccarryoveritemPojo insert(WkSccarryoveritemPojo wkSccarryoveritemPojo);

    /**
     * 修改数据
     *
     * @param wkSccarryoveritempojo 实例对象
     * @return 实例对象
     */
    WkSccarryoveritemPojo update(WkSccarryoveritemPojo wkSccarryoveritempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkSccarryoveritempojo 实例对象
     * @return 实例对象
     */
    WkSccarryoveritemPojo clearNull(WkSccarryoveritemPojo wkSccarryoveritempojo);
}
