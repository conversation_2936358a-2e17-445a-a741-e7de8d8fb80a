package inks.service.std.manu.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.manu.domain.pojo.WkWipfinishPojo;
import inks.service.std.manu.domain.pojo.WkWipfinishitemPojo;
import inks.service.std.manu.domain.pojo.WkWipfinishitemdetailPojo;
import inks.service.std.manu.domain.WkWipfinishEntity;
import inks.service.std.manu.domain.WkWipfinishitemEntity;
import inks.service.std.manu.mapper.WkWipfinishMapper;
import inks.service.std.manu.service.WkWipfinishService;
import inks.service.std.manu.service.WkWipfinishitemService;
import inks.service.std.manu.mapper.WkWipfinishitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 完工表(WkWipfinish)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-06 14:33:54
 */
@Service("wkWipfinishService")
public class WkWipfinishServiceImpl implements WkWipfinishService {
    @Resource
    private WkWipfinishMapper wkWipfinishMapper;
    
    @Resource
    private WkWipfinishitemMapper wkWipfinishitemMapper;
    

    @Resource
    private WkWipfinishitemService wkWipfinishitemService;

    @Override
    public WkWipfinishPojo getEntity(String key, String tid) {
        return this.wkWipfinishMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<WkWipfinishitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipfinishitemdetailPojo> lst = wkWipfinishMapper.getPageList(queryParam);
            PageInfo<WkWipfinishitemdetailPojo> pageInfo = new PageInfo<WkWipfinishitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public WkWipfinishPojo getBillEntity(String key, String tid) {
       try {
           //读取主表
           WkWipfinishPojo wkWipfinishPojo = this.wkWipfinishMapper.getEntity(key,tid);
           //读取子表
           wkWipfinishPojo.setItem(wkWipfinishitemMapper.getList(wkWipfinishPojo.getId(),tid));
           return wkWipfinishPojo;
       }catch (Exception e){
          throw new BaseBusinessException(e.getMessage());
       } 
    }


    @Override
    public PageInfo<WkWipfinishPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipfinishPojo> lst = wkWipfinishMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(WkWipfinishPojo item : lst){
                item.setItem(wkWipfinishitemMapper.getList(item.getId(), tid));
            }
            PageInfo<WkWipfinishPojo> pageInfo = new PageInfo<WkWipfinishPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<WkWipfinishPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipfinishPojo> lst = wkWipfinishMapper.getPageTh(queryParam);
            PageInfo<WkWipfinishPojo> pageInfo = new PageInfo<WkWipfinishPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public WkWipfinishPojo insert(WkWipfinishPojo wkWipfinishPojo) {
    String tid = wkWipfinishPojo.getTenantid();
        //初始化NULL字段
        cleanNull(wkWipfinishPojo);
         //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkWipfinishEntity wkWipfinishEntity = new WkWipfinishEntity(); 
        BeanUtils.copyProperties(wkWipfinishPojo,wkWipfinishEntity);
      
        //设置id和新建日期
        wkWipfinishEntity.setId(id);
        wkWipfinishEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkWipfinishMapper.insert(wkWipfinishEntity);
        //Item子表处理
        List<WkWipfinishitemPojo> lst = wkWipfinishPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(WkWipfinishitemPojo item : lst){
               //初始化item的NULL
               WkWipfinishitemPojo itemPojo =this.wkWipfinishitemService.clearNull(item);
               WkWipfinishitemEntity wkWipfinishitemEntity = new WkWipfinishitemEntity(); 
               BeanUtils.copyProperties(itemPojo,wkWipfinishitemEntity);
               //设置id和Pid
               wkWipfinishitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               wkWipfinishitemEntity.setPid(id);
               wkWipfinishitemEntity.setTenantid(tid);
               wkWipfinishitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.wkWipfinishitemMapper.insert(wkWipfinishitemEntity);
            }
            
        } 
        //返回Bill实例
        return this.getBillEntity(wkWipfinishEntity.getId(),tid);
    }


    @Override
    @Transactional
    public WkWipfinishPojo update(WkWipfinishPojo wkWipfinishPojo) {
        String tid = wkWipfinishPojo.getTenantid();
        //主表更改
        WkWipfinishEntity wkWipfinishEntity = new WkWipfinishEntity(); 
        BeanUtils.copyProperties(wkWipfinishPojo,wkWipfinishEntity);
        this.wkWipfinishMapper.update(wkWipfinishEntity);
        if (wkWipfinishPojo.getItem() != null) {
        //Item子表处理
        List<WkWipfinishitemPojo> lst = wkWipfinishPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =wkWipfinishMapper.getDelItemIds(wkWipfinishPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for (String delId : lstDelIds) {
             this.wkWipfinishitemMapper.delete(delId, tid);
            }
        }
        if (lst != null){
            //循环每个item子表
            for(WkWipfinishitemPojo item : lst){
               WkWipfinishitemEntity wkWipfinishitemEntity = new WkWipfinishitemEntity(); 
               if ("".equals(item.getId()) || item.getId() == null){
                //初始化item的NULL
               WkWipfinishitemPojo itemPojo =this.wkWipfinishitemService.clearNull(item);
               BeanUtils.copyProperties(itemPojo,wkWipfinishitemEntity);
               //设置id和Pid
               wkWipfinishitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               wkWipfinishitemEntity.setPid(wkWipfinishEntity.getId());  // 主表 id
               wkWipfinishitemEntity.setTenantid(tid);   // 租户id
               wkWipfinishitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.wkWipfinishitemMapper.insert(wkWipfinishitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(item,wkWipfinishitemEntity);       
                wkWipfinishitemEntity.setTenantid(tid);        
               this.wkWipfinishitemMapper.update(wkWipfinishitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(wkWipfinishEntity.getId(),tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
       WkWipfinishPojo wkWipfinishPojo =  this.getBillEntity(key,tid);
        //Item子表处理
        List<WkWipfinishitemPojo> lst = wkWipfinishPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(WkWipfinishitemPojo item : lst){
              this.wkWipfinishitemMapper.delete(item.getId(),tid);
            }
        }        
        return this.wkWipfinishMapper.delete(key,tid) ;
    }
    

    

    private static void cleanNull(WkWipfinishPojo wkWipfinishPojo) {
        if(wkWipfinishPojo.getRefno()==null) wkWipfinishPojo.setRefno("");
        if(wkWipfinishPojo.getBilltitle()==null) wkWipfinishPojo.setBilltitle("");
        if(wkWipfinishPojo.getBilltype()==null) wkWipfinishPojo.setBilltype("");
        if(wkWipfinishPojo.getBilldate()==null) wkWipfinishPojo.setBilldate(new Date());
        if(wkWipfinishPojo.getWipid()==null) wkWipfinishPojo.setWipid("");
        if(wkWipfinishPojo.getWpid()==null) wkWipfinishPojo.setWpid("");
        if(wkWipfinishPojo.getWpcode()==null) wkWipfinishPojo.setWpcode("");
        if(wkWipfinishPojo.getWpname()==null) wkWipfinishPojo.setWpname("");
        if(wkWipfinishPojo.getStationid()==null) wkWipfinishPojo.setStationid("");
        if(wkWipfinishPojo.getStatcode()==null) wkWipfinishPojo.setStatcode("");
        if(wkWipfinishPojo.getStatname()==null) wkWipfinishPojo.setStatname("");
        if(wkWipfinishPojo.getWorkdate()==null) wkWipfinishPojo.setWorkdate(new Date());
        if(wkWipfinishPojo.getQuantity()==null) wkWipfinishPojo.setQuantity(0D);
        if(wkWipfinishPojo.getWorktime()==null) wkWipfinishPojo.setWorktime(0D);
        if(wkWipfinishPojo.getRemark()==null) wkWipfinishPojo.setRemark("");
        if(wkWipfinishPojo.getRownum()==null) wkWipfinishPojo.setRownum(0);
        if(wkWipfinishPojo.getCreateby()==null) wkWipfinishPojo.setCreateby("");
        if(wkWipfinishPojo.getCreatebyid()==null) wkWipfinishPojo.setCreatebyid("");
        if(wkWipfinishPojo.getCreatedate()==null) wkWipfinishPojo.setCreatedate(new Date());
        if(wkWipfinishPojo.getLister()==null) wkWipfinishPojo.setLister("");
        if(wkWipfinishPojo.getListerid()==null) wkWipfinishPojo.setListerid("");
        if(wkWipfinishPojo.getModifydate()==null) wkWipfinishPojo.setModifydate(new Date());
        if(wkWipfinishPojo.getCustom1()==null) wkWipfinishPojo.setCustom1("");
        if(wkWipfinishPojo.getCustom2()==null) wkWipfinishPojo.setCustom2("");
        if(wkWipfinishPojo.getCustom3()==null) wkWipfinishPojo.setCustom3("");
        if(wkWipfinishPojo.getCustom4()==null) wkWipfinishPojo.setCustom4("");
        if(wkWipfinishPojo.getCustom5()==null) wkWipfinishPojo.setCustom5("");
        if(wkWipfinishPojo.getCustom6()==null) wkWipfinishPojo.setCustom6("");
        if(wkWipfinishPojo.getCustom7()==null) wkWipfinishPojo.setCustom7("");
        if(wkWipfinishPojo.getCustom8()==null) wkWipfinishPojo.setCustom8("");
        if(wkWipfinishPojo.getCustom9()==null) wkWipfinishPojo.setCustom9("");
        if(wkWipfinishPojo.getCustom10()==null) wkWipfinishPojo.setCustom10("");
        if(wkWipfinishPojo.getTenantid()==null) wkWipfinishPojo.setTenantid("");
        if(wkWipfinishPojo.getTenantname()==null) wkWipfinishPojo.setTenantname("");
        if(wkWipfinishPojo.getRevision()==null) wkWipfinishPojo.setRevision(0);
   }

}
