package inks.service.std.manu.domain;

import java.io.Serializable;

/**
 * 阶梯项目(WkStepprogroupitem)Entity
 *
 * <AUTHOR>
 * @since 2024-02-01 16:33:49
 */
public class WkStepprogroupitemEntity implements Serializable {
    private static final long serialVersionUID = -27595605797767449L;
          // id
         private String id;
          // Pid
         private String pid;
          // 数量下限
         private Integer startqty;
          // 数量上限
         private Integer endqty;
          // 制程编码
         private String groupcode;
          // 制程名称
         private String groupname;
          // 流程描述
         private String flowdesc;
          // 工序行数
         private Integer flowcount;
          // 流程描述
         private String flowjson;
          // 行号
         private Integer rownum;
          // 备注
         private String remark;
          // 自定义1
         private String custom1;
          // 自定义2
         private String custom2;
          // 自定义3
         private String custom3;
          // 自定义4
         private String custom4;
          // 自定义5
         private String custom5;
          // 自定义6
         private String custom6;
          // 自定义7
         private String custom7;
          // 自定义8
         private String custom8;
          // 自定义9
         private String custom9;
          // 自定义10
         private String custom10;
          // 租户id
         private String tenantid;
          // 乐观锁
         private Integer revision;

    // id
  
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // Pid
  
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
    // 数量下限
  
    public Integer getStartqty() {
        return startqty;
    }

    public void setStartqty(Integer startqty) {
        this.startqty = startqty;
    }
    // 数量上限
  
    public Integer getEndqty() {
        return endqty;
    }

    public void setEndqty(Integer endqty) {
        this.endqty = endqty;
    }
    // 制程编码
  
    public String getGroupcode() {
        return groupcode;
    }

    public void setGroupcode(String groupcode) {
        this.groupcode = groupcode;
    }
    // 制程名称
  
    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }
    // 流程描述
  
    public String getFlowdesc() {
        return flowdesc;
    }

    public void setFlowdesc(String flowdesc) {
        this.flowdesc = flowdesc;
    }
    // 工序行数
  
    public Integer getFlowcount() {
        return flowcount;
    }

    public void setFlowcount(Integer flowcount) {
        this.flowcount = flowcount;
    }
    // 流程描述
  
    public String getFlowjson() {
        return flowjson;
    }

    public void setFlowjson(String flowjson) {
        this.flowjson = flowjson;
    }
    // 行号
  
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 备注
  
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 自定义1
  
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2
  
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3
  
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4
  
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5
  
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 自定义6
  
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
    // 自定义7
  
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
    // 自定义8
  
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
    // 自定义9
  
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
    // 自定义10
  
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
    // 租户id
  
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
    // 乐观锁
  
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

