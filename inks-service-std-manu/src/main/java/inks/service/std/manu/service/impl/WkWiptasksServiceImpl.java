package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.std.manu.domain.WkWiptasksEntity;
import inks.service.std.manu.domain.WkWiptasksitemEntity;
import inks.service.std.manu.domain.pojo.WkWiptasksPojo;
import inks.service.std.manu.domain.pojo.WkWiptasksitemPojo;
import inks.service.std.manu.domain.pojo.WkWiptasksitemdetailPojo;
import inks.service.std.manu.mapper.WkWiptasksMapper;
import inks.service.std.manu.mapper.WkWiptasksitemMapper;
import inks.service.std.manu.service.WkWiptasksService;
import inks.service.std.manu.service.WkWiptasksitemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 派工单(WkWiptasks)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-06 14:33:54
 */
@Service("wkWiptasksService")
public class WkWiptasksServiceImpl implements WkWiptasksService {
    @Resource
    private WkWiptasksMapper wkWiptasksMapper;

    @Resource
    private WkWiptasksitemMapper wkWiptasksitemMapper;


    @Resource
    private WkWiptasksitemService wkWiptasksitemService;

    @Override
    public WkWiptasksPojo getEntity(String key, String tid) {
        return this.wkWiptasksMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<WkWiptasksitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWiptasksitemdetailPojo> lst = wkWiptasksMapper.getPageList(queryParam);
            PageInfo<WkWiptasksitemdetailPojo> pageInfo = new PageInfo<WkWiptasksitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<Map<String, Object>> getPageListByMach(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWiptasksitemdetailPojo> lst = wkWiptasksMapper.getPageList(queryParam);
            // 单据Item拆开SPU. 带属性List转为Map
            List<Map<String, Object>> mapLst = attrListToMaps(lst);
            for (Map<String, Object> map : mapLst) {
                // 格式化时间itemplandate
                Date itemplandate = (Date) map.get("itemplandate");
                if (itemplandate != null) {
                    map.put("itemplandate", DateUtils.dateTime(itemplandate));
                }

                String wkWpName = null;
                String workitemid = (String) map.get("workitemid");

                // 1. 强制优先使用 workitemid（无论 machitemid 是否存在）
                if (StringUtils.isNotBlank(workitemid)) {
                    wkWpName = wkWiptasksMapper.getWkWpNameByWorkitemid(workitemid, queryParam.getTenantid());
                }
                // 2. 只有 workitemid 不存在时，才尝试 machitemid
                else {
                    String machitemid = (String) map.get("machitemid");
                    if (StringUtils.isNotBlank(machitemid)) {
                        wkWpName = wkWiptasksMapper.getWkWpNameByMachitemid(machitemid, queryParam.getTenantid());
                    }
                }
                // 统一更新结果
                if (wkWpName != null) {
                    map.put("wkwpname", wkWpName);
                }
            }
            return new PageInfo<>(mapLst);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<Map<String, Object>> getOnlinePageListByWip(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWiptasksitemdetailPojo> lst = wkWiptasksMapper.getOnlinePageListByWip(queryParam);
            // 单据Item拆开SPU. 带属性List转为Map
            List<Map<String, Object>> mapLst = attrListToMaps(lst);
            for (Map<String, Object> map : mapLst) {
                // 格式化时间itemplandate
                Date itemplandate = (Date) map.get("itemplandate");
                if (itemplandate != null) {
                    map.put("itemplandate", DateUtils.dateTime(itemplandate));
                }
            }
            return new PageInfo<>(mapLst);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public WkWiptasksPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkWiptasksPojo wkWiptasksPojo = this.wkWiptasksMapper.getEntity(key, tid);
            //读取子表
            wkWiptasksPojo.setItem(wkWiptasksitemMapper.getList(wkWiptasksPojo.getId(), tid));
            return wkWiptasksPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<WkWiptasksPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWiptasksPojo> lst = wkWiptasksMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (WkWiptasksPojo item : lst) {
                item.setItem(wkWiptasksitemMapper.getList(item.getId(), tid));
            }
            PageInfo<WkWiptasksPojo> pageInfo = new PageInfo<WkWiptasksPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<WkWiptasksPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWiptasksPojo> lst = wkWiptasksMapper.getPageTh(queryParam);
            PageInfo<WkWiptasksPojo> pageInfo = new PageInfo<WkWiptasksPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public WkWiptasksPojo insert(WkWiptasksPojo wkWiptasksPojo) {
        String tid = wkWiptasksPojo.getTenantid();
        //初始化NULL字段
        cleanNull(wkWiptasksPojo);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkWiptasksEntity wkWiptasksEntity = new WkWiptasksEntity();
        BeanUtils.copyProperties(wkWiptasksPojo, wkWiptasksEntity);

        //设置id和新建日期
        wkWiptasksEntity.setId(id);
        wkWiptasksEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkWiptasksMapper.insert(wkWiptasksEntity);
        //Item子表处理
        List<WkWiptasksitemPojo> lst = wkWiptasksPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (WkWiptasksitemPojo item : lst) {
                //初始化item的NULL
                WkWiptasksitemPojo itemPojo = this.wkWiptasksitemService.clearNull(item);
                WkWiptasksitemEntity wkWiptasksitemEntity = new WkWiptasksitemEntity();
                BeanUtils.copyProperties(itemPojo, wkWiptasksitemEntity);
                //设置id和Pid
                wkWiptasksitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWiptasksitemEntity.setPid(id);
                wkWiptasksitemEntity.setTenantid(tid);
                wkWiptasksitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkWiptasksitemMapper.insert(wkWiptasksitemEntity);
                //   根据wipitemid同步WIPitem表的派工数字段
                String wipitemid = itemPojo.getWipitemid();
                if (StringUtils.isNotBlank(wipitemid)) {
                    this.wkWiptasksitemMapper.syncWipItemTasksQty(wipitemid, tid);
                }
            }
            //// 子表所有的wipitemids
            //List<String> wipitemids = lst.stream().map(ApsWiptasksitemPojo::getWipitemid).collect(Collectors.toList());
            //// 同步wip子表的派工数，派工率；主表的平均派工数，末行派工数
            //if (CollectionUtils.isNotEmpty(wipitemids)) {
            //    this.apsWiptasksMapper.syncWipItemTasksQtyAndTasksRate(wipitemids, tid);
            //    List<String> wipids = this.apsWiptasksMapper.getWipidsByWipitemids(wipitemids, tid);
            //    this.apsWiptasksMapper.syncWipNoteAvgTasksQty(wipids, tid);
            //}
        }
        //返回Bill实例
        return this.getBillEntity(wkWiptasksEntity.getId(), tid);
    }


    @Override
    @Transactional
    public WkWiptasksPojo update(WkWiptasksPojo wkWiptasksPojo) {
        String tid = wkWiptasksPojo.getTenantid();
        //主表更改
        WkWiptasksEntity wkWiptasksEntity = new WkWiptasksEntity();
        BeanUtils.copyProperties(wkWiptasksPojo, wkWiptasksEntity);
        this.wkWiptasksMapper.update(wkWiptasksEntity);
        if (wkWiptasksPojo.getItem() != null) {
            //Item子表处理
            List<WkWiptasksitemPojo> lst = wkWiptasksPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkWiptasksMapper.getDelItemIds(wkWiptasksPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String delId : lstDelIds) {
                    WkWiptasksitemPojo delPojo = wkWiptasksitemMapper.getEntity(delId, tid);
                    this.wkWiptasksitemMapper.delete(delId, tid);
                    //   根据wipitemid同步WIPitem表的派工数字段
                    String wipitemid = delPojo.getWipitemid();
                    if (StringUtils.isNotBlank(wipitemid)) {
                        this.wkWiptasksitemMapper.syncWipItemTasksQty(wipitemid, tid);
                    }
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (WkWiptasksitemPojo item : lst) {
                    WkWiptasksitemEntity wkWiptasksitemEntity = new WkWiptasksitemEntity();
                    if ("".equals(item.getId()) || item.getId() == null) {
                        //初始化item的NULL
                        WkWiptasksitemPojo itemPojo = this.wkWiptasksitemService.clearNull(item);
                        BeanUtils.copyProperties(itemPojo, wkWiptasksitemEntity);
                        //设置id和Pid
                        wkWiptasksitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkWiptasksitemEntity.setPid(wkWiptasksEntity.getId());  // 主表 id
                        wkWiptasksitemEntity.setTenantid(tid);   // 租户id
                        wkWiptasksitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkWiptasksitemMapper.insert(wkWiptasksitemEntity);
                    } else {
                        BeanUtils.copyProperties(item, wkWiptasksitemEntity);
                        wkWiptasksitemEntity.setTenantid(tid);
                        this.wkWiptasksitemMapper.update(wkWiptasksitemEntity);
                    }
                }

                for (WkWiptasksitemPojo item : lst) {
                    //   根据wipitemid同步WIPitem表的派工数字段
                    String wipitemid = item.getWipitemid();
                    if (StringUtils.isNotBlank(wipitemid)) {
                        this.wkWiptasksitemMapper.syncWipItemTasksQty(wipitemid, tid);
                    }
                }
            }
        }

        //// 子表所有的wipitemids
        //List<String> wipitemids = lst.stream().map(ApsWiptasksitemPojo::getWipitemid).collect(Collectors.toList());
        //// 同步wip子表的派工数，派工率；主表的平均派工数，末行派工数
        //if (CollectionUtils.isNotEmpty(wipitemids)) {
        //    this.apsWiptasksMapper.syncWipItemTasksQtyAndTasksRate(wipitemids, tid);
        //    List<String> wipids = this.apsWiptasksMapper.getWipidsByWipitemids(wipitemids, tid);
        //    this.apsWiptasksMapper.syncWipNoteAvgTasksQty(wipids, tid);
        //}
        //返回Bill实例
        return this.getBillEntity(wkWiptasksEntity.getId(), tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
        WkWiptasksPojo wkWiptasksPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkWiptasksitemPojo> lst = wkWiptasksPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkWiptasksitemPojo item : lst) {
                this.wkWiptasksitemMapper.delete(item.getId(), tid);
                //   根据wipitemid同步WIPitem表的派工数字段
                String wipitemid = item.getWipitemid();
                if (StringUtils.isNotBlank(wipitemid)) {
                    this.wkWiptasksitemMapper.syncWipItemTasksQty(wipitemid, tid);
                }
            }
        }
        //// 子表所有的wipitemids
        //List<String> wipitemids = lst.stream().map(ApsWiptasksitemPojo::getWipitemid).collect(Collectors.toList());
        //// 同步wip子表的派工数，派工率；主表的平均派工数，末行派工数
        //if (CollectionUtils.isNotEmpty(wipitemids)) {
        //    this.apsWiptasksMapper.syncWipItemTasksQtyAndTasksRate(wipitemids, tid);
        //    List<String> wipids = this.apsWiptasksMapper.getWipidsByWipitemids(wipitemids, tid);
        //    this.apsWiptasksMapper.syncWipNoteAvgTasksQty(wipids, tid);
        //}
        return this.wkWiptasksMapper.delete(key, tid);
    }


    @Override
    @Transactional
    public WkWiptasksPojo approval(WkWiptasksPojo wkWiptasksPojo) {
        String tid = wkWiptasksPojo.getTenantid();
        //主表更改
        WkWiptasksEntity wkWiptasksEntity = new WkWiptasksEntity();
        BeanUtils.copyProperties(wkWiptasksPojo, wkWiptasksEntity);
        this.wkWiptasksMapper.approval(wkWiptasksEntity);
        //返回Bill实例
        return this.getBillEntity(wkWiptasksEntity.getId(), tid);
    }

    private static void cleanNull(WkWiptasksPojo wkWiptasksPojo) {
        if (wkWiptasksPojo.getRefno() == null) wkWiptasksPojo.setRefno("");
        if (wkWiptasksPojo.getBilltype() == null) wkWiptasksPojo.setBilltype("");
        if (wkWiptasksPojo.getBilltitle() == null) wkWiptasksPojo.setBilltitle("");
        if (wkWiptasksPojo.getBilldate() == null) wkWiptasksPojo.setBilldate(new Date());
        if (wkWiptasksPojo.getWpid() == null) wkWiptasksPojo.setWpid("");
        if (wkWiptasksPojo.getWpcode() == null) wkWiptasksPojo.setWpcode("");
        if (wkWiptasksPojo.getWpname() == null) wkWiptasksPojo.setWpname("");
        if (wkWiptasksPojo.getStationid() == null) wkWiptasksPojo.setStationid("");
        if (wkWiptasksPojo.getStatcode() == null) wkWiptasksPojo.setStatcode("");
        if (wkWiptasksPojo.getStatname() == null) wkWiptasksPojo.setStatname("");
        if (wkWiptasksPojo.getPlandate() == null) wkWiptasksPojo.setPlandate(new Date());
        if (wkWiptasksPojo.getSummary() == null) wkWiptasksPojo.setSummary("");
        if (wkWiptasksPojo.getDisannulcount() == null) wkWiptasksPojo.setDisannulcount(0);
        if (wkWiptasksPojo.getFinishcount() == null) wkWiptasksPojo.setFinishcount(0);
        if (wkWiptasksPojo.getCreateby() == null) wkWiptasksPojo.setCreateby("");
        if (wkWiptasksPojo.getCreatebyid() == null) wkWiptasksPojo.setCreatebyid("");
        if (wkWiptasksPojo.getCreatedate() == null) wkWiptasksPojo.setCreatedate(new Date());
        if (wkWiptasksPojo.getLister() == null) wkWiptasksPojo.setLister("");
        if (wkWiptasksPojo.getListerid() == null) wkWiptasksPojo.setListerid("");
        if (wkWiptasksPojo.getModifydate() == null) wkWiptasksPojo.setModifydate(new Date());
        if (wkWiptasksPojo.getAssessor() == null) wkWiptasksPojo.setAssessor("");
        if (wkWiptasksPojo.getAssessorid() == null) wkWiptasksPojo.setAssessorid("");
        if (wkWiptasksPojo.getAssessdate() == null) wkWiptasksPojo.setAssessdate(new Date());
        if (wkWiptasksPojo.getCustom1() == null) wkWiptasksPojo.setCustom1("");
        if (wkWiptasksPojo.getCustom2() == null) wkWiptasksPojo.setCustom2("");
        if (wkWiptasksPojo.getCustom3() == null) wkWiptasksPojo.setCustom3("");
        if (wkWiptasksPojo.getCustom4() == null) wkWiptasksPojo.setCustom4("");
        if (wkWiptasksPojo.getCustom5() == null) wkWiptasksPojo.setCustom5("");
        if (wkWiptasksPojo.getCustom6() == null) wkWiptasksPojo.setCustom6("");
        if (wkWiptasksPojo.getCustom7() == null) wkWiptasksPojo.setCustom7("");
        if (wkWiptasksPojo.getCustom8() == null) wkWiptasksPojo.setCustom8("");
        if (wkWiptasksPojo.getCustom9() == null) wkWiptasksPojo.setCustom9("");
        if (wkWiptasksPojo.getCustom10() == null) wkWiptasksPojo.setCustom10("");
        if (wkWiptasksPojo.getTenantid() == null) wkWiptasksPojo.setTenantid("");
        if (wkWiptasksPojo.getTenantname() == null) wkWiptasksPojo.setTenantname("");
        if (wkWiptasksPojo.getRevision() == null) wkWiptasksPojo.setRevision(0);
    }

    @Override
    public WkWiptasksPojo disannul(List<WkWiptasksitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            WkWiptasksitemPojo Pojo = lst.get(i);
            WkWiptasksitemPojo dbPojo = this.wkWiptasksitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getItemname() + "已关闭,禁止作废操作");
                    }
                    WkWiptasksitemEntity entity = new WkWiptasksitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setTenantid(loginUser.getTenantid());
                    this.wkWiptasksitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getItemname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.wkWiptasksMapper.updateDisannulCount(Pid, tid);
            //主表更改
            WkWiptasksEntity wkWiptasksEntity = new WkWiptasksEntity();
            wkWiptasksEntity.setId(Pid);
            wkWiptasksEntity.setLister(loginUser.getRealname());
            wkWiptasksEntity.setListerid(loginUser.getUserid());
            wkWiptasksEntity.setModifydate(new Date());
            wkWiptasksEntity.setTenantid(loginUser.getTenantid());
            this.wkWiptasksMapper.update(wkWiptasksEntity);
            //返回Bill实例
            return this.getBillEntity(wkWiptasksEntity.getId(), wkWiptasksEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    public WkWiptasksPojo closed(List<WkWiptasksitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            WkWiptasksitemPojo Pojo = lst.get(i);
            WkWiptasksitemPojo dbPojo = this.wkWiptasksitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getItemname() + "已作废,禁止操作");
                    }
                    WkWiptasksitemEntity entity = new WkWiptasksitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.wkWiptasksitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getItemname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.wkWiptasksMapper.updateFinishCount(Pid, tid);
            //主表更改
            WkWiptasksEntity wkWiptasksEntity = new WkWiptasksEntity();
            wkWiptasksEntity.setId(Pid);
            wkWiptasksEntity.setLister(loginUser.getRealname());
            wkWiptasksEntity.setListerid(loginUser.getUserid());
            wkWiptasksEntity.setModifydate(new Date());
            wkWiptasksEntity.setTenantid(loginUser.getTenantid());
            this.wkWiptasksMapper.update(wkWiptasksEntity);
            //返回Bill实例
            return this.getBillEntity(wkWiptasksEntity.getId(), wkWiptasksEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }
}
