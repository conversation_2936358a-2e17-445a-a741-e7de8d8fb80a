package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkGrouplossrateEntity;
import inks.service.std.manu.domain.pojo.WkGrouplossratePojo;
import inks.service.std.manu.mapper.WkGrouplossrateMapper;
import inks.service.std.manu.service.WkGrouplossrateService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 车间预损率(WkGrouplossrate)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-16 10:56:19
 */
@Service("wkGrouplossrateService")
public class WkGrouplossrateServiceImpl implements WkGrouplossrateService {
    @Resource
    private WkGrouplossrateMapper wkGrouplossrateMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkGrouplossratePojo getEntity(String key, String tid) {
        return this.wkGrouplossrateMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkGrouplossratePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkGrouplossratePojo> lst = wkGrouplossrateMapper.getPageList(queryParam);
            PageInfo<WkGrouplossratePojo> pageInfo = new PageInfo<WkGrouplossratePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkGrouplossratePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkGrouplossratePojo insert(WkGrouplossratePojo wkGrouplossratePojo) {
        //初始化NULL字段
        if (wkGrouplossratePojo.getGroupid() == null) wkGrouplossratePojo.setGroupid("");
        if (wkGrouplossratePojo.getRownum() == null) wkGrouplossratePojo.setRownum(0);
        if (wkGrouplossratePojo.getBetterrate() == null) wkGrouplossratePojo.setBetterrate(0D);
        if (wkGrouplossratePojo.getTargetrate() == null) wkGrouplossratePojo.setTargetrate(0D);
        if (wkGrouplossratePojo.getAlertsrate() == null) wkGrouplossratePojo.setAlertsrate(0D);
        if (wkGrouplossratePojo.getMaxrate() == null) wkGrouplossratePojo.setMaxrate(0D);
        if (wkGrouplossratePojo.getRemark() == null) wkGrouplossratePojo.setRemark("");
        if (wkGrouplossratePojo.getCreateby() == null) wkGrouplossratePojo.setCreateby("");
        if (wkGrouplossratePojo.getCreatebyid() == null) wkGrouplossratePojo.setCreatebyid("");
        if (wkGrouplossratePojo.getCreatedate() == null) wkGrouplossratePojo.setCreatedate(new Date());
        if (wkGrouplossratePojo.getLister() == null) wkGrouplossratePojo.setLister("");
        if (wkGrouplossratePojo.getListerid() == null) wkGrouplossratePojo.setListerid("");
        if (wkGrouplossratePojo.getModifydate() == null) wkGrouplossratePojo.setModifydate(new Date());
        if (wkGrouplossratePojo.getCustom1() == null) wkGrouplossratePojo.setCustom1("");
        if (wkGrouplossratePojo.getCustom2() == null) wkGrouplossratePojo.setCustom2("");
        if (wkGrouplossratePojo.getCustom3() == null) wkGrouplossratePojo.setCustom3("");
        if (wkGrouplossratePojo.getCustom4() == null) wkGrouplossratePojo.setCustom4("");
        if (wkGrouplossratePojo.getCustom5() == null) wkGrouplossratePojo.setCustom5("");
        if (wkGrouplossratePojo.getCustom6() == null) wkGrouplossratePojo.setCustom6("");
        if (wkGrouplossratePojo.getCustom7() == null) wkGrouplossratePojo.setCustom7("");
        if (wkGrouplossratePojo.getCustom8() == null) wkGrouplossratePojo.setCustom8("");
        if (wkGrouplossratePojo.getCustom9() == null) wkGrouplossratePojo.setCustom9("");
        if (wkGrouplossratePojo.getCustom10() == null) wkGrouplossratePojo.setCustom10("");
        if (wkGrouplossratePojo.getTenantid() == null) wkGrouplossratePojo.setTenantid("");
        if (wkGrouplossratePojo.getTenantname() == null) wkGrouplossratePojo.setTenantname("");
        if (wkGrouplossratePojo.getRevision() == null) wkGrouplossratePojo.setRevision(0);
        WkGrouplossrateEntity wkGrouplossrateEntity = new WkGrouplossrateEntity();
        BeanUtils.copyProperties(wkGrouplossratePojo, wkGrouplossrateEntity);
        //生成雪花id
        wkGrouplossrateEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkGrouplossrateEntity.setRevision(1);  //乐观锁
        this.wkGrouplossrateMapper.insert(wkGrouplossrateEntity);
        return this.getEntity(wkGrouplossrateEntity.getId(), wkGrouplossrateEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkGrouplossratePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkGrouplossratePojo update(WkGrouplossratePojo wkGrouplossratePojo) {
        WkGrouplossrateEntity wkGrouplossrateEntity = new WkGrouplossrateEntity();
        BeanUtils.copyProperties(wkGrouplossratePojo, wkGrouplossrateEntity);
        this.wkGrouplossrateMapper.update(wkGrouplossrateEntity);
        return this.getEntity(wkGrouplossrateEntity.getId(), wkGrouplossrateEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkGrouplossrateMapper.delete(key, tid);
    }


}
