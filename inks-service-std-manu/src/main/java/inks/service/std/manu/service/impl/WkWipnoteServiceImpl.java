package inks.service.std.manu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.WkWipnoteEntity;
import inks.service.std.manu.domain.WkWipnoteitemEntity;
import inks.service.std.manu.domain.WkWipqtyEntity;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.mapper.*;
import inks.service.std.manu.service.WkWipnoteService;
import inks.service.std.manu.service.WkWipnoteitemService;
import inks.service.std.manu.service.WkWipnotemergeService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * WIP记录(WkWipnote)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-23 13:18:06
 */
@Service("wkWipnoteService")
public class WkWipnoteServiceImpl implements WkWipnoteService {
    @Resource
    private WkWipnoteMapper wkWipnoteMapper;

    @Resource
    private WkWipnoteitemMapper wkWipnoteitemMapper;

    @Resource
    private WkWipqtyMapper wkWipqtyMapper;
    @Resource
    private RedisService redisService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkWipnoteitemService wkWipnoteitemService;
    @Resource
    private WkWorksheetitemMapper wkWorksheetitemMapper;
    @Resource
    private WkWorksheetMapper wkWorksheetMapper;
    @Resource
    private WkWipnotemergeService wkWipnotemergeService;
    @Resource
    private WkWipnoteService wkWipnoteService;
    @Resource
    private WkProgroupitemMapper wkProgroupitemMapper;
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private WkWipnotemergeMapper wkWipnotemergeMapper;
    @Resource
    private manu_SyncMapper manuSyncMapper;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private WkProccycleMapper wkProccycleMapper;
    @Resource
    private TokenService tokenService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipnotePojo getEntity(String key, String tid) {
        return this.wkWipnoteMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipnoteitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipnoteitemdetailPojo> lst = wkWipnoteMapper.getPageList(queryParam);
            String tid = queryParam.getTenantid();
            lst.forEach(item -> {
                //根据machitemid反查销售订单的摘要machsummary,和workitemid反查生产加工单worksummary的摘要
                String machitemid = item.getMachitemid();
                String workitemid = item.getWorkitemid();
                if (isNotBlank(machitemid)) {
                    String machsummary = wkWipnoteMapper.getSummaryByMachitemid(machitemid, tid);
                    item.setMachsummary(machsummary);
                }
                if (isNotBlank(workitemid)) {
                    String worksummary = wkWipnoteMapper.getSummaryByWorkitemid(workitemid, tid);
                    item.setWorksummary(worksummary);
                }
            });
            PageInfo<WkWipnoteitemdetailPojo> pageInfo = new PageInfo<WkWipnoteitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<WkWipnoteitemdetailPojo> getOnlinePageListBySummary(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipnoteitemdetailPojo> lst = wkWipnoteMapper.getOnlinePageListBySummary(queryParam);
            return new PageInfo<>(lst);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipnotePojo getBillEntity(String key, String tid) {
        try {
            // 读取主表
            WkWipnotePojo wkWipnotePojo = wkWipnoteMapper.getEntity(key, tid);
            // 读取子表
            List<WkWipnoteitemPojo> itemList = wkWipnoteitemMapper.getList(wkWipnotePojo.getId(), wkWipnotePojo.getTenantid());
            // 循环设置每个item子表,追加前置/全程的左右容差
            itemList.forEach(item -> {
                String wpid = item.getWpid();
                // 0 全程 1 前置 2 后置(暂时不用后置)
                WkProccyclePojo preProccycle = wkProccycleMapper.getEntityByWpidAndType(wpid, 1, tid);
                WkProccyclePojo allProccycle = wkProccycleMapper.getEntityByWpidAndType(wpid, 0, tid);

                item.setPrelefttole(preProccycle != null ? preProccycle.getLefttole() : 0);
                item.setPrerighttole(preProccycle != null ? preProccycle.getRighttole() : 0);

                item.setAlllefttole(allProccycle != null ? allProccycle.getLefttole() : 0);
                item.setAllrighttole(allProccycle != null ? allProccycle.getRighttole() : 0);
            });

            wkWipnotePojo.setItem(itemList);
            return wkWipnotePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public WkWipnotePojo getBillEntityByWorkUid(String workuid, String tid) {
        try {
            //读取主表
            WkWipnotePojo wkWipnotePojo = this.wkWipnoteMapper.getEntityByWorkUid(workuid, tid);
            //读取子表
            wkWipnotePojo.setItem(wkWipnoteitemMapper.getList(wkWipnotePojo.getId(), tid));
            return wkWipnotePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipnotePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipnotePojo> lst = wkWipnoteMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkWipnoteitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkWipnotePojo> pageInfo = new PageInfo<WkWipnotePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipnotePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipnotePojo> lst = wkWipnoteMapper.getPageTh(queryParam);
            PageInfo<WkWipnotePojo> pageInfo = new PageInfo<WkWipnotePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWipnotePojo 实例对象
     * @return 实例对象
     */
    @Override
//    @Transactional(isolation = Isolation.READ_COMMITTED)
    public WkWipnotePojo insert(WkWipnotePojo wkWipnotePojo, Integer inFirst) {
        String tid = wkWipnotePojo.getTenantid();
//初始化NULL字段
        if (wkWipnotePojo.getRefno() == null) wkWipnotePojo.setRefno("");
        if (wkWipnotePojo.getBilldate() == null) wkWipnotePojo.setBilldate(new Date());
        if (wkWipnotePojo.getWorktype() == null) wkWipnotePojo.setWorktype("");
        if (wkWipnotePojo.getWorkshopid() == null) wkWipnotePojo.setWorkshopid("");
        if (wkWipnotePojo.getWorkshop() == null) wkWipnotePojo.setWorkshop("");
        if (wkWipnotePojo.getGoodsid() == null) wkWipnotePojo.setGoodsid("");
        if (wkWipnotePojo.getPlandate() == null) wkWipnotePojo.setPlandate(new Date());
        if (wkWipnotePojo.getQuantity() == null) wkWipnotePojo.setQuantity(0D);
        if (wkWipnotePojo.getWkpcsqty() == null) wkWipnotePojo.setWkpcsqty(0D);
        if (wkWipnotePojo.getWksecqty() == null) wkWipnotePojo.setWksecqty(0D);
        if (wkWipnotePojo.getMrbpcsqty() == null) wkWipnotePojo.setMrbpcsqty(0D);
        if (wkWipnotePojo.getMrbsecqty() == null) wkWipnotePojo.setMrbsecqty(0D);
        if (wkWipnotePojo.getSupplement() == null) wkWipnotePojo.setSupplement(0);
        if (wkWipnotePojo.getCreateby() == null) wkWipnotePojo.setCreateby("");
        if (wkWipnotePojo.getCreatebyid() == null) wkWipnotePojo.setCreatebyid("");
        if (wkWipnotePojo.getCreatedate() == null) wkWipnotePojo.setCreatedate(new Date());
        if (wkWipnotePojo.getLister() == null) wkWipnotePojo.setLister("");
        if (wkWipnotePojo.getListerid() == null) wkWipnotePojo.setListerid("");
        if (wkWipnotePojo.getModifydate() == null) wkWipnotePojo.setModifydate(new Date());
        if (wkWipnotePojo.getStatecode() == null) wkWipnotePojo.setStatecode("");
        if (wkWipnotePojo.getStatedate() == null) wkWipnotePojo.setStatedate(new Date());
        if (wkWipnotePojo.getWkwpid() == null) wkWipnotePojo.setWkwpid("");
        if (wkWipnotePojo.getWkwpcode() == null) wkWipnotePojo.setWkwpcode("");
        if (wkWipnotePojo.getWkwpname() == null) wkWipnotePojo.setWkwpname("");
        if (wkWipnotePojo.getWkrownum() == null) wkWipnotePojo.setWkrownum(0);
        if (wkWipnotePojo.getCustomer() == null) wkWipnotePojo.setCustomer("");
        if (wkWipnotePojo.getCustpo() == null) wkWipnotePojo.setCustpo("");
        if (wkWipnotePojo.getMachuid() == null) wkWipnotePojo.setMachuid("");
        if (wkWipnotePojo.getMachitemid() == null) wkWipnotePojo.setMachitemid("");
        if (wkWipnotePojo.getMachgroupid() == null) wkWipnotePojo.setMachgroupid("");
        if (wkWipnotePojo.getMainplanuid() == null) wkWipnotePojo.setMainplanuid("");
        if (wkWipnotePojo.getMainplanitemid() == null) wkWipnotePojo.setMainplanitemid("");
        if (wkWipnotePojo.getWorkuid() == null) wkWipnotePojo.setWorkuid("");
        if (wkWipnotePojo.getWorkrefno() == null) wkWipnotePojo.setWorkrefno("");
        if (wkWipnotePojo.getWorkrownum() == null) wkWipnotePojo.setWorkrownum(0);
        if (wkWipnotePojo.getWorkitemid() == null) wkWipnotePojo.setWorkitemid("");
        if (wkWipnotePojo.getSubstwpid() == null) wkWipnotePojo.setSubstwpid("");
        if (wkWipnotePojo.getSubstwpcode() == null) wkWipnotePojo.setSubstwpcode("");
        if (wkWipnotePojo.getSubstwpname() == null) wkWipnotePojo.setSubstwpname("");
        if (wkWipnotePojo.getSubendwpid() == null) wkWipnotePojo.setSubendwpid("");
        if (wkWipnotePojo.getSubendwpcode() == null) wkWipnotePojo.setSubendwpcode("");
        if (wkWipnotePojo.getSubendwpname() == null) wkWipnotePojo.setSubendwpname("");
        if (wkWipnotePojo.getSubuid() == null) wkWipnotePojo.setSubuid("");
        if (wkWipnotePojo.getWorkdate() == null) wkWipnotePojo.setWorkdate(new Date());
        if (wkWipnotePojo.getCompwpid() == null) wkWipnotePojo.setCompwpid("");
        if (wkWipnotePojo.getCompwpcode() == null) wkWipnotePojo.setCompwpcode("");
        if (wkWipnotePojo.getCompwpname() == null) wkWipnotePojo.setCompwpname("");
        if (wkWipnotePojo.getComppcsqty() == null) wkWipnotePojo.setComppcsqty(0D);
        if (wkWipnotePojo.getWipgroupid() == null) wkWipnotePojo.setWipgroupid("");
        if (wkWipnotePojo.getSummary() == null) wkWipnotePojo.setSummary("");
        if (wkWipnotePojo.getAttributejson() == null) wkWipnotePojo.setAttributejson("");
        if (wkWipnotePojo.getAttributestr() == null) wkWipnotePojo.setAttributestr("");
        if (wkWipnotePojo.getMatcode() == null) wkWipnotePojo.setMatcode("");
        if (wkWipnotePojo.getMatused() == null) wkWipnotePojo.setMatused(0);
        if (wkWipnotePojo.getWkspecjson() == null) wkWipnotePojo.setWkspecjson("");
        if (wkWipnotePojo.getItemcount() == null) wkWipnotePojo.setItemcount(0);
        if (wkWipnotePojo.getFinishcount() == null) wkWipnotePojo.setFinishcount(0);
        if (wkWipnotePojo.getPrintcount() == null) wkWipnotePojo.setPrintcount(0);
        if (wkWipnotePojo.getColorlevel() == null) wkWipnotePojo.setColorlevel("");
        if (wkWipnotePojo.getSizex() == null) wkWipnotePojo.setSizex(0D);
        if (wkWipnotePojo.getSizey() == null) wkWipnotePojo.setSizey(0D);
        if (wkWipnotePojo.getSizez() == null) wkWipnotePojo.setSizez(0D);
        if (wkWipnotePojo.getClosed() == null) wkWipnotePojo.setClosed(0);
        if (wkWipnotePojo.getDisannullisterid() == null) wkWipnotePojo.setDisannullisterid("");
        if (wkWipnotePojo.getDisannullister() == null) wkWipnotePojo.setDisannullister("");
        if (wkWipnotePojo.getDisannuldate() == null) wkWipnotePojo.setDisannuldate(new Date());
        if (wkWipnotePojo.getDisannulmark() == null) wkWipnotePojo.setDisannulmark(0);
        if (wkWipnotePojo.getMergemark() == null) wkWipnotePojo.setMergemark(0);
        if (wkWipnotePojo.getSourcetype() == null) wkWipnotePojo.setSourcetype(0);
//        if(wkWipnotePojo.getItemjson()==null) wkWipnotePojo.setItemjson("");
        if (wkWipnotePojo.getIsolation() == null) wkWipnotePojo.setIsolation(0);
        if (wkWipnotePojo.getExponent() == null) wkWipnotePojo.setExponent(0);
        if (wkWipnotePojo.getJobpcsqty() == null) wkWipnotePojo.setJobpcsqty(0D);
        if (wkWipnotePojo.getJobsecqty() == null) wkWipnotePojo.setJobsecqty(0D);
        if (wkWipnotePojo.getCustom1() == null) wkWipnotePojo.setCustom1("");
        if (wkWipnotePojo.getCustom2() == null) wkWipnotePojo.setCustom2("");
        if (wkWipnotePojo.getCustom3() == null) wkWipnotePojo.setCustom3("");
        if (wkWipnotePojo.getCustom4() == null) wkWipnotePojo.setCustom4("");
        if (wkWipnotePojo.getCustom5() == null) wkWipnotePojo.setCustom5("");
        if (wkWipnotePojo.getCustom6() == null) wkWipnotePojo.setCustom6("");
        if (wkWipnotePojo.getCustom7() == null) wkWipnotePojo.setCustom7("");
        if (wkWipnotePojo.getCustom8() == null) wkWipnotePojo.setCustom8("");
        if (wkWipnotePojo.getCustom9() == null) wkWipnotePojo.setCustom9("");
        if (wkWipnotePojo.getCustom10() == null) wkWipnotePojo.setCustom10("");
        if (wkWipnotePojo.getTenantname() == null) wkWipnotePojo.setTenantname("");
        if (wkWipnotePojo.getRevision() == null) wkWipnotePojo.setRevision(0);
        if (tid == null) wkWipnotePojo.setTenantid("");
        //  0其他，1销售订单，2加工单
        wkWipnotePojo.setSourcetype(0);
        if (wkWipnotePojo.getMachitemid() != null && !"".equals(wkWipnotePojo.getMachitemid())) {
            wkWipnotePojo.setSourcetype(1);
        } else if (wkWipnotePojo.getWorkitemid() != null && !"".equals(wkWipnotePojo.getWorkitemid())) {
            wkWipnotePojo.setSourcetype(2);
        }

        // 临时加入SPU转入Size Eric 20221008
        if (wkWipnotePojo.getAttributejson() != null && !"".equals(wkWipnotePojo.getAttributejson())) {
            this.Spu2Size(wkWipnotePojo);
        }

        //Item子表处理
        List<WkWipnoteitemPojo> lst = wkWipnotePojo.getItem();

        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkWipnoteEntity wkWipnoteEntity = new WkWipnoteEntity();
        if (inFirst == 1 && !lst.isEmpty()) {
            wkWipnotePojo.setWkwpid(lst.get(0).getWpid());
            wkWipnotePojo.setWkwpcode(lst.get(0).getWpcode());
            wkWipnotePojo.setWkwpname(lst.get(0).getWpname());
            wkWipnotePojo.setWkrownum(1);
        }
        BeanUtils.copyProperties(wkWipnotePojo, wkWipnoteEntity);
        //设置id和新建日期
        wkWipnoteEntity.setId(id);
        wkWipnoteEntity.setRevision(1);  //乐观锁
        // 读取wip.attributestr公式
        Map<String, Object> tencfg = this.redisService.getCacheObject("tenant_config:" + tid);
        // 检查tencfg是否包含 "system.wip.attributestr" 键
        if (tencfg != null && tencfg.containsKey("system.bill.attributestr")) {
            String attrstrExpr = tencfg.get("system.bill.attributestr").toString();
            // 然后再计算主表的 AttributeStr（如果 attrstrExpr 不为空）
            if (StringUtils.isNotBlank(attrstrExpr) && isNotBlank(wkWipnoteEntity.getAttributejson())) {
                wkWipnoteEntity.setAttributestr(inks.common.core.utils.bean.BeanUtils.calculateAttrStr(wkWipnoteEntity.getAttributejson(), attrstrExpr));
            }
        }
        // 在需要的地方设置事务隔离级别为READ_COMMITTED
        transactionTemplate.setIsolationLevel(Isolation.READ_COMMITTED.value());
        transactionTemplate.execute((status) -> {
            //插入主表
            this.wkWipnoteMapper.insert(wkWipnoteEntity);

            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    //初始化item的NULL
                    WkWipnoteitemPojo itemPojo = this.wkWipnoteitemService.clearNull(lst.get(i));
                    WkWipnoteitemEntity wkWipnoteitemEntity = new WkWipnoteitemEntity();
                    BeanUtils.copyProperties(itemPojo, wkWipnoteitemEntity);
                    //设置id和Pid
                    wkWipnoteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    if (i == 0) {
                        lst.get(0).setId(wkWipnoteitemEntity.getId());
                    }
                    wkWipnoteitemEntity.setPid(id);
                    wkWipnoteitemEntity.setTenantid(tid);
                    wkWipnoteitemEntity.setRevision(1);  //乐观锁
                    // 首工序自动
                    if (inFirst == 1 && i == 0) {
                        wkWipnoteitemEntity.setInpcsqty(wkWipnotePojo.getWkpcsqty());
                        wkWipnoteitemEntity.setInsecqty(wkWipnotePojo.getWksecqty());
                        wkWipnoteitemEntity.setStartdate(new Date());
                    }
                    //插入子表
                    this.wkWipnoteitemMapper.insert(wkWipnoteitemEntity);

                }
            }

            // 首工序自动
            if (inFirst == 1 && !lst.isEmpty()) {
                inFirstQty(wkWipnotePojo, lst.get(0));
                // 加工单
                if (isNotBlank(wkWipnotePojo.getWorkitemid())) {
                    this.wkWipqtyMapper.updateWorkWkwp(wkWipnotePojo);
                    this.wkWipqtyMapper.updateWorkBillWkwp(wkWipnotePojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateWorkWkwpByMerge(wkWipnotePojo);
                        this.wkWipqtyMapper.updateWorkBillWkwpByMerge(wkWipnotePojo);
                    }
                }
                //更新bus_machiningitem表
                if (isNotBlank(wkWipnotePojo.getMachitemid())) {
                    this.wkWipqtyMapper.updateMachBillWkwp(wkWipnotePojo);
                    this.wkWipqtyMapper.updateMachWkwp(wkWipnotePojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        if (wkWipnotePojo.getSourcetype() == 2) {
                            System.out.println("------合并单更新订单当前工序-----");
                            this.wkWipqtyMapper.updateMachWkwpByMerge(wkWipnotePojo);
                            this.wkWipqtyMapper.updateMachBillWkwpByMerge(wkWipnotePojo);
                        }
                    }
                }
                // 同步Wk_MainPlan生产主计划主子表工序状态
                if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                    this.wkWipqtyMapper.updateMainPlanWkwp(wkWipnotePojo);
                    this.wkWipqtyMapper.updateMainPlanBillWkwp(wkWipnotePojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateMainPlanWkwpByMerge(wkWipnotePojo);
                        this.wkWipqtyMapper.updateMainPlanBillWkwpByMerge(wkWipnotePojo);
                    }
                }
            }


            // 更新订单子表转Wip标识 主表已转Wip行数
            if (!"".equals(wkWipnotePojo.getMachitemid())) {
                this.wkWipnoteMapper.updateMachWipUsed(wkWipnotePojo);
                this.wkWipnoteMapper.updateMachWipCount(wkWipnotePojo.getMachitemid(), tid);
            }

            // 更新加工单转Wip
            if (!"".equals(wkWipnotePojo.getWorkitemid())) {
                this.wkWipnoteMapper.updateWorkWipUsed(wkWipnotePojo);
                // 同步Wip.WorkRefNo=加工单的Refno;  WorkRowNum=加工单item.RowNum;
                this.wkWipnoteMapper.updateWorkRefNoWorkRowNum(id, wkWipnotePojo.getWorkitemid(), tid);
            }

            return Boolean.TRUE;
        });
        //返回Bill实例
        return this.getBillEntity(wkWipnoteEntity.getId(), wkWipnoteEntity.getTenantid());

    }

    private void inFirstQty(WkWipnotePojo wkWipnotePojo, WkWipnoteitemPojo wkWipnoteitemPojo) {
        WkWipqtyPojo wkWipqtyPojo = new WkWipqtyPojo();
        wkWipqtyPojo.setRefno("wq" + DateUtils.dateTimeNow());
        wkWipqtyPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipqtyPojo.setWpid(wkWipnoteitemPojo.getWpid());
        wkWipqtyPojo.setWpcode(wkWipnoteitemPojo.getWpcode());
        wkWipqtyPojo.setWpname(wkWipnoteitemPojo.getWpname());
        wkWipqtyPojo.setWkdate(new Date());
        wkWipqtyPojo.setDirection("入组");
        wkWipqtyPojo.setGoodsid(wkWipnotePojo.getGoodsid());
        wkWipqtyPojo.setWorker(wkWipnoteitemPojo.getItemworker());
        wkWipqtyPojo.setPcsqty(wkWipnotePojo.getWkpcsqty());
        wkWipqtyPojo.setSecqty(wkWipnotePojo.getWksecqty());
        wkWipqtyPojo.setRemark(wkWipnoteitemPojo.getRemark());
        wkWipqtyPojo.setWorkuid(wkWipnotePojo.getWorkuid());
        wkWipqtyPojo.setWipuid(wkWipnotePojo.getRefno());
        wkWipqtyPojo.setWiprownum(wkWipnoteitemPojo.getRownum());
        wkWipqtyPojo.setWipitemid(wkWipnoteitemPojo.getId());
        wkWipqtyPojo.setMrbpcsqty(0D);
        wkWipqtyPojo.setMrbsecqty(0D);
        wkWipqtyPojo.setMrbid("");
        wkWipqtyPojo.setAcceid("");
        wkWipqtyPojo.setCreatebyid(wkWipnotePojo.getCreatebyid());
        wkWipqtyPojo.setListerid(wkWipnotePojo.getListerid());
        wkWipqtyPojo.setCreateby(wkWipnotePojo.getLister());
        wkWipqtyPojo.setLister(wkWipnotePojo.getLister());
        wkWipqtyPojo.setCreatedate(new Date());
        wkWipqtyPojo.setModifydate(new Date());
        wkWipqtyPojo.setMachuid(wkWipnotePojo.getMachuid());
        wkWipqtyPojo.setMachitemid(wkWipnotePojo.getMachitemid());
        wkWipqtyPojo.setMainplanuid(wkWipnotePojo.getMainplanuid());
        wkWipqtyPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
        wkWipqtyPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
        wkWipqtyPojo.setMachgroupid(wkWipnotePojo.getMachgroupid());
        wkWipqtyPojo.setTenantid(wkWipnotePojo.getTenantid());
        wkWipqtyPojo.setRemark("新建入组");
        wkWipqtyPojo.setAttributejson(wkWipnotePojo.getAttributejson());
        wkWipqtyPojo.setSizex(wkWipnotePojo.getSizex());
        wkWipqtyPojo.setSizey(wkWipnotePojo.getSizey());
        wkWipqtyPojo.setSizez(wkWipnotePojo.getSizez());
        //初始化NULL字段
        if (wkWipqtyPojo.getRefno() == null) wkWipqtyPojo.setRefno("");
        if (wkWipqtyPojo.getWpid() == null) wkWipqtyPojo.setWpid("");
        if (wkWipqtyPojo.getWpcode() == null) wkWipqtyPojo.setWpcode("");
        if (wkWipqtyPojo.getWpname() == null) wkWipqtyPojo.setWpname("");
        if (wkWipqtyPojo.getWkdate() == null) wkWipqtyPojo.setWkdate(new Date());
        if (wkWipqtyPojo.getDirection() == null) wkWipqtyPojo.setDirection("");
        if (wkWipqtyPojo.getGoodsid() == null) wkWipqtyPojo.setGoodsid("");
        if (wkWipqtyPojo.getWorker() == null) wkWipqtyPojo.setWorker("");
        if (wkWipqtyPojo.getPcsqty() == null) wkWipqtyPojo.setPcsqty(0D);
        if (wkWipqtyPojo.getSecqty() == null) wkWipqtyPojo.setSecqty(0D);
        if (wkWipqtyPojo.getRemark() == null) wkWipqtyPojo.setRemark("");
        if (wkWipqtyPojo.getWorkuid() == null) wkWipqtyPojo.setWorkuid("");
        if (wkWipqtyPojo.getWipuid() == null) wkWipqtyPojo.setWipuid("");
        if (wkWipqtyPojo.getWiprownum() == null) wkWipqtyPojo.setWiprownum(0);
        if (wkWipqtyPojo.getWipitemid() == null) wkWipqtyPojo.setWipitemid("");
        if (wkWipqtyPojo.getMrbpcsqty() == null) wkWipqtyPojo.setMrbpcsqty(0D);
        if (wkWipqtyPojo.getMrbsecqty() == null) wkWipqtyPojo.setMrbsecqty(0D);
        if (wkWipqtyPojo.getMrbid() == null) wkWipqtyPojo.setMrbid("");
        if (wkWipqtyPojo.getAcceid() == null) wkWipqtyPojo.setAcceid("");
        if (wkWipqtyPojo.getInspector() == null) wkWipqtyPojo.setInspector("");
        if (wkWipqtyPojo.getCreateby() == null) wkWipqtyPojo.setCreateby("");
        if (wkWipqtyPojo.getCreatebyid() == null) wkWipqtyPojo.setCreatebyid("");
        if (wkWipqtyPojo.getCreatedate() == null) wkWipqtyPojo.setCreatedate(new Date());
        if (wkWipqtyPojo.getLister() == null) wkWipqtyPojo.setLister("");
        if (wkWipqtyPojo.getListerid() == null) wkWipqtyPojo.setListerid("");
        if (wkWipqtyPojo.getModifydate() == null) wkWipqtyPojo.setModifydate(new Date());
        if (wkWipqtyPojo.getMachuid() == null) wkWipqtyPojo.setMachuid("");
        if (wkWipqtyPojo.getMachitemid() == null) wkWipqtyPojo.setMachitemid("");
        if (wkWipqtyPojo.getMachgroupid() == null) wkWipqtyPojo.setMachgroupid("");
        if (wkWipqtyPojo.getMainplanuid() == null) wkWipqtyPojo.setMainplanuid("");
        if (wkWipqtyPojo.getMainplanitemid() == null) wkWipqtyPojo.setMainplanitemid("");
        if (wkWipqtyPojo.getWorkitemid() == null) wkWipqtyPojo.setWorkitemid("");
        if (wkWipqtyPojo.getAttributejson() == null) wkWipqtyPojo.setAttributejson("");
        if (wkWipqtyPojo.getSpecjson() == null) wkWipqtyPojo.setSpecjson("");
        if (wkWipqtyPojo.getCustom1() == null) wkWipqtyPojo.setCustom1("");
        if (wkWipqtyPojo.getCustom2() == null) wkWipqtyPojo.setCustom2("");
        if (wkWipqtyPojo.getCustom3() == null) wkWipqtyPojo.setCustom3("");
        if (wkWipqtyPojo.getCustom4() == null) wkWipqtyPojo.setCustom4("");
        if (wkWipqtyPojo.getCustom5() == null) wkWipqtyPojo.setCustom5("");
        if (wkWipqtyPojo.getCustom6() == null) wkWipqtyPojo.setCustom6("");
        if (wkWipqtyPojo.getCustom7() == null) wkWipqtyPojo.setCustom7("");
        if (wkWipqtyPojo.getCustom8() == null) wkWipqtyPojo.setCustom8("");
        if (wkWipqtyPojo.getCustom9() == null) wkWipqtyPojo.setCustom9("");
        if (wkWipqtyPojo.getCustom10() == null) wkWipqtyPojo.setCustom10("");
        if (wkWipqtyPojo.getTenantid() == null) wkWipqtyPojo.setTenantid("");
        if (wkWipqtyPojo.getRevision() == null) wkWipqtyPojo.setRevision(0);
        WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
        BeanUtils.copyProperties(wkWipqtyPojo, wkWipqtyEntity);
        wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipqtyEntity.setRevision(1);  //乐观锁
        this.wkWipqtyMapper.insert(wkWipqtyEntity);
    }

    // SPU的长宽厚转到Size
    private void Spu2Size(WkWipnotePojo wkWipnotePojo) {
        try {
            List<Map<String, Object>> listObjectSec = JSONArray.parseObject(wkWipnotePojo.getAttributejson(), List.class);
            // 长度参数，重填SPU
            for (Map<String, Object> mapList : listObjectSec) {
                // 长
                if (mapList.get("key").equals("spuchang")) {
                    wkWipnotePojo.setSizex(Double.parseDouble(mapList.get("value").toString()));
                }
                // 宽
                if (mapList.get("key").equals("spukuan")) {
                    wkWipnotePojo.setSizey(Double.parseDouble(mapList.get("value").toString()));
                }
                // 厚
                if (mapList.get("key").equals("spuhou")) {
                    wkWipnotePojo.setSizez(Double.parseDouble(mapList.get("value").toString()));
                }
            }
        } catch (Exception ignored) {

        }

    }

    /**
     * 修改数据
     *
     * @param wkWipnotePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipnotePojo update(WkWipnotePojo wkWipnotePojo) {
        String tid = wkWipnotePojo.getTenantid();
        //主表更改
        WkWipnoteEntity wkWipnoteEntity = new WkWipnoteEntity();
        BeanUtils.copyProperties(wkWipnotePojo, wkWipnoteEntity);
        // 读取wip.attributestr公式
        Map<String, Object> tencfg = this.redisService.getCacheObject("tenant_config:" + tid);
        // 检查tencfg是否包含"system.wip.attributestr"键
        if (tencfg != null && tencfg.containsKey("system.bill.attributestr")) {
            String attrstrExpr = tencfg.get("system.bill.attributestr").toString();
            // 然后再计算主表的 AttributeStr（如果 attrstrExpr 不为空）
            if (StringUtils.isNotBlank(attrstrExpr) && isNotBlank(wkWipnoteEntity.getAttributejson())) {
                wkWipnoteEntity.setAttributestr(inks.common.core.utils.bean.BeanUtils.calculateAttrStr(wkWipnoteEntity.getAttributejson(), attrstrExpr));
            }
        }
        this.wkWipnoteMapper.update(wkWipnoteEntity);
        if (wkWipnotePojo.getItem() != null) {
            wkWipnotePojo.setItemcount(wkWipnotePojo.getItem().size());
            //Item子表处理
            List<WkWipnoteitemPojo> lst = wkWipnotePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkWipnoteMapper.getDelItemIds(wkWipnotePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.wkWipnoteitemMapper.delete(lstDelId, tid);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (WkWipnoteitemPojo wkWipnoteitemPojo : lst) {
                    WkWipnoteitemEntity wkWipnoteitemEntity = new WkWipnoteitemEntity();
                    if ("".equals(wkWipnoteitemPojo.getId()) || wkWipnoteitemPojo.getId() == null) {
                        //初始化item的NULL
                        WkWipnoteitemPojo itemPojo = this.wkWipnoteitemService.clearNull(wkWipnoteitemPojo);
                        BeanUtils.copyProperties(itemPojo, wkWipnoteitemEntity);
                        //设置id和Pid
                        wkWipnoteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkWipnoteitemEntity.setPid(wkWipnoteEntity.getId());  // 主表 id
                        wkWipnoteitemEntity.setTenantid(tid);   // 租户id
                        wkWipnoteitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkWipnoteitemMapper.insert(wkWipnoteitemEntity);
                    } else {
                        BeanUtils.copyProperties(wkWipnoteitemPojo, wkWipnoteitemEntity);
                        wkWipnoteitemEntity.setTenantid(tid);
                        this.wkWipnoteitemMapper.update(wkWipnoteitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkWipnoteEntity.getId(), tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        WkWipnotePojo wkWipnotePojo = this.getBillEntity(key, tid);
        // 判断是不是Wip拆分单 有2个下划线的是拆分单 如：WS250103029_1_1
        // 如果是拆分单，需要返回原Wip单主表的WkPcsQty、WkSecQty、SubUid；子表该RowNum行的InPcsQty、InSecQty
        String workUid = wkWipnotePojo.getWorkuid();
        if (StringUtils.countMatches(workUid, "_") == 2) {
            this.splitBack(wkWipnotePojo);
        }
        //Item子表处理
        List<WkWipnoteitemPojo> lst = wkWipnotePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkWipnoteitemPojo wkWipnoteitemPojo : lst) {
                if (wkWipnoteitemPojo.getOutpcsqty() > 0) {
                    throw new BaseBusinessException("WIP已有过记录禁止删除" + wkWipnoteitemPojo.getWpname());
                }
                this.wkWipnoteitemMapper.delete(wkWipnoteitemPojo.getId(), tid);
                // 删除过数记录；
                this.wkWipqtyMapper.deleteByWipItemid(wkWipnoteitemPojo.getId(), tid);
            }
        }
        // 更新订单转Wip
        if (!"".equals(wkWipnotePojo.getMachitemid())) {
            this.wkWipnoteMapper.updateUnMachWipUsed(wkWipnotePojo);
        }
        // 同步更新销售订单工序
        if (wkWipnotePojo.getSourcetype() == 1) {
            this.wkWipqtyMapper.updateMachWkwpByWipNoteMerge(wkWipnotePojo);
            this.wkWipqtyMapper.updateMachBillWkwpByWipNoteMerge(wkWipnotePojo);
        }
        // 更新加工单转Wip
        if (!"".equals(wkWipnotePojo.getWorkitemid())) {
            this.wkWipnoteMapper.updateUnWorkWipUsed(wkWipnotePojo);
        }
        return this.wkWipnoteMapper.delete(key, tid);
    }


    // 如果是拆分单，需要返回原Wip单主表的WkPcsQty、WkSecQty、SubUid；子表该RowNum行的InPcsQty、InSecQty
    public void splitBack(WkWipnotePojo splitWipDB) { //key即Wk_WipNote.id
        // 获取传入的拆分单数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        //假设拆分单号为WS250103029_1_1
        String workUid = splitWipDB.getWorkuid();

        // 获取原单号 WS250103029_1
        String orgWorkUid = workUid.substring(0, workUid.lastIndexOf("_"));

        // 获取原单数据
        WkWipnotePojo orgWipDB = wkWipnoteService.getBillEntityByWorkUid(orgWorkUid, tid);
        if (orgWipDB == null) {
            throw new BaseBusinessException("原单不存在 WorkUid:" + orgWorkUid);
        }

        // 恢复原单主表数据
        orgWipDB.setWkpcsqty(orgWipDB.getWkpcsqty() + splitWipDB.getWkpcsqty());
        orgWipDB.setWksecqty(orgWipDB.getWksecqty() + splitWipDB.getWksecqty());

        // 合并子表数据
        List<WkWipnoteitemPojo> splitItems = splitWipDB.getItem();
        List<WkWipnoteitemPojo> originalItems = orgWipDB.getItem();

        for (WkWipnoteitemPojo splitItem : splitItems) {
            originalItems.stream()
                    .filter(item -> item.getRownum().equals(splitItem.getRownum()))
                    .findFirst()
                    .ifPresent(originalItem -> {
                        originalItem.setInpcsqty(originalItem.getInpcsqty() + splitItem.getInpcsqty());
                        originalItem.setInsecqty(originalItem.getInsecqty() + splitItem.getInsecqty());
                    });
        }

        // 更新原单SubUid字段
        String subUid = orgWipDB.getSubuid();
        if (StringUtils.isNotBlank(subUid)) {
            subUid = Arrays.stream(subUid.split(","))
                    .filter(uid -> !uid.equals(workUid))
                    .collect(Collectors.joining(","));
            orgWipDB.setSubuid(subUid);
        }

        // 更新原单数据
        wkWipnoteService.update(orgWipDB);
    }


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipnotePojo getEntityByWorkUid(String key, String tid) {
        return this.wkWipnoteMapper.getEntityByWorkUid(key, tid);
    }

    @Override
    public WkWipnotePojo getScmBillEntity(String key, String groupids, String tid) {
        try {
            //读取主表
            WkWipnotePojo wkWipnotePojo = this.wkWipnoteMapper.getEntity(key, tid);
            if (!groupids.contains(wkWipnotePojo.getMachgroupid())) {
                throw new BaseBusinessException("您没有权限查看该单据");
            }
            //读取子表
            wkWipnotePojo.setItem(wkWipnoteitemMapper.getList(wkWipnotePojo.getId(), wkWipnotePojo.getTenantid()));
            return wkWipnotePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 作废数据
     *
     * @param key 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkWipnotePojo disannul(String key, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        WkWipnotePojo dbPojo = this.wkWipnoteMapper.getEntity(key, loginUser.getTenantid());
        if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
            if (dbPojo.getClosed() == 1) {
                throw new RuntimeException(dbPojo.getRefno() + "已关闭,禁止作废操作");
            }
            if (dbPojo.getWkrownum() > 0) {
                throw new RuntimeException(dbPojo.getRefno() + "当前工序号大于0,禁止作废操作");
            }
        } else {
            throw new RuntimeException(dbPojo.getRefno() + "已" + strType + ",无需操作");
        }
        //主表更改
        WkWipnoteEntity busInvoiceEntity = new WkWipnoteEntity();
        busInvoiceEntity.setId(key);
        busInvoiceEntity.setDisannulmark(type);
        busInvoiceEntity.setLister(loginUser.getRealname());
        busInvoiceEntity.setListerid(loginUser.getUserid());
        busInvoiceEntity.setModifydate(new Date());
        busInvoiceEntity.setTenantid(loginUser.getTenantid());
        this.wkWipnoteMapper.update(busInvoiceEntity);
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());

    }

    /**
     * 作废数据
     *
     * @param ids wip需要中止的idList
     * @return 实例对象
     */
    @Override
    @Transactional
    public String closed(List<String> ids, Integer type, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        // 构建要中止的WkWipnoteidSet和WkWorksheetidSet, WkWorksheetItemidSet
        HashSet<String> wkWipnoteidSet = new HashSet<>();
        HashSet<String> worksheetidSet = new HashSet<>();
        HashSet<String> worksheetItemidSet = new HashSet<>();
        // 去重的goodsid Set集合
        HashSet<String> goodsidSet = new HashSet<>();
        String strType = type == 1 ? "关闭" : "开启";
        for (String id : ids) {
            WkWipnotePojo dbPojo = this.wkWipnoteMapper.getEntity(id, tid);
            if (dbPojo != null) {
                goodsidSet.add(dbPojo.getGoodsid());
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(dbPojo.getRefno() + "已作废,禁止操作");
                    }
                } else {
                    throw new RuntimeException(dbPojo.getRefno() + "已" + strType + ",无需操作");
                }
                // 正常的Wipnoteid
                wkWipnoteidSet.add(id);
                //同步中止掉对应的WkWorksheetItem
                if (isNotBlank(dbPojo.getWorkitemid())) {
                    worksheetItemidSet.add(dbPojo.getWorkitemid());
                    String pid = this.wkWorksheetitemMapper.getPid(dbPojo.getWorkitemid(), tid);
                    worksheetidSet.add(pid);
                }
                // 同步账面库存的生产数

            } else {
                throw new RuntimeException("未找到对应的WIP");
            }
        }

        // wip主表批量改为中止状态
        Date now = new Date();
        int wipnote = this.wkWipnoteMapper.closedids(wkWipnoteidSet, type, loginUser.getRealname(), loginUser.getUserid(), now, tid);
        System.out.println("===================wipnoteCount = " + wipnote);
        // 判断worksheetidSet,WkWorksheetItemidSet不为空时，同步关闭掉对应的WkWorksheet生产加工单
        if (CollectionUtils.isNotEmpty(worksheetItemidSet)) {
            int sheetitem = this.wkWorksheetitemMapper.closedids(worksheetItemidSet, type, tid);
            int sheet = this.wkWorksheetMapper.updateFinishCountByIds(worksheetidSet, loginUser.getRealname(), loginUser.getUserid(), now, tid);
            System.out.println("===================sheetitemCount = " + sheetitem);
            System.out.println("===================sheetCount = " + sheet);
        }

        // 同步货品数量 SQL替代MQ
        goodsidSet.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkWsRemQty(goodsid, tid);
        });
        return "批量中止成功";
    }

    /**
     * @return R<WkWorksheetitemdetailPojo>
     * @Description 合并销售订单子项物料转为WipNote (需传入相同销售订单子项物料)
     * <AUTHOR>
     * @param[1] json  List<WkWipnotePojo>
     * @param[2] inFirst 首工序入组
     * @param[3] progroupid 生产制程id
     * @time 2023/6/13 14:11
     */
    @Override
    public WkWipnotePojo mergeItem(List<WkWipnotePojo> lstitem, int inFirst, String progroupid, LoginUser loginUser) {
        String tenantid = loginUser.getTenantid();
        // 检查销售订单子项物料是否已合并过
        for (WkWipnotePojo wkWipnotePojo : lstitem) {
            WkWipnotemergePojo entity = wkWipnotemergeMapper.getEntityByMachItemid(wkWipnotePojo.getMachitemid(), tenantid);// 只返id
            if (entity != null) throw new RuntimeException(wkWipnotePojo.getMachuid() + ":物料已合并过!");
        }
        // 0.生产制程ItemList转为WipNoteItemList
        List<WkProgroupitemPojo> progroupitemList = wkProgroupitemMapper.getList(progroupid, tenantid);
        List<WkWipnoteitemPojo> wipnoteitemList = new ArrayList<>();
        for (WkProgroupitemPojo wkProgroupitemPojo : progroupitemList) {
            WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
            BeanUtils.copyProperties(wkProgroupitemPojo, wkWipnoteitemPojo);
            wipnoteitemList.add(wkWipnoteitemPojo);
        }

        // 1.传入几条销售订单子表物料,就生成几条WipNoteMerge
        StringBuilder mergeIds = new StringBuilder(); // 用于拼接生成的WipNoteMerge的id
        for (WkWipnotePojo wipnotePojo : lstitem) {
            //生成WipNoteMerge单
            WkWipnotemergePojo wkWipnotemergePojo = new WkWipnotemergePojo();
            BeanUtils.copyProperties(wipnotePojo, wkWipnotemergePojo);
            wkWipnotemergePojo.setTenantid(tenantid);
            WkWipnotemergePojo insertmerge = wkWipnotemergeService.insert(wkWipnotemergePojo);
            if (mergeIds.length() > 0) {
                mergeIds.append(",");
            }
            mergeIds.append("'").append(insertmerge.getId()).append("'");
        }

        // 2.合并相同goodsid和attributejson的货品明细项
        for (int i = 0; i < lstitem.size(); i++) {
            // 获取当前项的goodsid,attributejson(SPU),quantity
            String goodsid = lstitem.get(i).getGoodsid();
            String attributejson = lstitem.get(i).getAttributejson();
            Double quantity = lstitem.get(i).getQuantity();
            Double wkpcsqty = lstitem.get(i).getWkpcsqty();
            // 在当前项之后的所有项中查找是否有相同goodsid的项
            for (int j = i + 1; j < lstitem.size(); j++) {
                if (lstitem.get(j).getGoodsid().equals(goodsid) && lstitem.get(j).getAttributejson().equals(attributejson)) {
                    // 如果存在相同goodsid,attributejson的项，则累加数量和总投数到当前项中
                    quantity += lstitem.get(j).getQuantity();
                    wkpcsqty += lstitem.get(j).getWkpcsqty();
                    // 删除相同项
                    lstitem.remove(j);
                    j--;
                } else {
                    throw new RuntimeException("传入货品信息不一致");//所以lstitem最终只剩下一条数据
                }
            }
            // 更新当前项的数量和总投数
            lstitem.get(i).setQuantity(quantity);
            lstitem.get(i).setWkpcsqty(wkpcsqty);
        }

        // 3.合并后的WkWipnotePojo插入到WkWipnote表(item加入生产制程)
        WkWipnotePojo wipnotePojo = lstitem.get(0);
        wipnotePojo.setItem(wipnoteitemList);

        //生成单据编码
        R r = systemFeignService.getBillCode("D05M05B1", loginUser.getToken());
        if (r.getCode() == 200)
            wipnotePojo.setRefno(r.getData().toString());
        else {
            throw new RuntimeException("单据编码读取出错" + r);
        }
        wipnotePojo.setCreateby(loginUser.getRealname());   // 创建者
        wipnotePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        wipnotePojo.setCreatedate(new Date());   // 创建时间
        wipnotePojo.setLister(loginUser.getRealname());   // 制表
        wipnotePojo.setListerid(loginUser.getUserid());    // 制表id
        wipnotePojo.setModifydate(new Date());   //修改时间
        wipnotePojo.setTenantid(tenantid);   //租户id
        wipnotePojo.setItemcount(wipnotePojo.getItem().size());
        // 加工单号加后缀 获取加工单号BM-2023-06-0015   _1   _2
        wipnotePojo.setWorkuid(findMaxWorkuid(wipnotePojo.getWorkuid(), tenantid));
        WkWipnotePojo insertPojo = wkWipnoteService.insert(wipnotePojo, inFirst);

        // 4.更新多个WkWipnoteMerge表的Mergeid（合并后主表id）
        wkWipnotemergeService.updateMergeid(mergeIds.toString(), insertPojo.getId(), tenantid);
        // 5.同步销售订单子项物料的工序
        if (insertPojo.getSourcetype() == 1) {
            this.wkWipqtyMapper.updateMachWkwpByWipNoteMerge(insertPojo);
            this.wkWipqtyMapper.updateMachBillWkwpByWipNoteMerge(insertPojo);
        }
        // 返回生成的合并单
        return insertPojo;
    }

    //获取加工单号(获取新的WorkUid) 原加工单号格式：BM-2023-04-0073 或 BM-2023-04-0073_1
    private String findMaxWorkuid(String workuid, String tid) {
        // 在workuid后面加上下划线，再进行like查询
        String workuid_ = workuid + "_";
        String maxWorkUid = wkWipnoteMapper.getByWorkuidLike(workuid_, tid);
        //查不到,默认加后缀1 ; 查到了,累加1
        int newNumber = 1;
        if (isNotBlank(maxWorkUid)) {
            String numString = maxWorkUid.substring(maxWorkUid.lastIndexOf("_") + 1);
            newNumber = Integer.parseInt(numString) + 1;
        }
        return workuid + "_" + newNumber;
    }

    /**
     * @return R<WkWorksheetitemdetailPojo>
     * @Description 合并销售订单子项物料转为WipNote (任意传入销售订单子项物料)
     * <AUTHOR>
     * @param[1] json  List<WkWipnotePojo>
     * @param[2] inFirst 首工序入组
     * @param[3] progroupid 生产制程id
     * @time 2023/6/13 14:11
     */
//    @Override
    public void mergeItemAll(List<WkWipnotePojo> lstitem, int inFirst, String progroupid, LoginUser loginUser) {
        String tenantid = loginUser.getTenantid();
        // 0.生产制程ItemList转为WipNoteItemList
        List<WkProgroupitemPojo> progroupitemList = wkProgroupitemMapper.getList(progroupid, tenantid);
        List<WkWipnoteitemPojo> wipnoteitemList = new ArrayList<>();
        for (WkProgroupitemPojo wkProgroupitemPojo : progroupitemList) {
            WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
            BeanUtils.copyProperties(wkProgroupitemPojo, wkWipnoteitemPojo);
            wipnoteitemList.add(wkWipnoteitemPojo);
        }
        // 1.获取不能合并的销售订单物料进行普通转WIP  ;能合并的销售订单物料进行合并转WIP
        // 返回的mergedItems是能合并的销售订单物料
        List<WkWipnotePojo> mergedItems = getWkWipnotePojos(lstitem, inFirst, tenantid, wipnoteitemList, loginUser);

        // 2.传入几条销售订单子表物料,就生成几条WipNoteMerge
        for (WkWipnotePojo wipnotePojo : mergedItems) {
            //生成WipNoteMerge单
            WkWipnotemergePojo wkWipnotemergePojo = new WkWipnotemergePojo();
            BeanUtils.copyProperties(wipnotePojo, wkWipnotemergePojo);
            wkWipnotemergePojo.setTenantid(tenantid);
            wkWipnotemergeService.insert(wkWipnotemergePojo);
        }
        // 3.合并相同goodsid和attributejson的货品明细项
        for (int i = 0; i < mergedItems.size(); i++) {
            // 获取当前项的goodsid,attributejson(SPU),quantity
            String goodsid = mergedItems.get(i).getGoodsid();
            String attributejson = mergedItems.get(i).getAttributejson();
            Double quantity = mergedItems.get(i).getQuantity();
            Double wkpcsqty = mergedItems.get(i).getWkpcsqty();
            // 在当前项之后的所有项中查找是否有相同goodsid的项
            for (int j = i + 1; j < mergedItems.size(); j++) {
                if (mergedItems.get(j).getGoodsid().equals(goodsid) && mergedItems.get(j).getAttributejson().equals(attributejson)) {
                    // 如果存在相同goodsid,attributejson的项，则累加数量和总投数到当前项中
                    quantity += mergedItems.get(j).getQuantity();
                    wkpcsqty += mergedItems.get(j).getWkpcsqty();
                    // 删除相同项
                    mergedItems.remove(j);
                    j--;
                }
            }
            // 更新当前项的数量和总投数
            mergedItems.get(i).setQuantity(quantity);
            mergedItems.get(i).setWkpcsqty(wkpcsqty);
        }
        // 4.合并后的WkWipnotePojo插入到WkWipnote表(item加入生产制程)
        for (WkWipnotePojo wipnotePojo : mergedItems) {
            WkWipnotePojo wkWipnotePojo = new WkWipnotePojo();
            BeanUtils.copyProperties(wipnotePojo, wkWipnotePojo);
            wkWipnotePojo.setTenantid(tenantid);
            wipnotePojo.setItem(wipnoteitemList);
            WkWipnotePojo insertPojo = wkWipnoteService.insert(wkWipnotePojo, inFirst);
        }

    }

    //    获取不能合并的销售订单物料进行普通转WIP,返回能合并的销售订单物料List<WkWipnotePojo>
    private List<WkWipnotePojo> getWkWipnotePojos(List<WkWipnotePojo> lstitem, int inFirst, String tenantid, List<WkWipnoteitemPojo> wipnoteitemList, LoginUser loginUser) {
        List<WkWipnotePojo> unmergedItems = new ArrayList<>();
        List<WkWipnotePojo> mergedItems = new ArrayList<>();
        for (WkWipnotePojo item : lstitem) {
            boolean isMerged = false;
            for (WkWipnotePojo mergedItem : lstitem) {
                if (item != mergedItem && item.getGoodsid().equals(mergedItem.getGoodsid()) && item.getAttributejson().equals(mergedItem.getAttributejson())) {
                    isMerged = true;
                    break;
                }
            }
            if (!isMerged) {
                unmergedItems.add(item); // 不能合并的销售订单物料
            } else {
                mergedItems.add(item); // 能合并的销售订单物料
            }
        }
        // unmergedItems转为json
        for (WkWipnotePojo unmergedItem : unmergedItems) {
            String json = JSON.toJSONString(unmergedItem);
            this.create(json, inFirst);
        }
        return mergedItems;
    }


    //@ApiOperation(value = " 新增WIP记录", notes = "新增WIP记录", produces = "application/json")
    //@RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Wk_WipNote.Add")
    // 因为不能直接调用d05M05B1Controller.create(json, inFirst);所以把代码移过来
    public R<WkWipnotePojo> create(@RequestBody String json, @RequestParam(required = false) Integer infirst) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkWipnotePojo wkWipnotePojo = JSONArray.parseObject(json, WkWipnotePojo.class);
            Integer mergemark = wkWipnotePojo.getMergemark() == null ? 0 : wkWipnotePojo.getMergemark();
            if (mergemark == 1) return R.fail("被合并加工单子项禁止转WIP");
            WkWipnotePojo dbPojo = this.wkWipnoteService.getEntityByWorkUid(wkWipnotePojo.getWorkuid(), loginUser.getTenantid());
            if (dbPojo != null) {
                return R.fail(wkWipnotePojo.getWorkuid() + "单据已导入WIP,禁止重复导入");
            }
            //生成单据编码
            R r = systemFeignService.getBillCode("D05M05B1", loginUser.getToken());
            if (r.getCode() == 200)
                wkWipnotePojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            wkWipnotePojo.setCreateby(loginUser.getRealname());   // 创建者
            wkWipnotePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkWipnotePojo.setCreatedate(new Date());   // 创建时间
            wkWipnotePojo.setLister(loginUser.getRealname());   // 制表
            wkWipnotePojo.setListerid(loginUser.getUserid());    // 制表id
            wkWipnotePojo.setModifydate(new Date());   //修改时间
            wkWipnotePojo.setTenantid(loginUser.getTenantid());   //租户id
            wkWipnotePojo.setItemcount(wkWipnotePojo.getItem().size());
            if (infirst == null) infirst = 0;
            return R.ok(this.wkWipnoteService.insert(wkWipnotePojo, infirst));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @Override
    public void updatePrintcount(WkWipnotePojo billPrintPojo) {
        wkWipnoteMapper.updatePrintcount(billPrintPojo);
    }

    @Override
    public List<WkWipnoteitemPojo> getSpecPcbItemListByGoodsid(String goodsid, String tenantid) {
        return wkWipnoteMapper.getSpecPcbItemListByGoodsid(goodsid, tenantid);
    }
}
