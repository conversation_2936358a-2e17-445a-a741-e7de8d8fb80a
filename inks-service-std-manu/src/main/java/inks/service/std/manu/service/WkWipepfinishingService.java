package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipepfinishingPojo;
import inks.service.std.manu.domain.pojo.WkWipepfinishingitemdetailPojo;

/**
 * 工序收货(WkWipepfinishing)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-04 11:12:34
 */
public interface WkWipepfinishingService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipepfinishingPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipepfinishingitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipepfinishingPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipepfinishingPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipepfinishingPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipepfinishingPojo 实例对象
     * @return 实例对象
     */
    WkWipepfinishingPojo insert(WkWipepfinishingPojo wkWipepfinishingPojo);

    /**
     * 修改数据
     *
     * @param wkWipepfinishingpojo 实例对象
     * @return 实例对象
     */
    WkWipepfinishingPojo update(WkWipepfinishingPojo wkWipepfinishingpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkWipepfinishingPojo 实例对象
     * @return 实例对象
     */
    WkWipepfinishingPojo approval(WkWipepfinishingPojo wkWipepfinishingPojo);

    void updatePrintcount(WkWipepfinishingPojo billPrintPojo);
}
