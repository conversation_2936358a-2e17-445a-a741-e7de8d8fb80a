package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkProccostPojo;
import inks.service.std.manu.service.WkProccostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 委外费用类型(Wk_ScCostType)表控制层
 *
 * <AUTHOR>
 * @since 2023-11-13 15:12:07
 */
@RestController
@RequestMapping("D05M21S10")
@Api(tags = "D05M21S10:委外费用类型")
public class D05M21S10Controller extends WkSccosttypeController {
    /**
     * 服务对象
     */
    @Resource
    private WkProccostService wkProccostService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 分页查询
     *
     * @param 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询(主表工序,附表工序成本)", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getAllList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ProcCost.List")
    public R<List<WkProccostPojo>> getAllList() {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkProccostService.getAllList(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改工序成本", notes = "修改工序成本", produces = "application/json")
    @RequestMapping(value = "/updateList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ProcCost.Edit")
    public R<List<WkProccostPojo>> updateList(@RequestBody String json) {
        try {
            //json转为List<WkProcessPojo>
            List<WkProccostPojo> wkProccostList = JSONArray.parseArray(json, WkProccostPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkProccostService.updateList(wkProccostList, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
