package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSmtpartrecEntity;
import inks.service.std.manu.domain.pojo.WkSmtpartrecPojo;
import inks.service.std.manu.mapper.WkSmtpartrecMapper;
import inks.service.std.manu.service.WkSmtpartrecService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 上料记录(WkSmtpartrec)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-21 11:14:05
 */
@Service("wkSmtpartrecService")
public class WkSmtpartrecServiceImpl implements WkSmtpartrecService {
    @Resource
    private WkSmtpartrecMapper wkSmtpartrecMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSmtpartrecPojo getEntity(String key, String tid) {
        return this.wkSmtpartrecMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSmtpartrecPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSmtpartrecPojo> lst = wkSmtpartrecMapper.getPageList(queryParam);
            PageInfo<WkSmtpartrecPojo> pageInfo = new PageInfo<WkSmtpartrecPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkSmtpartrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSmtpartrecPojo insert(WkSmtpartrecPojo wkSmtpartrecPojo) {
        //初始化NULL字段
        if (wkSmtpartrecPojo.getRefno() == null) wkSmtpartrecPojo.setRefno("");
        if (wkSmtpartrecPojo.getBilltype() == null) wkSmtpartrecPojo.setBilltype("");
        if (wkSmtpartrecPojo.getBilltitle() == null) wkSmtpartrecPojo.setBilltitle("");
        if (wkSmtpartrecPojo.getBilldate() == null) wkSmtpartrecPojo.setBilldate(new Date());
        if (wkSmtpartrecPojo.getPartuid() == null) wkSmtpartrecPojo.setPartuid("");
        if (wkSmtpartrecPojo.getPartitemid() == null) wkSmtpartrecPojo.setPartitemid("");
        if (wkSmtpartrecPojo.getWorkuid() == null) wkSmtpartrecPojo.setWorkuid("");
        if (wkSmtpartrecPojo.getWorkitemid() == null) wkSmtpartrecPojo.setWorkitemid("");
        if (wkSmtpartrecPojo.getMachuid() == null) wkSmtpartrecPojo.setMachuid("");
        if (wkSmtpartrecPojo.getMachitemid() == null) wkSmtpartrecPojo.setMachitemid("");
        if (wkSmtpartrecPojo.getMachgroupid() == null) wkSmtpartrecPojo.setMachgroupid("");
        if (wkSmtpartrecPojo.getDevcode() == null) wkSmtpartrecPojo.setDevcode("");
        if (wkSmtpartrecPojo.getFeedernum() == null) wkSmtpartrecPojo.setFeedernum(0);
        if (wkSmtpartrecPojo.getFeedercode() == null) wkSmtpartrecPojo.setFeedercode("");
        if (wkSmtpartrecPojo.getStationnum() == null) wkSmtpartrecPojo.setStationnum(0);
        if (wkSmtpartrecPojo.getStationcode() == null) wkSmtpartrecPojo.setStationcode("");
        if (wkSmtpartrecPojo.getPointmark() == null) wkSmtpartrecPojo.setPointmark("");
        if (wkSmtpartrecPojo.getGoodsid() == null) wkSmtpartrecPojo.setGoodsid("");
        if (wkSmtpartrecPojo.getPrevpacksn() == null) wkSmtpartrecPojo.setPrevpacksn("");
        if (wkSmtpartrecPojo.getPacksn() == null) wkSmtpartrecPojo.setPacksn("");
        if (wkSmtpartrecPojo.getBatchno() == null) wkSmtpartrecPojo.setBatchno("");
        if (wkSmtpartrecPojo.getItemcode() == null) wkSmtpartrecPojo.setItemcode("");
        if (wkSmtpartrecPojo.getItemname() == null) wkSmtpartrecPojo.setItemname("");
        if (wkSmtpartrecPojo.getItemspec() == null) wkSmtpartrecPojo.setItemspec("");
        if (wkSmtpartrecPojo.getItemunit() == null) wkSmtpartrecPojo.setItemunit("");
        if (wkSmtpartrecPojo.getSingleqty() == null) wkSmtpartrecPojo.setSingleqty(0D);
        if (wkSmtpartrecPojo.getQuantity() == null) wkSmtpartrecPojo.setQuantity(0D);
        if (wkSmtpartrecPojo.getOperatorid() == null) wkSmtpartrecPojo.setOperatorid("");
        if (wkSmtpartrecPojo.getOperator() == null) wkSmtpartrecPojo.setOperator("");
        if (wkSmtpartrecPojo.getRownum() == null) wkSmtpartrecPojo.setRownum(0);
        if (wkSmtpartrecPojo.getQcfeedercode() == null) wkSmtpartrecPojo.setQcfeedercode("");
        if (wkSmtpartrecPojo.getQcstationcode() == null) wkSmtpartrecPojo.setQcstationcode("");
        if (wkSmtpartrecPojo.getQcprevpacksn() == null) wkSmtpartrecPojo.setQcprevpacksn("");
        if (wkSmtpartrecPojo.getQcpacksn() == null) wkSmtpartrecPojo.setQcpacksn("");
        if (wkSmtpartrecPojo.getQcstatusnum() == null) wkSmtpartrecPojo.setQcstatusnum(0);
        if (wkSmtpartrecPojo.getInspector() == null) wkSmtpartrecPojo.setInspector("");
        if (wkSmtpartrecPojo.getInspectorid() == null) wkSmtpartrecPojo.setInspectorid("");
        if (wkSmtpartrecPojo.getInspdate() == null) wkSmtpartrecPojo.setInspdate(new Date());
        if (wkSmtpartrecPojo.getRemark() == null) wkSmtpartrecPojo.setRemark("");
        if (wkSmtpartrecPojo.getCreateby() == null) wkSmtpartrecPojo.setCreateby("");
        if (wkSmtpartrecPojo.getCreatebyid() == null) wkSmtpartrecPojo.setCreatebyid("");
        if (wkSmtpartrecPojo.getCreatedate() == null) wkSmtpartrecPojo.setCreatedate(new Date());
        if (wkSmtpartrecPojo.getLister() == null) wkSmtpartrecPojo.setLister("");
        if (wkSmtpartrecPojo.getListerid() == null) wkSmtpartrecPojo.setListerid("");
        if (wkSmtpartrecPojo.getModifydate() == null) wkSmtpartrecPojo.setModifydate(new Date());
        if (wkSmtpartrecPojo.getCustom1() == null) wkSmtpartrecPojo.setCustom1("");
        if (wkSmtpartrecPojo.getCustom2() == null) wkSmtpartrecPojo.setCustom2("");
        if (wkSmtpartrecPojo.getCustom3() == null) wkSmtpartrecPojo.setCustom3("");
        if (wkSmtpartrecPojo.getCustom4() == null) wkSmtpartrecPojo.setCustom4("");
        if (wkSmtpartrecPojo.getCustom5() == null) wkSmtpartrecPojo.setCustom5("");
        if (wkSmtpartrecPojo.getCustom6() == null) wkSmtpartrecPojo.setCustom6("");
        if (wkSmtpartrecPojo.getCustom7() == null) wkSmtpartrecPojo.setCustom7("");
        if (wkSmtpartrecPojo.getCustom8() == null) wkSmtpartrecPojo.setCustom8("");
        if (wkSmtpartrecPojo.getCustom9() == null) wkSmtpartrecPojo.setCustom9("");
        if (wkSmtpartrecPojo.getCustom10() == null) wkSmtpartrecPojo.setCustom10("");
        if (wkSmtpartrecPojo.getTenantid() == null) wkSmtpartrecPojo.setTenantid("");
        if (wkSmtpartrecPojo.getTenantname() == null) wkSmtpartrecPojo.setTenantname("");
        if (wkSmtpartrecPojo.getRevision() == null) wkSmtpartrecPojo.setRevision(0);
        WkSmtpartrecEntity wkSmtpartrecEntity = new WkSmtpartrecEntity();
        BeanUtils.copyProperties(wkSmtpartrecPojo, wkSmtpartrecEntity);

        wkSmtpartrecEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkSmtpartrecEntity.setRevision(1);  //乐观锁
        this.wkSmtpartrecMapper.insert(wkSmtpartrecEntity);
        // 刷新完工数
        this.wkSmtpartrecMapper.updatePartFinish(wkSmtpartrecEntity.getPartitemid(), wkSmtpartrecEntity.getPartuid(), wkSmtpartrecEntity.getTenantid());
        return this.getEntity(wkSmtpartrecEntity.getId(), wkSmtpartrecEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSmtpartrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSmtpartrecPojo update(WkSmtpartrecPojo wkSmtpartrecPojo) {
        WkSmtpartrecEntity wkSmtpartrecEntity = new WkSmtpartrecEntity();
        BeanUtils.copyProperties(wkSmtpartrecPojo, wkSmtpartrecEntity);
        this.wkSmtpartrecMapper.update(wkSmtpartrecEntity);
        // 刷新完工数
        this.wkSmtpartrecMapper.updatePartFinish(wkSmtpartrecEntity.getPartitemid(), wkSmtpartrecEntity.getPartuid(), wkSmtpartrecEntity.getTenantid());
        // 刷新通过数
        this.wkSmtpartrecMapper.updatePartAccept(wkSmtpartrecEntity.getPartitemid(), wkSmtpartrecEntity.getPartuid(), wkSmtpartrecEntity.getTenantid());
        return this.getEntity(wkSmtpartrecEntity.getId(), wkSmtpartrecEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkSmtpartrecMapper.delete(key, tid);
    }


}
