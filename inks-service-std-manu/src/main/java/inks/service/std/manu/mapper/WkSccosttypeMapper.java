package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSccosttypeEntity;
import inks.service.std.manu.domain.pojo.WkSccosttypePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 委外费用类型(WkSccosttype)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-13 15:12:07
 */
@Mapper
public interface WkSccosttypeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccosttypePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSccosttypePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param wkSccosttypeEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSccosttypeEntity wkSccosttypeEntity);

    
    /**
     * 修改数据
     *
     * @param wkSccosttypeEntity 实例对象
     * @return 影响行数
     */
    int update(WkSccosttypeEntity wkSccosttypeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                        }

