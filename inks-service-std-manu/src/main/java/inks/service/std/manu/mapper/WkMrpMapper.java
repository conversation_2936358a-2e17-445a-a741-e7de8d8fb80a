package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkMrpEntity;
import inks.service.std.manu.domain.pojo.WkMrpPojo;
import inks.service.std.manu.domain.pojo.WkMrpitemPojo;
import inks.service.std.manu.domain.pojo.WkMrpitemdetailPojo;
import inks.service.std.manu.domain.pojo.WkMrpobjdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashSet;
import java.util.List;

/**
 * MRP运算(WkMrp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-14 21:00:34
 */
@Mapper
public interface WkMrpMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMrpPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMrpitemdetailPojo> getPageList(QueryParam queryParam);

    List<WkMrpobjdetailPojo> getObjPageList(QueryParam queryParam);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMrpPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkMrpEntity 实例对象
     * @return 影响行数
     */
    int insert(WkMrpEntity wkMrpEntity);


    /**
     * 修改数据
     *
     * @param wkMrpEntity 实例对象
     * @return 影响行数
     */
    int update(WkMrpEntity wkMrpEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkMrpPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkMrpPojo wkMrpPojo);

    List<String> getDelObjIds(WkMrpPojo wkMrpPojo);
    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMrpitemPojo> getItemListByParentid(@Param("Pid") String Pid, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMachMrpUid(@Param("key") String key, @Param("mrpid") String mrpid, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMpMrpUid(@Param("key") String key, @Param("mrpid") String mrpid, @Param("refno") String refno, @Param("tid") String tid);


     // 指定查询仓库集合
    Double getGoodsInveQtyInStoreids(@Param("key") String key, @Param("storeIdSet") HashSet storeIds, @Param("tid") String tid);


    Double getBuyPlanRemQty(@Param("key") String key, @Param("mrpitemid") String mrpitemid, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Double getBuyOrderRemQty(@Param("key") String key, @Param("mrpitemid") String mrpitemid, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Double getWkWsRemQty(@Param("key") String key, @Param("mrpitemid") String mrpitemid, @Param("tid") String tid);


    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Double getWkScRemQty(@Param("key") String key, @Param("mrpitemid") String mrpitemid, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Double getBusMachRemQty(@Param("key") String key, @Param("machitemid") String machitemid, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Double getBusDeliRemQty(@Param("key") String key, @Param("machitemid") String machitemid, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
//    Double getMrpRemQty(@Param("key") String key, @Param("mrpid") String mrpid, @Param("mrpdate") Date mrpdate, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Double getWkWsMatRemQty(@Param("key") String key, @Param("mrpitemid") String mrpitemid, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Double getWkScMatRemQty(@Param("key") String key, @Param("mrpitemid") String mrpitemid, @Param("tid") String tid);


    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Double getReqRemQty(@Param("key") String key, @Param("mrpitemid") String mrpitemid, @Param("tid") String tid);

    /**
     * 修改完工记数
     *
     * @param key Item的id
     * @return 影响行数
     */
    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    int syncMrpCount(@Param("key") String key, @Param("tid") String tid);

    Double getSafestock(@Param("goodsid") String goodsid, @Param("tid") String tid);

    List<String> getItemIds(String mrpid, String tid);

    int syncMrpItemMergeMarkAndGrossQty(String mrpid, String tid);

    void syncMrpItemWkFinishQty(String mrpitemid, String tid);

}

