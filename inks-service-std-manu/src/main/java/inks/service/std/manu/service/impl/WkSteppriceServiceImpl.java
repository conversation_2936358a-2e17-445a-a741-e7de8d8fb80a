package inks.service.std.manu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.redis.service.RedisService;
import inks.service.std.manu.domain.WkSteppriceEntity;
import inks.service.std.manu.domain.WkSteppriceitemEntity;
import inks.service.std.manu.domain.constant.MyConstant;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.mapper.WkSteppriceMapper;
import inks.service.std.manu.mapper.WkSteppriceitemMapper;
import inks.service.std.manu.mapper.WkStepprogroupMapper;
import inks.service.std.manu.service.WkProgroupitemService;
import inks.service.std.manu.service.WkSteppriceService;
import inks.service.std.manu.service.WkSteppriceitemService;
import inks.service.std.manu.service.WkSteppricesetService;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 阶梯工价(WkStepprice)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-28 09:07:25
 */
@Service("wkSteppriceService")
public class WkSteppriceServiceImpl implements WkSteppriceService {
    @Resource
    private WkSteppriceMapper wkSteppriceMapper;
    @Resource
    private WkStepprogroupMapper wkStepprogroupMapper;

    @Resource
    private WkSteppriceitemMapper wkSteppriceitemMapper;
    @Resource
    private WkSteppricesetService wkSteppricesetService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkSteppriceitemService wkSteppriceitemService;
    @Resource
    private WkProgroupitemService wkProgroupitemService;
    @Resource
    private RedisService redisService;

    /**
     * @Description spuJson转为SpuValue【且按照key的升序排列】
     * SpuJson格式：[{"key":"spuhou","name":"厚","startvalue":"10","endvalue":"20"},{"key":"spuchang","name":"长","startvalue":"1000","endvalue":"2000"}]
     * <AUTHOR>
     * @param[1] wkSteppricePojo
     * @time 2023/6/28 16:43
     */
    public static String spuJsonToSpuValue(String spuJson) {
        // 将Spujson转换为SpuValue【拼接Spujson中的key和value】
        // 将给定的Spujson格式转换为 spuchang:1000-2000,spuhou:10-20 的形式
        List<WkSteppriceSpusPojo> spusList = JSONArray.parseArray(spuJson, WkSteppriceSpusPojo.class);
        // 对spusList进行排序，按照key的升序排列，以确保spuchang在spuhou的前面
        spusList.sort(Comparator.comparing(WkSteppriceSpusPojo::getKey));
        String spuValue = spusList.stream()
                .map(spu -> spu.getKey() + ":" + spu.getStartval() + "-" + spu.getEndval())
                .collect(Collectors.joining(","));
        return spuValue;
    }

//    public static void main(String[] args) {
//        String spusAttr = "[{\"key\":\"spuchang\",\"value\":\"1005\"},{\"key\":\"spuhou\",\"value\":\"12\"}]";
//        List<KeyValuePojo> keyValuePojos = JSONArray.parseArray(spusAttr, KeyValuePojo.class);
//        for (KeyValuePojo keyValuePojo : keyValuePojos) {
//            System.out.println(keyValuePojo.getKey());
//            System.out.println(keyValuePojo.getValue());
//        }
//
//        // 模拟测试数据
//        String attributejson = "[{\"key\":\"spuchang\",\"value\":\"1000\"},{\"key\":\"spuhou\",\"value\":\"10\"}]";
//        String spus = "[{\"key\":\"spuchang\",\"name\":\"长\"},{\"key\":\"spuhou\",\"name\":\"厚\"}]";
//        // 创建实例并调用方法
//        String result = new WkSteppriceServiceImpl().getSpuValueByAttrAndSpus2(attributejson, spus);
//        // 打印结果
//        System.out.println(result);
//    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSteppricePojo getEntity(String key, String tid) {
        return this.wkSteppriceMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSteppriceitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSteppriceitemdetailPojo> lst = wkSteppriceMapper.getPageList(queryParam);
            PageInfo<WkSteppriceitemdetailPojo> pageInfo = new PageInfo<WkSteppriceitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSteppricePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkSteppricePojo wkSteppricePojo = this.wkSteppriceMapper.getEntity(key, tid);
            //读取子表
            wkSteppricePojo.setItem(wkSteppriceitemMapper.getList(wkSteppricePojo.getId(), wkSteppricePojo.getTenantid()));
            // 子表2:  wkSteppricePojo.getSpujson()转为的Spus子表
            List<WkSteppriceSpusPojo> spusList = JSONArray.parseArray(wkSteppricePojo.getSpujson(), WkSteppriceSpusPojo.class);
            wkSteppricePojo.setSpuslst(spusList);
            return wkSteppricePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSteppricePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSteppricePojo> lst = wkSteppriceMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkSteppriceitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkSteppricePojo> pageInfo = new PageInfo<WkSteppricePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSteppricePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSteppricePojo> lst = wkSteppriceMapper.getPageTh(queryParam);
            PageInfo<WkSteppricePojo> pageInfo = new PageInfo<WkSteppricePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkSteppricePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSteppricePojo insert(WkSteppricePojo wkSteppricePojo) {
//初始化NULL字段
        if (wkSteppricePojo.getRefno() == null) wkSteppricePojo.setRefno("");
        if (wkSteppricePojo.getBilltype() == null) wkSteppricePojo.setBilltype("");
        if (wkSteppricePojo.getBilltitle() == null) wkSteppricePojo.setBilltitle("");
        if (wkSteppricePojo.getBilldate() == null) wkSteppricePojo.setBilldate(new Date());
        if (wkSteppricePojo.getGoodsid() == null) wkSteppricePojo.setGoodsid("");
        if (wkSteppricePojo.getWpid() == null) wkSteppricePojo.setWpid("");
        if (wkSteppricePojo.getWpcode() == null) wkSteppricePojo.setWpcode("");
        if (wkSteppricePojo.getWpname() == null) wkSteppricePojo.setWpname("");
        if (wkSteppricePojo.getSpus() == null) wkSteppricePojo.setSpus("");
        if (wkSteppricePojo.getSpujson() == null) wkSteppricePojo.setSpujson("");
        if (wkSteppricePojo.getSpuvalue() == null) wkSteppricePojo.setSpuvalue("");
        if (wkSteppricePojo.getCreateby() == null) wkSteppricePojo.setCreateby("");
        if (wkSteppricePojo.getCreatebyid() == null) wkSteppricePojo.setCreatebyid("");
        if (wkSteppricePojo.getCreatedate() == null) wkSteppricePojo.setCreatedate(new Date());
        if (wkSteppricePojo.getLister() == null) wkSteppricePojo.setLister("");
        if (wkSteppricePojo.getListerid() == null) wkSteppricePojo.setListerid("");
        if (wkSteppricePojo.getModifydate() == null) wkSteppricePojo.setModifydate(new Date());
        if (wkSteppricePojo.getAssessor() == null) wkSteppricePojo.setAssessor("");
        if (wkSteppricePojo.getAssessorid() == null) wkSteppricePojo.setAssessorid("");
        if (wkSteppricePojo.getAssessdate() == null) wkSteppricePojo.setAssessdate(new Date());
        if (wkSteppricePojo.getSummary() == null) wkSteppricePojo.setSummary("");
        if (wkSteppricePojo.getCustom1() == null) wkSteppricePojo.setCustom1("");
        if (wkSteppricePojo.getCustom2() == null) wkSteppricePojo.setCustom2("");
        if (wkSteppricePojo.getCustom3() == null) wkSteppricePojo.setCustom3("");
        if (wkSteppricePojo.getCustom4() == null) wkSteppricePojo.setCustom4("");
        if (wkSteppricePojo.getCustom5() == null) wkSteppricePojo.setCustom5("");
        if (wkSteppricePojo.getCustom6() == null) wkSteppricePojo.setCustom6("");
        if (wkSteppricePojo.getCustom7() == null) wkSteppricePojo.setCustom7("");
        if (wkSteppricePojo.getCustom8() == null) wkSteppricePojo.setCustom8("");
        if (wkSteppricePojo.getCustom9() == null) wkSteppricePojo.setCustom9("");
        if (wkSteppricePojo.getCustom10() == null) wkSteppricePojo.setCustom10("");
        if (wkSteppricePojo.getTenantid() == null) wkSteppricePojo.setTenantid("");
        if (wkSteppricePojo.getTenantname() == null) wkSteppricePojo.setTenantname("");
        if (wkSteppricePojo.getRevision() == null) wkSteppricePojo.setRevision(0);
        //  spuJson转为SpuValue 且按照key的升序排列
        String spuValue = spuJsonToSpuValue(wkSteppricePojo.getSpujson());
        //检查goodsid+wpid+spuvalue , 三码唯一
        int count = this.wkSteppriceitemMapper.checkGoodsidWpidSpuvalue(wkSteppricePojo.getGoodsid(), wkSteppricePojo.getWpid(), spuValue, wkSteppricePojo.getTenantid());
        if (count > 0) throw new RuntimeException("该(货品+工序下)SpuJson值重复");
        wkSteppricePojo.setSpuvalue(spuValue);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkSteppriceEntity wkSteppriceEntity = new WkSteppriceEntity();
        BeanUtils.copyProperties(wkSteppricePojo, wkSteppriceEntity);

        //设置id和新建日期
        wkSteppriceEntity.setId(id);
        wkSteppriceEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkSteppriceMapper.insert(wkSteppriceEntity);
        //Item子表处理
        List<WkSteppriceitemPojo> lst = wkSteppricePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkSteppriceitemPojo itemPojo = this.wkSteppriceitemService.clearNull(lst.get(i));
                WkSteppriceitemEntity wkSteppriceitemEntity = new WkSteppriceitemEntity();
                BeanUtils.copyProperties(itemPojo, wkSteppriceitemEntity);
                //设置id和Pid
                wkSteppriceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkSteppriceitemEntity.setPid(id);
                wkSteppriceitemEntity.setTenantid(wkSteppricePojo.getTenantid());
                wkSteppriceitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkSteppriceitemMapper.insert(wkSteppriceitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkSteppriceEntity.getId(), wkSteppriceEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSteppricePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSteppricePojo update(WkSteppricePojo wkSteppricePojo) {
        String tenantid = wkSteppricePojo.getTenantid();
        //  spuJson转为SpuValue 且按照key的升序排列
        String spuValue = spuJsonToSpuValue(wkSteppricePojo.getSpujson());
        //当传入spuJson和数据库的spuJson不同时： 检查goodsid+wpid+spuvalue , 三码唯一
        String spujsonDB = this.wkSteppriceMapper.getEntity(wkSteppricePojo.getId(), tenantid).getSpujson();
        if (!spujsonDB.equals(wkSteppricePojo.getSpujson())) {
            int count = this.wkSteppriceitemMapper.checkGoodsidWpidSpuvalue(wkSteppricePojo.getGoodsid(), wkSteppricePojo.getWpid(), spuValue, tenantid);
            if (count > 0) throw new RuntimeException("该(货品+工序下)SpuJson值重复");
        }
        wkSteppricePojo.setSpuvalue(spuValue);
        //主表更改
        WkSteppriceEntity wkSteppriceEntity = new WkSteppriceEntity();
        BeanUtils.copyProperties(wkSteppricePojo, wkSteppriceEntity);
        this.wkSteppriceMapper.update(wkSteppriceEntity);
        if (wkSteppricePojo.getItem() != null) {
            //Item子表处理
            List<WkSteppriceitemPojo> lst = wkSteppricePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkSteppriceMapper.getDelItemIds(wkSteppricePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkSteppriceitemMapper.delete(lstDelIds.get(i), wkSteppriceEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    WkSteppriceitemEntity wkSteppriceitemEntity = new WkSteppriceitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        WkSteppriceitemPojo itemPojo = this.wkSteppriceitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, wkSteppriceitemEntity);
                        //设置id和Pid
                        wkSteppriceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkSteppriceitemEntity.setPid(wkSteppriceEntity.getId());  // 主表 id
                        wkSteppriceitemEntity.setTenantid(tenantid);   // 租户id
                        wkSteppriceitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkSteppriceitemMapper.insert(wkSteppriceitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkSteppriceitemEntity);
                        wkSteppriceitemEntity.setTenantid(tenantid);
                        this.wkSteppriceitemMapper.update(wkSteppriceitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkSteppriceEntity.getId(), wkSteppriceEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkSteppricePojo wkSteppricePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkSteppriceitemPojo> lst = wkSteppricePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkSteppriceitemPojo wkSteppriceitemPojo : lst) {
                this.wkSteppriceitemMapper.delete(wkSteppriceitemPojo.getId(), tid);
            }
        }
        this.wkSteppriceMapper.delete(key, tid);
        return wkSteppricePojo.getRefno();
    }

    /**
     * 审核数据
     *
     * @param wkSteppricePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSteppricePojo approval(WkSteppricePojo wkSteppricePojo) {
        //主表更改
        WkSteppriceEntity wkSteppriceEntity = new WkSteppriceEntity();
        BeanUtils.copyProperties(wkSteppricePojo, wkSteppriceEntity);
        this.wkSteppriceMapper.approval(wkSteppriceEntity);
        //返回Bill实例
        return this.getBillEntity(wkSteppriceEntity.getId(), wkSteppriceEntity.getTenantid());
    }

    /**
     * @return List<Map < Object>>
     * @Description
     * <AUTHOR>
     * @param[1] jsonMap goodsid,attributejson,quantity
     * @param[2] progroupid 制程id
     * {
     * "AttributeJson": "[{\"key\":\"spuwaijing\",\"value\":\"12\"},{\"key\":\"spunajing\",\"value\":\"8\"},{\"key\":\"spugaodu\",\"value\":\"6\"}]",
     * "Goodsid": "1676756104144486400",
     * "Quantity": 300,
     * "id": "1746033078666526720"
     * }
     * @param[3] spu_configValue 参与计算的SPU
     * @param[4] tid
     * @time 2023/6/28 16:18
     */
    @Override
    public List<Map<String, Object>> getProductionCostBudget(Map<String, Object> machItemMap, String progroupid, String spu_configValue, String tid) {
        String machitemid = machItemMap.get("id").toString();
        String goodsid = machItemMap.get("Goodsid").toString();
        String attributejson = machItemMap.get("AttributeJson").toString();
        String quantity = machItemMap.get("Quantity").toString();
        // 获取制程对应工序列表(是否传入制程id分两种情况)
        List<WkProgroupitemPojo> progroupitemlist;
        // 1.传了制程id,直接查询制程子表Wk_ProGroupItem获得制程List
        if (isNotBlank(progroupid)) {
            progroupitemlist = wkProgroupitemService.getList(progroupid, tid);
        }
        // 2.没传制程id,查询阶梯制程表Wk_StepProGroup，获取制程List的JSON  (Wk_StepProGroup.FlowJson字段)
        else {
            List<KeyValuePojo> attrKeyValue = JSONArray.parseArray(attributejson, KeyValuePojo.class);
//            String spus = "spubiaomianchuli,spuwaijing,spugaodu";//参与计算的spu
            String spus = spu_configValue;//参与计算的spu
//            int spusSize = spus.split(",").length;
            int spusSize = 0;
            StringBuilder sql = new StringBuilder();

            for (KeyValuePojo keyValuePojo : attrKeyValue) {
                Object value = keyValuePojo.getValue();
                if (value != null && spus.contains(keyValuePojo.getKey())) {
                    if (sql.length() > 0) {
                        sql.append(" or ");
                    }
                    boolean isNumeric = false;
                    try {
                        Double.parseDouble(value.toString());
                        isNumeric = true;
                    } catch (NumberFormatException ignored) {
                    }
                    if (isNumeric) {
                        sql.append("(spukey='").append(keyValuePojo.getKey()).append("' and startvalue<=").append(value).append(" and endvalue>=").append(value).append(")");
                    } else {
                        sql.append("(spukey='").append(keyValuePojo.getKey()).append("' and (startvalue='").append(value).append("' or endvalue='").append(value).append("'))");
                    }
                    spusSize++;
                }
            }

//        for (int i = 0; i < attrKeyValue.size(); i++) {
//            if (i == attrKeyValue.size() - 1) {
//                sql.append("(spukey='").append(attrKeyValue.get(i).getKey()).append("' and startvalue<=").append(attrKeyValue.get(i).getValue()).append(" and endvalue>=").append(attrKeyValue.get(i).getValue()).append(")");
//            } else {
//                sql.append("(spukey='").append(attrKeyValue.get(i).getKey()).append("' and startvalue<=").append(attrKeyValue.get(i).getValue()).append(" and endvalue>=").append(attrKeyValue.get(i).getValue()).append(") or ");
//            }
//        }
            // 根据goodsid+spuvalue+quantity,查询阶梯制程出来，查不到报错
            String wkStepprogroupId = this.wkStepprogroupMapper.getidBySpuJson(goodsid, sql.toString(), spusSize, tid);
            if (isBlank(wkStepprogroupId)) {
                throw new BaseBusinessException("未找到相关阶梯制程表");
            }
            String flowJson = this.wkStepprogroupMapper.getFlowJson(wkStepprogroupId, quantity, tid);
            if (isBlank(flowJson)) {
                throw new BaseBusinessException("根据Wk_StepProGroup.id和数量查询不到制程");
            }
            progroupitemlist = JSON.parseObject(flowJson, new TypeReference<List<WkProgroupitemPojo>>() {
            });
        }


        List<Map<String, Object>> result = new ArrayList<>();
        for (WkProgroupitemPojo progroupitem : progroupitemlist) {
            // 获取工序对应的Spus
            String wpid = progroupitem.getWpid();
            String spus = this.wkSteppricesetService.getSpusByWpid(wpid, tid);
            Double price = 0.0; //有spus就查询价格，无则默认赋值0
            if (spus != null) {
                int spusSize = JSON.parseArray(spus).size();
                if (isNotBlank(spus)) {
                    // 对比订单子表attributejson和工序对应的spus，获取拼接的SpuValue
                    if (isNotBlank(attributejson)) {
                        // spuValue格式为[{"key":"spuchang","value":"1005"},{"key":"spuhou","value":"12"}]
                        String spusAttr = getSpuValueByAttrAndSpus2(attributejson, spus);
                        List<KeyValuePojo> keyValuePojos = JSONArray.parseArray(spusAttr, KeyValuePojo.class);
                        // keyValuePojos判空 不为空才拼接SQL查询单价
                        if (CollectionUtils.isNotEmpty(keyValuePojos)) {
                            StringBuilder sql = new StringBuilder();
                            for (int i = 0; i < keyValuePojos.size(); i++) {
                                if (i == keyValuePojos.size() - 1) {
                                    sql.append("(spukey='").append(keyValuePojos.get(i).getKey()).append("' and startvalue<=").append(keyValuePojos.get(i).getValue()).append(" and endvalue>=").append(keyValuePojos.get(i).getValue()).append(")");
                                } else {
                                    sql.append("(spukey='").append(keyValuePojos.get(i).getKey()).append("' and startvalue<=").append(keyValuePojos.get(i).getValue()).append(" and endvalue>=").append(keyValuePojos.get(i).getValue()).append(") or ");
                                }
                            }
                            // 根据goodsid+wpid+spuvalue+quantity,查询阶梯单价出来，查不到为0；
                            String id = this.wkSteppriceMapper.getidBySpuJson(goodsid, wpid, sql.toString(), spusSize, tid);
                            if (id != null) {
                                price = this.wkSteppriceMapper.getPrice(id, quantity, tid);
                                if (price == null) price = 0.0;
                            }
                        }
                    }
                }
            }

            // 计算金额=单价*数量，使用BigDecimal进行精确计算
            BigDecimal priceBigDecimal = BigDecimal.valueOf(price);
            BigDecimal quantityBigDecimal = BigDecimal.valueOf(Double.parseDouble(quantity));
            BigDecimal amountBigDecimal = priceBigDecimal.multiply(quantityBigDecimal);
            Double amount = amountBigDecimal.doubleValue();
            // 创建Map对象，存储结果
            Map<String, Object> item = new HashMap<>();
            item.put("wpcode", progroupitem.getWpcode());
            item.put("price", price);
            item.put("amount", amount);
            item.put("machitemid", machitemid);
            item.put("quantity", quantity);
            item.put("wpname", progroupitem.getWpname());
            result.add(item);
        }
        return result;
    }

    private String getSpuValueByAttrAndSpus2(String attributejson, String spus) {
        // 将attributejson和spusByWpid转换为JSONArray
        JSONArray attributeArray = JSON.parseArray(attributejson);
        JSONArray spusArray = JSON.parseArray(spus);
        // 创建一个新的JSONArray用于存储匹配的键值对
        JSONArray matchedArray = new JSONArray();
        // 遍历attributejson中的每个对象(key和value)
        for (int i = 0; i < attributeArray.size(); i++) {
            JSONObject attributeObj = attributeArray.getJSONObject(i);
            String attributeKey = attributeObj.getString("key");
            String attributeValue = attributeObj.getString("value");
            // 拿到attributejson中的key,去spus中对比是否存在相同key
            for (int j = 0; j < spusArray.size(); j++) {
                JSONObject spusObj = spusArray.getJSONObject(j);
                String spusKey = spusObj.getString("key");
                // 判断key是否匹配
                if (attributeKey.equals(spusKey)) {
                    // 保留attributejson的键值对
                    JSONObject matchedObj = new JSONObject();
                    matchedObj.put("key", attributeKey);
                    matchedObj.put("value", attributeValue);
                    matchedArray.add(matchedObj);
                    break;
                }
            }
        }
        return matchedArray.toJSONString();
    }

    /**
     * @return String
     * @Description 对比订单子表attributejson和工序对应的spus，获取拼接的SpuValue【且按照key的升序排列】
     * <AUTHOR>
     * @param[1] attributejson格式为[{"key":"spuchang","value":"1000"},{"key":"spuhou","value":"10"}]
     * @param[2] spus格式为[{"key":"spuchang","name":"长"},{"key":"spuhou","name":"厚"}]
     * @time 2023/6/28 14:51
     */
    private String getSpuValueByAttrAndSpus(String attributejson, String spus) {
        // 将attributejson和spusByWpid转换为JSONArray
        JSONArray attributeArray = JSON.parseArray(attributejson);
        JSONArray spusArray = JSON.parseArray(spus);
        // 对 attributeArray 进行排序，按照 key 的升序排列
        attributeArray.sort(Comparator.comparing(obj -> ((JSONObject) obj).getString("key")));
        // 创建一个StringBuilder用于拼接结果SpuValue
        StringBuilder spuValueBuilder = new StringBuilder();
        // 遍历attributejson中的每个对象(key和value)
        for (int i = 0; i < attributeArray.size(); i++) {
            JSONObject attributeObj = attributeArray.getJSONObject(i);
            String attributeKey = attributeObj.getString("key");
            String attributeValue = attributeObj.getString("value");
            // 拿到attributejson中的key,去spus中对比是否存在相同key
            for (int j = 0; j < spusArray.size(); j++) {
                JSONObject spusObj = spusArray.getJSONObject(j);
                String spusKey = spusObj.getString("key");
                // 判断key是否匹配
                if (attributeKey.equals(spusKey)) {
                    // 保留attributejson的key和value，并拼接到结果中
                    spuValueBuilder.append(attributeKey).append(":").append(attributeValue).append(",");
                    break;
                }
            }
        }
        // 删除最后一个逗号
        if (spuValueBuilder.toString().endsWith(",")) {
            spuValueBuilder.deleteCharAt(spuValueBuilder.length() - 1);
        }
        // 返回拼接结果
        String spuValue = spuValueBuilder.toString();
        return spuValue;
    }

    @Override
    public List<Map<String, Object>> getMachItemList(String id, String tenantid) {
        return this.wkSteppriceMapper.getMachItemList(id, tenantid);
    }

//    --------------------------------------------------异步--------------------------------------


    @Override
    @Async
    public void getProductionCostBudgetStart(String redisKey, String id, String progroupid, String spu_configValue, String tid) {
        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100"); //开始处理代码
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.redisService.setCacheMapValue(MyConstant.ASYNC_MACHPRICE_STATE, redisKey, missionMsg);
            //数据填充
            // 获取订单子表List
            List<Map<String, Object>> machItemList = wkSteppriceMapper.getMachItemList(id, tid);
            // 创建线程池 线程池的大小为10，表示最多同时执行10个任务
            ExecutorService executor = Executors.newFixedThreadPool(10);
            List<Future<List<Map<String, Object>>>> futures = new ArrayList<>();
            // 提交子任务到线程池(循环订单子表)
            for (Map<String, Object> machItem : machItemList) {
                Future<List<Map<String, Object>>> future = executor.submit(() -> {
                    // 调用并行处理的方法
                    return this.getProductionCostBudget(machItem, progroupid, spu_configValue, tid);
                });
                futures.add(future);
            }
            // 关闭线程池 shutdown()方法并不会马上关闭，而是不再接收新的任务,之前提交的任务执行完才关闭；shutdownNow()立即停止所有任务，正在执行的任务不确定是否停止
            executor.shutdown();
            // 合并子任务的结果 存入result
            List<Map<String, Object>> result = new ArrayList<>();
            for (Future<List<Map<String, Object>>> future : futures) {
                result.addAll(future.get());
            }
            // 将计算结果转json存入redis 5分钟后过期
            this.redisService.setCacheObject(redisKey, JSON.toJSONString(result), 5L, TimeUnit.MINUTES);
            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.redisService.setCacheMapValue(MyConstant.ASYNC_MACHPRICE_STATE, redisKey, missionMsg);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}
