package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.HiWipnotePojo;
import inks.service.std.manu.domain.pojo.HiWipnoteitemPojo;
import inks.service.std.manu.domain.pojo.HiWipnoteitemdetailPojo;
import inks.service.std.manu.service.HiWipnoteService;
import inks.service.std.manu.service.HiWipnoteitemService;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * WIP记录(HiWipnote)表控制层
 *
 * <AUTHOR>
 * @since 2023-10-16 16:19:14
 */
@RestController
@RequestMapping("hiWipnote")
public class HiWipnoteController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(HiWipnoteController.class);
    private final String moduleCode = "D05M05H1";
    /**
     * 服务对象
     */
    @Resource
    private HiWipnoteService hiWipnoteService;
    /**
     * 服务对象Item
     */
    @Resource
    private HiWipnoteitemService hiWipnoteitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取WIP记录详细信息", notes = "获取WIP记录详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Hi_WipNote.List")
    public R<HiWipnotePojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.hiWipnoteService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipNote.List")
    public R<PageInfo<HiWipnoteitemdetailPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Hi_WipNote.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.hiWipnoteService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取WIP记录详细信息", notes = "获取WIP记录详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Hi_WipNote.List")
    public R<HiWipnotePojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.hiWipnoteService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipNote.List")
    public R<PageInfo<HiWipnotePojo>> getBillList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Hi_WipNote.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.hiWipnoteService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipNote.List")
    public R<PageInfo<HiWipnotePojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Hi_WipNote.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.hiWipnoteService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增WIP记录", notes = "新增WIP记录", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipNote.Add")
    public R<HiWipnotePojo> create(@RequestBody String json) {
        try {
            HiWipnotePojo hiWipnotePojo = JSONArray.parseObject(json, HiWipnotePojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Hi_WipNote", null, loginUser.getTenantid());
            hiWipnotePojo.setRefno(refno);
            hiWipnotePojo.setCreateby(loginUser.getRealName());   // 创建者
            hiWipnotePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            hiWipnotePojo.setCreatedate(new Date());   // 创建时间
            hiWipnotePojo.setLister(loginUser.getRealname());   // 制表
            hiWipnotePojo.setListerid(loginUser.getUserid());    // 制表id            
            hiWipnotePojo.setModifydate(new Date());   //修改时间
            hiWipnotePojo.setTenantid(loginUser.getTenantid());   //租户id
            HiWipnotePojo insert = this.hiWipnoteService.insert(hiWipnotePojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改WIP记录", notes = "修改WIP记录", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipNote.Edit")
    public R<HiWipnotePojo> update(@RequestBody String json) {
        try {
            HiWipnotePojo hiWipnotePojo = JSONArray.parseObject(json, HiWipnotePojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            hiWipnotePojo.setLister(loginUser.getRealname());   // 制表
            hiWipnotePojo.setListerid(loginUser.getUserid());    // 制表id   
            hiWipnotePojo.setModifydate(new Date());   //修改时间
            hiWipnotePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.hiWipnoteService.update(hiWipnotePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除WIP记录", notes = "删除WIP记录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Hi_WipNote.Delete")
    public R<Integer> delete(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            this.hiWipnoteService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增WIP记录Item", notes = "新增WIP记录Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipNote.Add")
    public R<HiWipnoteitemPojo> createItem(@RequestBody String json) {
        try {
            HiWipnoteitemPojo hiWipnoteitemPojo = JSONArray.parseObject(json, HiWipnoteitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            hiWipnoteitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.hiWipnoteitemService.insert(hiWipnoteitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除WIP记录Item", notes = "删除WIP记录Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Hi_WipNote.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.hiWipnoteitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
//    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
//    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Hi_WipNote.Print")
//    public void printBill(String key, String ptid) throws IOException, JRException {
//        // 获得用户数据
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        //获取单据信息
//        HiWipnotePojo hiWipnotePojo = this.hiWipnoteService.getBillEntity(key,loginUser.getTenantid());
//        //表头转MAP
//        Map<String, Object> map = BeanUtils.beanToMap(hiWipnotePojo);
//        // 加入公司信息
//        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
//        //从redis中获取Reprot内容
//        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
//        String content ;
//        if (reportsPojo != null ) {
//          content = reportsPojo.getRptdata();
//        } else {
//          throw new BaseBusinessException("未找到报表");
//        }
//        // 判定是否需要追行
//       if(reportsPojo.getPagerow()>0){
//       int index=0;
//      // 取行余数
//      index =hiWipnotePojo.getItem().size()%reportsPojo.getPagerow();
//      if(index>0){
//        // 补全空白行
//        for(int i=0;i<reportsPojo.getPagerow()-index;i++){
//            HiWipnoteitemPojo hiWipnoteitemPojo = new HiWipnoteitemPojo();
//            hiWipnotePojo.getItem().add(hiWipnoteitemPojo);
//          }
//      }
//     }
//
//        //item转数据源
//        JRDataSource jrDataSource = new JRBeanCollectionDataSource(hiWipnotePojo.getItem());
//        //报表生成
//        InputStream stream = new ByteArrayInputStream(content.getBytes());
//        HttpServletResponse response= ServletUtils.getResponse();
//        ServletOutputStream os = response.getOutputStream();
//        try {
//            //编译报表
//            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
//            //数据填充
//            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
//            //打印PDF数据流
//            JasperExportManager.exportReportToPdfStream(print, os);
//        } catch (JRException e) {
//            e.printStackTrace();
//        } catch (BaseBusinessException base) {
//            base.getMessage();
//        } finally {
//            os.flush();
//        }
//    }
}

