package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkProcessstatEntity;
import inks.service.std.manu.domain.pojo.WkProcessstatPojo;
import inks.service.std.manu.mapper.WkProcessstatMapper;
import inks.service.std.manu.service.WkProcessstatService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工序工位(WkProcessstat)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-17 16:57:23
 */
@Service("wkProcessstatService")
public class WkProcessstatServiceImpl implements WkProcessstatService {
    @Resource
    private WkProcessstatMapper wkProcessstatMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkProcessstatPojo getEntity(String key, String tid) {
        return this.wkProcessstatMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkProcessstatPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProcessstatPojo> lst = wkProcessstatMapper.getPageList(queryParam);
            PageInfo<WkProcessstatPojo> pageInfo = new PageInfo<WkProcessstatPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkProcessstatPojo> getList(String Pid, String tid) {
        try {
            List<WkProcessstatPojo> lst = wkProcessstatMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkProcessstatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProcessstatPojo insert(WkProcessstatPojo wkProcessstatPojo) {
        //初始化item的NULL
        WkProcessstatPojo itempojo = this.clearNull(wkProcessstatPojo);
        WkProcessstatEntity wkProcessstatEntity = new WkProcessstatEntity();
        BeanUtils.copyProperties(itempojo, wkProcessstatEntity);
        //生成雪花id
        wkProcessstatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkProcessstatEntity.setRevision(1);  //乐观锁
        this.wkProcessstatMapper.insert(wkProcessstatEntity);
        return this.getEntity(wkProcessstatEntity.getId(), wkProcessstatEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkProcessstatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProcessstatPojo update(WkProcessstatPojo wkProcessstatPojo) {
        WkProcessstatEntity wkProcessstatEntity = new WkProcessstatEntity();
        BeanUtils.copyProperties(wkProcessstatPojo, wkProcessstatEntity);
        this.wkProcessstatMapper.update(wkProcessstatEntity);
        return this.getEntity(wkProcessstatEntity.getId(), wkProcessstatEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkProcessstatMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkProcessstatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProcessstatPojo clearNull(WkProcessstatPojo wkProcessstatPojo) {
        //初始化NULL字段
        if (wkProcessstatPojo.getPid() == null) wkProcessstatPojo.setPid("");
        if (wkProcessstatPojo.getStatid() == null) wkProcessstatPojo.setStatid("");
        if (wkProcessstatPojo.getStatcode() == null) wkProcessstatPojo.setStatcode("");
        if (wkProcessstatPojo.getStatname() == null) wkProcessstatPojo.setStatname("");
        if (wkProcessstatPojo.getRownum() == null) wkProcessstatPojo.setRownum(0);
        if (wkProcessstatPojo.getCustom1() == null) wkProcessstatPojo.setCustom1("");
        if (wkProcessstatPojo.getCustom2() == null) wkProcessstatPojo.setCustom2("");
        if (wkProcessstatPojo.getCustom3() == null) wkProcessstatPojo.setCustom3("");
        if (wkProcessstatPojo.getCustom4() == null) wkProcessstatPojo.setCustom4("");
        if (wkProcessstatPojo.getCustom5() == null) wkProcessstatPojo.setCustom5("");
        if (wkProcessstatPojo.getCustom6() == null) wkProcessstatPojo.setCustom6("");
        if (wkProcessstatPojo.getCustom7() == null) wkProcessstatPojo.setCustom7("");
        if (wkProcessstatPojo.getCustom8() == null) wkProcessstatPojo.setCustom8("");
        if (wkProcessstatPojo.getCustom9() == null) wkProcessstatPojo.setCustom9("");
        if (wkProcessstatPojo.getCustom10() == null) wkProcessstatPojo.setCustom10("");
        if (wkProcessstatPojo.getTenantid() == null) wkProcessstatPojo.setTenantid("");
        if (wkProcessstatPojo.getRevision() == null) wkProcessstatPojo.setRevision(0);
        return wkProcessstatPojo;
    }
}
