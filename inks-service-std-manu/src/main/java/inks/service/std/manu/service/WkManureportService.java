package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkManureportPojo;
import inks.service.std.manu.domain.pojo.WkManureportitemPojo;
import inks.service.std.manu.domain.pojo.WkManureportitemdetailPojo;

import java.util.List;

/**
 * 生产报工(WkManureport)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-17 16:16:42
 */
public interface WkManureportService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkManureportPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkManureportitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkManureportPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkManureportPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkManureportPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkManureportPojo 实例对象
     * @return 实例对象
     */
    WkManureportPojo insert(WkManureportPojo wkManureportPojo);

    /**
     * 修改数据
     *
     * @param wkManureportpojo 实例对象
     * @return 实例对象
     */
    WkManureportPojo update(WkManureportPojo wkManureportpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkManureportPojo 实例对象
     * @return 实例对象
     */
    WkManureportPojo approval(WkManureportPojo wkManureportPojo);

    // 根据加工单汇总报工总数
    ChartPojo getSumQtyByWork(String code, String key, String tid);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    WkManureportPojo disannul(List<WkManureportitemPojo> lst, Integer type, LoginUser loginUser);

    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    WkManureportPojo closed(List<WkManureportitemPojo> lst, Integer type, LoginUser loginUser);

    void updatePrintcount(WkManureportPojo billPrintPojo);
}
