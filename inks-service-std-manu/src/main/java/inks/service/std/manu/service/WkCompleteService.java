package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkCompletePojo;
import inks.service.std.manu.domain.pojo.WkCompleteitemdetailPojo;

/**
 * 完工验收(WkComplete)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-18 15:17:24
 */
public interface WkCompleteService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCompletePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCompleteitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCompletePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCompletePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCompletePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkCompletePojo 实例对象
     * @return 实例对象
     */
    WkCompletePojo insert(WkCompletePojo wkCompletePojo);

    /**
     * 修改数据
     *
     * @param wkCompletepojo 实例对象
     * @return 实例对象
     */
    WkCompletePojo update(WkCompletePojo wkCompletepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkCompletePojo 实例对象
     * @return 实例对象
     */
    WkCompletePojo approval(WkCompletePojo wkCompletePojo);
}
