package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipepfinishingitemPojo;

import java.util.List;

/**
 * 工序收货项目(WkWipepfinishingitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-04 11:13:55
 */
public interface WkWipepfinishingitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipepfinishingitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipepfinishingitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipepfinishingitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkWipepfinishingitemPojo 实例对象
     * @return 实例对象
     */
    WkWipepfinishingitemPojo insert(WkWipepfinishingitemPojo wkWipepfinishingitemPojo);

    /**
     * 修改数据
     *
     * @param wkWipepfinishingitempojo 实例对象
     * @return 实例对象
     */
    WkWipepfinishingitemPojo update(WkWipepfinishingitemPojo wkWipepfinishingitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkWipepfinishingitempojo 实例对象
     * @return 实例对象
     */
    WkWipepfinishingitemPojo clearNull(WkWipepfinishingitemPojo wkWipepfinishingitempojo);
}
