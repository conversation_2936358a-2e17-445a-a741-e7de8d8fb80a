package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipnotemergeEntity;
import inks.service.std.manu.domain.pojo.WkWipnotemergePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * WIP合并(WkWipnotemerge)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-13 09:51:14
 */
@Mapper
public interface WkWipnotemergeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipnotemergePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipnotemergePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param wkWipnotemergeEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipnotemergeEntity wkWipnotemergeEntity);

    
    /**
     * 修改数据
     *
     * @param wkWipnotemergeEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipnotemergeEntity wkWipnotemergeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int updateMergeid(@Param("mergeIds")String mergeIds, @Param("insertId")String insertId, @Param("tid")String tid);

    WkWipnotemergePojo getEntityByMachItemid(@Param("machitemid")String machitemid, @Param("tid")String tenantid);

}

