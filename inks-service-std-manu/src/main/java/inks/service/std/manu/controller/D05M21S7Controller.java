package inks.service.std.manu.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkProccyclePojo;
import inks.service.std.manu.service.WkProccycleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 工序周期(Wk_ProcCycle)表控制层
 *
 * <AUTHOR>
 * @since 2024-11-06 16:32:31
 */
@RestController
@RequestMapping("D05M21S7")
@Api(tags = "D05M21S7:工序周期")
public class D05M21S7Controller extends WkProccycleController {
    @Resource
    private WkProccycleService wkProccycleService;
    @Resource
    private TokenService tokenService;

    /**
     * @return WkProccyclePojo
     * @Description 通过工序id和周期类型查询工序周期
     * <AUTHOR>
     * @param[1] wpid 工序id
     * @param[2] type 周期类型 0全程 1前置 2后置
     * @time 2023/7/20 13:14
     */
    @ApiOperation(value = " 通过wpid和type获取工序周期", notes = "获取工序周期详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByWpidAndType", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_ProcCycle.List")
    public R<WkProccyclePojo> getEntityByWpidAndType(String wpid, int type) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkProccycleService.getEntityByWpidAndType(wpid, type, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
