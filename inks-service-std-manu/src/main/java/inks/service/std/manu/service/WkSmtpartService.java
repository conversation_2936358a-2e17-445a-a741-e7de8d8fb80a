package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSmtpartPojo;
import inks.service.std.manu.domain.pojo.WkSmtpartitemdetailPojo;

/**
 * SMT上料表(WkSmtpart)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-12 13:59:10
 */
public interface WkSmtpartService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSmtpartPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSmtpartitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSmtpartPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSmtpartPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSmtpartPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSmtpartPojo 实例对象
     * @return 实例对象
     */
    WkSmtpartPojo insert(WkSmtpartPojo wkSmtpartPojo);

    /**
     * 修改数据
     *
     * @param wkSmtpartpojo 实例对象
     * @return 实例对象
     */
    WkSmtpartPojo update(WkSmtpartPojo wkSmtpartpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkSmtpartPojo 实例对象
     * @return 实例对象
     */
    WkSmtpartPojo approval(WkSmtpartPojo wkSmtpartPojo);


}
