package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSubcontractPojo;
import inks.service.std.manu.domain.pojo.WkSubcontractitemPojo;
import inks.service.std.manu.domain.pojo.WkSubcontractitemdetailPojo;

import java.util.List;

/**
 * 委制单据(WkSubcontract)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 20:36:28
 */
public interface WkSubcontractService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSubcontractPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSubcontractitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSubcontractPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSubcontractPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSubcontractPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSubcontractPojo 实例对象
     * @return 实例对象
     */
    WkSubcontractPojo insert(WkSubcontractPojo wkSubcontractPojo);

    /**
     * 修改数据
     *
     * @param wkSubcontractpojo 实例对象
     * @return 实例对象
     */
    WkSubcontractPojo update(WkSubcontractPojo wkSubcontractpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkSubcontractPojo 实例对象
     * @return 实例对象
     */
    WkSubcontractPojo approval(WkSubcontractPojo wkSubcontractPojo);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    WkSubcontractPojo disannul(List<WkSubcontractitemPojo> lst, Integer type, LoginUser loginUser);

    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    WkSubcontractPojo closed(List<WkSubcontractitemPojo> lst, Integer type, LoginUser loginUser);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);

    void updatePrintcount(WkSubcontractPojo billPrintPojo);
}
