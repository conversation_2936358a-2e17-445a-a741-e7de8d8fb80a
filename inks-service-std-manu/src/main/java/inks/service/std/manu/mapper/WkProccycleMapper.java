package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkProccyclePojo;
import inks.service.std.manu.domain.WkProccycleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工序周期(Wk_ProcCycle)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-06 16:32:32
 */
@Mapper
public interface WkProccycleMapper {

    WkProccyclePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<WkProccyclePojo> getPageList(QueryParam queryParam);

    int insert(WkProccycleEntity wkProccycleEntity);

    int update(WkProccycleEntity wkProccycleEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    /**
     * @Description 通过工序id和周期类型查询工序周期
     * <AUTHOR>
     * @param[1] wpid 工序id
     * @param[2] type 周期类型 0全程 1前置 2后置
     * @return WkProccyclePojo
     * @time 2023/7/20 13:14
     */
    WkProccyclePojo getEntityByWpidAndType(@Param("wpid") String wpid, @Param("type") int type, @Param("tid") String tid);}

