package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkCostbudgetitemEntity;
import inks.service.std.manu.domain.pojo.WkCostbudgetitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成本预测Item(WkCostbudgetitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-03 19:16:38
 */
 @Mapper
public interface WkCostbudgetitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCostbudgetitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkCostbudgetitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkCostbudgetitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkCostbudgetitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkCostbudgetitemEntity wkCostbudgetitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkCostbudgetitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkCostbudgetitemEntity wkCostbudgetitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

