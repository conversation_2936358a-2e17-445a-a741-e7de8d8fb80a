package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSteppriceEntity;
import inks.service.std.manu.domain.pojo.WkSteppricePojo;
import inks.service.std.manu.domain.pojo.WkSteppriceitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 阶梯工价(WkStepprice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-26 16:21:18
 */
@Mapper
public interface WkSteppriceMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSteppricePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSteppriceitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSteppricePojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param wkSteppriceEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSteppriceEntity wkSteppriceEntity);

    
    /**
     * 修改数据
     *
     * @param wkSteppriceEntity 实例对象
     * @return 影响行数
     */
    int update(WkSteppriceEntity wkSteppriceEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param wkSteppricePojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(WkSteppricePojo wkSteppricePojo);
                                                                                         /**
     * 修改数据
     *
     * @param wkSteppriceEntity 实例对象
     * @return 影响行数
     */
    int approval(WkSteppriceEntity wkSteppriceEntity);

    String getidBySpuJson(@Param("goodsid")String goodsid, @Param("wpid")String wpid, @Param("sql")String sql, @Param("size")Integer size, @Param("tid")String tid);

    Double getPrice(@Param("id")String id, @Param("quantity")String quantity, @Param("tid")String tid);

    List<Map<String, Object>> getMachItemList(@Param("id")String id, @Param("tid")String tenantid);
}

