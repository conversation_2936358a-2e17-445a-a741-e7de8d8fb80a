package inks.service.std.manu.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSectionitemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 生产工段工序子表(WkSectionitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-28 11:21:43
 */
public interface WkSectionitemService {

    WkSectionitemPojo getEntity(String key,String tid);

    PageInfo<WkSectionitemPojo> getPageList(QueryParam queryParam);

    List<WkSectionitemPojo> getList(String Pid,String tid);  

    WkSectionitemPojo insert(WkSectionitemPojo wkSectionitemPojo);

    WkSectionitemPojo update(WkSectionitemPojo wkSectionitempojo);

    int delete(String key,String tid);

    WkSectionitemPojo clearNull(WkSectionitemPojo wkSectionitempojo);
}
