package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.HiWipqtyPojo;
import inks.service.std.manu.service.HiWipqtyService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * Hi生产过数(Hi_WipQty)表控制层
 *
 * <AUTHOR>
 * @since 2023-10-16 16:20:43
 */

public class HiWipqtyController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(HiWipqtyController.class);
    /**
     * 服务对象
     */
    @Resource
    private HiWipqtyService hiWipqtyService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    @Resource
    private SystemFeignService systemFeignService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取生产过数详细信息", notes = "获取生产过数详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Hi_WipQty.List")
    public R<HiWipqtyPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.hiWipqtyService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipQty.List")
    public R<PageInfo<HiWipqtyPojo>> getPageList(@RequestBody String json, String wpid, String dir) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Hi_WipQty.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (wpid != null) {
                qpfilter += " and Hi_WipQty.wpid='" + wpid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.hiWipqtyService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增生产过数", notes = "新增生产过数", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipQty.Add")
    public R<HiWipqtyPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            HiWipqtyPojo hiWipqtyPojo = JSONArray.parseObject(json, HiWipqtyPojo.class);
            //生成单据编码
            R r = systemFeignService.getBillCode("DxxMxxB1", loginUser.getToken());
            if (r.getCode() == 200)
                hiWipqtyPojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            hiWipqtyPojo.setCreateby(loginUser.getRealName());   // 创建者
            hiWipqtyPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            hiWipqtyPojo.setCreatedate(new Date());   // 创建时间
            hiWipqtyPojo.setLister(loginUser.getRealname());   // 制表
            hiWipqtyPojo.setListerid(loginUser.getUserid());    // 制表id
            hiWipqtyPojo.setModifydate(new Date());   //修改时间
            hiWipqtyPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.hiWipqtyService.insert(hiWipqtyPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改生产过数", notes = "修改生产过数", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Hi_WipQty.Edit")
    public R<HiWipqtyPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            HiWipqtyPojo hiWipqtyPojo = JSONArray.parseObject(json, HiWipqtyPojo.class);
            hiWipqtyPojo.setLister(loginUser.getRealname());   // 制表
            hiWipqtyPojo.setListerid(loginUser.getUserid());    // 制表id  
            hiWipqtyPojo.setTenantid(loginUser.getTenantid());   //租户id
            hiWipqtyPojo.setModifydate(new Date());   //修改时间
//            hiWipqtyPojo.setAssessor(""); // 审核员
//            hiWipqtyPojo.setAssessorid(""); // 审核员id
//            hiWipqtyPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.hiWipqtyService.update(hiWipqtyPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除生产过数", notes = "删除生产过数", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Hi_WipQty.Delete")
    public R<Integer> delete(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.hiWipqtyService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Hi_WipQty.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        HiWipqtyPojo hiWipqtyPojo = this.hiWipqtyService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(hiWipqtyPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

