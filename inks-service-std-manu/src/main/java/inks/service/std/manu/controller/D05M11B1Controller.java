package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.constant.MyConstant;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.mapper.WkMrpMapper;
import inks.service.std.manu.service.WkMrpService;
import inks.service.std.manu.service.WkMrpitemService;
import inks.service.std.manu.service.WkMrpobjService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * MRP运算(Wk_Mrp)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 21:00:33
 */
@RestController
@RequestMapping("D05M11B1")
@Api(tags = "D05M11B1:MRP运算")
public class D05M11B1Controller extends WkMrpController {

    private final String moduleCode = "D05M11B1";
    @Resource
    private WkMrpService wkMrpService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkMrpitemService wkMrpitemService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkMrpobjService wkMrpobjService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private WkMrpMapper wkMrpMapper;

    @ApiOperation(value = " 新增MRP运算", notes = "新增MRP运算", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
    public R<WkMrpPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkMrpPojo wkMrpPojo = JSONArray.parseObject(json, WkMrpPojo.class);
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Wk_Mrp", null, loginUser.getTenantid());
            wkMrpPojo.setRefno(refno);
            wkMrpPojo.setCreateby(loginUser.getRealname());   // 创建者
            wkMrpPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkMrpPojo.setCreatedate(new Date());   // 创建时间
            wkMrpPojo.setLister(loginUser.getRealname());   // 制表
            wkMrpPojo.setListerid(loginUser.getUserid());    // 制表id
            wkMrpPojo.setModifydate(new Date());   //修改时间
            wkMrpPojo.setTenantid(loginUser.getTenantid());   //租户id
            //生成id
            String id = inksSnowflake.getSnowflake().nextIdStr();
            wkMrpPojo.setId(id);
            WkMrpPojo insert = this.wkMrpService.insert(wkMrpPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "/getPageList基础上，把item改为Wk_MrpObj信息", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getObjPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.List")
    public R<PageInfo<WkMrpobjdetailPojo>> getObjPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Mrp.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";

            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkMrpService.getObjPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询物料关联的PNL尺寸 传入key(即objid)：根据objid,去查无children的item行(无bomid)，查出来的行根据itemparentid查到父亲的货品加pnl尺寸，父亲货品为pgoods。\n" +
            "返回的格式obj内容是主表，子表为查出来的list。", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getMatListAndParent", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.List")
    public R<WkMrpobjdetailPojo> getMatListAndParent(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkMrpService.getMatListAndParent(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询物料关联的PNL尺寸 传入ids(即mrp.ids)：根据objid,去查无children的item行(无bomid)，查出来的行根据itemparentid查到父亲的货品加pnl尺寸，父亲货品为pgoods。\n" +
            "返回的格式obj内容是主表，子表为查出来的list。", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getMatListAndParentByids", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.List")
    public R<WkMrpobjdetailPojo> getMatListAndParentByids(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<String> mrpids = JSON.parseObject(json, new TypeReference<List<String>>() {
            });
            if (CollectionUtils.isEmpty(mrpids)) {
                return R.fail("未传入mrpids");
            }
            return R.ok(this.wkMrpService.getMatListAndParentByids(mrpids, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取MRP运算详细信息", notes = "获取MRP运算详细信息,key=Mrp.id", produces = "application/json")
    @RequestMapping(value = "/pullItemList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
    public R<List<WkMrpitemPojo>> pullItemList(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            if (loginUser == null) {
                R.fail("用户未登录");
            }
            List<WkMrpobjPojo> lstobj = this.wkMrpobjService.getList(key, tid);
            if (lstobj.isEmpty()) {
                R.fail("未找到单据项目");
            }
            //module.manu.mrpqtyupper 上进位 “true”或“false”
            //module.manu.mrpqtydec 保留几位小数 0到10
            String mrpqtyupper = systemFeignService.getConfigValue("module.manu.mrpqtyupper", tid, loginUser.getToken()).getData();
            String data = systemFeignService.getConfigValue("module.manu.mrpqtydec", tid, loginUser.getToken()).getData();
            int mrpqtydec = (StringUtils.isNotBlank(data)) ? Integer.parseInt(data) : 0;
            return R.ok(this.wkMrpService.pullItemList(lstobj, lstobj.get(0).getPid(), mrpqtyupper, mrpqtydec));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //-----------------------------------/pullItemList的异步版本 *3---------------------------------------
    @ApiOperation(value = "开始异步:获取MRP运算详细信息", notes = "获取MRP运算详细信息,key=Mrp.id", produces = "application/json")
    @RequestMapping(value = "/pullItemListStart", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
    public R<String> pullItemListStart(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            if (loginUser == null) {
                R.fail("用户未登录");
            }
            String redisKey = UUID.randomUUID().toString();
            // redis锁，锁名：
            String lockKey = MyConstant.MRP_LOCK + key;
            // 加锁，设置6秒自动过期，防止死锁
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, 1, 6, TimeUnit.MINUTES);
            if (Boolean.FALSE.equals(locked)) {
                return R.fail("正在Bom拉取中...请勿重复点击！");
            }
            List<WkMrpobjPojo> lstobj = this.wkMrpobjService.getList(key, tid);
            if (lstobj.isEmpty()) {
                R.fail("未找到单据项目");
            }
            //module.manu.mrpqtyupper 上进位 “true”或“false”
            //module.manu.mrpqtydec 保留几位小数 0到10
            String mrpqtyupper = systemFeignService.getConfigValue("module.manu.mrpqtyupper", tid, loginUser.getToken()).getData();
            String data = systemFeignService.getConfigValue("module.manu.mrpqtydec", tid, loginUser.getToken()).getData();
            int mrpqtydec = (StringUtils.isNotBlank(data)) ? Integer.parseInt(data) : 0;
            // 开始异步:
            this.wkMrpService.pullItemListStart(redisKey, lockKey, lstobj, lstobj.get(0).getPid(), mrpqtyupper, mrpqtydec, loginUser);
            return R.ok(redisKey);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "获取异步状态", notes = "getAsyncState", produces = "application/json")
    @RequestMapping(value = "/getAsyncState", method = RequestMethod.GET)
    public R<Map<String, Object>> getAsyncState(@RequestParam String rediskey) {
        return R.ok(this.redisService.getCacheObject(MyConstant.ASYNC_MRP_PULLITEMLIST_STATE + rediskey));
    }

    @ApiOperation(value = "根据Pid获取MrpItem的List", produces = "application/json")
    @RequestMapping(value = "/getItemList", method = RequestMethod.GET)
    public R<List<WkMrpitemPojo>> getItemList(String key) {
        LoginUser loginUser = this.tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(wkMrpitemService.getList(key, loginUser.getTenantid()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
//---------------------------------------------------------------------------------------------------

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "重拉BOM表，刷新MrpItem(update了MrpItem表),cmd='cud' create update delete不传默认cud", notes = "重拉BOM表，刷新MrpItem,key=Mrp.id,输出Map.num为更新数，Map.list为新内容", produces = "application/json")
    @RequestMapping(value = "/refreshItemList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Mrp.Edit")
    public R<Map<String, Object>> refreshItemList(String key, String cmd) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            if (StringUtils.isBlank(cmd)) {
                cmd = "cud";
            }
            //module.manu.mrpqtyupper 上进位 “true”或“false”
            //module.manu.mrpqtydec 保留几位小数 0到10
            String mrpqtyupper = systemFeignService.getConfigValue("module.manu.mrpqtyupper", tid, loginUser.getToken()).getData();
            String data = systemFeignService.getConfigValue("module.manu.mrpqtydec", tid, loginUser.getToken()).getData();
            int mrpqtydec = (StringUtils.isNotBlank(data)) ? Integer.parseInt(data) : 0;
            return R.ok(this.wkMrpService.refreshItemList(key, cmd, mrpqtyupper, mrpqtydec, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "重拉BOM表，对比MrpItem(仅做对比 不update)", notes = "重拉BOM表，刷新MrpItem,key=Mrp.id,输出Map.num为更新数，Map.list为新内容", produces = "application/json")
    @RequestMapping(value = "/contrastItemList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Mrp.Edit")
    public R<List<WkMrpitemPojo>> contrastItemList(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            //module.manu.mrpqtyupper 上进位 “true”或“false”
            //module.manu.mrpqtydec 保留几位小数 0到10
            String mrpqtyupper = systemFeignService.getConfigValue("module.manu.mrpqtyupper", tid, loginUser.getToken()).getData();
            String data = systemFeignService.getConfigValue("module.manu.mrpqtydec", tid, loginUser.getToken()).getData();
            int mrpqtydec = (StringUtils.isNotBlank(data)) ? Integer.parseInt(data) : 0;
            return R.ok(this.wkMrpService.contrastItemList(key, mrpqtyupper, mrpqtydec, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    /**
//     * 通过主键查询单条数据
//     *
//     * @param key 主键
//     * @return 单条数据
//     */
//    @ApiOperation(value=" 获取MRP运算详细信息", notes="获取MRP运算详细信息", produces="application/json")
//    @RequestMapping(value="/pullMrpAll",method= RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
//    public R<List<WkMrpitemPojo>> pullMrpAll(String key) {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = this.tokenService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.wkMrpService.pullMrpAll(key, loginUser.getTenantid()));
//        }catch (Exception e){
//            return R.fail(e.getMessage());
//        }
//    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取MRP运算详细信息", notes = "获取MRP运算详细信息", produces = "application/json")
    @RequestMapping(value = "/pullMrpitem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
    public R<WkMrpitemPojo> pullMrpitem(@RequestBody(required = false) String json, String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            MrpCalculationPojo mrpCalculationPojo = JSON.parseObject(json, new TypeReference<MrpCalculationPojo>() {
            });
            WkMrpitemPojo wkMrpitemPojo = wkMrpitemService.getEntity(key, tid);
            //module.manu.mrpqtyupper 上进位 “true”或“false”
            //module.manu.mrpqtydec 保留几位小数 0到10
            String mrpqtyupper = systemFeignService.getConfigValue("module.manu.mrpqtyupper", tid, loginUser.getToken()).getData();
            String data = systemFeignService.getConfigValue("module.manu.mrpqtydec", tid, loginUser.getToken()).getData();
            int mrpqtydec = (StringUtils.isNotBlank(data)) ? Integer.parseInt(data) : 0;
            WkMrpitemPojo wkMrpitemPojoDB = this.wkMrpService.pullMrpitem(wkMrpitemPojo, mrpCalculationPojo, mrpqtyupper, mrpqtydec, tid);
            //子表全部创建后，刷新主表的5个Count
            this.wkMrpMapper.syncMrpCount(wkMrpitemPojoDB.getPid(), tid);
            return R.ok(wkMrpitemPojoDB);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取MRP运算详细信息，返回state_redisKey", notes = "获取MRP运算详细信息", produces = "application/json")
    @RequestMapping(value = "/pullMrpitemByMrpidStart", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
    public R<String> pullMrpitemByMrpid(@RequestBody(required = false) String json, String mrpid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MrpCalculationPojo mrpCalculationPojo = JSON.parseObject(json, new TypeReference<MrpCalculationPojo>() {
            });
            //module.manu.mrpqtyupper 上进位 “true”或“false”
            //module.manu.mrpqtydec 保留几位小数 0到10
            String mrpqtyupper = systemFeignService.getConfigValue("module.manu.mrpqtyupper", loginUser.getTenantid(), loginUser.getToken()).getData();
            String data = systemFeignService.getConfigValue("module.manu.mrpqtydec", loginUser.getTenantid(), loginUser.getToken()).getData();
            int mrpqtydec = (StringUtils.isNotBlank(data)) ? Integer.parseInt(data) : 0;
            return R.ok(this.wkMrpService.pullMrpitemByMrpidStart(mrpid, mrpCalculationPojo, mrpqtyupper, mrpqtydec, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 根据销售订单的mrpid查到对应的Mrp记录，返回Mrpitem表attrcode等于外购的数据。
     * 接口传参：销售订单itemid的数组。
     * 接口返回：Mrpitem表attrcode等于外购，加一个AttributeJson字段，AttributeJson的内容为Wk_MrpObj的，
     * 可以根据Wk_MrpItem的mrpobjid去查AttributeJson。
     */
    @ApiOperation(value = " 销售订单查询对应Mrp运算的外购item", notes = "获取MRP运算详细信息", produces = "application/json")
    @RequestMapping(value = "/getMrpItemListByMachItemids", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
    public R<List<WkMrpitemPojo>> getMrpItemListByMachItemids(@RequestBody(required = false) String json) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //json转List<String>
            List<String> machitems = JSON.parseObject(json, new TypeReference<List<String>>() {
            });
            return R.ok(this.wkMrpitemService.getMrpItemListByMachItemids(machitems, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
