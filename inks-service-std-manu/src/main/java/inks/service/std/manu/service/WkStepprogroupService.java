package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkStepprogroupPojo;
import inks.service.std.manu.domain.pojo.WkStepprogroupitemdetailPojo;

/**
 * 阶梯制程(WkStepprogroup)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-01 16:33:33
 */
public interface WkStepprogroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStepprogroupPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkStepprogroupitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStepprogroupPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkStepprogroupPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkStepprogroupPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkStepprogroupPojo 实例对象
     * @return 实例对象
     */
    WkStepprogroupPojo insert(WkStepprogroupPojo wkStepprogroupPojo);

    /**
     * 修改数据
     *
     * @param wkStepprogrouppojo 实例对象
     * @return 实例对象
     */
    WkStepprogroupPojo update(WkStepprogroupPojo wkStepprogrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkStepprogroupPojo 实例对象
     * @return 实例对象
     */
    WkStepprogroupPojo approval(WkStepprogroupPojo wkStepprogroupPojo);
}
