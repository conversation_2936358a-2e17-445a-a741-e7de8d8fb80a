package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSccompleteitemEntity;
import inks.service.std.manu.domain.pojo.WkSccompleteitemPojo;
import inks.service.std.manu.mapper.WkSccompleteitemMapper;
import inks.service.std.manu.service.WkSccompleteitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 验收项目(WkSccompleteitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-07 11:01:57
 */
@Service("wkSccompleteitemService")
public class WkSccompleteitemServiceImpl implements WkSccompleteitemService {
    @Resource
    private WkSccompleteitemMapper wkSccompleteitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSccompleteitemPojo getEntity(String key, String tid) {
        return this.wkSccompleteitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSccompleteitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSccompleteitemPojo> lst = wkSccompleteitemMapper.getPageList(queryParam);
            PageInfo<WkSccompleteitemPojo> pageInfo = new PageInfo<WkSccompleteitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkSccompleteitemPojo> getList(String Pid, String tid) {
        try {
            List<WkSccompleteitemPojo> lst = wkSccompleteitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkSccompleteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccompleteitemPojo insert(WkSccompleteitemPojo wkSccompleteitemPojo) {
        //初始化item的NULL
        WkSccompleteitemPojo itempojo = this.clearNull(wkSccompleteitemPojo);
        WkSccompleteitemEntity wkSccompleteitemEntity = new WkSccompleteitemEntity();
        BeanUtils.copyProperties(itempojo, wkSccompleteitemEntity);
        //生成雪花id
        wkSccompleteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkSccompleteitemEntity.setRevision(1);  //乐观锁
        this.wkSccompleteitemMapper.insert(wkSccompleteitemEntity);
        return this.getEntity(wkSccompleteitemEntity.getId(), wkSccompleteitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSccompleteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccompleteitemPojo update(WkSccompleteitemPojo wkSccompleteitemPojo) {
        WkSccompleteitemEntity wkSccompleteitemEntity = new WkSccompleteitemEntity();
        BeanUtils.copyProperties(wkSccompleteitemPojo, wkSccompleteitemEntity);
        this.wkSccompleteitemMapper.update(wkSccompleteitemEntity);
        return this.getEntity(wkSccompleteitemEntity.getId(), wkSccompleteitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkSccompleteitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkSccompleteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccompleteitemPojo clearNull(WkSccompleteitemPojo wkSccompleteitemPojo) {
        //初始化NULL字段
        if (wkSccompleteitemPojo.getPid() == null) wkSccompleteitemPojo.setPid("");
        if (wkSccompleteitemPojo.getWorkdate() == null) wkSccompleteitemPojo.setWorkdate("");
        if (wkSccompleteitemPojo.getWorkuid() == null) wkSccompleteitemPojo.setWorkuid("");
        if (wkSccompleteitemPojo.getWorkitemid() == null) wkSccompleteitemPojo.setWorkitemid("");
        if (wkSccompleteitemPojo.getGoodsid() == null) wkSccompleteitemPojo.setGoodsid("");
        if (wkSccompleteitemPojo.getItemcode() == null) wkSccompleteitemPojo.setItemcode("");
        if (wkSccompleteitemPojo.getItemname() == null) wkSccompleteitemPojo.setItemname("");
        if (wkSccompleteitemPojo.getItemspec() == null) wkSccompleteitemPojo.setItemspec("");
        if (wkSccompleteitemPojo.getItemunit() == null) wkSccompleteitemPojo.setItemunit("");
        if (wkSccompleteitemPojo.getQuantity() == null) wkSccompleteitemPojo.setQuantity(0D);
        if (wkSccompleteitemPojo.getSubitemid() == null) wkSccompleteitemPojo.setSubitemid("");
        if (wkSccompleteitemPojo.getSubuse() == null) wkSccompleteitemPojo.setSubuse("");
        if (wkSccompleteitemPojo.getSubunit() == null) wkSccompleteitemPojo.setSubunit("");
        if (wkSccompleteitemPojo.getSubqty() == null) wkSccompleteitemPojo.setSubqty(0D);
        if (wkSccompleteitemPojo.getTaxprice() == null) wkSccompleteitemPojo.setTaxprice(0D);
        if (wkSccompleteitemPojo.getTaxamount() == null) wkSccompleteitemPojo.setTaxamount(0D);
        if (wkSccompleteitemPojo.getPrice() == null) wkSccompleteitemPojo.setPrice(0D);
        if (wkSccompleteitemPojo.getAmount() == null) wkSccompleteitemPojo.setAmount(0D);
        if (wkSccompleteitemPojo.getTaxtotal() == null) wkSccompleteitemPojo.setTaxtotal(0D);
        if (wkSccompleteitemPojo.getItemtaxrate() == null) wkSccompleteitemPojo.setItemtaxrate(0);
        if (wkSccompleteitemPojo.getStartdate() == null) wkSccompleteitemPojo.setStartdate(new Date());
        if (wkSccompleteitemPojo.getPlandate() == null) wkSccompleteitemPojo.setPlandate(new Date());
        if (wkSccompleteitemPojo.getFinishqty() == null) wkSccompleteitemPojo.setFinishqty(0D);
        if (wkSccompleteitemPojo.getFinishhour() == null) wkSccompleteitemPojo.setFinishhour(0D);
        if (wkSccompleteitemPojo.getMrbqty() == null) wkSccompleteitemPojo.setMrbqty(0D);
        if (wkSccompleteitemPojo.getEnabledmark() == null) wkSccompleteitemPojo.setEnabledmark(0);
        if (wkSccompleteitemPojo.getClosed() == null) wkSccompleteitemPojo.setClosed(0);
        if (wkSccompleteitemPojo.getRemark() == null) wkSccompleteitemPojo.setRemark("");
        if (wkSccompleteitemPojo.getStatecode() == null) wkSccompleteitemPojo.setStatecode("");
        if (wkSccompleteitemPojo.getStatedate() == null) wkSccompleteitemPojo.setStatedate(new Date());
        if (wkSccompleteitemPojo.getRownum() == null) wkSccompleteitemPojo.setRownum(0);
        if (wkSccompleteitemPojo.getMachuid() == null) wkSccompleteitemPojo.setMachuid("");
        if (wkSccompleteitemPojo.getMachitemid() == null) wkSccompleteitemPojo.setMachitemid("");
        if (wkSccompleteitemPojo.getMachgroupid() == null) wkSccompleteitemPojo.setMachgroupid("");
        if (wkSccompleteitemPojo.getMrpuid() == null) wkSccompleteitemPojo.setMrpuid("");
        if (wkSccompleteitemPojo.getMrpitemid() == null) wkSccompleteitemPojo.setMrpitemid("");
        if (wkSccompleteitemPojo.getCustomer() == null) wkSccompleteitemPojo.setCustomer("");
        if (wkSccompleteitemPojo.getCustpo() == null) wkSccompleteitemPojo.setCustpo("");
        if (wkSccompleteitemPojo.getMainplanuid() == null) wkSccompleteitemPojo.setMainplanuid("");
        if (wkSccompleteitemPojo.getMainplanitemid() == null) wkSccompleteitemPojo.setMainplanitemid("");
        if (wkSccompleteitemPojo.getCiteuid() == null) wkSccompleteitemPojo.setCiteuid("");
        if (wkSccompleteitemPojo.getCiteitemid() == null) wkSccompleteitemPojo.setCiteitemid("");
        if (wkSccompleteitemPojo.getLocation() == null) wkSccompleteitemPojo.setLocation("");
        if (wkSccompleteitemPojo.getBatchno() == null) wkSccompleteitemPojo.setBatchno("");
        if (wkSccompleteitemPojo.getAttributejson() == null) wkSccompleteitemPojo.setAttributejson("");
        if (wkSccompleteitemPojo.getAttributestr() == null) wkSccompleteitemPojo.setAttributestr("");
        if (wkSccompleteitemPojo.getInvoqty() == null) wkSccompleteitemPojo.setInvoqty(0D);
        if (wkSccompleteitemPojo.getInvoclosed() == null) wkSccompleteitemPojo.setInvoclosed(0);
        if (wkSccompleteitemPojo.getSourcetype() == null) wkSccompleteitemPojo.setSourcetype(0);
        if (wkSccompleteitemPojo.getVirtualitem() == null) wkSccompleteitemPojo.setVirtualitem(0);
     if(wkSccompleteitemPojo.getSubbypcs()==null) wkSccompleteitemPojo.setSubbypcs(0D);
     if(wkSccompleteitemPojo.getDisannulmark()==null) wkSccompleteitemPojo.setDisannulmark(0);
        if (wkSccompleteitemPojo.getCustom1() == null) wkSccompleteitemPojo.setCustom1("");
        if (wkSccompleteitemPojo.getCustom2() == null) wkSccompleteitemPojo.setCustom2("");
        if (wkSccompleteitemPojo.getCustom3() == null) wkSccompleteitemPojo.setCustom3("");
        if (wkSccompleteitemPojo.getCustom4() == null) wkSccompleteitemPojo.setCustom4("");
        if (wkSccompleteitemPojo.getCustom5() == null) wkSccompleteitemPojo.setCustom5("");
        if (wkSccompleteitemPojo.getCustom6() == null) wkSccompleteitemPojo.setCustom6("");
        if (wkSccompleteitemPojo.getCustom7() == null) wkSccompleteitemPojo.setCustom7("");
        if (wkSccompleteitemPojo.getCustom8() == null) wkSccompleteitemPojo.setCustom8("");
        if (wkSccompleteitemPojo.getCustom9() == null) wkSccompleteitemPojo.setCustom9("");
        if (wkSccompleteitemPojo.getCustom10() == null) wkSccompleteitemPojo.setCustom10("");
        if (wkSccompleteitemPojo.getTenantid() == null) wkSccompleteitemPojo.setTenantid("");
        if (wkSccompleteitemPojo.getRevision() == null) wkSccompleteitemPojo.setRevision(0);
        return wkSccompleteitemPojo;
    }
}
