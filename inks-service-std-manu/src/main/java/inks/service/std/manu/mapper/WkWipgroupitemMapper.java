package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipgroupitemEntity;
import inks.service.std.manu.domain.pojo.WkWipgroupitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * WIP分管工序(WkWipgroupitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-26 10:08:03
 */
@Mapper
public interface WkWipgroupitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipgroupitemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipgroupitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipgroupitemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param wkWipgroupitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipgroupitemEntity wkWipgroupitemEntity);


    /**
     * 修改数据
     *
     * @param wkWipgroupitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipgroupitemEntity wkWipgroupitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

}

