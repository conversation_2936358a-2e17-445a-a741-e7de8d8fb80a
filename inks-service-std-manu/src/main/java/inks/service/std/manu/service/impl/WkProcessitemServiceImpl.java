package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkProcessitemEntity;
import inks.service.std.manu.domain.pojo.WkProcessitemPojo;
import inks.service.std.manu.mapper.WkProcessitemMapper;
import inks.service.std.manu.service.WkProcessitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工序子项(WkProcessitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-26 10:05:42
 */
@Service("wkProcessitemService")
public class WkProcessitemServiceImpl implements WkProcessitemService {
    @Resource
    private WkProcessitemMapper wkProcessitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkProcessitemPojo getEntity(String key, String tid) {
        return this.wkProcessitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkProcessitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProcessitemPojo> lst = wkProcessitemMapper.getPageList(queryParam);
            PageInfo<WkProcessitemPojo> pageInfo = new PageInfo<WkProcessitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkProcessitemPojo> getList(String Pid, String tid) {
        try {
            List<WkProcessitemPojo> lst = wkProcessitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkProcessitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProcessitemPojo insert(WkProcessitemPojo wkProcessitemPojo) {
        //初始化item的NULL
        WkProcessitemPojo itempojo = this.clearNull(wkProcessitemPojo);
        WkProcessitemEntity wkProcessitemEntity = new WkProcessitemEntity();
        BeanUtils.copyProperties(itempojo, wkProcessitemEntity);

        wkProcessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkProcessitemEntity.setRevision(1);  //乐观锁
        this.wkProcessitemMapper.insert(wkProcessitemEntity);
        return this.getEntity(wkProcessitemEntity.getId(), wkProcessitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkProcessitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProcessitemPojo update(WkProcessitemPojo wkProcessitemPojo) {
        WkProcessitemEntity wkProcessitemEntity = new WkProcessitemEntity();
        BeanUtils.copyProperties(wkProcessitemPojo, wkProcessitemEntity);
        this.wkProcessitemMapper.update(wkProcessitemEntity);
        return this.getEntity(wkProcessitemEntity.getId(), wkProcessitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkProcessitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkProcessitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProcessitemPojo clearNull(WkProcessitemPojo wkProcessitemPojo) {
        //初始化NULL字段
        if (wkProcessitemPojo.getPid() == null) wkProcessitemPojo.setPid("");
        if (wkProcessitemPojo.getItemname() == null) wkProcessitemPojo.setItemname("");
        if (wkProcessitemPojo.getDefvalue() == null) wkProcessitemPojo.setDefvalue("");
        if (wkProcessitemPojo.getItemvalue() == null) wkProcessitemPojo.setItemvalue("");
        if (wkProcessitemPojo.getRequisite() == null) wkProcessitemPojo.setRequisite(0);
        if (wkProcessitemPojo.getRownum() == null) wkProcessitemPojo.setRownum(0);
        if (wkProcessitemPojo.getStonly() == null) wkProcessitemPojo.setStonly(0);
        if (wkProcessitemPojo.getCustom1() == null) wkProcessitemPojo.setCustom1("");
        if (wkProcessitemPojo.getCustom2() == null) wkProcessitemPojo.setCustom2("");
        if (wkProcessitemPojo.getCustom3() == null) wkProcessitemPojo.setCustom3("");
        if (wkProcessitemPojo.getCustom4() == null) wkProcessitemPojo.setCustom4("");
        if (wkProcessitemPojo.getCustom5() == null) wkProcessitemPojo.setCustom5("");
        if (wkProcessitemPojo.getCustom6() == null) wkProcessitemPojo.setCustom6("");
        if (wkProcessitemPojo.getCustom7() == null) wkProcessitemPojo.setCustom7("");
        if (wkProcessitemPojo.getCustom8() == null) wkProcessitemPojo.setCustom8("");
        if (wkProcessitemPojo.getCustom9() == null) wkProcessitemPojo.setCustom9("");
        if (wkProcessitemPojo.getCustom10() == null) wkProcessitemPojo.setCustom10("");
        if (wkProcessitemPojo.getTenantid() == null) wkProcessitemPojo.setTenantid("");
        if (wkProcessitemPojo.getRevision() == null) wkProcessitemPojo.setRevision(0);
        return wkProcessitemPojo;
    }
}
