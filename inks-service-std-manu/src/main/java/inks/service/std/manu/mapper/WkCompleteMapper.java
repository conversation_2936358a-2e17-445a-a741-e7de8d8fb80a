package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkCompleteEntity;
import inks.service.std.manu.domain.pojo.WkCompletePojo;
import inks.service.std.manu.domain.pojo.WkCompleteitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 完工验收(WkComplete)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-18 15:17:24
 */
@Mapper
public interface WkCompleteMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCompletePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkCompleteitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkCompletePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkCompleteEntity 实例对象
     * @return 影响行数
     */
    int insert(WkCompleteEntity wkCompleteEntity);


    /**
     * 修改数据
     *
     * @param wkCompleteEntity 实例对象
     * @return 影响行数
     */
    int update(WkCompleteEntity wkCompleteEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkCompletePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkCompletePojo wkCompletePojo);

    /**
     * 修改数据
     *
     * @param wkCompleteEntity 实例对象
     * @return 影响行数
     */
    int approval(WkCompleteEntity wkCompleteEntity);


    /**
     * 查询 被删除的Mat
     *
     * @param wkCompletePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelMatIds(WkCompletePojo wkCompletePojo);
}

