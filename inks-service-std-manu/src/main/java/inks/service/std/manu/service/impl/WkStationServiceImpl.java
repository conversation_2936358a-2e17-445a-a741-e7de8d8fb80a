package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkStationEntity;
import inks.service.std.manu.domain.WkStationitemEntity;
import inks.service.std.manu.domain.pojo.WkStationPojo;
import inks.service.std.manu.domain.pojo.WkStationitemPojo;
import inks.service.std.manu.domain.pojo.WkStationitemdetailPojo;
import inks.service.std.manu.mapper.WkStationMapper;
import inks.service.std.manu.mapper.WkStationitemMapper;
import inks.service.std.manu.service.WkStationService;
import inks.service.std.manu.service.WkStationitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 工位(WkStation)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-17 15:42:16
 */
@Service("wkStationService")
public class WkStationServiceImpl implements WkStationService {
    @Resource
    private WkStationMapper wkStationMapper;

    @Resource
    private WkStationitemMapper wkStationitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkStationitemService wkStationitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkStationPojo getEntity(String key, String tid) {
        return this.wkStationMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkStationitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkStationitemdetailPojo> lst = wkStationMapper.getPageList(queryParam);
            PageInfo<WkStationitemdetailPojo> pageInfo = new PageInfo<WkStationitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkStationPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkStationPojo wkStationPojo = this.wkStationMapper.getEntity(key, tid);
            //读取子表
            wkStationPojo.setItem(wkStationitemMapper.getList(wkStationPojo.getId(), wkStationPojo.getTenantid()));
            return wkStationPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkStationPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkStationPojo> lst = wkStationMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkStationitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkStationPojo> pageInfo = new PageInfo<WkStationPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkStationPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkStationPojo> lst = wkStationMapper.getPageTh(queryParam);
            PageInfo<WkStationPojo> pageInfo = new PageInfo<WkStationPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkStationPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkStationPojo insert(WkStationPojo wkStationPojo) {
        //初始化NULL字段
        cleanNull(wkStationPojo);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkStationEntity wkStationEntity = new WkStationEntity();
        BeanUtils.copyProperties(wkStationPojo, wkStationEntity);

        //设置id和新建日期
        wkStationEntity.setId(id);
        wkStationEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkStationMapper.insert(wkStationEntity);
        //Item子表处理
        List<WkStationitemPojo> lst = wkStationPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkStationitemPojo itemPojo = this.wkStationitemService.clearNull(lst.get(i));
                WkStationitemEntity wkStationitemEntity = new WkStationitemEntity();
                BeanUtils.copyProperties(itemPojo, wkStationitemEntity);
                //设置id和Pid
                wkStationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkStationitemEntity.setPid(id);
                wkStationitemEntity.setTenantid(wkStationPojo.getTenantid());
                wkStationitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkStationitemMapper.insert(wkStationitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkStationEntity.getId(), wkStationEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkStationPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkStationPojo update(WkStationPojo wkStationPojo) {
        //主表更改
        WkStationEntity wkStationEntity = new WkStationEntity();
        BeanUtils.copyProperties(wkStationPojo, wkStationEntity);
        this.wkStationMapper.update(wkStationEntity);
        if (wkStationPojo.getItem() != null) {
            //Item子表处理
            List<WkStationitemPojo> lst = wkStationPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkStationMapper.getDelItemIds(wkStationPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkStationitemMapper.delete(lstDelIds.get(i), wkStationEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    WkStationitemEntity wkStationitemEntity = new WkStationitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        WkStationitemPojo itemPojo = this.wkStationitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, wkStationitemEntity);
                        //设置id和Pid
                        wkStationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkStationitemEntity.setPid(wkStationEntity.getId());  // 主表 id
                        wkStationitemEntity.setTenantid(wkStationPojo.getTenantid());   // 租户id
                        wkStationitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkStationitemMapper.insert(wkStationitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkStationitemEntity);
                        wkStationitemEntity.setTenantid(wkStationPojo.getTenantid());
                        this.wkStationitemMapper.update(wkStationitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkStationEntity.getId(), wkStationEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkStationPojo wkStationPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkStationitemPojo> lst = wkStationPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkStationitemPojo wkStationitemPojo : lst) {
                this.wkStationitemMapper.delete(wkStationitemPojo.getId(), tid);
            }
        }
        this.wkStationMapper.delete(key, tid);
        return wkStationPojo.getStatname();
    }

    private static void cleanNull(WkStationPojo wkStationPojo) {
        if(wkStationPojo.getSectionid()==null) wkStationPojo.setSectionid("");
        if(wkStationPojo.getStatcode()==null) wkStationPojo.setStatcode("");
        if(wkStationPojo.getStatname()==null) wkStationPojo.setStatname("");
        if(wkStationPojo.getStattype()==null) wkStationPojo.setStattype("");
        if(wkStationPojo.getStatdesc()==null) wkStationPojo.setStatdesc("");
        if(wkStationPojo.getStaterefresh()==null) wkStationPojo.setStaterefresh(0);
        if(wkStationPojo.getRownum()==null) wkStationPojo.setRownum(0);
        if(wkStationPojo.getSummary()==null) wkStationPojo.setSummary("");
        if(wkStationPojo.getEnabledmark()==null) wkStationPojo.setEnabledmark(0);
        if(wkStationPojo.getCreateby()==null) wkStationPojo.setCreateby("");
        if(wkStationPojo.getCreatebyid()==null) wkStationPojo.setCreatebyid("");
        if(wkStationPojo.getCreatedate()==null) wkStationPojo.setCreatedate(new Date());
        if(wkStationPojo.getLister()==null) wkStationPojo.setLister("");
        if(wkStationPojo.getListerid()==null) wkStationPojo.setListerid("");
        if(wkStationPojo.getModifydate()==null) wkStationPojo.setModifydate(new Date());
        if(wkStationPojo.getDeletemark()==null) wkStationPojo.setDeletemark(0);
        if(wkStationPojo.getDeletelisterid()==null) wkStationPojo.setDeletelisterid("");
        if(wkStationPojo.getDeletelister()==null) wkStationPojo.setDeletelister("");
        if(wkStationPojo.getDeletedate()==null) wkStationPojo.setDeletedate(new Date());
        if(wkStationPojo.getDisablewip()==null) wkStationPojo.setDisablewip(0);
        if(wkStationPojo.getLoadtime()==null) wkStationPojo.setLoadtime(0D);
        if(wkStationPojo.getLoadqty()==null) wkStationPojo.setLoadqty(0D);
        if(wkStationPojo.getLoadamt()==null) wkStationPojo.setLoadamt(0D);
        if(wkStationPojo.getCustom1()==null) wkStationPojo.setCustom1("");
        if(wkStationPojo.getCustom2()==null) wkStationPojo.setCustom2("");
        if(wkStationPojo.getCustom3()==null) wkStationPojo.setCustom3("");
        if(wkStationPojo.getCustom4()==null) wkStationPojo.setCustom4("");
        if(wkStationPojo.getCustom5()==null) wkStationPojo.setCustom5("");
        if(wkStationPojo.getCustom6()==null) wkStationPojo.setCustom6("");
        if(wkStationPojo.getCustom7()==null) wkStationPojo.setCustom7("");
        if(wkStationPojo.getCustom8()==null) wkStationPojo.setCustom8("");
        if(wkStationPojo.getCustom9()==null) wkStationPojo.setCustom9("");
        if(wkStationPojo.getCustom10()==null) wkStationPojo.setCustom10("");
        if(wkStationPojo.getTenantid()==null) wkStationPojo.setTenantid("");
        if(wkStationPojo.getTenantname()==null) wkStationPojo.setTenantname("");
        if(wkStationPojo.getRevision()==null) wkStationPojo.setRevision(0);
    }


    @Override
    public List<WkStationPojo> getAllList(String tenantid) {
        return wkStationMapper.getAllList(tenantid);
    }

    @Override
    public HashMap<String, Object> getStationStateAndWk(String key, String tenantid) { //key是工位id Wk_Station.id
        HashMap<String, Object> stationStateAndWk = wkStationMapper.getStationStateAndWk(key, tenantid);
        //// 查询工位id下关联的所有设备中设备状态(UseState)是否有不正常的 20240124 改为Dm_Device.UseState = '未保养'时才算作异常
        //int abnormalCount=wkStationdevMapper.getDevsUseStateAbnormalCount(key, tenantid);
        //stationStateAndWk.put("devstate", abnormalCount==0?"正常":"异常"); //没有异常设备条数即为正常
        return stationStateAndWk;
    }

    @Override
    public String getStateJsonByWpid(String key, String tenantid) {
        return wkStationMapper.getStateJsonByWpid(key, tenantid);
    }
}
