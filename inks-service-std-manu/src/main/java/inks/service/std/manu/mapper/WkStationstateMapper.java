package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkStationstateEntity;
import inks.service.std.manu.domain.pojo.WkStationstatePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工位状态(WkStationstate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-17 15:34:48
 */
@Mapper
public interface WkStationstateMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStationstatePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkStationstatePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param wkStationstateEntity 实例对象
     * @return 影响行数
     */
    int insert(WkStationstateEntity wkStationstateEntity);

    
    /**
     * 修改数据
     *
     * @param wkStationstateEntity 实例对象
     * @return 影响行数
     */
    int update(WkStationstateEntity wkStationstateEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                                                                          }

