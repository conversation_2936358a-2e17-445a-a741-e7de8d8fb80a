package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkGrouplossrateEntity;
import inks.service.std.manu.domain.pojo.WkGrouplossratePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 车间预损率(WkGrouplossrate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-16 10:56:19
 */
@Mapper
public interface WkGrouplossrateMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkGrouplossratePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkGrouplossratePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param wkGrouplossrateEntity 实例对象
     * @return 影响行数
     */
    int insert(WkGrouplossrateEntity wkGrouplossrateEntity);

    
    /**
     * 修改数据
     *
     * @param wkGrouplossrateEntity 实例对象
     * @return 影响行数
     */
    int update(WkGrouplossrateEntity wkGrouplossrateEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                                       }

