package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipqtygroupitemEntity;
import inks.service.std.manu.domain.pojo.WkWipqtygroupitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 过数小组人员(WkWipqtygroupitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-10 11:08:19
 */
 @Mapper
public interface WkWipqtygroupitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtygroupitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipqtygroupitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipqtygroupitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkWipqtygroupitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipqtygroupitemEntity wkWipqtygroupitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkWipqtygroupitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipqtygroupitemEntity wkWipqtygroupitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

