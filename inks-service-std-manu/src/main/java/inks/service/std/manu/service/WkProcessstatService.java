package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkProcessstatPojo;

import java.util.List;

/**
 * 工序工位(WkProcessstat)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-17 16:57:23
 */
public interface WkProcessstatService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProcessstatPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkProcessstatPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkProcessstatPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkProcessstatPojo 实例对象
     * @return 实例对象
     */
    WkProcessstatPojo insert(WkProcessstatPojo wkProcessstatPojo);

    /**
     * 修改数据
     *
     * @param wkProcessstatpojo 实例对象
     * @return 实例对象
     */
    WkProcessstatPojo update(WkProcessstatPojo wkProcessstatpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkProcessstatpojo 实例对象
     * @return 实例对象
     */
    WkProcessstatPojo clearNull(WkProcessstatPojo wkProcessstatpojo);
}
