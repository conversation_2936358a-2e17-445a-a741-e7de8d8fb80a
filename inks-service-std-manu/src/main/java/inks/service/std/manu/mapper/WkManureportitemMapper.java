package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkManureportitemEntity;
import inks.service.std.manu.domain.pojo.WkManureportitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产报工明细(WkManureportitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-17 16:16:53
 */
 @Mapper
public interface WkManureportitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkManureportitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkManureportitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkManureportitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkManureportitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkManureportitemEntity wkManureportitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkManureportitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkManureportitemEntity wkManureportitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

