package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSccarryoverPojo;
import inks.service.std.manu.domain.pojo.WkSccarryoveritemdetailPojo;

/**
 * 加工结转(WkSccarryover)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-09 14:04:49
 */
public interface WkSccarryoverService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccarryoverPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSccarryoveritemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccarryoverPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSccarryoverPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSccarryoverPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSccarryoverPojo 实例对象
     * @return 实例对象
     */
    WkSccarryoverPojo insert(WkSccarryoverPojo wkSccarryoverPojo);

    /**
     * 修改数据
     *
     * @param wkSccarryoverpojo 实例对象
     * @return 实例对象
     */
    WkSccarryoverPojo update(WkSccarryoverPojo wkSccarryoverpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 新增数据
     *
     * @param wkSccarryoverpojo 实例对象
     * @return 实例对象
     */
    WkSccarryoverPojo createCarry(WkSccarryoverPojo wkSccarryoverpojo);

}
