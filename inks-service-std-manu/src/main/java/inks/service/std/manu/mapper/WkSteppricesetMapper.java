package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSteppricesetEntity;
import inks.service.std.manu.domain.pojo.WkSteppricesetPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阶梯工价设置(WkSteppriceset)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-26 16:21:54
 */
@Mapper
public interface WkSteppricesetMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSteppricesetPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSteppricesetPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param wkSteppricesetEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSteppricesetEntity wkSteppricesetEntity);

    
    /**
     * 修改数据
     *
     * @param wkSteppricesetEntity 实例对象
     * @return 影响行数
     */
    int update(WkSteppricesetEntity wkSteppricesetEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);


    WkSteppricesetPojo getSpusEntityByWpid(@Param("wpid")String wpid,@Param("tid")String tid);

    String getSpusByWpid(@Param("wpid")String wpid, @Param("tid")String tid);

    List<WkSteppricesetPojo> getAllList(@Param("tid")String tid);
}

