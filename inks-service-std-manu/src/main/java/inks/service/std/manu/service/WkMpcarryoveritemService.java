package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkMpcarryoveritemPojo;

import java.util.List;

/**
 * 结转子表(WkMpcarryoveritem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-09 09:43:09
 */
public interface WkMpcarryoveritemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMpcarryoveritemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMpcarryoveritemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMpcarryoveritemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkMpcarryoveritemPojo 实例对象
     * @return 实例对象
     */
    WkMpcarryoveritemPojo insert(WkMpcarryoveritemPojo wkMpcarryoveritemPojo);

    /**
     * 修改数据
     *
     * @param wkMpcarryoveritempojo 实例对象
     * @return 实例对象
     */
    WkMpcarryoveritemPojo update(WkMpcarryoveritemPojo wkMpcarryoveritempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkMpcarryoveritempojo 实例对象
     * @return 实例对象
     */
    WkMpcarryoveritemPojo clearNull(WkMpcarryoveritemPojo wkMpcarryoveritempojo);
}
