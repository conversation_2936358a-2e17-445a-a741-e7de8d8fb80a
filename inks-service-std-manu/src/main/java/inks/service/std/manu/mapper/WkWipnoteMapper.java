package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipnoteEntity;
import inks.service.std.manu.domain.pojo.WkWipnotePojo;
import inks.service.std.manu.domain.pojo.WkWipnoteitemPojo;
import inks.service.std.manu.domain.pojo.WkWipnoteitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * WIP记录(WkWipnote)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-06 13:57:45
 */
@Mapper
public interface WkWipnoteMapper {


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipnotePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipnoteitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipnotePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipnoteEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipnoteEntity wkWipnoteEntity);


    /**
     * 修改数据
     *
     * @param wkWipnoteEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipnoteEntity wkWipnoteEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkWipnotePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkWipnotePojo wkWipnotePojo);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipnotePojo getEntityByWorkUid(@Param("key") String key, @Param("tid") String tid);


    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateMachWipUsed(WkWipnotePojo wkWipnotePojo);

    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateWorkWipUsed(WkWipnotePojo wkWipnotePojo);

    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateUnMachWipUsed(WkWipnotePojo wkWipnotePojo);

    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateUnWorkWipUsed(WkWipnotePojo wkWipnotePojo);

    String getByWorkuidLike(@Param("workuid")String workuid, @Param("tid")String tid);

    void updatePrintcount(WkWipnotePojo billPrintPojo);

    int closedids(@Param("ids") HashSet<String> wkWipnoteidSet, @Param("type") Integer type, @Param("realname") String realname,
                  @Param("userid") String userid, @Param("date")Date date, @Param("tenantid") String tenantid);


    void updateMachWipCount(@Param("machitemid") String machitemid, @Param("tid") String tid);

    List<Map<String, String>> getAllByTid(String tid);

    int upateAttrStr(@Param("id") String id, @Param("attrStr") String attrStr, @Param("tid") String tid);

    Map<String,Object> getMaxWipItemRowNumAndId(@Param("wipid") String wipid, @Param("tid") String tid);

    List<WkWipnoteitemPojo> getSpecPcbItemListByGoodsid(@Param("goodsid") String goodsid, @Param("tid") String tenantid);

    void wipSyncWkPcsQty(@Param("wipitemid") String wipitemid, @Param("addqty") Double addqty, @Param("tenantid") String tenantid);

    void updateWkpcsqty(@Param("wkpcsqtyAdd") double v, @Param("id") String id, @Param("tid") String tid);

    void syncSheetItemWkpsqty(double wkpcsqtyAdd, String workitemid, String tid);

    int getOnlineCountByWp(String wpid, String tid);

    WkWipnotePojo getEntityByitemid(String wipitemid, String tid);

    WkWipnotePojo getPreWipNoteByGoodsid(String goodsid, Integer wkrownum, String machitemid, String tid);

    void updateWorkRefNoWorkRowNum(String wipid, String worksheetitemid, String tid);

    String getSummaryByMachitemid(String machitemid, String tid);

    String getSummaryByWorkitemid(String workitemid, String tid);

    List<WkWipnoteitemdetailPojo> getOnlinePageListBySummary(QueryParam queryParam);
}

