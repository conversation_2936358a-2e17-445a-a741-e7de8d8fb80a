package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkMainplanPojo;
import inks.service.std.manu.domain.pojo.WkMainplanitemdetailPojo;
import inks.service.std.manu.service.WkMainplanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产主计划(Wk_MainPlan)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 15:21:37
 */
@RestController
@RequestMapping("D05M04B1")
@Api(tags = "D05M04B1:生产主计划")
public class D05M04B1Controller extends WkMainplanController {

    @Resource
    private WkMainplanService wkMainplanService;

    @Resource
    private TokenService tokenService;


    @ApiOperation(value = "合并多条生产主计划子表为一条，生成一个新的主计划主子表 入参：[\"aaa\",\"bbb\"]", notes = "", produces = "application/json")
    @RequestMapping(value = "/mergeItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_MainPlan.Add")
    public R<WkMainplanPojo> mergeItem(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 拿到需要合并的主计划子表id集合并查询出来
            List<String> mainPlanItemIds = JSON.parseObject(json, new TypeReference<List<String>>() {
            });
            if (mainPlanItemIds.size() <= 1) {
                throw new BaseBusinessException("请选择需要合并的多条主计划子表");
            }
            wkMainplanService.mergeItem(mainPlanItemIds, loginUser);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineMrpPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_MainPlan.List")
    public R<PageInfo<WkMainplanitemdetailPojo>> getOnlineMrpPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_MainPlan.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Wk_MainPlanItem.Closed=0 and Wk_MainPlanItem.DisannulMark=0";
            qpfilter += " and Wk_MainPlanItem.Mrpid='' and Wk_MainPlan.Assessorid<>''";
            // 被合并的不能再查出来
            qpfilter += " and Wk_MainPlanItem.MergeMark<>2 and Wk_MainPlanItem.MergeMark<>3";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkMainplanService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineStartPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_MainPlan.List")
    public R<PageInfo<WkMainplanitemdetailPojo>> getOnlineStartPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_MainPlan.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Wk_MainPlanItem.Closed=0 and Wk_MainPlanItem.DisannulMark=0";
            qpfilter += " and Wk_MainPlanItem.StartQty<Wk_MainPlanItem.Quantity and Wk_MainPlan.Assessorid<>''";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkMainplanService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_MainPlan.List")
    public R<PageInfo<WkMainplanitemdetailPojo>> getOnlinePageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_MainPlan.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Wk_MainPlanItem.Closed=0 and Wk_MainPlanItem.DisannulMark=0";
            qpfilter += " and Wk_MainPlanItem.FinishQty+Wk_MainPlanItem.MrbQty<Wk_MainPlanItem.Quantity and Wk_MainPlan.Assessorid<>''";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkMainplanService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
