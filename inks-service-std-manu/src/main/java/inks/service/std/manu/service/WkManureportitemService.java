package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkManureportitemPojo;

import java.util.List;

/**
 * 生产报工明细(WkManureportitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-17 16:16:53
 */
public interface WkManureportitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkManureportitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkManureportitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkManureportitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkManureportitemPojo 实例对象
     * @return 实例对象
     */
    WkManureportitemPojo insert(WkManureportitemPojo wkManureportitemPojo);

    /**
     * 修改数据
     *
     * @param wkManureportitempojo 实例对象
     * @return 实例对象
     */
    WkManureportitemPojo update(WkManureportitemPojo wkManureportitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkManureportitempojo 实例对象
     * @return 实例对象
     */
    WkManureportitemPojo clearNull(WkManureportitemPojo wkManureportitempojo);
}
