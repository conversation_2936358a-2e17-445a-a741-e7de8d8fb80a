package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWorksheetEntity;
import inks.service.std.manu.domain.pojo.MatSpecpcbitemPojo;
import inks.service.std.manu.domain.pojo.WkWorksheetPojo;
import inks.service.std.manu.domain.pojo.WkWorksheetitemdetailPojo;
import inks.service.std.manu.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * 厂制工单(WkWorksheet)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-16 08:01:19
 */
@Mapper
public interface WkWorksheetMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWorksheetitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWorksheetPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWorksheetEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWorksheetEntity wkWorksheetEntity);


    /**
     * 修改数据
     *
     * @param wkWorksheetEntity 实例对象
     * @return 影响行数
     */
    int update(WkWorksheetEntity wkWorksheetEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkWorksheetPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkWorksheetPojo wkWorksheetPojo);

    /**
     * 查询 被删除的Item
     *
     * @param wkWorksheetPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelMatIds(WkWorksheetPojo wkWorksheetPojo);

    /**
     * 修改数据
     *
     * @param wkWorksheetEntity 实例对象
     * @return 影响行数
     */
    int approval(WkWorksheetEntity wkWorksheetEntity);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDisannulCount(@Param("key") String key, @Param("tid") String tid);


    /**
     * 修改完工记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMrpWsFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetPojo getEntityByRefNo(@Param("key") String key, @Param("tid") String tid);

    WkWorksheetPojo getEntityByItemid(String itemid, String tid);
    Map<String, Object> getMachitemWKqty(@Param("key")String key,  @Param("tid")String tid);

    int updateMachWKQuantity(@Param("key") String key,@Param("tid")String tid);

    int updateMergeCount(@Param("pid")String pid,@Param("tid")String tid);

    int updateMatMergeCount(@Param("id") String id, @Param("size") int size, @Param("tid") String tid);
    // 查询Item是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid, @Param("tid") String tid);

    void updatePrintcount(WkWorksheetPojo billPrintPojo);

    int updateFinishCountByIds(@Param("ids") HashSet<String> worksheetidSet, @Param("realname") String realname,
                               @Param("userid") String userid, @Param("now") Date now, @Param("tid") String tid);

    void updateMainPlanStartQty(@Param("mainplanitemid") String mainplanitemid, @Param("tid") String tid);

    WkWorksheetitemdetailPojo getItemDetailEntity(@Param("itemid") String key, @Param("tid") String tenantid);

    List<MatSpecpcbitemPojo> getMatSpecpcbitemByGoodsid(@Param("goodsid") String goodsid, @Param("tid") String tenantid);

    Map<String, Object> getMatSpecpcbByGoodsid(@Param("goodsid") String goodsid, @Param("tid") String tenantid);

    double getQuantityByMachitemid(String machitemid, String tenantid);

    manu_MatSpecpcbPojo getEntityByGoodsid_Specpcb(String goodsid, String tid);

    List<manu_MatSpecpcbitemVo> getListVO_Specpcbitem(String Pid, String tid);

    List<manu_MatSpecpcbdrlPojo> getList_Specpcbdrl(String Pid, String tid);

    List<manu_MatSpecpcbdrawPojo> getList_Specpcbdraw(String Pid, String tid);

    List<manu_MatEcnPojo> getListByGoodsid_Ecn(String goodsid, String tid);

    List<manu_MatSpecpcbdrlVo> getList_SpecpcbdrlVo(String Pid, String tid);

    List<manu_MatSpecpcbdrawVo> getList_SpecpcbdrawVo(String Pid, String tid);

    Map<String, Object> getQtyBymachitemid(String machitemid, String tid);

    Map<String, Object> getQtyByplanitemid(String mainplanitemid, String tid);

    String getIdByRefNo(String refno, String tid);
}

