package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipqtyrolesitemEntity;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 过数角色子表(WkWipqtyrolesitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-26 10:08:49
 */
 @Mapper
public interface WkWipqtyrolesitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtyrolesitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipqtyrolesitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipqtyrolesitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkWipqtyrolesitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipqtyrolesitemEntity wkWipqtyrolesitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkWipqtyrolesitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipqtyrolesitemEntity wkWipqtyrolesitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

