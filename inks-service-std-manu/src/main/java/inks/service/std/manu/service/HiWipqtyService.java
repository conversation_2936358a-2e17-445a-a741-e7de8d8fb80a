package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.HiWipqtyPojo;

import java.util.List;

/**
 * 生产过数(HiWipqty)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-16 16:20:44
 */
public interface HiWipqtyService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    HiWipqtyPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<HiWipqtyPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param hiWipqtyPojo 实例对象
     * @return 实例对象
     */
    HiWipqtyPojo insert(HiWipqtyPojo hiWipqtyPojo);

    /**
     * 修改数据
     *
     * @param hiWipqtypojo 实例对象
     * @return 实例对象
     */
    HiWipqtyPojo update(HiWipqtyPojo hiWipqtypojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    int moveNowToHi(String startdate, String enddate, String tenantid);

    int moveHiToNow(String startdate, String enddate, List<String> ids, String tenantid);

}
