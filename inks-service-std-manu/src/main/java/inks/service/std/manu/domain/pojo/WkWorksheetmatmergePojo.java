package inks.service.std.manu.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

import java.io.Serializable;

/**
 * 厂制物料合并表(WkWorksheetmatmerge)Pojo
 *
 * <AUTHOR>
 * @since 2024-04-11 16:07:46
 */
public class WkWorksheetmatmergePojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = -80983441288443323L;
    // id
    @Excel(name = "id")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // Itemid
    @Excel(name = "Itemid")
    private String itemid;
    // 产品id
    @Excel(name = "产品id")
    private String goodsid;
    // 需求数量
    @Excel(name = "需求数量")
    private Double quantity;
    // 已申领
    @Excel(name = "已申领")
    private Double finishqty;
    // Mrp单号
    @Excel(name = "Mrp单号")
    private String mrpuid;
    // Mrp子项id
    @Excel(name = "Mrp子项id")
    private String mrpitemid;
    // 子件数量
    @Excel(name = "子件数量")
    private Double subqty;
    // 主件数量
    @Excel(name = "主件数量")
    private Double mainqty;
    // 损耗率
    @Excel(name = "损耗率")
    private Double lossrate;
    // BOMid
    @Excel(name = "BOMid")
    private String bomid;
    // 0标准1销售2订单
    @Excel(name = "0标准1销售2订单")
    private Integer bomtype;
    // BOM项目id
    @Excel(name = "BOM项目id")
    private String bomitemid;
    // Item行编码
    @Excel(name = "Item行编码")
    private String itemrowcode;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 关闭
    @Excel(name = "关闭")
    private Integer closed;
    // 毛需求
    @Excel(name = "毛需求")
    private Double bomqty;
    // 可用量
    @Excel(name = "可用量")
    private Double avaiqty;
    // 净需求
    @Excel(name = "净需求")
    private Double needqty;
    // 生产领用
    @Excel(name = "生产领用")
    private Double stoplanqty;
    // 生产需求
    @Excel(name = "生产需求")
    private Double realqty;
    // 流程编码
    @Excel(name = "流程编码")
    private String flowcode;
    // 属性Josn
    @Excel(name = "属性Josn")
    private String attributejson;
    // 行数
    @Excel(name = "行数")
    private Integer itemcount;
    // 销售订单uid
    @Excel(name = "销售订单uid")
    private String machuid;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // Itemid
    public String getItemid() {
        return itemid;
    }

    public void setItemid(String itemid) {
        this.itemid = itemid;
    }

    // 产品id
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 需求数量
    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    // 已申领
    public Double getFinishqty() {
        return finishqty;
    }

    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }

    // Mrp单号
    public String getMrpuid() {
        return mrpuid;
    }

    public void setMrpuid(String mrpuid) {
        this.mrpuid = mrpuid;
    }

    // Mrp子项id
    public String getMrpitemid() {
        return mrpitemid;
    }

    public void setMrpitemid(String mrpitemid) {
        this.mrpitemid = mrpitemid;
    }

    // 子件数量
    public Double getSubqty() {
        return subqty;
    }

    public void setSubqty(Double subqty) {
        this.subqty = subqty;
    }

    // 主件数量
    public Double getMainqty() {
        return mainqty;
    }

    public void setMainqty(Double mainqty) {
        this.mainqty = mainqty;
    }

    // 损耗率
    public Double getLossrate() {
        return lossrate;
    }

    public void setLossrate(Double lossrate) {
        this.lossrate = lossrate;
    }

    // BOMid
    public String getBomid() {
        return bomid;
    }

    public void setBomid(String bomid) {
        this.bomid = bomid;
    }

    // 0标准1销售2订单
    public Integer getBomtype() {
        return bomtype;
    }

    public void setBomtype(Integer bomtype) {
        this.bomtype = bomtype;
    }

    // BOM项目id
    public String getBomitemid() {
        return bomitemid;
    }

    public void setBomitemid(String bomitemid) {
        this.bomitemid = bomitemid;
    }

    // Item行编码
    public String getItemrowcode() {
        return itemrowcode;
    }

    public void setItemrowcode(String itemrowcode) {
        this.itemrowcode = itemrowcode;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 关闭
    public Integer getClosed() {
        return closed;
    }

    public void setClosed(Integer closed) {
        this.closed = closed;
    }

    // 毛需求
    public Double getBomqty() {
        return bomqty;
    }

    public void setBomqty(Double bomqty) {
        this.bomqty = bomqty;
    }

    // 可用量
    public Double getAvaiqty() {
        return avaiqty;
    }

    public void setAvaiqty(Double avaiqty) {
        this.avaiqty = avaiqty;
    }

    // 净需求
    public Double getNeedqty() {
        return needqty;
    }

    public void setNeedqty(Double needqty) {
        this.needqty = needqty;
    }

    // 生产领用
    public Double getStoplanqty() {
        return stoplanqty;
    }

    public void setStoplanqty(Double stoplanqty) {
        this.stoplanqty = stoplanqty;
    }

    // 生产需求
    public Double getRealqty() {
        return realqty;
    }

    public void setRealqty(Double realqty) {
        this.realqty = realqty;
    }

    // 流程编码
    public String getFlowcode() {
        return flowcode;
    }

    public void setFlowcode(String flowcode) {
        this.flowcode = flowcode;
    }

    // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }

    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }

    // 行数
    public Integer getItemcount() {
        return itemcount;
    }

    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }

    // 销售订单uid
    public String getMachuid() {
        return machuid;
    }

    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

