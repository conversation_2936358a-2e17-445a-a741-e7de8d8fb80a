package inks.service.std.manu.domain.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * Pcb工艺画(MatSpecpcbdraw)Pojo
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:25
 */
public class manu_MatSpecpcbdrawVo implements Serializable {
    private static final long serialVersionUID = 461873337268943594L;
    // id
    @Excel(name = "id")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 类型Set/Pnl/Cut/VCut/Layer
    @Excel(name = "类型Set/Pnl/Cut/VCut/Layer")
    private String drawtype;
    // 图片标题
    @Excel(name = "图片标题")
    private String drawtitle;
    // 图片Base64
    @Excel(name = "图片Base64")
    private String drawimage;
    // 失量Json
    @Excel(name = "失量Json")
    private String drawjson;
    // 图片Url
    @Excel(name = "图片Url")
    private String drawurl;
    // 内嵌图稿
    @Excel(name = "内嵌图稿")
    private Integer insidemark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 类型Set/Pnl/Cut/VCut/Layer
    public String getDrawtype() {
        return drawtype;
    }

    public void setDrawtype(String drawtype) {
        this.drawtype = drawtype;
    }

    // 图片标题
    public String getDrawtitle() {
        return drawtitle;
    }

    public void setDrawtitle(String drawtitle) {
        this.drawtitle = drawtitle;
    }

    // 图片Base64
    public String getDrawimage() {
        return drawimage;
    }

    public void setDrawimage(String drawimage) {
        this.drawimage = drawimage;
    }

    // 失量Json
    public String getDrawjson() {
        return drawjson;
    }

    public void setDrawjson(String drawjson) {
        this.drawjson = drawjson;
    }

    // 图片Url
    public String getDrawurl() {
        return drawurl;
    }

    public void setDrawurl(String drawurl) {
        this.drawurl = drawurl;
    }

    // 内嵌图稿
    public Integer getInsidemark() {
        return insidemark;
    }

    public void setInsidemark(Integer insidemark) {
        this.insidemark = insidemark;
    }



    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }



}

