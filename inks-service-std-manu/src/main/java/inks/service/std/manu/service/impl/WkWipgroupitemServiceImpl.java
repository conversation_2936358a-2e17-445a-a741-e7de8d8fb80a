package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipgroupitemEntity;
import inks.service.std.manu.domain.pojo.WkWipgroupitemPojo;
import inks.service.std.manu.mapper.WkWipgroupitemMapper;
import inks.service.std.manu.service.WkWipgroupitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * WIP分管工序(WkWipgroupitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-26 10:08:04
 */
@Service("wkWipgroupitemService")
public class WkWipgroupitemServiceImpl implements WkWipgroupitemService {
    @Resource
    private WkWipgroupitemMapper wkWipgroupitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipgroupitemPojo getEntity(String key, String tid) {
        return this.wkWipgroupitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipgroupitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipgroupitemPojo> lst = wkWipgroupitemMapper.getPageList(queryParam);
            PageInfo<WkWipgroupitemPojo> pageInfo = new PageInfo<WkWipgroupitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkWipgroupitemPojo> getList(String Pid, String tid) {
        try {
            List<WkWipgroupitemPojo> lst = wkWipgroupitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkWipgroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipgroupitemPojo insert(WkWipgroupitemPojo wkWipgroupitemPojo) {
        //初始化item的NULL
        WkWipgroupitemPojo itempojo = this.clearNull(wkWipgroupitemPojo);
        WkWipgroupitemEntity wkWipgroupitemEntity = new WkWipgroupitemEntity();
        BeanUtils.copyProperties(itempojo, wkWipgroupitemEntity);

        wkWipgroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipgroupitemEntity.setRevision(1);  //乐观锁
        this.wkWipgroupitemMapper.insert(wkWipgroupitemEntity);
        return this.getEntity(wkWipgroupitemEntity.getId(), wkWipgroupitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipgroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipgroupitemPojo update(WkWipgroupitemPojo wkWipgroupitemPojo) {
        WkWipgroupitemEntity wkWipgroupitemEntity = new WkWipgroupitemEntity();
        BeanUtils.copyProperties(wkWipgroupitemPojo, wkWipgroupitemEntity);
        this.wkWipgroupitemMapper.update(wkWipgroupitemEntity);
        return this.getEntity(wkWipgroupitemEntity.getId(), wkWipgroupitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWipgroupitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkWipgroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipgroupitemPojo clearNull(WkWipgroupitemPojo wkWipgroupitemPojo) {
        //初始化NULL字段
        if (wkWipgroupitemPojo.getPid() == null) wkWipgroupitemPojo.setPid("");
        if (wkWipgroupitemPojo.getWpid() == null) wkWipgroupitemPojo.setWpid("");
        if (wkWipgroupitemPojo.getWpcode() == null) wkWipgroupitemPojo.setWpcode("");
        if (wkWipgroupitemPojo.getWpname() == null) wkWipgroupitemPojo.setWpname("");
        if (wkWipgroupitemPojo.getHeadmark() == null) wkWipgroupitemPojo.setHeadmark(0);
        if (wkWipgroupitemPojo.getRownum() == null) wkWipgroupitemPojo.setRownum(0);
        if (wkWipgroupitemPojo.getCustom1() == null) wkWipgroupitemPojo.setCustom1("");
        if (wkWipgroupitemPojo.getCustom2() == null) wkWipgroupitemPojo.setCustom2("");
        if (wkWipgroupitemPojo.getCustom3() == null) wkWipgroupitemPojo.setCustom3("");
        if (wkWipgroupitemPojo.getCustom4() == null) wkWipgroupitemPojo.setCustom4("");
        if (wkWipgroupitemPojo.getCustom5() == null) wkWipgroupitemPojo.setCustom5("");
        if (wkWipgroupitemPojo.getCustom6() == null) wkWipgroupitemPojo.setCustom6("");
        if (wkWipgroupitemPojo.getCustom7() == null) wkWipgroupitemPojo.setCustom7("");
        if (wkWipgroupitemPojo.getCustom8() == null) wkWipgroupitemPojo.setCustom8("");
        if (wkWipgroupitemPojo.getCustom9() == null) wkWipgroupitemPojo.setCustom9("");
        if (wkWipgroupitemPojo.getCustom10() == null) wkWipgroupitemPojo.setCustom10("");
        if (wkWipgroupitemPojo.getTenantid() == null) wkWipgroupitemPojo.setTenantid("");
        if (wkWipgroupitemPojo.getRevision() == null) wkWipgroupitemPojo.setRevision(0);
        return wkWipgroupitemPojo;
    }
}
