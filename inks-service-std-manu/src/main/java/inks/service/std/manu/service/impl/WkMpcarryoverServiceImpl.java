package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.std.manu.domain.WkMpcarryoverEntity;
import inks.service.std.manu.domain.WkMpcarryoveritemEntity;
import inks.service.std.manu.domain.pojo.WkMpcarryoverPojo;
import inks.service.std.manu.domain.pojo.WkMpcarryoveritemPojo;
import inks.service.std.manu.domain.pojo.WkMpcarryoveritemdetailPojo;
import inks.service.std.manu.mapper.WkMpcarryoverMapper;
import inks.service.std.manu.mapper.WkMpcarryoveritemMapper;
import inks.service.std.manu.service.WkMpcarryoverService;
import inks.service.std.manu.service.WkMpcarryoveritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 生产结转(WkMpcarryover)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-09 09:42:57
 */
@Service("wkMpcarryoverService")
public class WkMpcarryoverServiceImpl implements WkMpcarryoverService {
    @Resource
    private WkMpcarryoverMapper wkMpcarryoverMapper;

    @Resource
    private WkMpcarryoveritemMapper wkMpcarryoveritemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkMpcarryoveritemService wkMpcarryoveritemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMpcarryoverPojo getEntity(String key, String tid) {
        return this.wkMpcarryoverMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMpcarryoveritemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMpcarryoveritemdetailPojo> lst = wkMpcarryoverMapper.getPageList(queryParam);
            PageInfo<WkMpcarryoveritemdetailPojo> pageInfo = new PageInfo<WkMpcarryoveritemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMpcarryoverPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkMpcarryoverPojo wkMpcarryoverPojo = this.wkMpcarryoverMapper.getEntity(key, tid);
            //读取子表
            wkMpcarryoverPojo.setItem(wkMpcarryoveritemMapper.getList(wkMpcarryoverPojo.getId(), wkMpcarryoverPojo.getTenantid()));
            return wkMpcarryoverPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMpcarryoverPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMpcarryoverPojo> lst = wkMpcarryoverMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkMpcarryoveritemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkMpcarryoverPojo> pageInfo = new PageInfo<WkMpcarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMpcarryoverPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMpcarryoverPojo> lst = wkMpcarryoverMapper.getPageTh(queryParam);
            PageInfo<WkMpcarryoverPojo> pageInfo = new PageInfo<WkMpcarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkMpcarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkMpcarryoverPojo insert(WkMpcarryoverPojo wkMpcarryoverPojo) {
//初始化NULL字段
        if (wkMpcarryoverPojo.getRefno() == null) wkMpcarryoverPojo.setRefno("");
        if (wkMpcarryoverPojo.getBilltype() == null) wkMpcarryoverPojo.setBilltype("");
        if (wkMpcarryoverPojo.getBilldate() == null) wkMpcarryoverPojo.setBilldate(new Date());
        if (wkMpcarryoverPojo.getBilltitle() == null) wkMpcarryoverPojo.setBilltitle("");
        if (wkMpcarryoverPojo.getCarryyear() == null) wkMpcarryoverPojo.setCarryyear(0);
        if (wkMpcarryoverPojo.getCarrymonth() == null) wkMpcarryoverPojo.setCarrymonth(0);
        if (wkMpcarryoverPojo.getStartdate() == null) wkMpcarryoverPojo.setStartdate(new Date());
        if (wkMpcarryoverPojo.getEnddate() == null) wkMpcarryoverPojo.setEnddate(new Date());
        if (wkMpcarryoverPojo.getOperator() == null) wkMpcarryoverPojo.setOperator("");
        if (wkMpcarryoverPojo.getOperatorid() == null) wkMpcarryoverPojo.setOperatorid("");
        if (wkMpcarryoverPojo.getSummary() == null) wkMpcarryoverPojo.setSummary("");
        if (wkMpcarryoverPojo.getCreateby() == null) wkMpcarryoverPojo.setCreateby("");
        if (wkMpcarryoverPojo.getCreatebyid() == null) wkMpcarryoverPojo.setCreatebyid("");
        if (wkMpcarryoverPojo.getCreatedate() == null) wkMpcarryoverPojo.setCreatedate(new Date());
        if (wkMpcarryoverPojo.getLister() == null) wkMpcarryoverPojo.setLister("");
        if (wkMpcarryoverPojo.getListerid() == null) wkMpcarryoverPojo.setListerid("");
        if (wkMpcarryoverPojo.getModifydate() == null) wkMpcarryoverPojo.setModifydate(new Date());
        if (wkMpcarryoverPojo.getBillopenamount() == null) wkMpcarryoverPojo.setBillopenamount(0D);
        if (wkMpcarryoverPojo.getBillinamount() == null) wkMpcarryoverPojo.setBillinamount(0D);
        if (wkMpcarryoverPojo.getBilloutamount() == null) wkMpcarryoverPojo.setBilloutamount(0D);
        if (wkMpcarryoverPojo.getBillcloseamount() == null) wkMpcarryoverPojo.setBillcloseamount(0D);
        if (wkMpcarryoverPojo.getItemcount() == null) wkMpcarryoverPojo.setItemcount(0);
        if (wkMpcarryoverPojo.getPrintcount() == null) wkMpcarryoverPojo.setPrintcount(0);
        if (wkMpcarryoverPojo.getCustom1() == null) wkMpcarryoverPojo.setCustom1("");
        if (wkMpcarryoverPojo.getCustom2() == null) wkMpcarryoverPojo.setCustom2("");
        if (wkMpcarryoverPojo.getCustom3() == null) wkMpcarryoverPojo.setCustom3("");
        if (wkMpcarryoverPojo.getCustom4() == null) wkMpcarryoverPojo.setCustom4("");
        if (wkMpcarryoverPojo.getCustom5() == null) wkMpcarryoverPojo.setCustom5("");
        if (wkMpcarryoverPojo.getCustom6() == null) wkMpcarryoverPojo.setCustom6("");
        if (wkMpcarryoverPojo.getCustom7() == null) wkMpcarryoverPojo.setCustom7("");
        if (wkMpcarryoverPojo.getCustom8() == null) wkMpcarryoverPojo.setCustom8("");
        if (wkMpcarryoverPojo.getCustom9() == null) wkMpcarryoverPojo.setCustom9("");
        if (wkMpcarryoverPojo.getCustom10() == null) wkMpcarryoverPojo.setCustom10("");
        if (wkMpcarryoverPojo.getTenantid() == null) wkMpcarryoverPojo.setTenantid("");
        if (wkMpcarryoverPojo.getTenantname() == null) wkMpcarryoverPojo.setTenantname("");
        if (wkMpcarryoverPojo.getRevision() == null) wkMpcarryoverPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkMpcarryoverEntity wkMpcarryoverEntity = new WkMpcarryoverEntity();
        BeanUtils.copyProperties(wkMpcarryoverPojo, wkMpcarryoverEntity);
        //设置id和新建日期
        wkMpcarryoverEntity.setId(id);
        wkMpcarryoverEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkMpcarryoverMapper.insert(wkMpcarryoverEntity);
        //Item子表处理
        List<WkMpcarryoveritemPojo> lst = wkMpcarryoverPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkMpcarryoveritemPojo itemPojo = this.wkMpcarryoveritemService.clearNull(lst.get(i));
                WkMpcarryoveritemEntity wkMpcarryoveritemEntity = new WkMpcarryoveritemEntity();
                BeanUtils.copyProperties(itemPojo, wkMpcarryoveritemEntity);
                //设置id和Pid
                wkMpcarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkMpcarryoveritemEntity.setPid(id);
                wkMpcarryoveritemEntity.setTenantid(wkMpcarryoverPojo.getTenantid());
                wkMpcarryoveritemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkMpcarryoveritemMapper.insert(wkMpcarryoveritemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkMpcarryoverEntity.getId(), wkMpcarryoverEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkMpcarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkMpcarryoverPojo update(WkMpcarryoverPojo wkMpcarryoverPojo) {
        //主表更改
        WkMpcarryoverEntity wkMpcarryoverEntity = new WkMpcarryoverEntity();
        BeanUtils.copyProperties(wkMpcarryoverPojo, wkMpcarryoverEntity);
        this.wkMpcarryoverMapper.update(wkMpcarryoverEntity);
        if (wkMpcarryoverPojo.getItem() != null) {
            //Item子表处理
            List<WkMpcarryoveritemPojo> lst = wkMpcarryoverPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkMpcarryoverMapper.getDelItemIds(wkMpcarryoverPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkMpcarryoveritemMapper.delete(lstDelIds.get(i), wkMpcarryoverEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    WkMpcarryoveritemEntity wkMpcarryoveritemEntity = new WkMpcarryoveritemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        WkMpcarryoveritemPojo itemPojo = this.wkMpcarryoveritemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, wkMpcarryoveritemEntity);
                        //设置id和Pid
                        wkMpcarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkMpcarryoveritemEntity.setPid(wkMpcarryoverEntity.getId());  // 主表 id
                        wkMpcarryoveritemEntity.setTenantid(wkMpcarryoverPojo.getTenantid());   // 租户id
                        wkMpcarryoveritemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkMpcarryoveritemMapper.insert(wkMpcarryoveritemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkMpcarryoveritemEntity);
                        wkMpcarryoveritemEntity.setTenantid(wkMpcarryoverPojo.getTenantid());
                        this.wkMpcarryoveritemMapper.update(wkMpcarryoveritemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkMpcarryoverEntity.getId(), wkMpcarryoverEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkMpcarryoverPojo wkMpcarryoverPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkMpcarryoveritemPojo> lst = wkMpcarryoverPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkMpcarryoveritemPojo wkMpcarryoveritemPojo : lst) {
                this.wkMpcarryoveritemMapper.delete(wkMpcarryoveritemPojo.getId(), tid);
            }
        }
        this.wkMpcarryoverMapper.delete(key, tid);
        return wkMpcarryoverPojo.getRefno();
    }

    /**
     * 新增数据
     *
     * @param wkMpcarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkMpcarryoverPojo createCarry(WkMpcarryoverPojo wkMpcarryoverPojo) {

        // 查询当前客户之前的销售账单
        QueryParam queryParam = new QueryParam();
        queryParam.setOrderBy("Wk_MpCarryover.EndDate");
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1);
        queryParam.setTenantid(wkMpcarryoverPojo.getTenantid());
        List<WkMpcarryoverPojo> lstCarry = this.wkMpcarryoverMapper.getPageTh(queryParam);
        if (lstCarry.size() == 0) {
            throw new RuntimeException("请先初始化生产结转");
        }

        // 查询上个结转表内容
        List<WkMpcarryoveritemPojo> lstOpen = this.wkMpcarryoveritemMapper.getList(lstCarry.get(0).getId(), wkMpcarryoverPojo.getTenantid());

        // =============本期出入库汇总 =====================
        // 初始化时间
        Date dtStart = lstCarry.get(0).getEnddate();
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(wkMpcarryoverPojo.getEnddate()) + " 23:59:59");
        wkMpcarryoverPojo.setStartdate(lstCarry.get(0).getEnddate());

        queryParam = new QueryParam();
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setTenantid(wkMpcarryoverPojo.getTenantid());
        List<WkMpcarryoveritemPojo> lstMp = this.wkMpcarryoverMapper.getGoodsMpList(queryParam);
        List<WkMpcarryoveritemPojo> lstAcce = this.wkMpcarryoverMapper.getGoodsAcceList(queryParam);
        //  ==========开始拼接 进项==============
        int rowNum = 1;
        List<WkMpcarryoveritemPojo> lstNew = new ArrayList<>();
        for (WkMpcarryoveritemPojo openPojo : lstOpen) {
            WkMpcarryoveritemPojo newPojo = new WkMpcarryoveritemPojo();
            newPojo.setGoodsid(openPojo.getGoodsid());
            newPojo.setOpenqty(openPojo.getCloseqty());
            newPojo.setOpenamount(openPojo.getCloseamount());
            if (lstMp.size() > 0) {
                for (WkMpcarryoveritemPojo mpPojo : lstMp) {
                    if (openPojo.getGoodsid().equals(mpPojo.getGoodsid())) {
                        newPojo.setInqty(mpPojo.getInqty());
                        newPojo.setInamount(mpPojo.getInamount());
                        lstMp.remove(mpPojo);
                        break;
                    }
                }
            } else {
                newPojo.setInqty(0D);
                newPojo.setInamount(0D);
            }
            if (lstAcce.size() > 0) {
                for (WkMpcarryoveritemPojo accePojo : lstAcce) {
                    if (openPojo.getGoodsid().equals(accePojo.getGoodsid()) || newPojo.getGoodsid().equals(accePojo.getGoodsid())) {
                        newPojo.setOutqty(accePojo.getOutqty());
                        newPojo.setOutamount(accePojo.getOutamount());
                        lstAcce.remove(accePojo);
                        break;
                    }
                }
            } else {
                newPojo.setOutqty(0D);
                newPojo.setOutamount(0D);
            }

            // 三项非空，添加到列表
            if (newPojo.getOpenqty() != 0D || newPojo.getInqty() != 0D || newPojo.getOutqty() != 0D) {
                newPojo.setOutqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setOutamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }

        // =========本期新增================
        if (lstMp.size() > 0) {
            for (WkMpcarryoveritemPojo mpPojo : lstMp) {
                WkMpcarryoveritemPojo newPojo = new WkMpcarryoveritemPojo();
                newPojo.setGoodsid(mpPojo.getGoodsid());
                newPojo.setOpenqty(0D);
                newPojo.setOpenamount(0D);
                newPojo.setInqty(mpPojo.getInqty());
                newPojo.setInamount(mpPojo.getInamount());
                if (lstAcce.size() > 0) {
                    for (WkMpcarryoveritemPojo accePojo : lstAcce) {
                        if (newPojo.getGoodsid().equals(accePojo.getGoodsid())) {
                            newPojo.setOutqty(accePojo.getOutqty());
                            newPojo.setOutamount(accePojo.getOutamount());
                            lstAcce.remove(accePojo);
                            break;
                        }
                    }
                } else {
                    newPojo.setOutqty(0D);
                    newPojo.setOutamount(0D);
                }
                /// 添加到列表
                newPojo.setOutqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setOutamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }
        if (lstAcce.size() > 0) {
            for (WkMpcarryoveritemPojo accePojo : lstAcce) {
                WkMpcarryoveritemPojo newPojo = new WkMpcarryoveritemPojo();
                newPojo.setGoodsid(accePojo.getGoodsid());
                newPojo.setOpenqty(0D);
                newPojo.setOpenamount(0D);
                newPojo.setInqty(0D);
                newPojo.setInamount(0D);
                newPojo.setOutqty(accePojo.getOutqty());
                newPojo.setOutamount(accePojo.getOutamount());
                /// 添加到列表
                newPojo.setOutqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setOutamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }
        //  =======插入数据========
        wkMpcarryoverPojo.setItem(lstNew);
        wkMpcarryoverPojo.setItemcount(lstNew.size());
        wkMpcarryoverPojo = insert(wkMpcarryoverPojo);

        //返回Bill实例
        return this.getEntity(wkMpcarryoverPojo.getId(), wkMpcarryoverPojo.getTenantid());

    }


}
