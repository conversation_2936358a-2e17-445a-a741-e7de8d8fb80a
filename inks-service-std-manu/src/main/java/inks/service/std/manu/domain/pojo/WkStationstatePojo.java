package inks.service.std.manu.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 工位状态(WkStationstate)实体类
 *
 * <AUTHOR>
 * @since 2023-07-17 15:34:48
 */
public class WkStationstatePojo implements Serializable {
    private static final long serialVersionUID = 728219046713939659L;
         // id
         @Excel(name = "id") 
    private String id;
         // 单据类型
         @Excel(name = "单据类型") 
    private String billtype;
         // 单据日期
         @Excel(name = "单据日期") 
    private Date billdate;
         // 单据标题
         @Excel(name = "单据标题") 
    private String billtitle;
         // 经办人
         @Excel(name = "经办人") 
    private String operator;
         // 经办人id
         @Excel(name = "经办人id") 
    private String operatorid;
         // 工序id
         @Excel(name = "工序id") 
    private String wpid;
         // 工序编码
         @Excel(name = "工序编码") 
    private String wpcode;
         // 工序名称
         @Excel(name = "工序名称") 
    private String wpname;
         // 工位
         @Excel(name = "工位") 
    private String statid;
         // 编码
         @Excel(name = "编码") 
    private String statcode;
         // 名称
         @Excel(name = "名称") 
    private String statname;
         // 状态刷新H
         @Excel(name = "状态刷新H") 
    private Integer staterefresh;
         // 有效日期
         @Excel(name = "有效日期") 
    private Date enddate;
         // 状态参数
         @Excel(name = "状态参数") 
    private String statejson;
         // 创建者
         @Excel(name = "创建者") 
    private String createby;
         // 创建者id
         @Excel(name = "创建者id") 
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期") 
    private Date createdate;
         // 制表
         @Excel(name = "制表") 
    private String lister;
         // 制表id
         @Excel(name = "制表id") 
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期") 
    private Date modifydate;
         // 自定义1
         @Excel(name = "自定义1") 
    private String custom1;
         // 自定义2
         @Excel(name = "自定义2") 
    private String custom2;
         // 自定义3
         @Excel(name = "自定义3") 
    private String custom3;
         // 自定义4
         @Excel(name = "自定义4") 
    private String custom4;
         // 自定义5
         @Excel(name = "自定义5") 
    private String custom5;
         // 自定义6
         @Excel(name = "自定义6") 
    private String custom6;
         // 自定义7
         @Excel(name = "自定义7") 
    private String custom7;
         // 自定义8
         @Excel(name = "自定义8") 
    private String custom8;
         // 自定义9
         @Excel(name = "自定义9") 
    private String custom9;
         // 自定义10
         @Excel(name = "自定义10") 
    private String custom10;
         // 租户id
         @Excel(name = "租户id") 
    private String tenantid;
         // 乐观锁
         @Excel(name = "乐观锁") 
    private Integer revision;
         // 租户名称
         @Excel(name = "租户名称") 
    private String tenantname;

     // id
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // 单据类型
       public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
     // 单据日期
       public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
     // 单据标题
       public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
     // 经办人
       public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
        
     // 经办人id
       public String getOperatorid() {
        return operatorid;
    }
    
    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }
        
     // 工序id
       public String getWpid() {
        return wpid;
    }
    
    public void setWpid(String wpid) {
        this.wpid = wpid;
    }
        
     // 工序编码
       public String getWpcode() {
        return wpcode;
    }
    
    public void setWpcode(String wpcode) {
        this.wpcode = wpcode;
    }
        
     // 工序名称
       public String getWpname() {
        return wpname;
    }
    
    public void setWpname(String wpname) {
        this.wpname = wpname;
    }
        
     // 工位
       public String getStatid() {
        return statid;
    }
    
    public void setStatid(String statid) {
        this.statid = statid;
    }
        
     // 编码
       public String getStatcode() {
        return statcode;
    }
    
    public void setStatcode(String statcode) {
        this.statcode = statcode;
    }
        
     // 名称
       public String getStatname() {
        return statname;
    }
    
    public void setStatname(String statname) {
        this.statname = statname;
    }
        
     // 状态刷新H
       public Integer getStaterefresh() {
        return staterefresh;
    }
    
    public void setStaterefresh(Integer staterefresh) {
        this.staterefresh = staterefresh;
    }
        
     // 有效日期
       public Date getEnddate() {
        return enddate;
    }
    
    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }
        
     // 状态参数
       public String getStatejson() {
        return statejson;
    }
    
    public void setStatejson(String statejson) {
        this.statejson = statejson;
    }
        
     // 创建者
       public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
     // 创建者id
       public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
     // 新建日期
       public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
     // 制表
       public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
     // 制表id
       public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
     // 修改日期
       public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
     // 自定义1
       public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
     // 自定义2
       public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
     // 自定义3
       public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
     // 自定义4
       public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
     // 自定义5
       public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
     // 自定义6
       public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
     // 自定义7
       public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
     // 自定义8
       public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
     // 自定义9
       public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
     // 自定义10
       public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
     // 租户id
       public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        
     // 租户名称
       public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        

}

