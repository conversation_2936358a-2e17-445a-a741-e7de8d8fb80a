package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWorksheetitemEntity;
import inks.service.std.manu.domain.pojo.WkWorksheetitemPojo;
import inks.service.std.manu.domain.pojo.WkWorksheetitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashSet;
import java.util.List;

/**
 * 厂制项目(WkWorksheetitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 13:28:00
 */
 @Mapper
public interface WkWorksheetitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWorksheetitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWorksheetitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkWorksheetitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWorksheetitemEntity wkWorksheetitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkWorksheetitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkWorksheetitemEntity wkWorksheetitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int updateMergemark1(@Param("itemids") String itemids, @Param("tid") String tid);

    String getUid(@Param("id")String id,@Param("tid") String tid);

    WkWorksheetitemdetailPojo getEntityDetail(@Param("key")String key, @Param("tid")String tid);

    int updateMergemark0(@Param("key")String key, @Param("tid")String tid);

    int closedids(@Param("ids") HashSet<String> worksheetItemidSet, @Param("type") Integer type, @Param("tenantid") String tenantid);

    String getPid(@Param("workitemid") String workitemid, @Param("tenantid") String tenantid);

    String getIdByMrpitemid(@Param("itemparentid") String itemparentid, @Param("tenantid") String tenantid);

    List<WkWorksheetitemPojo> getItemListByIds(String ids, String pid, String tid);
}

