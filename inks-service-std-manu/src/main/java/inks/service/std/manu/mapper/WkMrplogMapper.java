package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkMrplogEntity;
import inks.service.std.manu.domain.pojo.WkMrplogPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 操作日志(WkMrplog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-22 12:35:01
 */
 @Mapper
public interface WkMrplogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMrplogPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMrplogPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMrplogPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkMrplogEntity 实例对象
     * @return 影响行数
     */
    int insert(WkMrplogEntity wkMrplogEntity);

    
    /**
     * 修改数据
     *
     * @param wkMrplogEntity 实例对象
     * @return 影响行数
     */
    int update(WkMrplogEntity wkMrplogEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

