package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkProcessstatEntity;
import inks.service.std.manu.domain.pojo.WkProcessstatPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工序工位(WkProcessstat)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-17 16:57:23
 */
 @Mapper
public interface WkProcessstatMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProcessstatPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkProcessstatPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkProcessstatPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkProcessstatEntity 实例对象
     * @return 影响行数
     */
    int insert(WkProcessstatEntity wkProcessstatEntity);

    
    /**
     * 修改数据
     *
     * @param wkProcessstatEntity 实例对象
     * @return 影响行数
     */
    int update(WkProcessstatEntity wkProcessstatEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

