package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkMrplogEntity;
import inks.service.std.manu.domain.pojo.WkMrplogPojo;
import inks.service.std.manu.mapper.WkMrplogMapper;
import inks.service.std.manu.service.WkMrplogService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 操作日志(WkMrplog)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-22 12:35:01
 */
@Service("wkMrplogService")
public class WkMrplogServiceImpl implements WkMrplogService {
    @Resource
    private WkMrplogMapper wkMrplogMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMrplogPojo getEntity(String key, String tid) {
        return this.wkMrplogMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMrplogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMrplogPojo> lst = wkMrplogMapper.getPageList(queryParam);
            PageInfo<WkMrplogPojo> pageInfo = new PageInfo<WkMrplogPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkMrplogPojo> getList(String Pid, String tid) {
        try {
            List<WkMrplogPojo> lst = wkMrplogMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkMrplogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMrplogPojo insert(WkMrplogPojo wkMrplogPojo) {
        //初始化item的NULL
        WkMrplogPojo itempojo = this.clearNull(wkMrplogPojo);
        WkMrplogEntity wkMrplogEntity = new WkMrplogEntity();
        BeanUtils.copyProperties(itempojo, wkMrplogEntity);
        //生成雪花id
        wkMrplogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.wkMrplogMapper.insert(wkMrplogEntity);
        return this.getEntity(wkMrplogEntity.getId(), wkMrplogEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkMrplogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMrplogPojo update(WkMrplogPojo wkMrplogPojo) {
        WkMrplogEntity wkMrplogEntity = new WkMrplogEntity();
        BeanUtils.copyProperties(wkMrplogPojo, wkMrplogEntity);
        this.wkMrplogMapper.update(wkMrplogEntity);
        return this.getEntity(wkMrplogEntity.getId(), wkMrplogEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkMrplogMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkMrplogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkMrplogPojo clearNull(WkMrplogPojo wkMrplogPojo) {
        //初始化NULL字段
        if (wkMrplogPojo.getPid() == null) wkMrplogPojo.setPid("");
        if (wkMrplogPojo.getRemarkjson() == null) wkMrplogPojo.setRemarkjson("");
        if (wkMrplogPojo.getOpertitle() == null) wkMrplogPojo.setOpertitle("");
        if (wkMrplogPojo.getBusinesstype() == null) wkMrplogPojo.setBusinesstype(0);
        if (wkMrplogPojo.getMethod() == null) wkMrplogPojo.setMethod("");
        if (wkMrplogPojo.getRequestmethod() == null) wkMrplogPojo.setRequestmethod("");
        if (wkMrplogPojo.getOperatortype() == null) wkMrplogPojo.setOperatortype(0);
        if (wkMrplogPojo.getOperuserid() == null) wkMrplogPojo.setOperuserid("");
        if (wkMrplogPojo.getOpername() == null) wkMrplogPojo.setOpername("");
        if (wkMrplogPojo.getDeptname() == null) wkMrplogPojo.setDeptname("");
        if (wkMrplogPojo.getOperurl() == null) wkMrplogPojo.setOperurl("");
        if (wkMrplogPojo.getOperip() == null) wkMrplogPojo.setOperip("");
        if (wkMrplogPojo.getOperlocation() == null) wkMrplogPojo.setOperlocation("");
        if (wkMrplogPojo.getOperparam() == null) wkMrplogPojo.setOperparam("");
        if (wkMrplogPojo.getJsonresult() == null) wkMrplogPojo.setJsonresult("");
        if (wkMrplogPojo.getStatus() == null) wkMrplogPojo.setStatus(0);
        if (wkMrplogPojo.getErrormsg() == null) wkMrplogPojo.setErrormsg("");
        if (wkMrplogPojo.getOpertime() == null) wkMrplogPojo.setOpertime(new Date());
        if (wkMrplogPojo.getTenantid() == null) wkMrplogPojo.setTenantid("");
        return wkMrplogPojo;
    }
}
