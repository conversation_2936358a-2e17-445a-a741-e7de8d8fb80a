package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkSubcontractPojo;
import inks.service.std.manu.domain.pojo.WkSubcontractitemdetailPojo;
import inks.service.std.manu.domain.pojo.WkSubcontractmatPojo;
import inks.service.std.manu.service.WkSubcontractService;
import inks.service.std.manu.service.WkSubcontractitemService;
import inks.service.std.manu.service.WkSubcontractmatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 委制单据(Wk_Subcontract)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:36:26
 */
@RestController
@RequestMapping("D05M02B1")
@Api(tags = "D05M02B1:委制单据:MRP需求")
public class D05M02B1Controller extends WkSubcontractController {
    /**
     * 服务对象
     */
    @Resource
    private WkSubcontractService wkSubcontractService;

    /**
     * 服务对象Item
     */
    @Resource
    private WkSubcontractitemService wkSubcontractitemService;

    /**
     * 服务对象
     */
    @Resource
    private WkSubcontractmatService wkSubcontractmatService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Subcontract.List")
    public R<PageInfo<WkSubcontractitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Subcontract.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Wk_Subcontract.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSubcontractService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Subcontract.List")
    public R<PageInfo<WkSubcontractitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid, Integer appl) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Subcontract.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += " and Wk_SubcontractItem.FinishQty<Wk_SubcontractItem.Quantity";
            qpfilter += " and Wk_SubcontractItem.DisannulMark=0 and Wk_SubcontractItem.Closed=0";
            if (groupid != null) {
                qpfilter += " and Wk_Subcontract.Groupid='" + groupid + "'";
            }
            // 审批通过的
            if (appl != null && appl == 1) {
                qpfilter += " and Wk_Subcontract.Assessor<>''";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSubcontractService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Subcontract.List")
    public R<PageInfo<WkSubcontractPojo>> getPageTh(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Subcontract.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Wk_Subcontract.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSubcontractService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Subcontract.List")
    public R<PageInfo<WkSubcontractPojo>> getOnlinePageTh(@RequestBody String json, String groupid, Integer appl) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Subcontract.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += " and Wk_Subcontract.FinishCount+Wk_Subcontract.DisannulCount<Wk_Subcontract.ItemCount";
            if (groupid != null) {
                qpfilter += " and Wk_Subcontract.Groupid='" + groupid + "'";
            }
            // 审批通过的
            if (appl != null && appl == 1) {
                qpfilter += " and Wk_Subcontract.Assessor<>''";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSubcontractService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按查询结余物料", notes = "按查询结余物料", produces = "application/json")
    @RequestMapping(value = "/getMatList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Subcontract.List")
    public R<List<WkSubcontractmatPojo>> getMatList(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkSubcontractmatService.getListByPid(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
