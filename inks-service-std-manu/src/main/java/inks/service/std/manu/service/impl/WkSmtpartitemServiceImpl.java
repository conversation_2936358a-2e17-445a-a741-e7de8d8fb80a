package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSmtpartitemEntity;
import inks.service.std.manu.domain.pojo.WkSmtpartitemPojo;
import inks.service.std.manu.mapper.WkSmtpartitemMapper;
import inks.service.std.manu.service.WkSmtpartitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 上料表项目(WkSmtpartitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-28 09:25:21
 */
@Service("wkSmtpartitemService")
public class WkSmtpartitemServiceImpl implements WkSmtpartitemService {
    @Resource
    private WkSmtpartitemMapper wkSmtpartitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSmtpartitemPojo getEntity(String key, String tid) {
        return this.wkSmtpartitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSmtpartitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSmtpartitemPojo> lst = wkSmtpartitemMapper.getPageList(queryParam);
            PageInfo<WkSmtpartitemPojo> pageInfo = new PageInfo<WkSmtpartitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkSmtpartitemPojo> getList(String Pid, String tid) {
        try {
            List<WkSmtpartitemPojo> lst = wkSmtpartitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkSmtpartitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSmtpartitemPojo insert(WkSmtpartitemPojo wkSmtpartitemPojo) {
        //初始化item的NULL
        WkSmtpartitemPojo itempojo = this.clearNull(wkSmtpartitemPojo);
        WkSmtpartitemEntity wkSmtpartitemEntity = new WkSmtpartitemEntity();
        BeanUtils.copyProperties(itempojo, wkSmtpartitemEntity);

        wkSmtpartitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkSmtpartitemEntity.setRevision(1);  //乐观锁
        this.wkSmtpartitemMapper.insert(wkSmtpartitemEntity);
        return this.getEntity(wkSmtpartitemEntity.getId(), wkSmtpartitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSmtpartitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSmtpartitemPojo update(WkSmtpartitemPojo wkSmtpartitemPojo) {
        WkSmtpartitemEntity wkSmtpartitemEntity = new WkSmtpartitemEntity();
        BeanUtils.copyProperties(wkSmtpartitemPojo, wkSmtpartitemEntity);
        this.wkSmtpartitemMapper.update(wkSmtpartitemEntity);
        return this.getEntity(wkSmtpartitemEntity.getId(), wkSmtpartitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkSmtpartitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkSmtpartitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSmtpartitemPojo clearNull(WkSmtpartitemPojo wkSmtpartitemPojo) {
        //初始化NULL字段
        if (wkSmtpartitemPojo.getPid() == null) wkSmtpartitemPojo.setPid("");
        if (wkSmtpartitemPojo.getDevcode() == null) wkSmtpartitemPojo.setDevcode("");
        if (wkSmtpartitemPojo.getTablecode() == null) wkSmtpartitemPojo.setTablecode("");
        if (wkSmtpartitemPojo.getStationnum() == null) wkSmtpartitemPojo.setStationnum(0);
        if (wkSmtpartitemPojo.getStationcode() == null) wkSmtpartitemPojo.setStationcode("");
        if (wkSmtpartitemPojo.getPointmark() == null) wkSmtpartitemPojo.setPointmark("");
        if (wkSmtpartitemPojo.getGoodsid() == null) wkSmtpartitemPojo.setGoodsid("");
        if (wkSmtpartitemPojo.getItemtype() == null) wkSmtpartitemPojo.setItemtype("");
        if (wkSmtpartitemPojo.getItemcode() == null) wkSmtpartitemPojo.setItemcode("");
        if (wkSmtpartitemPojo.getItemname() == null) wkSmtpartitemPojo.setItemname("");
        if (wkSmtpartitemPojo.getItemspec() == null) wkSmtpartitemPojo.setItemspec("");
        if (wkSmtpartitemPojo.getItemunit() == null) wkSmtpartitemPojo.setItemunit("");
        if (wkSmtpartitemPojo.getSingleqty() == null) wkSmtpartitemPojo.setSingleqty(0D);
        if (wkSmtpartitemPojo.getQuantity() == null) wkSmtpartitemPojo.setQuantity(0D);
        if (wkSmtpartitemPojo.getFinishqty() == null) wkSmtpartitemPojo.setFinishqty(0D);
        if (wkSmtpartitemPojo.getAcceptqty() == null) wkSmtpartitemPojo.setAcceptqty(0D);
        if (wkSmtpartitemPojo.getRownum() == null) wkSmtpartitemPojo.setRownum(0);
        if (wkSmtpartitemPojo.getRemark() == null) wkSmtpartitemPojo.setRemark("");
        if (wkSmtpartitemPojo.getPickcode() == null) wkSmtpartitemPojo.setPickcode("");
        // if (wkSmtpartitemPojo.getPickdate() == null) wkSmtpartitemPojo.setPickdate(new Date());
        if (wkSmtpartitemPojo.getPickmark() == null) wkSmtpartitemPojo.setPickmark(0);
        if (wkSmtpartitemPojo.getPicker() == null) wkSmtpartitemPojo.setPicker("");
        if (wkSmtpartitemPojo.getPickerid() == null) wkSmtpartitemPojo.setPickerid("");
        if (wkSmtpartitemPojo.getCustom1() == null) wkSmtpartitemPojo.setCustom1("");
        if (wkSmtpartitemPojo.getCustom2() == null) wkSmtpartitemPojo.setCustom2("");
        if (wkSmtpartitemPojo.getCustom3() == null) wkSmtpartitemPojo.setCustom3("");
        if (wkSmtpartitemPojo.getCustom4() == null) wkSmtpartitemPojo.setCustom4("");
        if (wkSmtpartitemPojo.getCustom5() == null) wkSmtpartitemPojo.setCustom5("");
        if (wkSmtpartitemPojo.getCustom6() == null) wkSmtpartitemPojo.setCustom6("");
        if (wkSmtpartitemPojo.getCustom7() == null) wkSmtpartitemPojo.setCustom7("");
        if (wkSmtpartitemPojo.getCustom8() == null) wkSmtpartitemPojo.setCustom8("");
        if (wkSmtpartitemPojo.getCustom9() == null) wkSmtpartitemPojo.setCustom9("");
        if (wkSmtpartitemPojo.getCustom10() == null) wkSmtpartitemPojo.setCustom10("");
        if (wkSmtpartitemPojo.getTenantid() == null) wkSmtpartitemPojo.setTenantid("");
        if (wkSmtpartitemPojo.getRevision() == null) wkSmtpartitemPojo.setRevision(0);
        return wkSmtpartitemPojo;
    }
}
