package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.*;

import java.util.List;
import java.util.Map;

/**
 * 厂制工单(WkWorksheet)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 15:25:56
 */
public interface WkWorksheetService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWorksheetitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWorksheetPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWorksheetPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWorksheetPojo 实例对象
     * @return 实例对象
     */
    WkWorksheetPojo insert(WkWorksheetPojo wkWorksheetPojo, String token);

    /**
     * 修改数据
     *
     * @param wkWorksheetpojo 实例对象
     * @return 实例对象
     */
    WkWorksheetPojo update(WkWorksheetPojo wkWorksheetpojo, String token);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid, String token);

    /**
     * 审核数据
     *
     * @param wkWorksheetPojo 实例对象
     * @return 实例对象
     */
    WkWorksheetPojo approval(WkWorksheetPojo wkWorksheetPojo);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    WkWorksheetPojo disannul(List<WkWorksheetitemPojo> lst, Integer type, LoginUser loginUser);

    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    WkWorksheetPojo closed(List<WkWorksheetitemPojo> lst, Integer type, LoginUser loginUser);

    /**
     * List<WkWipnotePojo> lst,
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetPojo getBillEntityByRefNo(String key, String tid);
    WkWorksheetPojo getBillEntityByItemid(String workitemid, String tid);

    Map<String, Object> getMachitemWKqty(String machitemid, String tid);

    int updateMergeCount(String pid, String tid);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);

    void updatePrintcount(WkWorksheetPojo billPrintPojo);

    WkWorksheetitemdetailPojo getItemDetailEntity(String key, String tenantid);

    List<MatSpecpcbitemPojo> getMatSpecpcbitemByGoodsid(String goodsid, String tenantid);

    Map<String, Object> getMatSpecpcbByGoodsid(String goodsid, String tenantid);

    WkMrpPojo contrastItemList(String key, LoginUser loginUser);

    Map<String, Object> refreshItemList(String key, String cmd, LoginUser loginUser);

    WkWorksheetPojo getMergeBillEntity(String key, String tenantid);

    double getQuantityByMachitemid(String machitemid, String tenantid);

    String getIdByRefNo(String refno, String tenantid);
}
