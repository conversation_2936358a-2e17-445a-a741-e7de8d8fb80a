package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWiptasksPojo;
import inks.service.std.manu.domain.pojo.WkWiptasksitemdetailPojo;
import inks.service.std.manu.domain.WkWiptasksEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 派工单(WkWiptasks)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-06 14:33:54
 */
@Mapper
public interface WkWiptasksMapper {

    WkWiptasksPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<WkWiptasksitemdetailPojo> getPageList(QueryParam queryParam);

    List<WkWiptasksPojo> getPageTh(QueryParam queryParam);

    int insert(WkWiptasksEntity wkWiptasksEntity);

    int update(WkWiptasksEntity wkWiptasksEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(WkWiptasksPojo wkWiptasksPojo);

    int approval(WkWiptasksEntity wkWiptasksEntity);

    String getWkWpNameByMachitemid(String machitemid, String tid);

    String getWkWpNameByWorkitemid(String workitemid, String tid);

    void updateDisannulCount(String pid, String tid);

    void updateFinishCount(String pid, String tid);

    List<WkWiptasksitemdetailPojo> getOnlinePageListByWip(QueryParam queryParam);
}

