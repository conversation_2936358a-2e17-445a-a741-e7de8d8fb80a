package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSccosttypePojo;

/**
 * 委外费用类型(WkSccosttype)表服务接口
 *
 * <AUTHOR>
 * @since 2023-11-13 15:12:07
 */
public interface WkSccosttypeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccosttypePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSccosttypePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSccosttypePojo 实例对象
     * @return 实例对象
     */
    WkSccosttypePojo insert(WkSccosttypePojo wkSccosttypePojo);

    /**
     * 修改数据
     *
     * @param wkSccosttypepojo 实例对象
     * @return 实例对象
     */
    WkSccosttypePojo update(WkSccosttypePojo wkSccosttypepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
