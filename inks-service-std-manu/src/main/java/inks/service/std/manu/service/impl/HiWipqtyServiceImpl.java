package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.service.std.manu.domain.HiWipqtyEntity;
import inks.service.std.manu.domain.constant.MyConstant;
import inks.service.std.manu.domain.pojo.HiWipqtyPojo;
import inks.service.std.manu.mapper.HiWipnoteMapper;
import inks.service.std.manu.mapper.HiWipqtyMapper;
import inks.service.std.manu.service.HiWipnoteService;
import inks.service.std.manu.service.HiWipqtyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 生产过数(HiWipqty)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-16 16:20:44
 */
@Service("hiWipqtyService")
public class HiWipqtyServiceImpl implements HiWipqtyService {
    private final static Logger log = LoggerFactory.getLogger(HiWipqtyServiceImpl.class);

    @Resource
    private HiWipqtyMapper hiWipqtyMapper;
    @Resource
    private HiWipnoteMapper hiWipnoteMapper;
    @Resource
    private HiWipnoteService hiWipnoteService;
    @Resource
    private RedisService redisService;
    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public HiWipqtyPojo getEntity(String key, String tid) {
        return this.hiWipqtyMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<HiWipqtyPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<HiWipqtyPojo> lst = hiWipqtyMapper.getPageList(queryParam);
            PageInfo<HiWipqtyPojo> pageInfo = new PageInfo<HiWipqtyPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param hiWipqtyPojo 实例对象
     * @return 实例对象
     */
    @Override
    public HiWipqtyPojo insert(HiWipqtyPojo hiWipqtyPojo) {
        //初始化NULL字段
        if (hiWipqtyPojo.getRefno() == null) hiWipqtyPojo.setRefno("");
        if (hiWipqtyPojo.getWpid() == null) hiWipqtyPojo.setWpid("");
        if (hiWipqtyPojo.getWpcode() == null) hiWipqtyPojo.setWpcode("");
        if (hiWipqtyPojo.getWpname() == null) hiWipqtyPojo.setWpname("");
        if (hiWipqtyPojo.getWkdate() == null) hiWipqtyPojo.setWkdate(new Date());
        if (hiWipqtyPojo.getDirection() == null) hiWipqtyPojo.setDirection("");
        if (hiWipqtyPojo.getGoodsid() == null) hiWipqtyPojo.setGoodsid("");
        if (hiWipqtyPojo.getWorker() == null) hiWipqtyPojo.setWorker("");
        if (hiWipqtyPojo.getPcsqty() == null) hiWipqtyPojo.setPcsqty(0D);
        if (hiWipqtyPojo.getSecqty() == null) hiWipqtyPojo.setSecqty(0D);
        if (hiWipqtyPojo.getRemark() == null) hiWipqtyPojo.setRemark("");
        if (hiWipqtyPojo.getWorkuid() == null) hiWipqtyPojo.setWorkuid("");
        if (hiWipqtyPojo.getWipuid() == null) hiWipqtyPojo.setWipuid("");
        if (hiWipqtyPojo.getWiprownum() == null) hiWipqtyPojo.setWiprownum(0);
        if (hiWipqtyPojo.getWipitemid() == null) hiWipqtyPojo.setWipitemid("");
        if (hiWipqtyPojo.getMrbpcsqty() == null) hiWipqtyPojo.setMrbpcsqty(0D);
        if (hiWipqtyPojo.getMrbsecqty() == null) hiWipqtyPojo.setMrbsecqty(0D);
        if (hiWipqtyPojo.getMrbid() == null) hiWipqtyPojo.setMrbid("");
        if (hiWipqtyPojo.getAcceid() == null) hiWipqtyPojo.setAcceid("");
        if (hiWipqtyPojo.getInspector() == null) hiWipqtyPojo.setInspector("");
        if (hiWipqtyPojo.getCreateby() == null) hiWipqtyPojo.setCreateby("");
        if (hiWipqtyPojo.getCreatebyid() == null) hiWipqtyPojo.setCreatebyid("");
        if (hiWipqtyPojo.getCreatedate() == null) hiWipqtyPojo.setCreatedate(new Date());
        if (hiWipqtyPojo.getLister() == null) hiWipqtyPojo.setLister("");
        if (hiWipqtyPojo.getListerid() == null) hiWipqtyPojo.setListerid("");
        if (hiWipqtyPojo.getModifydate() == null) hiWipqtyPojo.setModifydate(new Date());
        if (hiWipqtyPojo.getMachuid() == null) hiWipqtyPojo.setMachuid("");
        if (hiWipqtyPojo.getMachitemid() == null) hiWipqtyPojo.setMachitemid("");
        if (hiWipqtyPojo.getMachgroupid() == null) hiWipqtyPojo.setMachgroupid("");
        if (hiWipqtyPojo.getMainplanuid() == null) hiWipqtyPojo.setMainplanuid("");
        if (hiWipqtyPojo.getMainplanitemid() == null) hiWipqtyPojo.setMainplanitemid("");
        if (hiWipqtyPojo.getWorkitemid() == null) hiWipqtyPojo.setWorkitemid("");
        if (hiWipqtyPojo.getAttributejson() == null) hiWipqtyPojo.setAttributejson("");
        if (hiWipqtyPojo.getSpecjson() == null) hiWipqtyPojo.setSpecjson("");
        if (hiWipqtyPojo.getSizex() == null) hiWipqtyPojo.setSizex(0D);
        if (hiWipqtyPojo.getSizey() == null) hiWipqtyPojo.setSizey(0D);
        if (hiWipqtyPojo.getSizez() == null) hiWipqtyPojo.setSizez(0D);
        if (hiWipqtyPojo.getWorkparam() == null) hiWipqtyPojo.setWorkparam("");
        if (hiWipqtyPojo.getStatid() == null) hiWipqtyPojo.setStatid("");
        if (hiWipqtyPojo.getStatcode() == null) hiWipqtyPojo.setStatcode("");
        if (hiWipqtyPojo.getStatname() == null) hiWipqtyPojo.setStatname("");
        if (hiWipqtyPojo.getWorktime() == null) hiWipqtyPojo.setWorktime(0D);
        if (hiWipqtyPojo.getCustom1() == null) hiWipqtyPojo.setCustom1("");
        if (hiWipqtyPojo.getCustom2() == null) hiWipqtyPojo.setCustom2("");
        if (hiWipqtyPojo.getCustom3() == null) hiWipqtyPojo.setCustom3("");
        if (hiWipqtyPojo.getCustom4() == null) hiWipqtyPojo.setCustom4("");
        if (hiWipqtyPojo.getCustom5() == null) hiWipqtyPojo.setCustom5("");
        if (hiWipqtyPojo.getCustom6() == null) hiWipqtyPojo.setCustom6("");
        if (hiWipqtyPojo.getCustom7() == null) hiWipqtyPojo.setCustom7("");
        if (hiWipqtyPojo.getCustom8() == null) hiWipqtyPojo.setCustom8("");
        if (hiWipqtyPojo.getCustom9() == null) hiWipqtyPojo.setCustom9("");
        if (hiWipqtyPojo.getCustom10() == null) hiWipqtyPojo.setCustom10("");
        if (hiWipqtyPojo.getTenantid() == null) hiWipqtyPojo.setTenantid("");
        if (hiWipqtyPojo.getRevision() == null) hiWipqtyPojo.setRevision(0);
        HiWipqtyEntity hiWipqtyEntity = new HiWipqtyEntity();
        BeanUtils.copyProperties(hiWipqtyPojo, hiWipqtyEntity);
        //生成雪花id
        hiWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        hiWipqtyEntity.setRevision(1);  //乐观锁
        this.hiWipqtyMapper.insert(hiWipqtyEntity);
        return this.getEntity(hiWipqtyEntity.getId(), hiWipqtyEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param hiWipqtyPojo 实例对象
     * @return 实例对象
     */
    @Override
    public HiWipqtyPojo update(HiWipqtyPojo hiWipqtyPojo) {
        HiWipqtyEntity hiWipqtyEntity = new HiWipqtyEntity();
        BeanUtils.copyProperties(hiWipqtyPojo, hiWipqtyEntity);
        this.hiWipqtyMapper.update(hiWipqtyEntity);
        return this.getEntity(hiWipqtyEntity.getId(), hiWipqtyEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.hiWipqtyMapper.delete(key, tid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int moveNowToHi(String startdate, String enddate, String tid) {
        // 方法加锁
        String lockKey = "wipqty_moveNowToHi_lock:" + tid;
        Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 20, TimeUnit.MINUTES);
        if (!Boolean.TRUE.equals(locked)) {
            throw new BaseBusinessException("已有迁移任务正在执行，请稍后再试");
        }
        Map<String, Object> progress = new HashMap<>();
        String progressKey = MyConstant.WIPQTY_NOW_TO_HI + tid;
        try {
            // 添加表结构验证：在迁移前检查源表和目标表的字段名、类型及顺序是否一致
            hiWipnoteService.validateTableStructure("Wk_WipQty", "Hi_WipQty");

            // 0.先拿到时间范围内所有在线的WipNote的RefNo(禁止迁移到Hi)
            List<String> onlineRefNos = this.hiWipqtyMapper.getOnlineNowWipNoteRefNos(startdate, enddate, tid);

            // 获取所有可迁移的ID
            List<String> allIds = hiWipqtyMapper.getAllMigratableNowIds(startdate, enddate, onlineRefNos, tid);

            // 记录迁移进度到Redis
            progress.put("total", allIds.size());
            progress.put("finish", 0);
            redisService.setCacheObject(progressKey, progress, 20L, TimeUnit.SECONDS);

            // 设置批处理大小：每次迁移1000条数据
            int batchSize = 1000;
            int totalCount = 0;

            for (int i = 0; i < allIds.size(); i += batchSize) {
                int end = Math.min(i + batchSize, allIds.size());
                List<String> batch = allIds.subList(i, end);

                int count1 = hiWipqtyMapper.copyNowToHiByIds(batch, tid);
                int count2 = hiWipqtyMapper.deleteNowByIds(batch, tid);

                if (count1 != count2) {
                    throw new BaseBusinessException("WipQty表批次迁移插入条数和删除条数不一致-->事务回滚");
                }

                totalCount += count1;

                // 更新进度
                progress.put("finish", end);
                progress.put("msg", String.format("迁移进度：%d/%d, 总共迁移 %d 条", end, allIds.size(), totalCount));
                redisService.setCacheObject(progressKey, progress, 20L, TimeUnit.SECONDS);

                log.info("WipQty迁移进度：{}/{}，本批次{}条", end, allIds.size(), count1);
            }

            return totalCount;

        } catch (Exception ex) {
            // 记录错误信息到Redis
            progress.put("error", ex.getMessage());
            redisService.setCacheObject(progressKey, progress, 20L, TimeUnit.SECONDS);
            // 继续抛出，触发事务回滚
            throw ex;
        } finally {
            // 释放方法锁
            redisTemplate.delete(lockKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int moveHiToNow(String startdate, String enddate, List<String> ids, String tid) {
        // 方法加锁
        String lockKey = "wipqty_moveHiToNow_lock:" + tid;
        Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 20, TimeUnit.MINUTES);
        if (!Boolean.TRUE.equals(locked)) {
            throw new BaseBusinessException("已有迁移任务正在执行，请稍后再试");
        }
        Map<String, Object> progress = new HashMap<>();
        String progressKey = MyConstant.WIPQTY_HI_TO_NOW + tid;
        try {
            // 添加表结构验证：在迁移前检查源表和目标表的字段名、类型及顺序是否一致
            hiWipnoteService.validateTableStructure("Wk_WipQty", "Hi_WipQty");

            // 如果未指定IDs，则获取符合时间范围的所有ID
            if (ids == null || ids.isEmpty()) {
                ids = hiWipqtyMapper.getAllMigratableHiIds(startdate, enddate, tid);
            }

            // 记录迁移进度到Redis
            progress.put("total", ids.size());
            progress.put("finish", 0);
            redisService.setCacheObject(progressKey, progress, 20L, TimeUnit.SECONDS);

            // 设置批处理大小：每次迁移1000条数据
            int batchSize = 1000;
            int totalCount = 0;

            for (int i = 0; i < ids.size(); i += batchSize) {
                int end = Math.min(i + batchSize, ids.size());
                List<String> batchIds = ids.subList(i, end);

                // 复制数据到Now表
                int count1 = hiWipqtyMapper.copyHiToNowByIds(batchIds, tid);
                // 从Hi表删除已迁移数据
                int count2 = hiWipqtyMapper.deleteHiByIds(batchIds, tid);

                // 校验本批次迁移条数是否一致
                if (count1 != count2) {
                    throw new BaseBusinessException("WipQty表批次迁移插入条数和删除条数不一致-->事务回滚");
                }

                totalCount += count1;

                // 更新进度到Redis
                progress.put("finish", end);
                progress.put("msg", String.format("迁移进度：%d/%d, 总共迁移 %d 条", end, ids.size(), totalCount));
                redisService.setCacheObject(progressKey, progress, 20L, TimeUnit.SECONDS);

                log.info("WipQty迁移进度：{}/{}，本批次{}条", end, ids.size(), count1);
            }

            return totalCount;

        } catch (Exception ex) {
            // 记录错误信息到Redis
            progress.put("error", ex.getMessage());
            redisService.setCacheObject(progressKey, progress, 20L, TimeUnit.SECONDS);
            // 继续抛出，触发事务回滚
            throw ex;
        } finally {
            // 释放方法锁
            redisTemplate.delete(lockKey);
        }
    }

}
