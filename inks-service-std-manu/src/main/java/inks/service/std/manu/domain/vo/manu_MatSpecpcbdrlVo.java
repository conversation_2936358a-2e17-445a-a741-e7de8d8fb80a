package inks.service.std.manu.domain.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * Pcb工艺Drl(MatSpecpcbdrl)Pojo
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:46
 */
public class manu_MatSpecpcbdrlVo implements Serializable {
    private static final long serialVersionUID = -74797902883611163L;
    // id
    @Excel(name = "id")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 符号
    @Excel(name = "符号")
    private String symbol;
    // 孔径
    @Excel(name = "孔径")
    private Double holesize;
    // 公差
    @Excel(name = "公差")
    private String tolerance;
    // 刀径
    @Excel(name = "刀径")
    private Double drillsize;
    // PCS合计
    @Excel(name = "PCS合计")
    private Integer pcstotal;
    // Set合计
    @Excel(name = "Set合计")
    private Integer settotal;
    // PNL合计
    @Excel(name = "PNL合计")
    private Integer pnltotal;
    // PTH
    @Excel(name = "PTH")
    private String pthmark;
    // 备注
    @Excel(name = "备注")
    private String remark;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 符号
    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    // 孔径
    public Double getHolesize() {
        return holesize;
    }

    public void setHolesize(Double holesize) {
        this.holesize = holesize;
    }

    // 公差
    public String getTolerance() {
        return tolerance;
    }

    public void setTolerance(String tolerance) {
        this.tolerance = tolerance;
    }

    // 刀径
    public Double getDrillsize() {
        return drillsize;
    }

    public void setDrillsize(Double drillsize) {
        this.drillsize = drillsize;
    }

    // PCS合计
    public Integer getPcstotal() {
        return pcstotal;
    }

    public void setPcstotal(Integer pcstotal) {
        this.pcstotal = pcstotal;
    }

    // Set合计
    public Integer getSettotal() {
        return settotal;
    }

    public void setSettotal(Integer settotal) {
        this.settotal = settotal;
    }

    // PNL合计
    public Integer getPnltotal() {
        return pnltotal;
    }

    public void setPnltotal(Integer pnltotal) {
        this.pnltotal = pnltotal;
    }

    // PTH
    public String getPthmark() {
        return pthmark;
    }

    public void setPthmark(String pthmark) {
        this.pthmark = pthmark;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


}

