package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWscarryoveritemPojo;

import java.util.List;

/**
 * 结转子表(WkWscarryoveritem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-09 10:53:29
 */
public interface WkWscarryoveritemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWscarryoveritemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWscarryoveritemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWscarryoveritemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkWscarryoveritemPojo 实例对象
     * @return 实例对象
     */
    WkWscarryoveritemPojo insert(WkWscarryoveritemPojo wkWscarryoveritemPojo);

    /**
     * 修改数据
     *
     * @param wkWscarryoveritempojo 实例对象
     * @return 实例对象
     */
    WkWscarryoveritemPojo update(WkWscarryoveritemPojo wkWscarryoveritempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkWscarryoveritempojo 实例对象
     * @return 实例对象
     */
    WkWscarryoveritemPojo clearNull(WkWscarryoveritemPojo wkWscarryoveritempojo);
}
