package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSubcontractitemEntity;
import inks.service.std.manu.domain.pojo.WkSubcontractitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 委制项目(WkSubcontractitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 13:33:01
 */
 @Mapper
public interface WkSubcontractitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSubcontractitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSubcontractitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSubcontractitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkSubcontractitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSubcontractitemEntity wkSubcontractitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkSubcontractitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkSubcontractitemEntity wkSubcontractitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int updateWkSubcontractFinishCount(@Param("key") String key, @Param("tid") String tid);
}

