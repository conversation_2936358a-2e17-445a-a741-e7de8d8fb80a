package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkCostbudgetEntity;
import inks.service.std.manu.domain.pojo.WkCostbudgetPojo;
import inks.service.std.manu.domain.pojo.WkCostbudgetitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成本预测(WkCostbudget)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-03 19:16:26
 */
@Mapper
public interface WkCostbudgetMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCostbudgetPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkCostbudgetitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkCostbudgetPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkCostbudgetEntity 实例对象
     * @return 影响行数
     */
    int insert(WkCostbudgetEntity wkCostbudgetEntity);


    /**
     * 修改数据
     *
     * @param wkCostbudgetEntity 实例对象
     * @return 影响行数
     */
    int update(WkCostbudgetEntity wkCostbudgetEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkCostbudgetPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkCostbudgetPojo wkCostbudgetPojo);

    /**
     * 查询 被删除的Item
     *
     * @param wkCostbudgetPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelMatIds(WkCostbudgetPojo wkCostbudgetPojo);


    /**
     * 查询 被删除的Item
     *
     * @param wkCostbudgetPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelCostIds(WkCostbudgetPojo wkCostbudgetPojo);

    /**
     * 修改数据
     *
     * @param wkCostbudgetEntity 实例对象
     * @return 影响行数
     */
    int approval(WkCostbudgetEntity wkCostbudgetEntity);
}

