package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipepfinishingitemEntity;
import inks.service.std.manu.domain.pojo.WkWipepfinishingitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工序收货项目(WkWipepfinishingitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-26 10:07:14
 */
 @Mapper
public interface WkWipepfinishingitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipepfinishingitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipepfinishingitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipepfinishingitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkWipepfinishingitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipepfinishingitemEntity wkWipepfinishingitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkWipepfinishingitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipepfinishingitemEntity wkWipepfinishingitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

