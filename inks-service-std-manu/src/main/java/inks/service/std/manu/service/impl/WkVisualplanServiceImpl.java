package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkVisualplanEntity;
import inks.service.std.manu.domain.pojo.WkVisualplanPojo;
import inks.service.std.manu.mapper.WkVisualplanMapper;
import inks.service.std.manu.service.WkVisualplanService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 可视化排程(WkVisualplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-01 15:10:03
 */
@Service("wkVisualplanService")
public class WkVisualplanServiceImpl implements WkVisualplanService {
    @Resource
    private WkVisualplanMapper wkVisualplanMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkVisualplanPojo getEntity(String key, String tid) {
        return this.wkVisualplanMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkVisualplanPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkVisualplanPojo> lst = wkVisualplanMapper.getPageList(queryParam);
            PageInfo<WkVisualplanPojo> pageInfo = new PageInfo<WkVisualplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkVisualplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkVisualplanPojo insert(WkVisualplanPojo wkVisualplanPojo) {
        //初始化NULL字段
        if (wkVisualplanPojo.getGengroupid() == null) wkVisualplanPojo.setGengroupid("");
        if (wkVisualplanPojo.getBillid() == null) wkVisualplanPojo.setBillid("");
        if (wkVisualplanPojo.getBillcode() == null) wkVisualplanPojo.setBillcode("");
        if (wkVisualplanPojo.getBilldate() == null) wkVisualplanPojo.setBilldate(new Date());
        if (wkVisualplanPojo.getBilltype() == null) wkVisualplanPojo.setBilltype("");
        if (wkVisualplanPojo.getWorkshopid() == null) wkVisualplanPojo.setWorkshopid("");
        if (wkVisualplanPojo.getWorkshop() == null) wkVisualplanPojo.setWorkshop("");
        if (wkVisualplanPojo.getWpid() == null) wkVisualplanPojo.setWpid("");
        if (wkVisualplanPojo.getWpcode() == null) wkVisualplanPojo.setWpcode("");
        if (wkVisualplanPojo.getWpname() == null) wkVisualplanPojo.setWpname("");
        if (wkVisualplanPojo.getGoodsid() == null) wkVisualplanPojo.setGoodsid("");
        if (wkVisualplanPojo.getItemcode() == null) wkVisualplanPojo.setItemcode("");
        if (wkVisualplanPojo.getItemname() == null) wkVisualplanPojo.setItemname("");
        if (wkVisualplanPojo.getItemspec() == null) wkVisualplanPojo.setItemspec("");
        if (wkVisualplanPojo.getItemunit() == null) wkVisualplanPojo.setItemunit("");
        if (wkVisualplanPojo.getQuantity() == null) wkVisualplanPojo.setQuantity(0D);
        if (wkVisualplanPojo.getStartdate() == null) wkVisualplanPojo.setStartdate(new Date());
        if (wkVisualplanPojo.getEnddate() == null) wkVisualplanPojo.setEnddate(new Date());
        if (wkVisualplanPojo.getEfficiency() == null) wkVisualplanPojo.setEfficiency(0D);
        if (wkVisualplanPojo.getFinishqty() == null) wkVisualplanPojo.setFinishqty(0D);
        if (wkVisualplanPojo.getRemark() == null) wkVisualplanPojo.setRemark("");
        if (wkVisualplanPojo.getJsoncontent() == null) wkVisualplanPojo.setJsoncontent("");
        if (wkVisualplanPojo.getClosed() == null) wkVisualplanPojo.setClosed(0);
        if (wkVisualplanPojo.getCreateby() == null) wkVisualplanPojo.setCreateby("");
        if (wkVisualplanPojo.getCreatebyid() == null) wkVisualplanPojo.setCreatebyid("");
        if (wkVisualplanPojo.getCreatedate() == null) wkVisualplanPojo.setCreatedate(new Date());
        if (wkVisualplanPojo.getLister() == null) wkVisualplanPojo.setLister("");
        if (wkVisualplanPojo.getListerid() == null) wkVisualplanPojo.setListerid("");
        if (wkVisualplanPojo.getModifydate() == null) wkVisualplanPojo.setModifydate(new Date());
        if (wkVisualplanPojo.getCustom1() == null) wkVisualplanPojo.setCustom1("");
        if (wkVisualplanPojo.getCustom2() == null) wkVisualplanPojo.setCustom2("");
        if (wkVisualplanPojo.getCustom3() == null) wkVisualplanPojo.setCustom3("");
        if (wkVisualplanPojo.getCustom4() == null) wkVisualplanPojo.setCustom4("");
        if (wkVisualplanPojo.getCustom5() == null) wkVisualplanPojo.setCustom5("");
        if (wkVisualplanPojo.getCustom6() == null) wkVisualplanPojo.setCustom6("");
        if (wkVisualplanPojo.getCustom7() == null) wkVisualplanPojo.setCustom7("");
        if (wkVisualplanPojo.getCustom8() == null) wkVisualplanPojo.setCustom8("");
        if (wkVisualplanPojo.getCustom9() == null) wkVisualplanPojo.setCustom9("");
        if (wkVisualplanPojo.getCustom10() == null) wkVisualplanPojo.setCustom10("");
        if (wkVisualplanPojo.getTenantid() == null) wkVisualplanPojo.setTenantid("");
        if (wkVisualplanPojo.getTenantname() == null) wkVisualplanPojo.setTenantname("");
        if (wkVisualplanPojo.getRevision() == null) wkVisualplanPojo.setRevision(0);
        WkVisualplanEntity wkVisualplanEntity = new WkVisualplanEntity();
        BeanUtils.copyProperties(wkVisualplanPojo, wkVisualplanEntity);

        wkVisualplanEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkVisualplanEntity.setRevision(1);  //乐观锁
        this.wkVisualplanMapper.insert(wkVisualplanEntity);
        return this.getEntity(wkVisualplanEntity.getId(), wkVisualplanEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkVisualplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkVisualplanPojo update(WkVisualplanPojo wkVisualplanPojo) {
        WkVisualplanEntity wkVisualplanEntity = new WkVisualplanEntity();
        BeanUtils.copyProperties(wkVisualplanPojo, wkVisualplanEntity);
        this.wkVisualplanMapper.update(wkVisualplanEntity);
        return this.getEntity(wkVisualplanEntity.getId(), wkVisualplanEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkVisualplanMapper.delete(key, tid);
    }


}
