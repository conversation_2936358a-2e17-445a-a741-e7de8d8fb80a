package inks.service.std.manu.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 派工单子表(WkWiptasksitem)Entity
 *
 * <AUTHOR>
 * @since 2025-04-01 13:11:53
 */
public class WkWiptasksitemEntity implements Serializable {
    private static final long serialVersionUID = 175268822323599314L;
    private String id;
     // Pid
    private String pid;
     // 加工单itemid
    private String wipitemid;
     // 货品id
    private String goodsid;
     // 产品编码
    private String itemcode;
     // 产品名称
    private String itemname;
     // 产品规格
    private String itemspec;
     // 产品单位
    private String itemunit;
     // 产品标签
    private String itemlabel;
     // 模具id
    private String moldid;
     // 模具编码
    private String moldcode;
     // 模具名称
    private String moldname;
     // 生产类型
    private String worktype;
     // 生产车间id
    private String workshopid;
     // 生产车间
    private String workshop;
     // 数量
    private Double quantity;
     // 总投数
    private Double wkpcsqty;
     // 投料Sec
    private Double wksecqty;
     // 总报废
    private Double mrbpcsqty;
     // 报废Sec
    private Double mrbsecqty;
     // 已完数
    private Double finishqty;
     // 客户
    private String customer;
     // 客户PO
    private String custpo;
     // 销售单号
    private String machuid;
     // 销售子项id
    private String machitemid;
     // 销售客户id
    private String machgroupid;
     // 主计划号
    private String mainplanuid;
     // 主计划Itemid
    private String mainplanitemid;
     // 加工单号
    private String workuid;
     // 加工单Itemid
    private String workitemid;
     // 开工日期
    private Date workdate;
     // 摘要
    private String remark;
     // SPU属性
    private String attributejson;
     // SPU文本
    private String attributestr;
     // 关闭
    private Integer closed;
     // 来源:0=其他
    private Integer sourcetype;
     // RowNum
    private Integer rownum;
     // 权重
    private Integer weight;
     // 评审交期
    private Date itemplandate;
     // 作废
    private Integer disannulmark;
     // 作废经办
    private String disannullister;
     // 作废日期
    private Date disannuldate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 加工单itemid
    public String getWipitemid() {
        return wipitemid;
    }
    
    public void setWipitemid(String wipitemid) {
        this.wipitemid = wipitemid;
    }
        
   // 货品id
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 产品编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
   // 产品名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
   // 产品规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
   // 产品单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
   // 产品标签
    public String getItemlabel() {
        return itemlabel;
    }
    
    public void setItemlabel(String itemlabel) {
        this.itemlabel = itemlabel;
    }
        
   // 模具id
    public String getMoldid() {
        return moldid;
    }
    
    public void setMoldid(String moldid) {
        this.moldid = moldid;
    }
        
   // 模具编码
    public String getMoldcode() {
        return moldcode;
    }
    
    public void setMoldcode(String moldcode) {
        this.moldcode = moldcode;
    }
        
   // 模具名称
    public String getMoldname() {
        return moldname;
    }
    
    public void setMoldname(String moldname) {
        this.moldname = moldname;
    }
        
   // 生产类型
    public String getWorktype() {
        return worktype;
    }
    
    public void setWorktype(String worktype) {
        this.worktype = worktype;
    }
        
   // 生产车间id
    public String getWorkshopid() {
        return workshopid;
    }
    
    public void setWorkshopid(String workshopid) {
        this.workshopid = workshopid;
    }
        
   // 生产车间
    public String getWorkshop() {
        return workshop;
    }
    
    public void setWorkshop(String workshop) {
        this.workshop = workshop;
    }
        
   // 数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
   // 总投数
    public Double getWkpcsqty() {
        return wkpcsqty;
    }
    
    public void setWkpcsqty(Double wkpcsqty) {
        this.wkpcsqty = wkpcsqty;
    }
        
   // 投料Sec
    public Double getWksecqty() {
        return wksecqty;
    }
    
    public void setWksecqty(Double wksecqty) {
        this.wksecqty = wksecqty;
    }
        
   // 总报废
    public Double getMrbpcsqty() {
        return mrbpcsqty;
    }
    
    public void setMrbpcsqty(Double mrbpcsqty) {
        this.mrbpcsqty = mrbpcsqty;
    }
        
   // 报废Sec
    public Double getMrbsecqty() {
        return mrbsecqty;
    }
    
    public void setMrbsecqty(Double mrbsecqty) {
        this.mrbsecqty = mrbsecqty;
    }
        
   // 已完数
    public Double getFinishqty() {
        return finishqty;
    }
    
    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }
        
   // 客户
    public String getCustomer() {
        return customer;
    }
    
    public void setCustomer(String customer) {
        this.customer = customer;
    }
        
   // 客户PO
    public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
   // 销售单号
    public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
   // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
   // 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }
    
    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }
        
   // 主计划号
    public String getMainplanuid() {
        return mainplanuid;
    }
    
    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }
        
   // 主计划Itemid
    public String getMainplanitemid() {
        return mainplanitemid;
    }
    
    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }
        
   // 加工单号
    public String getWorkuid() {
        return workuid;
    }
    
    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }
        
   // 加工单Itemid
    public String getWorkitemid() {
        return workitemid;
    }
    
    public void setWorkitemid(String workitemid) {
        this.workitemid = workitemid;
    }
        
   // 开工日期
    public Date getWorkdate() {
        return workdate;
    }
    
    public void setWorkdate(Date workdate) {
        this.workdate = workdate;
    }
        
   // 摘要
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // SPU属性
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // SPU文本
    public String getAttributestr() {
        return attributestr;
    }
    
    public void setAttributestr(String attributestr) {
        this.attributestr = attributestr;
    }
        
   // 关闭
    public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
   // 来源:0=其他
    public Integer getSourcetype() {
        return sourcetype;
    }
    
    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }
        
   // RowNum
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 权重
    public Integer getWeight() {
        return weight;
    }
    
    public void setWeight(Integer weight) {
        this.weight = weight;
    }
        
   // 评审交期
    public Date getItemplandate() {
        return itemplandate;
    }
    
    public void setItemplandate(Date itemplandate) {
        this.itemplandate = itemplandate;
    }
        
   // 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
   // 作废经办
    public String getDisannullister() {
        return disannullister;
    }
    
    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }
        
   // 作废日期
    public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

