package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkScmachtypeEntity;
import inks.service.std.manu.domain.pojo.WkScmachtypePojo;
import inks.service.std.manu.mapper.WkScmachtypeMapper;
import inks.service.std.manu.service.WkScmachtypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 委外加工类型(WkScmachtype)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-13 15:12:07
 */
@Service("wkScmachtypeService")
public class WkScmachtypeServiceImpl implements WkScmachtypeService {
    @Resource
    private WkScmachtypeMapper wkScmachtypeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkScmachtypePojo getEntity(String key, String tid) {
        return this.wkScmachtypeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkScmachtypePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkScmachtypePojo> lst = wkScmachtypeMapper.getPageList(queryParam);
            PageInfo<WkScmachtypePojo> pageInfo = new PageInfo<WkScmachtypePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkScmachtypePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkScmachtypePojo insert(WkScmachtypePojo wkScmachtypePojo) {
        //初始化NULL字段
        if (wkScmachtypePojo.getItemcode() == null) wkScmachtypePojo.setItemcode("");
        if (wkScmachtypePojo.getItemname() == null) wkScmachtypePojo.setItemname("");
        if (wkScmachtypePojo.getRownum() == null) wkScmachtypePojo.setRownum(0);
        if (wkScmachtypePojo.getRemark() == null) wkScmachtypePojo.setRemark("");
        if (wkScmachtypePojo.getCreateby() == null) wkScmachtypePojo.setCreateby("");
        if (wkScmachtypePojo.getCreatebyid() == null) wkScmachtypePojo.setCreatebyid("");
        if (wkScmachtypePojo.getCreatedate() == null) wkScmachtypePojo.setCreatedate(new Date());
        if (wkScmachtypePojo.getLister() == null) wkScmachtypePojo.setLister("");
        if (wkScmachtypePojo.getListerid() == null) wkScmachtypePojo.setListerid("");
        if (wkScmachtypePojo.getModifydate() == null) wkScmachtypePojo.setModifydate(new Date());
        if (wkScmachtypePojo.getCustom1() == null) wkScmachtypePojo.setCustom1("");
        if (wkScmachtypePojo.getCustom2() == null) wkScmachtypePojo.setCustom2("");
        if (wkScmachtypePojo.getCustom3() == null) wkScmachtypePojo.setCustom3("");
        if (wkScmachtypePojo.getCustom4() == null) wkScmachtypePojo.setCustom4("");
        if (wkScmachtypePojo.getCustom5() == null) wkScmachtypePojo.setCustom5("");
        if (wkScmachtypePojo.getCustom6() == null) wkScmachtypePojo.setCustom6("");
        if (wkScmachtypePojo.getCustom7() == null) wkScmachtypePojo.setCustom7("");
        if (wkScmachtypePojo.getCustom8() == null) wkScmachtypePojo.setCustom8("");
        if (wkScmachtypePojo.getCustom9() == null) wkScmachtypePojo.setCustom9("");
        if (wkScmachtypePojo.getCustom10() == null) wkScmachtypePojo.setCustom10("");
        if (wkScmachtypePojo.getTenantid() == null) wkScmachtypePojo.setTenantid("");
        if (wkScmachtypePojo.getTenantname() == null) wkScmachtypePojo.setTenantname("");
        if (wkScmachtypePojo.getRevision() == null) wkScmachtypePojo.setRevision(0);
        WkScmachtypeEntity wkScmachtypeEntity = new WkScmachtypeEntity();
        BeanUtils.copyProperties(wkScmachtypePojo, wkScmachtypeEntity);
        //生成雪花id
        wkScmachtypeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkScmachtypeEntity.setRevision(1);  //乐观锁
        this.wkScmachtypeMapper.insert(wkScmachtypeEntity);
        return this.getEntity(wkScmachtypeEntity.getId(), wkScmachtypeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkScmachtypePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkScmachtypePojo update(WkScmachtypePojo wkScmachtypePojo) {
        WkScmachtypeEntity wkScmachtypeEntity = new WkScmachtypeEntity();
        BeanUtils.copyProperties(wkScmachtypePojo, wkScmachtypeEntity);
        this.wkScmachtypeMapper.update(wkScmachtypeEntity);
        return this.getEntity(wkScmachtypeEntity.getId(), wkScmachtypeEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkScmachtypeMapper.delete(key, tid);
    }


}
