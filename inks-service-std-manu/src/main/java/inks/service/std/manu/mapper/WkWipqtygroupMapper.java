package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipqtygroupEntity;
import inks.service.std.manu.domain.pojo.WkWipqtygroupPojo;
import inks.service.std.manu.domain.pojo.WkWipqtygroupitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 过数小组(WkWipqtygroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-10 11:07:59
 */
@Mapper
public interface WkWipqtygroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtygroupPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipqtygroupitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipqtygroupPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param wkWipqtygroupEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipqtygroupEntity wkWipqtygroupEntity);

    
    /**
     * 修改数据
     *
     * @param wkWipqtygroupEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipqtygroupEntity wkWipqtygroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param wkWipqtygroupPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(WkWipqtygroupPojo wkWipqtygroupPojo);
                                                                                                                        }

