package inks.service.std.manu.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 工序收货项目(WkWipepfinishingitem)Entity
 *
 * <AUTHOR>
 * @since 2022-10-04 11:08:45
 */
public class WkWipepfinishingitemEntity implements Serializable {
    private static final long serialVersionUID = 726268772577215675L;
          // id
         private String id;
          // Pid
         private String pid;
          // 货品编码
         private String goodsid;
          // 产品数量
         private Double quantity;
          // 报废数量
         private Double mrbqty;
          // 辅助单位id
         private String subitemid;
          // 结算方式
         private String subuse;
          // 辅助单位
         private String subunit;
          // 加工数量
         private Double subqty;
          // 含税单价
         private Double taxprice;
          // 含税金额
         private Double taxamount;
          // 税额
         private Double taxtotal;
          // 未税单价
         private Double price;
          // 未税金额
         private Double amount;
          // 记录税率
         private Integer itemtaxrate;
          // 备注
         private String remark;
          // 流水号
         private String citeuid;
          // 子项id
         private String citeitemid;
          // 状态
         private String statecode;
          // 状态时间
         private Date statedate;
          // 已检验
         private Integer inspected;
          // 关闭
         private Integer closed;
          // 行号
         private Integer rownum;
          // 发票数量
         private Double invoqty;
          // 发票关闭
         private Integer invoclosed;
          // 虚拟货品
         private Integer virtualitem;
          // Wipitemid
         private String wipitemid;
          // 加工单ID
         private String wsid;
          // 加工单号
         private String wsuid;
          // 加工工序
         private String wpid;
          // 完工工序
         private String endwpid;
          // 客户
         private String customer;
          // 客户PO
         private String custpo;
          // 销售单号
         private String machuid;
          // 销售子项id
         private String machitemid;
          // 主计划单号
         private String mainplanuid;
          // 主计划子项id
         private String mainplanitemid;
          // 销售客户id
         private String machgroupid;
          // 作废
         private Integer disannulmark;
          // 作废经办id
         private String disannullisterid;
          // 作废经办
         private String disannullister;
          // 作废日期
         private Date disannuldate;
          // 属性Josn
         private String attributejson;
          // 自定义1
         private String custom1;
          // 自定义2
         private String custom2;
          // 自定义3
         private String custom3;
          // 自定义4
         private String custom4;
          // 自定义5
         private String custom5;
          // 自定义6
         private String custom6;
          // 自定义7
         private String custom7;
          // 自定义8
         private String custom8;
          // 自定义9
         private String custom9;
          // 自定义10
         private String custom10;
          // 租户id
         private String tenantid;
          // 乐观锁
         private Integer revision;

    // id
      public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
    // Pid
      public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
    // 货品编码
      public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
    // 产品数量
      public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
    // 报废数量
      public Double getMrbqty() {
        return mrbqty;
    }
    
    public void setMrbqty(Double mrbqty) {
        this.mrbqty = mrbqty;
    }
        
    // 辅助单位id
      public String getSubitemid() {
        return subitemid;
    }
    
    public void setSubitemid(String subitemid) {
        this.subitemid = subitemid;
    }
        
    // 结算方式
      public String getSubuse() {
        return subuse;
    }
    
    public void setSubuse(String subuse) {
        this.subuse = subuse;
    }
        
    // 辅助单位
      public String getSubunit() {
        return subunit;
    }
    
    public void setSubunit(String subunit) {
        this.subunit = subunit;
    }
        
    // 加工数量
      public Double getSubqty() {
        return subqty;
    }
    
    public void setSubqty(Double subqty) {
        this.subqty = subqty;
    }
        
    // 含税单价
      public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
    // 含税金额
      public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
    // 税额
      public Double getTaxtotal() {
        return taxtotal;
    }
    
    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }
        
    // 未税单价
      public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
    // 未税金额
      public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
    // 记录税率
      public Integer getItemtaxrate() {
        return itemtaxrate;
    }
    
    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }
        
    // 备注
      public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
    // 流水号
      public String getCiteuid() {
        return citeuid;
    }
    
    public void setCiteuid(String citeuid) {
        this.citeuid = citeuid;
    }
        
    // 子项id
      public String getCiteitemid() {
        return citeitemid;
    }
    
    public void setCiteitemid(String citeitemid) {
        this.citeitemid = citeitemid;
    }
        
    // 状态
      public String getStatecode() {
        return statecode;
    }
    
    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }
        
    // 状态时间
      public Date getStatedate() {
        return statedate;
    }
    
    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
        
    // 已检验
      public Integer getInspected() {
        return inspected;
    }
    
    public void setInspected(Integer inspected) {
        this.inspected = inspected;
    }
        
    // 关闭
      public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
    // 行号
      public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
    // 发票数量
      public Double getInvoqty() {
        return invoqty;
    }
    
    public void setInvoqty(Double invoqty) {
        this.invoqty = invoqty;
    }
        
    // 发票关闭
      public Integer getInvoclosed() {
        return invoclosed;
    }
    
    public void setInvoclosed(Integer invoclosed) {
        this.invoclosed = invoclosed;
    }
        
    // 虚拟货品
      public Integer getVirtualitem() {
        return virtualitem;
    }
    
    public void setVirtualitem(Integer virtualitem) {
        this.virtualitem = virtualitem;
    }
        
    // Wipitemid
      public String getWipitemid() {
        return wipitemid;
    }
    
    public void setWipitemid(String wipitemid) {
        this.wipitemid = wipitemid;
    }
        
    // 加工单ID
      public String getWsid() {
        return wsid;
    }
    
    public void setWsid(String wsid) {
        this.wsid = wsid;
    }
        
    // 加工单号
      public String getWsuid() {
        return wsuid;
    }
    
    public void setWsuid(String wsuid) {
        this.wsuid = wsuid;
    }
        
    // 加工工序
      public String getWpid() {
        return wpid;
    }
    
    public void setWpid(String wpid) {
        this.wpid = wpid;
    }
        
    // 完工工序
      public String getEndwpid() {
        return endwpid;
    }
    
    public void setEndwpid(String endwpid) {
        this.endwpid = endwpid;
    }
        
    // 客户
      public String getCustomer() {
        return customer;
    }
    
    public void setCustomer(String customer) {
        this.customer = customer;
    }
        
    // 客户PO
      public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
    // 销售单号
      public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
    // 销售子项id
      public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
    // 主计划单号
      public String getMainplanuid() {
        return mainplanuid;
    }
    
    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }
        
    // 主计划子项id
      public String getMainplanitemid() {
        return mainplanitemid;
    }
    
    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }
        
    // 销售客户id
      public String getMachgroupid() {
        return machgroupid;
    }
    
    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }
        
    // 作废
      public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
    // 作废经办id
      public String getDisannullisterid() {
        return disannullisterid;
    }
    
    public void setDisannullisterid(String disannullisterid) {
        this.disannullisterid = disannullisterid;
    }
        
    // 作废经办
      public String getDisannullister() {
        return disannullister;
    }
    
    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }
        
    // 作废日期
      public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
    // 属性Josn
      public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
    // 自定义1
      public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
    // 自定义2
      public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
    // 自定义3
      public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
    // 自定义4
      public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
    // 自定义5
      public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
    // 自定义6
      public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
    // 自定义7
      public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
    // 自定义8
      public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
    // 自定义9
      public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
    // 自定义10
      public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
    // 租户id
      public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
    // 乐观锁
      public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

