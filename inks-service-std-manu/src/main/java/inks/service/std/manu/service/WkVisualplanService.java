package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkVisualplanPojo;

/**
 * 可视化排程(WkVisualplan)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-01 15:10:03
 */
public interface WkVisualplanService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkVisualplanPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkVisualplanPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkVisualplanPojo 实例对象
     * @return 实例对象
     */
    WkVisualplanPojo insert(WkVisualplanPojo wkVisualplanPojo);

    /**
     * 修改数据
     *
     * @param wkVisualplanpojo 实例对象
     * @return 实例对象
     */
    WkVisualplanPojo update(WkVisualplanPojo wkVisualplanpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
