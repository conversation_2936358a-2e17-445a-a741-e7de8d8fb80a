package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSteppriceitemPojo;

import java.util.List;

/**
 * 阶梯项目(WkSteppriceitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-26 16:21:33
 */
public interface WkSteppriceitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSteppriceitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSteppriceitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSteppriceitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkSteppriceitemPojo 实例对象
     * @return 实例对象
     */
    WkSteppriceitemPojo insert(WkSteppriceitemPojo wkSteppriceitemPojo);

    /**
     * 修改数据
     *
     * @param wkSteppriceitempojo 实例对象
     * @return 实例对象
     */
    WkSteppriceitemPojo update(WkSteppriceitemPojo wkSteppriceitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkSteppriceitempojo 实例对象
     * @return 实例对象
     */
    WkSteppriceitemPojo clearNull(WkSteppriceitemPojo wkSteppriceitempojo);
}
