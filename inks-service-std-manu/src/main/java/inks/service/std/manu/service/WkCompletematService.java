package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkCompletematPojo;

import java.util.List;

/**
 * 验收物料(WkCompletemat)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-01 14:40:53
 */
public interface WkCompletematService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCompletematPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCompletematPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkCompletematPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkCompletematPojo 实例对象
     * @return 实例对象
     */
    WkCompletematPojo insert(WkCompletematPojo wkCompletematPojo);

    /**
     * 修改数据
     *
     * @param wkCompletematpojo 实例对象
     * @return 实例对象
     */
    WkCompletematPojo update(WkCompletematPojo wkCompletematpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkCompletematpojo 实例对象
     * @return 实例对象
     */
    WkCompletematPojo clearNull(WkCompletematPojo wkCompletematpojo);
}
