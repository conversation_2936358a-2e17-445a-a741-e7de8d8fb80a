package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.constant.MyConstant;
import inks.service.std.manu.domain.pojo.WkSteppricePojo;
import inks.service.std.manu.domain.pojo.WkSteppriceSpusPojo;
import inks.service.std.manu.domain.pojo.WkSteppriceitemPojo;
import inks.service.std.manu.service.WkSteppriceService;
import inks.service.std.manu.service.WkSteppriceitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 阶梯工价(Wk_StepPrice)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-26 16:21:18
 */
@RestController
@RequestMapping("D05M16B1")
@Api(tags = "D05M16B1:阶梯工价")
public class D05M16B1Controller extends WkSteppriceController {
    @Resource
    private WkSteppriceService wkSteppriceService;
    @Resource
    private WkSteppriceitemService wkSteppriceitemService;
    @Resource
    private TokenService tokenService;
    @Resource
    private RedisService redisService;
    @Resource
    private UtilsFeignService utilsFeignService;
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * @return R<List < Map < String, Object>>>
     * @Description 获取订单生产成本预算(结合阶梯工价计算)
     * <AUTHOR>
     * @param[1] id 订单id
     * @param[2] progroupid 制程id
     * @time 2023/6/28 14:19
     */
    @ApiOperation(value = "10线程获取订单生产成本预算", notes = "订单生产成本预算", produces = "application/json")
    @RequestMapping(value = "/getProductionCostBudget", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_StepPrice.List")
    public R<List<Map<String, Object>>> getProductionCostBudget(String id, String progroupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            long startTime = System.currentTimeMillis(); // 记录方法开始时间
            System.out.println("\u001B[34m=============================前置普通当前线程为：：" + Thread.currentThread().getName() + "\u001B[0m");
            String tid = loginUser.getTenantid();
            // 获取订单子表List
            List<Map<String, Object>> machItemList = wkSteppriceService.getMachItemList(id, tid);
            // 创建线程池 线程池的大小为10，表示最多同时执行10个任务
            ExecutorService executor = Executors.newFixedThreadPool(10);
            List<Future<List<Map<String, Object>>>> futures = new ArrayList<>();
            // 读取指定系统参数：参与计算的阶梯制程SPUS
            String spu_configValue = systemFeignService.getConfigValue("module.manu.stepgroupspus", loginUser.getTenantid(), loginUser.getToken()).getData();
            if (StringUtils.isBlank(spu_configValue)) {
                throw new BaseBusinessException("未找到阶梯制程计算的SPUS： module.manu.stepgroupspus");
            }
            // 提交子任务到线程池(循环订单子表)
            for (Map<String, Object> machItem : machItemList) {
                Future<List<Map<String, Object>>> future = executor.submit(() -> {
                    System.out.println("\u001B[35m=============================开启线程池,当前线程为：：" + Thread.currentThread().getName() + "\u001B[0m");
                    // 调用并行处理的方法，传入不同的参数
                    return this.wkSteppriceService.getProductionCostBudget(machItem, progroupid, spu_configValue, tid);
                });
                futures.add(future);
            }
//            boolean b = executor.awaitTermination(1000, TimeUnit.SECONDS);//作用是阻塞当前线程，等待线程池中的所有任务执行完成。者等待时间超过指定的极长时间。
            // 关闭线程池 shutdown()方法并不会马上关闭，而是不再接收新的任务,之前提交的任务执行完才关闭；shutdownNow()立即停止所有任务，正在执行的任务不确定是否停止
            executor.shutdown();
            // 合并子任务的结果
            List<Map<String, Object>> result = new ArrayList<>();
            for (Future<List<Map<String, Object>>> future : futures) {
                result.addAll(future.get());
            }
            System.out.println("\u001B[33m=============================后置普通当前线程为：：" + Thread.currentThread().getName() + "\u001B[0m");
            long endTime = System.currentTimeMillis(); // 记录方法结束时间
            long executionTime = endTime - startTime; // 计算方法执行时间
            System.out.println("\u001B[33m接口进出时间统计：执行时间为 " + executionTime + " 毫秒\u001B[0m");
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @return R<List < Map < String, Object>>>
     * @Description 获取订单生产成本预算(结合阶梯工价计算)
     * <AUTHOR>   20240202前端只用了这个方法
     * @param[1] id 订单id
     * @param[2] progroupid 制程id
     * @time 2023/6/28 14:19
     */
    @ApiOperation(value = "获取一个订单生产成本预算", notes = "订单生产成本预算", produces = "application/json")
    @RequestMapping(value = "/getOneProductionCostBudget", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_StepPrice.List")
    public R<List<Map<String, Object>>> getOneProductionCostBudget(@RequestBody String json, String progroupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //json转Map<String, Object>
            Map<String, Object> map = JSON.parseObject(json, Map.class);
            String tid = loginUser.getTenantid();
            // 读取指定系统参数：参与计算的阶梯制程SPUS
            String spu_configValue = systemFeignService.getConfigValue("module.manu.stepgroupspus", loginUser.getTenantid(), loginUser.getToken()).getData();
            if (StringUtils.isBlank(spu_configValue)) {
                throw new BaseBusinessException("未找到阶梯制程计算的SPUS module.manu.stepgroupspus");
            }
            List<Map<String, Object>> productionCostBudget = this.wkSteppriceService.getProductionCostBudget(map, progroupid, spu_configValue, tid);
            return R.ok(productionCostBudget);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    /**
//     * @Description
//     * @return R<List < Map < Object>>>
//     * @Description 获取订单生产成本预算
//     * <AUTHOR>
//     * @param[1] json
//     * @param[1] json 包含goodsid,attributejson,quantity 格式如下：
//    {
//    "goodsid": "33e3548d-44bb-4276-b4db-3598cf66ab3f",
//    "attributejson": "[{\"key\":\"spuchang\",\"value\":\"1000\"},{\"key\":\"spuhou\",\"value\":\"10\"}]",
//    "quantity": "10"
//    }
//     * @param[2] progroupid 制程id
//     * @return R<List<Map<Object>>>
//     * @time 2023/6/28 14:19
//     */
//    @ApiOperation(value = " 获取订单生产成本预算", notes = "订单生产成本预算", produces = "application/json")
//    @RequestMapping(value = "/getProductionCostBudget", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Wk_StepPrice.List")
//    public R<List<Map<String, Object>>> getProductionCostBudget111(@RequestBody String json, String progroupid) {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            Map<String, Object> jsonMap = JSON.parseObject(json, Map.class);
//            String goodsid = jsonMap.get("goodsid").toString();
//            String attributejson = jsonMap.get("attributejson").toString();
//            String quantity = jsonMap.get("quantity").toString();
//            return R.ok(this.wkSteppriceService.getProductionCostBudget(jsonMap, progroupid, loginUser.getTenantid()));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }


    //------------------------------------------------------异步------------------------------------------------
    @ApiOperation(value = "开始异步10线程获取订单生产成本预算", notes = "订单生产成本预算", produces = "application/json")
    @RequestMapping(value = "/getProductionCostBudgetStart", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_StepPrice.List")
    public R<String> getProductionCostBudgetStart(String id, String progroupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String redisKey = UUID.randomUUID().toString();
            // 读取指定系统参数：参与计算的阶梯制程SPUS
            String spu_configValue = systemFeignService.getConfigValue("module.manu.stepgroupspus", loginUser.getTenantid(), loginUser.getToken()).getData();
            if (StringUtils.isBlank(spu_configValue)) {
                throw new BaseBusinessException("未找到阶梯制程计算的SPUS module.manu.stepgroupspus");
            }
            // 开始异步
            this.wkSteppriceService.getProductionCostBudgetStart(redisKey, id, progroupid, spu_configValue, loginUser.getTenantid());
            return R.ok(redisKey);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取异步状态", notes = "getAsyncState", produces = "application/json")
    @RequestMapping(value = "/getAsyncState", method = RequestMethod.GET)
    public R<Map<String, Object>> getAsyncState(@RequestParam String rediskey) {
        return this.redisService.getCacheMapValue(MyConstant.ASYNC_MACHPRICE_STATE, rediskey);
    }

    @ApiOperation(value = "获取异步结果", notes = "获取异步结果", produces = "application/json")
    @RequestMapping(value = "/getAsyncResult", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> getAsyncResult(@RequestParam String rediskey) {
        try {
            Object cacheObject = this.redisService.getCacheObject(rediskey);
            // 将缓存对象转换为 List<Map<String, Object>> 类型的结果
            List<Map<String, Object>> result = JSON.parseObject(cacheObject.toString(), new TypeReference<List<Map<String, Object>>>() {
            });
            return R.ok(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @ApiOperation(value = "1线程获取订单生产成本预算", notes = "订单生产成本预算", produces = "application/json")
    @RequestMapping(value = "/getProductionCostBudget2", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_StepPrice.List")
    public R<List<Map<String, Object>>> getProductionCostBudget2(String id, String progroupid) {
        try {
            long startTime = System.currentTimeMillis(); // 记录方法开始时间
//            System.out.println("\u001B[34m=============================前置普通当前线程为：：" + Thread.currentThread().getName() + "\u001B[0m");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            // 获取订单子表List
            List<Map<String, Object>> machItemList = wkSteppriceService.getMachItemList(id, tid);
            // 读取指定系统参数：参与计算的阶梯制程SPUS
            String spu_configValue = systemFeignService.getConfigValue("module.manu.stepgroupspus", loginUser.getTenantid(), loginUser.getToken()).getData();
            if (StringUtils.isBlank(spu_configValue)) {
                throw new BaseBusinessException("未找到阶梯制程计算的SPUS module.manu.stepgroupspus");
            }
            // 处理子任务的结果
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map<String, Object> machItem : machItemList) {
                List<Map<String, Object>> subResult = this.wkSteppriceService.getProductionCostBudget(machItem, progroupid, spu_configValue, tid);
                result.addAll(subResult);
            }

//            System.out.println("\u001B[33m=============================后置普通当前线程为：：" + Thread.currentThread().getName() + "\u001B[0m");
            long endTime = System.currentTimeMillis(); // 记录方法结束时间
            long executionTime = endTime - startTime; // 计算方法执行时间
            System.out.println("\u001B[33m接口进出时间统计：执行时间为 " + executionTime + " 毫秒\u001B[0m");

            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deposit.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            WkSteppricePojo wkSteppricePojo = this.wkSteppriceService.getEntity(key, loginUser.getTenantid());
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(wkSteppricePojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<WkSteppriceitemPojo> lstitem = this.wkSteppriceitemService.getList(key, loginUser.getTenantid());
            //=========获取单据cash信息========
            // 子表2:  wkSteppricePojo.getSpujson()转为的Spus子表
            List<WkSteppriceSpusPojo> lstspu = JSONArray.parseArray(wkSteppricePojo.getSpujson(), WkSteppriceSpusPojo.class);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("Spu", attrListToMaps(lstspu));
            mapreport.put("Item", attrListToMaps(lstitem));
            mapreport.put("Master", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("xml", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            // 打印命令
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "阶梯工价" + wkSteppricePojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
