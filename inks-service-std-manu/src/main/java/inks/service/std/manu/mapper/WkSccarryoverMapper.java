package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSccarryoverEntity;
import inks.service.std.manu.domain.pojo.WkSccarryoverPojo;
import inks.service.std.manu.domain.pojo.WkSccarryoveritemPojo;
import inks.service.std.manu.domain.pojo.WkSccarryoveritemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 加工结转(WkSccarryover)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-09 14:04:49
 */
@Mapper
public interface WkSccarryoverMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccarryoverPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSccarryoveritemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSccarryoverPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSccarryoverEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSccarryoverEntity wkSccarryoverEntity);


    /**
     * 修改数据
     *
     * @param wkSccarryoverEntity 实例对象
     * @return 影响行数
     */
    int update(WkSccarryoverEntity wkSccarryoverEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkSccarryoverPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkSccarryoverPojo wkSccarryoverPojo);


    /**
     * 根据
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<WkSccarryoveritemPojo> getGoodsScList(QueryParam queryParam);

    /**
     * 根据
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<WkSccarryoveritemPojo> getGoodsAcceList(QueryParam queryParam);
}

