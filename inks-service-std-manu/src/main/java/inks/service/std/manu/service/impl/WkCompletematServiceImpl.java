package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkCompletematEntity;
import inks.service.std.manu.domain.pojo.WkCompletematPojo;
import inks.service.std.manu.mapper.WkCompletematMapper;
import inks.service.std.manu.service.WkCompletematService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 验收物料(WkCompletemat)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-01 14:40:54
 */
@Service("wkCompletematService")
public class WkCompletematServiceImpl implements WkCompletematService {
    @Resource
    private WkCompletematMapper wkCompletematMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkCompletematPojo getEntity(String key, String tid) {
        return this.wkCompletematMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCompletematPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCompletematPojo> lst = wkCompletematMapper.getPageList(queryParam);
            PageInfo<WkCompletematPojo> pageInfo = new PageInfo<WkCompletematPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkCompletematPojo> getList(String Pid, String tid) {
        try {
            List<WkCompletematPojo> lst = wkCompletematMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkCompletematPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCompletematPojo insert(WkCompletematPojo wkCompletematPojo) {
        //初始化item的NULL
        WkCompletematPojo itempojo = this.clearNull(wkCompletematPojo);
        WkCompletematEntity wkCompletematEntity = new WkCompletematEntity();
        BeanUtils.copyProperties(itempojo, wkCompletematEntity);

        wkCompletematEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkCompletematEntity.setRevision(1);  //乐观锁
        this.wkCompletematMapper.insert(wkCompletematEntity);
        return this.getEntity(wkCompletematEntity.getId(), wkCompletematEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkCompletematPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCompletematPojo update(WkCompletematPojo wkCompletematPojo) {
        WkCompletematEntity wkCompletematEntity = new WkCompletematEntity();
        BeanUtils.copyProperties(wkCompletematPojo, wkCompletematEntity);
        this.wkCompletematMapper.update(wkCompletematEntity);
        return this.getEntity(wkCompletematEntity.getId(), wkCompletematEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkCompletematMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkCompletematPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCompletematPojo clearNull(WkCompletematPojo wkCompletematPojo) {
        //初始化NULL字段
        if (wkCompletematPojo.getPid() == null) wkCompletematPojo.setPid("");
        if (wkCompletematPojo.getItemid() == null) wkCompletematPojo.setItemid("");
        if (wkCompletematPojo.getGoodsid() == null) wkCompletematPojo.setGoodsid("");
        if (wkCompletematPojo.getItemcode() == null) wkCompletematPojo.setItemcode("");
        if (wkCompletematPojo.getItemname() == null) wkCompletematPojo.setItemname("");
        if (wkCompletematPojo.getItemspec() == null) wkCompletematPojo.setItemspec("");
        if (wkCompletematPojo.getItemunit() == null) wkCompletematPojo.setItemunit("");
        if (wkCompletematPojo.getQuantity() == null) wkCompletematPojo.setQuantity(0D);
        if (wkCompletematPojo.getTaxprice() == null) wkCompletematPojo.setTaxprice(0D);
        if (wkCompletematPojo.getTaxamount() == null) wkCompletematPojo.setTaxamount(0D);
        if (wkCompletematPojo.getPrice() == null) wkCompletematPojo.setPrice(0D);
        if (wkCompletematPojo.getAmount() == null) wkCompletematPojo.setAmount(0D);
        if (wkCompletematPojo.getTaxtotal() == null) wkCompletematPojo.setTaxtotal(0D);
        if (wkCompletematPojo.getItemtaxrate() == null) wkCompletematPojo.setItemtaxrate(0);
        if (wkCompletematPojo.getRownum() == null) wkCompletematPojo.setRownum(0);
        if (wkCompletematPojo.getRemark() == null) wkCompletematPojo.setRemark("");
        if (wkCompletematPojo.getFinishqty() == null) wkCompletematPojo.setFinishqty(0D);
        if (wkCompletematPojo.getBomid() == null) wkCompletematPojo.setBomid("");
        if (wkCompletematPojo.getBomtype() == null) wkCompletematPojo.setBomtype(0);
        if (wkCompletematPojo.getBomitemid() == null) wkCompletematPojo.setBomitemid("");
        if (wkCompletematPojo.getWorkitemid() == null) wkCompletematPojo.setWorkitemid("");
        if (wkCompletematPojo.getWorkitemmatid() == null) wkCompletematPojo.setWorkitemmatid("");
        if (wkCompletematPojo.getCustom1() == null) wkCompletematPojo.setCustom1("");
        if (wkCompletematPojo.getCustom2() == null) wkCompletematPojo.setCustom2("");
        if (wkCompletematPojo.getCustom3() == null) wkCompletematPojo.setCustom3("");
        if (wkCompletematPojo.getCustom4() == null) wkCompletematPojo.setCustom4("");
        if (wkCompletematPojo.getCustom5() == null) wkCompletematPojo.setCustom5("");
        if (wkCompletematPojo.getCustom6() == null) wkCompletematPojo.setCustom6("");
        if (wkCompletematPojo.getCustom7() == null) wkCompletematPojo.setCustom7("");
        if (wkCompletematPojo.getCustom8() == null) wkCompletematPojo.setCustom8("");
        if (wkCompletematPojo.getCustom9() == null) wkCompletematPojo.setCustom9("");
        if (wkCompletematPojo.getCustom10() == null) wkCompletematPojo.setCustom10("");
        if (wkCompletematPojo.getTenantid() == null) wkCompletematPojo.setTenantid("");
        if (wkCompletematPojo.getRevision() == null) wkCompletematPojo.setRevision(0);
        return wkCompletematPojo;
    }
}
