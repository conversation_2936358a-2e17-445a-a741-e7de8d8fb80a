package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkProgroupitemEntity;
import inks.service.std.manu.domain.pojo.WkProgroupitemPojo;
import inks.service.std.manu.mapper.WkProgroupitemMapper;
import inks.service.std.manu.service.WkProgroupitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工序清单(WkProgroupitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-26 10:05:56
 */
@Service("wkProgroupitemService")
public class WkProgroupitemServiceImpl implements WkProgroupitemService {
    @Resource
    private WkProgroupitemMapper wkProgroupitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkProgroupitemPojo getEntity(String key, String tid) {
        return this.wkProgroupitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkProgroupitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProgroupitemPojo> lst = wkProgroupitemMapper.getPageList(queryParam);
            PageInfo<WkProgroupitemPojo> pageInfo = new PageInfo<WkProgroupitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkProgroupitemPojo> getList(String Pid, String tid) {
        try {
            List<WkProgroupitemPojo> lst = wkProgroupitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkProgroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProgroupitemPojo insert(WkProgroupitemPojo wkProgroupitemPojo) {
        //初始化item的NULL
        WkProgroupitemPojo itempojo = this.clearNull(wkProgroupitemPojo);
        WkProgroupitemEntity wkProgroupitemEntity = new WkProgroupitemEntity();
        BeanUtils.copyProperties(itempojo, wkProgroupitemEntity);

        wkProgroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkProgroupitemEntity.setRevision(1);  //乐观锁
        this.wkProgroupitemMapper.insert(wkProgroupitemEntity);
        return this.getEntity(wkProgroupitemEntity.getId(), wkProgroupitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkProgroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProgroupitemPojo update(WkProgroupitemPojo wkProgroupitemPojo) {
        WkProgroupitemEntity wkProgroupitemEntity = new WkProgroupitemEntity();
        BeanUtils.copyProperties(wkProgroupitemPojo, wkProgroupitemEntity);
        this.wkProgroupitemMapper.update(wkProgroupitemEntity);
        return this.getEntity(wkProgroupitemEntity.getId(), wkProgroupitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkProgroupitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkProgroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkProgroupitemPojo clearNull(WkProgroupitemPojo wkProgroupitemPojo) {
        //初始化NULL字段
        if (wkProgroupitemPojo.getPid() == null) wkProgroupitemPojo.setPid("");
        if (wkProgroupitemPojo.getWpid() == null) wkProgroupitemPojo.setWpid("");
        if (wkProgroupitemPojo.getWpcode() == null) wkProgroupitemPojo.setWpcode("");
        if (wkProgroupitemPojo.getWpname() == null) wkProgroupitemPojo.setWpname("");
        if (wkProgroupitemPojo.getRownum() == null) wkProgroupitemPojo.setRownum(0);
        if (wkProgroupitemPojo.getRemark() == null) wkProgroupitemPojo.setRemark("");
        if (wkProgroupitemPojo.getCustom1() == null) wkProgroupitemPojo.setCustom1("");
        if (wkProgroupitemPojo.getCustom2() == null) wkProgroupitemPojo.setCustom2("");
        if (wkProgroupitemPojo.getCustom3() == null) wkProgroupitemPojo.setCustom3("");
        if (wkProgroupitemPojo.getCustom4() == null) wkProgroupitemPojo.setCustom4("");
        if (wkProgroupitemPojo.getCustom5() == null) wkProgroupitemPojo.setCustom5("");
        if (wkProgroupitemPojo.getCustom6() == null) wkProgroupitemPojo.setCustom6("");
        if (wkProgroupitemPojo.getCustom7() == null) wkProgroupitemPojo.setCustom7("");
        if (wkProgroupitemPojo.getCustom8() == null) wkProgroupitemPojo.setCustom8("");
        if (wkProgroupitemPojo.getCustom9() == null) wkProgroupitemPojo.setCustom9("");
        if (wkProgroupitemPojo.getCustom10() == null) wkProgroupitemPojo.setCustom10("");
        if (wkProgroupitemPojo.getTenantid() == null) wkProgroupitemPojo.setTenantid("");
        if (wkProgroupitemPojo.getRevision() == null) wkProgroupitemPojo.setRevision(0);
        return wkProgroupitemPojo;
    }
}
