package inks.service.std.manu.domain.constant;

public class MyConstant {

    //开始异步10线程获取订单生产成本预算
    public static final String ASYNC_MACHPRICE_STATE = "async_machprice_state";

    // 开始异步:获取MRP运算详细信息
    public static final String ASYNC_MRP_PULLITEMLIST_STATE = "async_mrpPullItemList_state:";
    public static final String ASYNC_MRP_PULLITEMLIST_RESULT = "async_mrpPullItemList_result:";
    // 开始异步:获取MRP运算详细信息 传mrp主表id
    public static final String ASYNC_MRP_PULLMRPITEMBYMRPID_STATE = "async_mrp_pullMrpitemByMrpid_state:";

    // Mrp相关的Redis锁
    public static final String MRP_LOCK = "mrp_lock:";

    // 迁移进度 WipNote
    public static final String WIP_NOW_TO_HI = "wip_now_to_hi:";
    public static final String WIP_HI_TO_NOW = "wip_hi_to_now:";
    // 迁移进度 WipQty
    public static final String WIPQTY_NOW_TO_HI = "wipqty_now_to_hi:";
    public static final String WIPQTY_HI_TO_NOW = "wipqty_hi_to_now:";
}
