package inks.service.std.manu.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipqtyEntity;
import inks.service.std.manu.domain.pojo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 生产过数(WkWipqty)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:51:54
 */
@Mapper
public interface WkWipqtyMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtyPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipqtyPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param wkWipqtyEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipqtyEntity wkWipqtyEntity);


    /**
     * 修改数据
     *
     * @param wkWipqtyEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipqtyEntity wkWipqtyEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateWipWkwp(WkWipnotePojo wkWipnotePojo);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateWipComp(@Param("key") String key,  @Param("tid") String tid);



    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateMachWkwp(WkWipnotePojo wkWipnotePojo);




    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateMachBillWkwp(WkWipnotePojo wkWipnotePojo);



    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateWorkWkwp(WkWipnotePojo wkWipnotePojo);

    int updateWorkBillWkwp(WkWipnotePojo wkWipnotePojo);

    int updateMainPlanWkwp(WkWipnotePojo wkWipnotePojo);

    int updateMainPlanBillWkwp(WkWipnotePojo wkWipnotePojo);
    /**
     * 新增数据
     *
     * @param matAccessPojo 实例对象
     * @return 影响行数
     */
    int insertAccess(MatAccessClonePojo matAccessPojo);

    /**
     * 新增数据
     *
     * @param matAccessitemPojo 实例对象
     * @return 影响行数
     */
    int insertAccessitem(MatAccessitemClonePojo matAccessitemPojo);


    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<WkWipqtyPojo> getListByWipUid(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<WkWipqtyPojo> getOutListByWipItemid(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int deleteByWipItemid(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<Map<String,Object>> getSumPageListByWp(QueryParam queryParam);


    List<WkWipqtyLastPojo> getOnlinePageListByLastMark(QueryParam queryParam);
    int copyIdToCustom(String table);

    List<Map<String, Object>> getidAndDateAndCus(@Param("table") String table,@Param("ordertype") String ordertype);

    int updateSnowflakeId(@Param("table") String table, @Param("newSnowflakeId") String newSnowflakeId, @Param("custom1") String custom1);

    int copyCiteItemidToCustom(@Param("table")String table,@Param("column")String column);

    int  updateCiteItemid(@Param("tgtable")String tgtable,@Param("tgcolumn")String tgcolumn,@Param("orgtable")String orgtable);

    List<WkWipqtyPojo> getCostPageList(QueryParam queryParam);


    int updateMachWkwpByMerge(WkWipnotePojo upWipPojo);

    int updateMachBillWkwpByMerge(WkWipnotePojo upWipPojo);

    int updateWorkWkwpByMerge(WkWipnotePojo upWipPojo);

    int updateWorkBillWkwpByMerge(WkWipnotePojo upWipPojo);

    void updateMainPlanWkwpByMerge(WkWipnotePojo upWipPojo);

    void updateMainPlanBillWkwpByMerge(WkWipnotePojo upWipPojo);

    List<ChartPojo> getSumPageListByCost(QueryParam queryParam);

    List<HashMap<String, Object>> getSumCostPageListByWpMachuid(@Param("processList")List<WkProcessPojo> processList, @Param("machuid")String machuid, @Param("tid")String tid);

    List<HashMap<String, Object>> getSumCostPageListByWpDate(@Param("processList")List<WkProcessPojo> processList, @Param("queryParam") QueryParam queryParam, @Param("tid")String tid);

    int updateMachWkwpByWipNoteMerge(WkWipnotePojo wkWipnotePojo);

    int updateMachBillWkwpByWipNoteMerge(WkWipnotePojo wkWipnotePojo);

    List<HashMap<String, Object>> getAvgWorkTimeGroupByWp(@Param("tenantid") String tenantid);

    int updateWipQtyPcsQty(@Param("wipqtyid") String wipqtyid, @Param("crtQty") Double crtQty, @Param("date") Date date, @Param("tid") String tid);

    WkWipqtyPojo getEntityByWipQtyid(@Param("wipqtyid") String wipqtyid, @Param("tid") String tid);

    WkWipqtyPojo checkWipQtyPcsQty(@Param("wipitemid") String wipitemid, @Param("tenantid") String tenantid);

    List<HashMap<String, Object>> getSumOutPcsQtyGroupByWorker(QueryParam queryParam);

    List<Map<String, Object>> getSumQtyAndWorkTimeByWp(QueryParam queryParam, String userid);

    List<HashMap<String, Object>> getSumWpByPlanItemid(String mainplanitemid, String tid);

    String getPidBygoodsid(String goodsid, String tid);
}

