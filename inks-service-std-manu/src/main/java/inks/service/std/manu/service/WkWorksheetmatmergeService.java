package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWorksheetmatmergePojo;

import java.util.List;

/**
 * 厂制物料合并表(WkWorksheetmatmerge)表服务接口
 *
 * <AUTHOR>
 * @since 2024-04-11 15:16:59
 */
public interface WkWorksheetmatmergeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetmatmergePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWorksheetmatmergePojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWorksheetmatmergePojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkWorksheetmatmergePojo 实例对象
     * @return 实例对象
     */
    WkWorksheetmatmergePojo insert(WkWorksheetmatmergePojo wkWorksheetmatmergePojo);

    /**
     * 修改数据
     *
     * @param wkWorksheetmatmergepojo 实例对象
     * @return 实例对象
     */
    WkWorksheetmatmergePojo update(WkWorksheetmatmergePojo wkWorksheetmatmergepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkWorksheetmatmergepojo 实例对象
     * @return 实例对象
     */
    WkWorksheetmatmergePojo clearNull(WkWorksheetmatmergePojo wkWorksheetmatmergepojo);
}
