package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipqtyrolesitemEntity;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesitemPojo;
import inks.service.std.manu.mapper.WkWipqtyrolesitemMapper;
import inks.service.std.manu.service.WkWipqtyrolesitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 过数角色子表(WkWipqtyrolesitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-26 10:08:50
 */
@Service("wkWipqtyrolesitemService")
public class WkWipqtyrolesitemServiceImpl implements WkWipqtyrolesitemService {
    @Resource
    private WkWipqtyrolesitemMapper wkWipqtyrolesitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipqtyrolesitemPojo getEntity(String key, String tid) {
        return this.wkWipqtyrolesitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipqtyrolesitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtyrolesitemPojo> lst = wkWipqtyrolesitemMapper.getPageList(queryParam);
            PageInfo<WkWipqtyrolesitemPojo> pageInfo = new PageInfo<WkWipqtyrolesitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkWipqtyrolesitemPojo> getList(String Pid, String tid) {
        try {
            List<WkWipqtyrolesitemPojo> lst = wkWipqtyrolesitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkWipqtyrolesitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipqtyrolesitemPojo insert(WkWipqtyrolesitemPojo wkWipqtyrolesitemPojo) {
        //初始化item的NULL
        WkWipqtyrolesitemPojo itempojo = this.clearNull(wkWipqtyrolesitemPojo);
        WkWipqtyrolesitemEntity wkWipqtyrolesitemEntity = new WkWipqtyrolesitemEntity();
        BeanUtils.copyProperties(itempojo, wkWipqtyrolesitemEntity);

        wkWipqtyrolesitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipqtyrolesitemEntity.setRevision(1);  //乐观锁
        this.wkWipqtyrolesitemMapper.insert(wkWipqtyrolesitemEntity);
        return this.getEntity(wkWipqtyrolesitemEntity.getId(), wkWipqtyrolesitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipqtyrolesitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipqtyrolesitemPojo update(WkWipqtyrolesitemPojo wkWipqtyrolesitemPojo) {
        WkWipqtyrolesitemEntity wkWipqtyrolesitemEntity = new WkWipqtyrolesitemEntity();
        BeanUtils.copyProperties(wkWipqtyrolesitemPojo, wkWipqtyrolesitemEntity);
        this.wkWipqtyrolesitemMapper.update(wkWipqtyrolesitemEntity);
        return this.getEntity(wkWipqtyrolesitemEntity.getId(), wkWipqtyrolesitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWipqtyrolesitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkWipqtyrolesitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipqtyrolesitemPojo clearNull(WkWipqtyrolesitemPojo wkWipqtyrolesitemPojo) {
        //初始化NULL字段
        if (wkWipqtyrolesitemPojo.getPid() == null) wkWipqtyrolesitemPojo.setPid("");
        if (wkWipqtyrolesitemPojo.getWpid() == null) wkWipqtyrolesitemPojo.setWpid("");
        if (wkWipqtyrolesitemPojo.getWpcode() == null) wkWipqtyrolesitemPojo.setWpcode("");
        if (wkWipqtyrolesitemPojo.getWpname() == null) wkWipqtyrolesitemPojo.setWpname("");
        if (wkWipqtyrolesitemPojo.getRownum() == null) wkWipqtyrolesitemPojo.setRownum(0);
        if (wkWipqtyrolesitemPojo.getCustom1() == null) wkWipqtyrolesitemPojo.setCustom1("");
        if (wkWipqtyrolesitemPojo.getCustom2() == null) wkWipqtyrolesitemPojo.setCustom2("");
        if (wkWipqtyrolesitemPojo.getCustom3() == null) wkWipqtyrolesitemPojo.setCustom3("");
        if (wkWipqtyrolesitemPojo.getCustom4() == null) wkWipqtyrolesitemPojo.setCustom4("");
        if (wkWipqtyrolesitemPojo.getCustom5() == null) wkWipqtyrolesitemPojo.setCustom5("");
        if (wkWipqtyrolesitemPojo.getCustom6() == null) wkWipqtyrolesitemPojo.setCustom6("");
        if (wkWipqtyrolesitemPojo.getCustom7() == null) wkWipqtyrolesitemPojo.setCustom7("");
        if (wkWipqtyrolesitemPojo.getCustom8() == null) wkWipqtyrolesitemPojo.setCustom8("");
        if (wkWipqtyrolesitemPojo.getCustom9() == null) wkWipqtyrolesitemPojo.setCustom9("");
        if (wkWipqtyrolesitemPojo.getCustom10() == null) wkWipqtyrolesitemPojo.setCustom10("");
        if (wkWipqtyrolesitemPojo.getTenantid() == null) wkWipqtyrolesitemPojo.setTenantid("");
        if (wkWipqtyrolesitemPojo.getRevision() == null) wkWipqtyrolesitemPojo.setRevision(0);
        return wkWipqtyrolesitemPojo;
    }
}
