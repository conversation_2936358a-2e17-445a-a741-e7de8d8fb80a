package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWorksheetmatEntity;
import inks.service.std.manu.domain.pojo.WkWorksheetmatPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 厂制物料(WkWorksheetmat)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-09 10:37:27
 */
 @Mapper
public interface WkWorksheetmatMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetmatPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWorksheetmatPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWorksheetmatPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkWorksheetmatEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWorksheetmatEntity wkWorksheetmatEntity);

    
    /**
     * 修改数据
     *
     * @param wkWorksheetmatEntity 实例对象
     * @return 影响行数
     */
    int update(WkWorksheetmatEntity wkWorksheetmatEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int deleteByItemid(@Param("key") String key, @Param("tid") String tid);


    /**
     * 查询指定行数据
     *
     * @param key 主键
     * @return 对象列表
     */
    List<WkWorksheetmatPojo> getListByItemid(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param key 主键
     * @return 对象列表
     */
    List<WkWorksheetmatPojo> getListByPid(@Param("key") String key, @Param("tid") String tid);


    List<WkWorksheetmatPojo> getMatListByItemIds(@Param("itemIdList") List<String> itemIdList, @Param("tid") String tenantid);

    String getMatIdByGoodsIdAndItemId(@Param("goodsid") String goodsid, @Param("itemid") String itemid, @Param("tid") String tenantid);

    int updateMergeid(@Param("id") String id, @Param("mergeidInsert") String mergeidInsert, @Param("tid") String tid);
}

