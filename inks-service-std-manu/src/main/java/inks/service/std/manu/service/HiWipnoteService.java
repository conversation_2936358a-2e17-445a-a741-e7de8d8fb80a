package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.HiWipnotePojo;
import inks.service.std.manu.domain.pojo.HiWipnoteitemdetailPojo;

import java.util.List;

/**
 * WIP记录(HiWipnote)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-16 16:19:17
 */
public interface HiWipnoteService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    HiWipnotePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<HiWipnoteitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    HiWipnotePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<HiWipnotePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<HiWipnotePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param hiWipnotePojo 实例对象
     * @return 实例对象
     */
    HiWipnotePojo insert(HiWipnotePojo hiWipnotePojo);

    /**
     * 修改数据
     *
     * @param hiWipnotepojo 实例对象
     * @return 实例对象
     */
    HiWipnotePojo update(HiWipnotePojo hiWipnotepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    String moveNowToHi(String startdate, String enddate, String tenantid);

    String moveHiToNow(String startdate, String enddate, List<String> ids, String tenantid);

    void validateTableStructure(String sourceTable, String targetTable);
}
