package inks.service.std.manu.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产工序(WkProcess)实体类
 *
 * <AUTHOR>
 * @since 2024-01-15 14:02:56
 */
public class WkProcessEntity implements Serializable {
    private static final long serialVersionUID = -53277318263399302L;
     // id
    private String id;
     // 编码
    private String wpcode;
     // 名称
    private String wpname;
     // 数量过账
    private Integer post;
     // 外包工序
    private Integer outsourcing;
     // 结算单位
    private String balanceunit;
     // 最大产值
    private Double capacity;
     // 最小用时
    private Double mintime;
     // 辅助数量
    private Double subqty;
     // 辅助单位
    private String subunit;
     // 单位用途
    private String subunituse;
     // 不良率
    private Double aimmrb;
     // 目的成本
    private Double aimcost;
     // 拆分过数
    private Integer splitqty;
     // 顺序
    private Integer rownum;
     // 摘要
    private String summary;
     // 有效标识
    private Integer enabledmark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 删除标识
    private Integer deletemark;
     // 删除人员id
    private String deletelisterid;
     // 删除人员
    private String deletelister;
     // 删除日期
    private Date deletedate;
     // 背景色
    private String backcolorargb;
     // 前景色
    private String forecolorargb;
     // 目标数量
    private Double targetqty;
     // 目标工时
    private Double targethours;
     // 目标金额
    private Double targetamt;
     // 封面图片
    private String frontphoto;
     // 工作内容
    private String workcontent;
     // 主管A
    private String leadernamea;
     // 职务A
    private String leadertitlea;
     // 头像A
    private String leaderavatara;
     // 主管B
    private String leadernameb;
     // 职务B
    private String leadertitleb;
     // 头像B
    private String leaderavatarb;
     // 工作参数
    private String workparam;
     // 最后工序
    private Integer lastmark;
     // 在线批次数
    private Integer onlinebatch;
     // 是否关键工序
    private Integer keymark;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 编码
    public String getWpcode() {
        return wpcode;
    }
    
    public void setWpcode(String wpcode) {
        this.wpcode = wpcode;
    }
        
   // 名称
    public String getWpname() {
        return wpname;
    }
    
    public void setWpname(String wpname) {
        this.wpname = wpname;
    }
        
   // 数量过账
    public Integer getPost() {
        return post;
    }
    
    public void setPost(Integer post) {
        this.post = post;
    }
        
   // 外包工序
    public Integer getOutsourcing() {
        return outsourcing;
    }
    
    public void setOutsourcing(Integer outsourcing) {
        this.outsourcing = outsourcing;
    }
        
   // 结算单位
    public String getBalanceunit() {
        return balanceunit;
    }
    
    public void setBalanceunit(String balanceunit) {
        this.balanceunit = balanceunit;
    }
        
   // 最大产值
    public Double getCapacity() {
        return capacity;
    }
    
    public void setCapacity(Double capacity) {
        this.capacity = capacity;
    }
        
   // 最小用时
    public Double getMintime() {
        return mintime;
    }
    
    public void setMintime(Double mintime) {
        this.mintime = mintime;
    }
        
   // 辅助数量
    public Double getSubqty() {
        return subqty;
    }
    
    public void setSubqty(Double subqty) {
        this.subqty = subqty;
    }
        
   // 辅助单位
    public String getSubunit() {
        return subunit;
    }
    
    public void setSubunit(String subunit) {
        this.subunit = subunit;
    }
        
   // 单位用途
    public String getSubunituse() {
        return subunituse;
    }
    
    public void setSubunituse(String subunituse) {
        this.subunituse = subunituse;
    }
        
   // 不良率
    public Double getAimmrb() {
        return aimmrb;
    }
    
    public void setAimmrb(Double aimmrb) {
        this.aimmrb = aimmrb;
    }
        
   // 目的成本
    public Double getAimcost() {
        return aimcost;
    }
    
    public void setAimcost(Double aimcost) {
        this.aimcost = aimcost;
    }
        
   // 拆分过数
    public Integer getSplitqty() {
        return splitqty;
    }
    
    public void setSplitqty(Integer splitqty) {
        this.splitqty = splitqty;
    }
        
   // 顺序
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 摘要
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
   // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 删除标识
    public Integer getDeletemark() {
        return deletemark;
    }
    
    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }
        
   // 删除人员id
    public String getDeletelisterid() {
        return deletelisterid;
    }
    
    public void setDeletelisterid(String deletelisterid) {
        this.deletelisterid = deletelisterid;
    }
        
   // 删除人员
    public String getDeletelister() {
        return deletelister;
    }
    
    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }
        
   // 删除日期
    public Date getDeletedate() {
        return deletedate;
    }
    
    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }
        
   // 背景色
    public String getBackcolorargb() {
        return backcolorargb;
    }
    
    public void setBackcolorargb(String backcolorargb) {
        this.backcolorargb = backcolorargb;
    }
        
   // 前景色
    public String getForecolorargb() {
        return forecolorargb;
    }
    
    public void setForecolorargb(String forecolorargb) {
        this.forecolorargb = forecolorargb;
    }
        
   // 目标数量
    public Double getTargetqty() {
        return targetqty;
    }
    
    public void setTargetqty(Double targetqty) {
        this.targetqty = targetqty;
    }
        
   // 目标工时
    public Double getTargethours() {
        return targethours;
    }
    
    public void setTargethours(Double targethours) {
        this.targethours = targethours;
    }
        
   // 目标金额
    public Double getTargetamt() {
        return targetamt;
    }
    
    public void setTargetamt(Double targetamt) {
        this.targetamt = targetamt;
    }
        
   // 封面图片
    public String getFrontphoto() {
        return frontphoto;
    }
    
    public void setFrontphoto(String frontphoto) {
        this.frontphoto = frontphoto;
    }
        
   // 工作内容
    public String getWorkcontent() {
        return workcontent;
    }
    
    public void setWorkcontent(String workcontent) {
        this.workcontent = workcontent;
    }
        
   // 主管A
    public String getLeadernamea() {
        return leadernamea;
    }
    
    public void setLeadernamea(String leadernamea) {
        this.leadernamea = leadernamea;
    }
        
   // 职务A
    public String getLeadertitlea() {
        return leadertitlea;
    }
    
    public void setLeadertitlea(String leadertitlea) {
        this.leadertitlea = leadertitlea;
    }
        
   // 头像A
    public String getLeaderavatara() {
        return leaderavatara;
    }
    
    public void setLeaderavatara(String leaderavatara) {
        this.leaderavatara = leaderavatara;
    }
        
   // 主管B
    public String getLeadernameb() {
        return leadernameb;
    }
    
    public void setLeadernameb(String leadernameb) {
        this.leadernameb = leadernameb;
    }
        
   // 职务B
    public String getLeadertitleb() {
        return leadertitleb;
    }
    
    public void setLeadertitleb(String leadertitleb) {
        this.leadertitleb = leadertitleb;
    }
        
   // 头像B
    public String getLeaderavatarb() {
        return leaderavatarb;
    }
    
    public void setLeaderavatarb(String leaderavatarb) {
        this.leaderavatarb = leaderavatarb;
    }
        
   // 工作参数
    public String getWorkparam() {
        return workparam;
    }
    
    public void setWorkparam(String workparam) {
        this.workparam = workparam;
    }
        
   // 最后工序
    public Integer getLastmark() {
        return lastmark;
    }
    
    public void setLastmark(Integer lastmark) {
        this.lastmark = lastmark;
    }
        
   // 在线批次数
    public Integer getOnlinebatch() {
        return onlinebatch;
    }
    
    public void setOnlinebatch(Integer onlinebatch) {
        this.onlinebatch = onlinebatch;
    }
        
   // 是否关键工序
    public Integer getKeymark() {
        return keymark;
    }
    
    public void setKeymark(Integer keymark) {
        this.keymark = keymark;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

