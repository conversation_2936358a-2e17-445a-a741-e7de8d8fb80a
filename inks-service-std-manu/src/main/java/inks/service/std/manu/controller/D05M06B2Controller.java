package inks.service.std.manu.controller;


import com.alibaba.fastjson.JSONArray;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkManureportPojo;
import inks.service.std.manu.service.WkManureportService;
import inks.service.std.manu.service.WkManureportitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 生产报工(Wk_ManuReport)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-17 16:16:42
 */
@RestController
@RequestMapping("D05M06B2")
@Api(tags = "D05M06B2:生产报工:报工单")
public class D05M06B2Controller extends WkManureportController {
    /**
     * 服务对象
     */
    @Resource
    private WkManureportService wkManureportService;

    /**
     * 服务对象Item
     */
    @Resource
    private WkManureportitemService wkManureportitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增生产报工", notes = "新增生产报工", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ManuReport.Add")
    public R<WkManureportPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkManureportPojo wkManureportPojo = JSONArray.parseObject(json, WkManureportPojo.class);
            //生成单据编码
            R r = systemFeignService.getBillCode("D05M06B2", loginUser.getToken(), "Wk_ManuReport", null);
            if (r.getCode() == 200)
                wkManureportPojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            wkManureportPojo.setCreateby(loginUser.getRealName());   // 创建者
            wkManureportPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkManureportPojo.setCreatedate(new Date());   // 创建时间
            wkManureportPojo.setLister(loginUser.getRealname());   // 制表
            wkManureportPojo.setListerid(loginUser.getUserid());    // 制表id
            wkManureportPojo.setModifydate(new Date());   //修改时间
            wkManureportPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.wkManureportService.insert(wkManureportPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
