package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkWipqtyPojo;
import inks.service.std.manu.service.WkWipqtyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 生产过数(Wk_WipQty)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:51:53
 */
@RestController
@RequestMapping("D05M07R1")
@Api(tags = "D05M07R1:工序报表")
public class D05M07R1Controller {

    /**
     * 服务对象
     */
    @Resource
    private WkWipqtyService wkWipqtyService;


    @Resource
    private TokenService tokenService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页汇总工序完工 不传json默认查询本月1号至今的数据", notes = "按条件分页汇总工序完工", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByWp", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<PageInfo<Map<String, Object>>> getSumPageListByWp(@RequestBody(required = false) String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (json == null) json = "{}";
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null) {
                Date now = new Date();
                // 截断日期到本月的第一天，即获取本月1号0点的时间
                Date firstDayOfMonth = DateUtils.truncate(now, java.util.Calendar.MONTH);
                queryParam.setDateRange(new DateRange(null, firstDayOfMonth, now));
            }
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQty.CreateDate");
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWipqtyService.getSumPageListByWp(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查一个人的累计工时和PcsQty 不传json默认查询本月1号至今的数据", notes = "按条件分页汇总工序完工", produces = "application/json")
    @RequestMapping(value = "/getSumQtyAndWorkTimeByWp", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<List<Map<String, Object>>> getSumQtyAndWorkTimeByWp(@RequestBody(required = false) String json, String userid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (json == null) json = "{}";
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null) {
                Date now = new Date();
                // 截断日期到本月的第一天，即获取本月1号0点的时间
                Date firstDayOfMonth = DateUtils.truncate(now, java.util.Calendar.MONTH);
                queryParam.setDateRange(new DateRange(null, firstDayOfMonth, now));
            }
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWipqtyService.getSumQtyAndWorkTimeByWp(queryParam, userid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @return R<PageInfo < WkWipqtyPojo>>
     * @Description 按条件分页汇总基材金额
     * <AUTHOR>
     * @param[1] json 传入分页参数和时间区间
     * @time 2023/4/21 12:37
     */

    @ApiOperation(value = "汇总基材金额", notes = "按条件分页汇总工序完工", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByCost", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<List<ChartPojo>> getSumPageListByCost(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            ChartPojo chartPojo = new ChartPojo();
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWipqtyService.getSumPageListByCost(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询 工序完工报表
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询工序完工报表", notes = "按条件分页查询wpid为工序", produces = "application/json")
    @RequestMapping(value = "/getCostPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<PageInfo<Map<String, Object>>> getCostPageList(@RequestBody String json, String wpid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipQty.CreateDate");
            String qpfilter = "";
            if (wpid != null) {
                qpfilter += " and Wk_WipQty.wpid='" + wpid + "'";
            }
            qpfilter += " and Wk_WipQty.direction='出组'";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            PageInfo<WkWipqtyPojo> list = this.wkWipqtyService.getCostPageList(queryParam);
            List<Map<String, Object>> lstMap = attrcostListToMaps(list.getList());
            PageInfo<Map<String, Object>> mapPageInfo = new PageInfo<Map<String, Object>>(lstMap);
            BeanUtils.copyProperties(list, mapPageInfo);
            mapPageInfo.setList(lstMap);
            return R.ok(mapPageInfo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //    过数完工人工成本分析表getSumCostPageListByWp
    //    1、以订单号，分析该工单每个工序的出组工费(工时*单价）；
    //    2、以时间为条件，分析每个工序这段时间内工费汇总（工时*单价）；
    @ApiOperation(value = "过数完工人工成本分析", notes = "过数完工人工成本分析", produces = "application/json")
    @RequestMapping(value = "/getSumCostPageListByWp", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<List<HashMap<String, Object>>> getSumCostPageListByWp(@RequestBody(required = false) String json, String machuid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (StringUtils.isNotBlank(machuid)) {//1、以订单号，分析该工单每个工序的出组工费(工时*单价） TODO
            return R.ok(this.wkWipqtyService.getSumCostPageListByWpMachuid(machuid, loginUser.getTenantid()));
        } else {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            return R.ok(this.wkWipqtyService.getSumCostPageListByWpDate(queryParam, loginUser.getTenantid()));
        }
    }


    @ApiOperation(value = "WipQty出组的平均WorkTime统计,GROUP BY Wk_WipQty.WpCode;", notes = "WipQty出组的平均WorkTime统计", produces = "application/json")
    @RequestMapping(value = "/getAvgWorkTimeGroupByWp", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<PageInfo<HashMap<String, Object>>> getAvgWorkTimeGroupByWp(@RequestBody(required = false) String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            return R.ok(this.wkWipqtyService.getAvgWorkTimeGroupByWp(queryParam, loginUser.getTenantid()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    //        SELECT IF(Worker = '', Lister, Worker) AS name, SUM(PcsQty)                     AS value
    @ApiOperation(value = "WipQty每个Worker的出组数量汇总(Group By Worker  没有OutWorker就用Lister)", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSumOutPcsQtyGroupByWorker", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<List<HashMap<String, Object>>> getSumOutPcsQtyGroupByWorker(@RequestBody(required = false) String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWipqtyService.getSumOutPcsQtyGroupByWorker(queryParam));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "通过mainplanitemid查询各个工序累计的数量（出入组报废数量pcs,secqty）", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSumWpByPlanItemid", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipQty.List")
    public R<List<HashMap<String, Object>>> getSumWpByPlanItemid(String mainplanitemid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkWipqtyService.getSumWpByPlanItemid(mainplanitemid, loginUser.getTenantid()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
