package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWorksheetEntity;
import inks.service.std.manu.domain.WkWorksheetitemEntity;
import inks.service.std.manu.domain.WkWorksheetmatEntity;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.mapper.WkWorksheetMapper;
import inks.service.std.manu.mapper.WkWorksheetitemMapper;
import inks.service.std.manu.mapper.WkWorksheetmatMapper;
import inks.service.std.manu.mapper.WkWorksheetmergeMapper;
import inks.service.std.manu.service.WkWorksheetService;
import inks.service.std.manu.service.WkWorksheetitemService;
import inks.service.std.manu.service.WkWorksheetmatService;
import inks.service.std.manu.service.WkWorksheetmergeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 厂制物料(WkWorksheetmat)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-09 12:59:47
 */
@Service("wkWorksheetmatService")
public class WkWorksheetmatServiceImpl implements WkWorksheetmatService {
    @Resource
    private WkWorksheetmatMapper wkWorksheetmatMapper;
    @Resource
    private WkWorksheetitemService wkWorksheetitemService;
    @Resource
    private WkWorksheetmergeService wkWorksheetmergeService;
    @Resource
    private WkWorksheetitemMapper wkWorksheetitemMapper;
    @Resource
    private WkWorksheetService wkWorksheetService;
    @Resource
    private WkWorksheetMapper wkWorksheetMapper;
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private WkWorksheetmergeMapper wkWorksheetmergeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWorksheetmatPojo getEntity(String key, String tid) {
        return this.wkWorksheetmatMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWorksheetmatPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWorksheetmatPojo> lst = wkWorksheetmatMapper.getPageList(queryParam);
            PageInfo<WkWorksheetmatPojo> pageInfo = new PageInfo<WkWorksheetmatPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkWorksheetmatPojo> getList(String Pid, String tid) {
        try {
            List<WkWorksheetmatPojo> lst = wkWorksheetmatMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkWorksheetmatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetmatPojo insert(WkWorksheetmatPojo wkWorksheetmatPojo) {
        //初始化item的NULL
        WkWorksheetmatPojo itempojo = this.clearNull(wkWorksheetmatPojo);
        WkWorksheetmatEntity wkWorksheetmatEntity = new WkWorksheetmatEntity();
        BeanUtils.copyProperties(itempojo, wkWorksheetmatEntity);

        wkWorksheetmatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWorksheetmatEntity.setRevision(1);  //乐观锁
        this.wkWorksheetmatMapper.insert(wkWorksheetmatEntity);
        return this.getEntity(wkWorksheetmatEntity.getId(), wkWorksheetmatEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWorksheetmatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetmatPojo update(WkWorksheetmatPojo wkWorksheetmatPojo) {
        WkWorksheetmatEntity wkWorksheetmatEntity = new WkWorksheetmatEntity();
        BeanUtils.copyProperties(wkWorksheetmatPojo, wkWorksheetmatEntity);
        this.wkWorksheetmatMapper.update(wkWorksheetmatEntity);
        return this.getEntity(wkWorksheetmatEntity.getId(), wkWorksheetmatEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWorksheetmatMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkWorksheetmatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWorksheetmatPojo clearNull(WkWorksheetmatPojo wkWorksheetmatPojo) {
        //初始化NULL字段
        if (wkWorksheetmatPojo.getPid() == null) wkWorksheetmatPojo.setPid("");
        if (wkWorksheetmatPojo.getItemid() == null) wkWorksheetmatPojo.setItemid("");
        if (wkWorksheetmatPojo.getGoodsid() == null) wkWorksheetmatPojo.setGoodsid("");
        if (wkWorksheetmatPojo.getQuantity() == null) wkWorksheetmatPojo.setQuantity(0D);
        if (wkWorksheetmatPojo.getFinishqty() == null) wkWorksheetmatPojo.setFinishqty(0D);
        if (wkWorksheetmatPojo.getMrpuid() == null) wkWorksheetmatPojo.setMrpuid("");
        if (wkWorksheetmatPojo.getMrpitemid() == null) wkWorksheetmatPojo.setMrpitemid("");
        if (wkWorksheetmatPojo.getSubqty() == null) wkWorksheetmatPojo.setSubqty(0D);
        if (wkWorksheetmatPojo.getMainqty() == null) wkWorksheetmatPojo.setMainqty(0D);
        if (wkWorksheetmatPojo.getLossrate() == null) wkWorksheetmatPojo.setLossrate(0D);
        if (wkWorksheetmatPojo.getBomid() == null) wkWorksheetmatPojo.setBomid("");
        if (wkWorksheetmatPojo.getBomtype() == null) wkWorksheetmatPojo.setBomtype(0);
        if (wkWorksheetmatPojo.getBomitemid() == null) wkWorksheetmatPojo.setBomitemid("");
        if (wkWorksheetmatPojo.getItemrowcode() == null) wkWorksheetmatPojo.setItemrowcode("");
        if (wkWorksheetmatPojo.getRownum() == null) wkWorksheetmatPojo.setRownum(0);
        if (wkWorksheetmatPojo.getClosed() == null) wkWorksheetmatPojo.setClosed(0);
        if (wkWorksheetmatPojo.getBomqty() == null) wkWorksheetmatPojo.setBomqty(0D);
        if (wkWorksheetmatPojo.getAvaiqty() == null) wkWorksheetmatPojo.setAvaiqty(0D);
        if (wkWorksheetmatPojo.getNeedqty() == null) wkWorksheetmatPojo.setNeedqty(0D);
        if (wkWorksheetmatPojo.getRealqty() == null) wkWorksheetmatPojo.setRealqty(0D);
        if (wkWorksheetmatPojo.getStoplanqty() == null) wkWorksheetmatPojo.setStoplanqty(0D);
        if (wkWorksheetmatPojo.getFlowcode() == null) wkWorksheetmatPojo.setFlowcode("");
        if (wkWorksheetmatPojo.getAttributejson() == null) wkWorksheetmatPojo.setAttributejson("");
        if (wkWorksheetmatPojo.getMergeid() == null) wkWorksheetmatPojo.setMergeid("");
        if (wkWorksheetmatPojo.getMergefinishqty() == null) wkWorksheetmatPojo.setMergefinishqty(0D);
        if (wkWorksheetmatPojo.getCustom1() == null) wkWorksheetmatPojo.setCustom1("");
        if (wkWorksheetmatPojo.getCustom2() == null) wkWorksheetmatPojo.setCustom2("");
        if (wkWorksheetmatPojo.getCustom3() == null) wkWorksheetmatPojo.setCustom3("");
        if (wkWorksheetmatPojo.getCustom4() == null) wkWorksheetmatPojo.setCustom4("");
        if (wkWorksheetmatPojo.getCustom5() == null) wkWorksheetmatPojo.setCustom5("");
        if (wkWorksheetmatPojo.getCustom6() == null) wkWorksheetmatPojo.setCustom6("");
        if (wkWorksheetmatPojo.getCustom7() == null) wkWorksheetmatPojo.setCustom7("");
        if (wkWorksheetmatPojo.getCustom8() == null) wkWorksheetmatPojo.setCustom8("");
        if (wkWorksheetmatPojo.getCustom9() == null) wkWorksheetmatPojo.setCustom9("");
        if (wkWorksheetmatPojo.getCustom10() == null) wkWorksheetmatPojo.setCustom10("");
        if (wkWorksheetmatPojo.getTenantid() == null) wkWorksheetmatPojo.setTenantid("");
        if (wkWorksheetmatPojo.getRevision() == null) wkWorksheetmatPojo.setRevision(0);
        return wkWorksheetmatPojo;
    }


    /**
     * @Description 合并加工单子项物料
     * <AUTHOR>
     * @param[1] lstitem
     * @time 2023/4/15 12:35
     */
    @Override
    public void mergeItem(List<WkWorksheetitemdetailPojo> lstitem, LoginUser loginUser) {
        if (lstitem == null || lstitem.size() == 0) {
            throw new RuntimeException("传入加工单子项为空");
        }
        // 检查传入加工单子项是否已经转为WIP,是否已经合并
        lstitem.forEach(item -> {
            if (item.getWipused() == 1 || item.getFinishqty() > 0) {
                throw new RuntimeException("加工单已经转为WIP，禁止合并");
            }
            if (item.getMergemark() != null && item.getMergemark() == 1) {
                throw new RuntimeException("加工单已经合并，禁止重复合并");
            }
        });
        //生成主单
        String tenantid = lstitem.get(0).getTenantid();
        WkWorksheetitemPojo wkWorksheetitemPojo = wkWorksheetitemMapper.getEntity(lstitem.get(0).getId(), tenantid);
        WkWorksheetPojo wkWorksheetPojo = wkWorksheetMapper.getEntity(wkWorksheetitemPojo.getPid(), tenantid);
        wkWorksheetPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
        String itemid = inksSnowflake.getSnowflake().nextIdStr();
        wkWorksheetitemPojo.setPid(wkWorksheetPojo.getId());
        wkWorksheetitemPojo.setId(itemid);
        wkWorksheetitemPojo.setMergemark(2);
        Double quantity = 0D;
        Double wkpcsqty = 0D;
        //记录需要修改为Mergemark=1的子项id集合
        String itemids = "'" + lstitem.get(0).getId() + "'";
        for (int i = 0; i < lstitem.size(); i++) {
            quantity += lstitem.get(i).getQuantity();
            wkpcsqty += lstitem.get(i).getWkpcsqty();//总投数累加
            WkWorksheetitemPojo itemPojo = wkWorksheetitemMapper.getEntity(lstitem.get(i).getId(), tenantid);
            WkWorksheetmergePojo wkWorksheetmergePojo = new WkWorksheetmergePojo();
            BeanUtils.copyProperties(itemPojo, wkWorksheetmergePojo);
            wkWorksheetmergePojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWorksheetmergePojo.setItemid(itemid);
            wkWorksheetmergePojo.setWorkitemid(lstitem.get(i).getId());
            wkWorksheetmergePojo.setWorkuid(lstitem.get(i).getRefno());
            if (i == 0) {
                wkWorksheetmergePojo.setMainmark(1);
            } else {
                itemids += ",'" + lstitem.get(i).getId() + "'";
            }
            wkWorksheetmergeService.insert(wkWorksheetmergePojo);
            // 更新合并前原始的加工单主表的已合并行数
            wkWorksheetService.updateMergeCount(lstitem.get(i).getPid(), tenantid);
        }
        wkWorksheetitemPojo.setQuantity(quantity);
        wkWorksheetitemPojo.setWkpcsqty(wkpcsqty);
        WkWorksheetitemEntity wkWorksheetitemEntity = new WkWorksheetitemEntity();
        BeanUtils.copyProperties(wkWorksheetitemPojo, wkWorksheetitemEntity);
        wkWorksheetitemMapper.insert(wkWorksheetitemEntity);
        //生成单据编码
        R r = systemFeignService.getBillCode("D05M01B1", loginUser.getToken());
        if (r.getCode() == 200)
            wkWorksheetPojo.setRefno(r.getData().toString());
        else {
            throw new RuntimeException("生成单据编码失败");
        }
        wkWorksheetPojo.setCreateby(loginUser.getRealname());   // 创建者
        wkWorksheetPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        wkWorksheetPojo.setCreatedate(new Date());   // 创建时间
        wkWorksheetPojo.setLister(loginUser.getRealname());   // 制表
        wkWorksheetPojo.setListerid(loginUser.getUserid());    // 制表id
        wkWorksheetPojo.setModifydate(new Date());   //修改时间
        wkWorksheetPojo.setTenantid(loginUser.getTenantid());   //租户id
        wkWorksheetPojo.setItemcount(1);
        WkWorksheetEntity wkWorksheetEntity = new WkWorksheetEntity();
        BeanUtils.copyProperties(wkWorksheetPojo, wkWorksheetEntity);
        wkWorksheetMapper.insert(wkWorksheetEntity);
        // 更新合并前原始的子项的Mergemark=1表示已经被合并
        wkWorksheetitemService.updateMergemark1(itemids, tenantid);
    }


//        //合并相同goodsid,sku的货品明细项
//        for (int i = 0; i < lstitem.size(); i++) {
//            // 获取当前项的goodsid、quantity和attributejson(即sku)值
//            String goodsid = lstitem.get(i).getGoodsid();
//            String attributejson = lstitem.get(i).getAttributejson();
//            double quantity = lstitem.get(i).getQuantity();
//            Double wkpcsqty = lstitem.get(i).getWkpcsqty();//总投数累加
//            //记录需要修改为Mergemark=1的子项id集合
//            String itemids = "'" + lstitem.get(i).getId() + "'";
//            // 在当前项之后的所有项中查找是否有相同(goodsid且相同attributejson)的项
//            for (int j = i + 1; j < lstitem.size(); j++) {
//                if (lstitem.get(j).getGoodsid().equals(goodsid) && lstitem.get(j).getAttributejson().equals(attributejson)) {
//                    // 如果存在相同goodsid且相同attributejson的项，则累加数量到当前项中
//                    quantity += lstitem.get(j).getQuantity();
//                    wkpcsqty += lstitem.get(j).getWkpcsqty();
//                    itemids += ",'" + lstitem.get(j).getId() + "'";
//                    // 插入到合并记录表中,MainMark=0表示不是主表
//                    WkWorksheetmergePojo wkWorksheetmergePojo = new WkWorksheetmergePojo();
//                    BeanUtils.copyProperties(lstitem.get(j), wkWorksheetmergePojo);
//                    wkWorksheetmergePojo.setWorkitemid(lstitem.get(j).getId());
//                    wkWorksheetmergePojo.setWorkuid(wkWorksheetitemMapper.getUid(lstitem.get(j).getId(), lstitem.get(j).getTenantid()));//TODO 改为workuid
//                    wkWorksheetmergeService.insert(wkWorksheetmergePojo);
//                    // 更新合并前原始的加工单主表的已合并行数
//                    wkWorksheetService.updateMergeCount(lstitem.get(j).getPid(), lstitem.get(j).getTenantid());
//                    // 删除相同项
//                    lstitem.remove(j);
//                    j--;
//                }
//            }
//            // 插入到合并记录表中,MainMark=1表示是主表
//            WkWorksheetmergePojo wkWorksheetmergePojo = new WkWorksheetmergePojo();
//            BeanUtils.copyProperties(lstitem.get(i), wkWorksheetmergePojo);
//            wkWorksheetmergePojo.setMainmark(1);
//            wkWorksheetmergePojo.setWorkitemid(lstitem.get(i).getId());
//            wkWorksheetmergePojo.setWorkuid(wkWorksheetitemMapper.getUid(lstitem.get(i).getId(), lstitem.get(i).getTenantid()));
//            wkWorksheetmergeService.insert(wkWorksheetmergePojo);
//            // 更新合并前原始的子项的Mergemark=1表示已经被合并
//            wkWorksheetitemService.updateMergemark1(itemids, lstitem.get(i).getTenantid());
//            // 更新合并前原始的加工单主表的已合并行数
//            wkWorksheetService.updateMergeCount(lstitem.get(i).getPid(), lstitem.get(i).getTenantid());
//            // 先生成合并后的主表（新id和RefNo）
//            WkWorksheetPojo wkWorksheetPojoDB = wkWorksheetService.getEntity(lstitem.get(i).getPid(), lstitem.get(i).getTenantid());
//            //生成单据编码
//            R r = systemFeignService.getBillCode("D05M01B1", token);
//            if (r.getCode() == 200)
//                wkWorksheetPojoDB.setRefno(r.getData().toString());
//            else {
//                throw new RuntimeException("生成单据编码失败");
//            }
//            WkWorksheetPojo wkWorksheetPojo = wkWorksheetService.insert(wkWorksheetPojoDB);
//            // 生成合并后的子表: 更新当前项的数量和价格 并作为合并后的子项结果插入到WkWorksheetitem表中,Mergemark=2表示合并生成的子项
//            lstitem.get(i).setPid(wkWorksheetPojo.getId());
//            lstitem.get(i).setQuantity(quantity);
//            lstitem.get(i).setWkpcsqty(wkpcsqty);
//            lstitem.get(i).setMergemark(2);
//            WkWorksheetitemPojo insert = wkWorksheetitemService.insert(lstitem.get(i));
//            String mergeItemid = insert.getId();//合并生成的子项id,即合并记录表的合并itemid
////            // 插入到合并记录表中,MainMark=1表示不是主表
////            WkWorksheetmergePojo wkWorksheetmergePojoMerge = new WkWorksheetmergePojo();
////            BeanUtils.copyProperties(lstitem.get(i), wkWorksheetmergePojoMerge);
////            wkWorksheetmergePojoMerge.setMainmark(1);
////            wkWorksheetmergePojoMerge.setWorkitemid(mergeItemid);
////            wkWorksheetmergePojoMerge.setItemid(mergeItemid);
////            wkWorksheetmergePojoMerge.setWorkuid(wkWorksheetitemMapper.getUid(mergeItemid, lstitem.get(i).getTenantid()));
////            wkWorksheetmergeService.insert(wkWorksheetmergePojoMerge);
//            //更新合并的itemid为mergeItemid
//            wkWorksheetmergeService.updateMergeItemid(mergeItemid, itemids, lstitem.get(i).getTenantid());
//        }
//    }


    @Override
    public List<WkWorksheetmergePojo> getMergeListByItemid(String key, String tid) {
        return wkWorksheetmergeMapper.getMergeListByItemid(key, tid);
    }

    @Override
    public List<WkWorksheetmatPojo> getMatListByItemIds(List<String> itemIdList, String tenantid) {
        return wkWorksheetmatMapper.getMatListByItemIds(itemIdList, tenantid);
    }
}
