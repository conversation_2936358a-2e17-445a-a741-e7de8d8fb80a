package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipgroupPojo;
import inks.service.std.manu.domain.pojo.WkWipgroupitemdetailPojo;

/**
 * WIP设定(WkWipgroup)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 20:45:45
 */
public interface WkWipgroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipgroupPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipgroupitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipgroupPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipgroupPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipgroupPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipgroupPojo 实例对象
     * @return 实例对象
     */
    WkWipgroupPojo insert(WkWipgroupPojo wkWipgroupPojo);

    /**
     * 修改数据
     *
     * @param wkWipgrouppojo 实例对象
     * @return 实例对象
     */
    WkWipgroupPojo update(WkWipgroupPojo wkWipgrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

}
