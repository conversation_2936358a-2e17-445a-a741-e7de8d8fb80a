package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSectionPojo;
import inks.service.std.manu.domain.pojo.WkSectionitemdetailPojo;
import inks.service.std.manu.domain.WkSectionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 生产工段(WkSection)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-28 11:21:19
 */
@Mapper
public interface WkSectionMapper {

    WkSectionPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<WkSectionitemdetailPojo> getPageList(QueryParam queryParam);

    List<WkSectionPojo> getPageTh(QueryParam queryParam);

    int insert(WkSectionEntity wkSectionEntity);

    int update(WkSectionEntity wkSectionEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(WkSectionPojo wkSectionPojo);

    List<WkSectionPojo> getListAll(String key,String tid);

    List<String> getItemCiteBillName(String key, String tid);
}

