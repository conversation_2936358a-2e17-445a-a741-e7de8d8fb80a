package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkCostbudgetmatPojo;

import java.util.List;

/**
 * 成本物料(WkCostbudgetmat)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-03 19:17:02
 */
public interface WkCostbudgetmatService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCostbudgetmatPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCostbudgetmatPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkCostbudgetmatPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkCostbudgetmatPojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetmatPojo insert(WkCostbudgetmatPojo wkCostbudgetmatPojo);

    /**
     * 修改数据
     *
     * @param wkCostbudgetmatpojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetmatPojo update(WkCostbudgetmatPojo wkCostbudgetmatpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkCostbudgetmatpojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetmatPojo clearNull(WkCostbudgetmatPojo wkCostbudgetmatpojo);
}
