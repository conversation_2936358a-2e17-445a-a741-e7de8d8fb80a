package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkMpcarryoverPojo;
import inks.service.std.manu.domain.pojo.WkMpcarryoveritemdetailPojo;

/**
 * 生产结转(WkMpcarryover)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-09 09:42:57
 */
public interface WkMpcarryoverService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMpcarryoverPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMpcarryoveritemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMpcarryoverPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMpcarryoverPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMpcarryoverPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkMpcarryoverPojo 实例对象
     * @return 实例对象
     */
    WkMpcarryoverPojo insert(WkMpcarryoverPojo wkMpcarryoverPojo);

    /**
     * 修改数据
     *
     * @param wkMpcarryoverpojo 实例对象
     * @return 实例对象
     */
    WkMpcarryoverPojo update(WkMpcarryoverPojo wkMpcarryoverpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 新增数据
     *
     * @param wkMpcarryoverPojo 实例对象
     * @return 实例对象
     */
    WkMpcarryoverPojo createCarry(WkMpcarryoverPojo wkMpcarryoverPojo);
}
