package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.HiWipnoteitemEntity;
import inks.service.std.manu.domain.pojo.HiWipnoteitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Wip记录子表(HiWipnoteitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-16 16:20:26
 */
 @Mapper
public interface HiWipnoteitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    HiWipnoteitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<HiWipnoteitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<HiWipnoteitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param hiWipnoteitemEntity 实例对象
     * @return 影响行数
     */
    int insert(HiWipnoteitemEntity hiWipnoteitemEntity);

    
    /**
     * 修改数据
     *
     * @param hiWipnoteitemEntity 实例对象
     * @return 影响行数
     */
    int update(HiWipnoteitemEntity hiWipnoteitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

