package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkManureportEntity;
import inks.service.std.manu.domain.WkManureportitemEntity;
import inks.service.std.manu.domain.pojo.WkManureportPojo;
import inks.service.std.manu.domain.pojo.WkManureportitemPojo;
import inks.service.std.manu.domain.pojo.WkManureportitemdetailPojo;
import inks.service.std.manu.mapper.WkManureportMapper;
import inks.service.std.manu.mapper.WkManureportitemMapper;
import inks.service.std.manu.service.WkManureportService;
import inks.service.std.manu.service.WkManureportitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 生产报工(WkManureport)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-17 16:16:42
 */
@Service("wkManureportService")
public class WkManureportServiceImpl implements WkManureportService {
    @Resource
    private WkManureportMapper wkManureportMapper;

    @Resource
    private WkManureportitemMapper wkManureportitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkManureportitemService wkManureportitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkManureportPojo getEntity(String key, String tid) {
        return this.wkManureportMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkManureportitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkManureportitemdetailPojo> lst = wkManureportMapper.getPageList(queryParam);
            PageInfo<WkManureportitemdetailPojo> pageInfo = new PageInfo<WkManureportitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkManureportPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkManureportPojo wkManureportPojo = this.wkManureportMapper.getEntity(key, tid);
            //读取子表
            wkManureportPojo.setItem(wkManureportitemMapper.getList(wkManureportPojo.getId(), wkManureportPojo.getTenantid()));
            return wkManureportPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkManureportPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkManureportPojo> lst = wkManureportMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkManureportitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkManureportPojo> pageInfo = new PageInfo<WkManureportPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkManureportPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkManureportPojo> lst = wkManureportMapper.getPageTh(queryParam);
            PageInfo<WkManureportPojo> pageInfo = new PageInfo<WkManureportPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkManureportPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkManureportPojo insert(WkManureportPojo wkManureportPojo) {
//初始化NULL字段
        if (wkManureportPojo.getRefno() == null) wkManureportPojo.setRefno("");
        if (wkManureportPojo.getBilltype() == null) wkManureportPojo.setBilltype("");
        if (wkManureportPojo.getBilldate() == null) wkManureportPojo.setBilldate(new Date());
        if (wkManureportPojo.getBilltitle() == null) wkManureportPojo.setBilltitle("");
        if (wkManureportPojo.getOperator() == null) wkManureportPojo.setOperator("");
        if (wkManureportPojo.getOperatorid() == null) wkManureportPojo.setOperatorid("");
        if (wkManureportPojo.getGroupid() == null) wkManureportPojo.setGroupid("");
        if (wkManureportPojo.getTradercode() == null) wkManureportPojo.setTradercode("");
        if (wkManureportPojo.getTradername() == null) wkManureportPojo.setTradername("");
        if (wkManureportPojo.getSummary() == null) wkManureportPojo.setSummary("");
        if (wkManureportPojo.getCreateby() == null) wkManureportPojo.setCreateby("");
        if (wkManureportPojo.getCreatebyid() == null) wkManureportPojo.setCreatebyid("");
        if (wkManureportPojo.getCreatedate() == null) wkManureportPojo.setCreatedate(new Date());
        if (wkManureportPojo.getLister() == null) wkManureportPojo.setLister("");
        if (wkManureportPojo.getListerid() == null) wkManureportPojo.setListerid("");
        if (wkManureportPojo.getModifydate() == null) wkManureportPojo.setModifydate(new Date());
        if (wkManureportPojo.getAssessor() == null) wkManureportPojo.setAssessor("");
        if (wkManureportPojo.getAssessorid() == null) wkManureportPojo.setAssessorid("");
        if (wkManureportPojo.getAssessdate() == null) wkManureportPojo.setAssessdate(new Date());
        if (wkManureportPojo.getItemcount() == null) wkManureportPojo.setItemcount(0);
        if (wkManureportPojo.getDisannulcount() == null) wkManureportPojo.setDisannulcount(0);
        if (wkManureportPojo.getFinishcount() == null) wkManureportPojo.setFinishcount(0);
        if (wkManureportPojo.getPrintcount() == null) wkManureportPojo.setPrintcount(0);
        if (wkManureportPojo.getCustom1() == null) wkManureportPojo.setCustom1("");
        if (wkManureportPojo.getCustom2() == null) wkManureportPojo.setCustom2("");
        if (wkManureportPojo.getCustom3() == null) wkManureportPojo.setCustom3("");
        if (wkManureportPojo.getCustom4() == null) wkManureportPojo.setCustom4("");
        if (wkManureportPojo.getCustom5() == null) wkManureportPojo.setCustom5("");
        if (wkManureportPojo.getCustom6() == null) wkManureportPojo.setCustom6("");
        if (wkManureportPojo.getCustom7() == null) wkManureportPojo.setCustom7("");
        if (wkManureportPojo.getCustom8() == null) wkManureportPojo.setCustom8("");
        if (wkManureportPojo.getCustom9() == null) wkManureportPojo.setCustom9("");
        if (wkManureportPojo.getCustom10() == null) wkManureportPojo.setCustom10("");
        if (wkManureportPojo.getTenantid() == null) wkManureportPojo.setTenantid("");
        if (wkManureportPojo.getTenantname() == null) wkManureportPojo.setTenantname("");
        if (wkManureportPojo.getRevision() == null) wkManureportPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkManureportEntity wkManureportEntity = new WkManureportEntity();
        BeanUtils.copyProperties(wkManureportPojo, wkManureportEntity);
        //设置id和新建日期
        wkManureportEntity.setId(id);
        wkManureportEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkManureportMapper.insert(wkManureportEntity);
        //Item子表处理
        List<WkManureportitemPojo> lst = wkManureportPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (WkManureportitemPojo wkManureportitemPojo : lst) {
                //初始化item的NULL
                WkManureportitemPojo itemPojo = this.wkManureportitemService.clearNull(wkManureportitemPojo);
                WkManureportitemEntity wkManureportitemEntity = new WkManureportitemEntity();
                BeanUtils.copyProperties(itemPojo, wkManureportitemEntity);
                //设置id和Pid
                wkManureportitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkManureportitemEntity.setPid(id);
                wkManureportitemEntity.setTenantid(wkManureportPojo.getTenantid());
                wkManureportitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkManureportitemMapper.insert(wkManureportitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkManureportEntity.getId(), wkManureportEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkManureportPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkManureportPojo update(WkManureportPojo wkManureportPojo) {
        //主表更改
        WkManureportEntity wkManureportEntity = new WkManureportEntity();
        BeanUtils.copyProperties(wkManureportPojo, wkManureportEntity);
        this.wkManureportMapper.update(wkManureportEntity);
        if (wkManureportPojo.getItem() != null) {
            //Item子表处理
            List<WkManureportitemPojo> lst = wkManureportPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkManureportMapper.getDelItemIds(wkManureportPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.wkManureportitemMapper.delete(lstDelId, wkManureportEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (WkManureportitemPojo wkManureportitemPojo : lst) {
                    WkManureportitemEntity wkManureportitemEntity = new WkManureportitemEntity();
                    if ("".equals(wkManureportitemPojo.getId()) || wkManureportitemPojo.getId() == null) {
                        //初始化item的NULL
                        WkManureportitemPojo itemPojo = this.wkManureportitemService.clearNull(wkManureportitemPojo);
                        BeanUtils.copyProperties(itemPojo, wkManureportitemEntity);
                        //设置id和Pid
                        wkManureportitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkManureportitemEntity.setPid(wkManureportEntity.getId());  // 主表 id
                        wkManureportitemEntity.setTenantid(wkManureportPojo.getTenantid());   // 租户id
                        wkManureportitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkManureportitemMapper.insert(wkManureportitemEntity);
                    } else {
                        BeanUtils.copyProperties(wkManureportitemPojo, wkManureportitemEntity);
                        wkManureportitemEntity.setTenantid(wkManureportPojo.getTenantid());
                        this.wkManureportitemMapper.update(wkManureportitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkManureportEntity.getId(), wkManureportEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkManureportPojo wkManureportPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkManureportitemPojo> lst = wkManureportPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkManureportitemPojo wkManureportitemPojo : lst) {
                this.wkManureportitemMapper.delete(wkManureportitemPojo.getId(), tid);
            }
        }
        this.wkManureportMapper.delete(key, tid);
        return wkManureportPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkManureportPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkManureportPojo approval(WkManureportPojo wkManureportPojo) {
        //主表更改
        WkManureportEntity wkManureportEntity = new WkManureportEntity();
        BeanUtils.copyProperties(wkManureportPojo, wkManureportEntity);
        this.wkManureportMapper.approval(wkManureportEntity);
        //返回Bill实例
        return this.getBillEntity(wkManureportEntity.getId(), wkManureportEntity.getTenantid());
    }

    // 根据加工单汇总报工总数
    @Override
    public ChartPojo getSumQtyByWork(String code, String key, String tid) {
        return this.wkManureportMapper.getSumQtyByWork(code, key, tid);
    }


    @Override
    public WkManureportPojo disannul(List<WkManureportitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            WkManureportitemPojo Pojo = lst.get(i);
            WkManureportitemPojo dbPojo = this.wkManureportitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    WkManureportitemEntity entity = new WkManureportitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.wkManureportitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.wkManureportMapper.updateDisannulCount(Pid, tid);
            //主表更改
            WkManureportEntity wkManureportEntity = new WkManureportEntity();
            wkManureportEntity.setId(Pid);
            wkManureportEntity.setLister(loginUser.getRealname());
            wkManureportEntity.setListerid(loginUser.getUserid());
            wkManureportEntity.setModifydate(new Date());
            wkManureportEntity.setTenantid(loginUser.getTenantid());
            this.wkManureportMapper.update(wkManureportEntity);
            //返回Bill实例
            return this.getBillEntity(wkManureportEntity.getId(), wkManureportEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    public WkManureportPojo closed(List<WkManureportitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            WkManureportitemPojo Pojo = lst.get(i);
            WkManureportitemPojo dbPojo = this.wkManureportitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    WkManureportitemEntity entity = new WkManureportitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.wkManureportitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.wkManureportMapper.updateFinishCount(Pid, tid);
            //主表更改
            WkManureportEntity wkManureportEntity = new WkManureportEntity();
            wkManureportEntity.setId(Pid);
            wkManureportEntity.setLister(loginUser.getRealname());
            wkManureportEntity.setListerid(loginUser.getUserid());
            wkManureportEntity.setModifydate(new Date());
            wkManureportEntity.setTenantid(loginUser.getTenantid());
            this.wkManureportMapper.update(wkManureportEntity);
            //返回Bill实例
            return this.getBillEntity(wkManureportEntity.getId(), wkManureportEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    public void updatePrintcount(WkManureportPojo billPrintPojo) {
        this.wkManureportMapper.updatePrintcount(billPrintPojo);
    }
}
