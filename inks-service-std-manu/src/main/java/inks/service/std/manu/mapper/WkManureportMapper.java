package inks.service.std.manu.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkManureportEntity;
import inks.service.std.manu.domain.pojo.WkManureportPojo;
import inks.service.std.manu.domain.pojo.WkManureportitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产报工(WkManureport)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-17 16:16:42
 */
@Mapper
public interface WkManureportMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkManureportPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkManureportitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkManureportPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkManureportEntity 实例对象
     * @return 影响行数
     */
    int insert(WkManureportEntity wkManureportEntity);


    /**
     * 修改数据
     *
     * @param wkManureportEntity 实例对象
     * @return 影响行数
     */
    int update(WkManureportEntity wkManureportEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkManureportPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkManureportPojo wkManureportPojo);

    /**
     * 修改数据
     *
     * @param wkManureportEntity 实例对象
     * @return 影响行数
     */
    int approval(WkManureportEntity wkManureportEntity);

    // 根据加工单汇总报工总数
    ChartPojo getSumQtyByWork(@Param("code") String code, @Param("key") String key, @Param("tid") String tid);

    int updateDisannulCount(@Param("key") String key, @Param("tid") String tid);
    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    void updatePrintcount(WkManureportPojo billPrintPojo);
}

