package inks.service.std.manu.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * MRP对象(WkMrpobj)Entity
 *
 * <AUTHOR>
 * @since 2024-09-25 16:21:14
 */
public class WkMrpobjEntity implements Serializable {
    private static final long serialVersionUID = 634639955154725820L;
     // id
    private String id;
     // Pid
    private String pid;
     // 商品ID
    private String goodsid;
     // 产品编码
    private String itemcode;
     // 产品名称
    private String itemname;
     // 产品规格
    private String itemspec;
     // 产品单位
    private String itemunit;
     // 数量
    private Double quantity;
     // 计划完工
    private Date plandate;
     // 销售单号
    private String machuid;
     // 销售子项id
    private String machitemid;
     // 订单批次
    private String machbatch;
     // 销售客户id
    private String machgroupid;
     // 主计划单号
    private String mainplanuid;
     // 主计划子项id
    private String mainplanitemid;
     // 客户
    private String customer;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 客户PO
    private String custpo;
    private String attributejson;
     // 总投数
    private Double wkpcsqty;
     // 投料Sec
    private Double wksecqty;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 商品ID
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 产品编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
   // 产品名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
   // 产品规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
   // 产品单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
   // 数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
   // 计划完工
    public Date getPlandate() {
        return plandate;
    }
    
    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }
        
   // 销售单号
    public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
   // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
   // 订单批次
    public String getMachbatch() {
        return machbatch;
    }
    
    public void setMachbatch(String machbatch) {
        this.machbatch = machbatch;
    }
        
   // 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }
    
    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }
        
   // 主计划单号
    public String getMainplanuid() {
        return mainplanuid;
    }
    
    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }
        
   // 主计划子项id
    public String getMainplanitemid() {
        return mainplanitemid;
    }
    
    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }
        
   // 客户
    public String getCustomer() {
        return customer;
    }
    
    public void setCustomer(String customer) {
        this.customer = customer;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 客户PO
    public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // 总投数
    public Double getWkpcsqty() {
        return wkpcsqty;
    }
    
    public void setWkpcsqty(Double wkpcsqty) {
        this.wkpcsqty = wkpcsqty;
    }
        
   // 投料Sec
    public Double getWksecqty() {
        return wksecqty;
    }
    
    public void setWksecqty(Double wksecqty) {
        this.wksecqty = wksecqty;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

