package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkCostbudgetcostPojo;

import java.util.List;

/**
 * 生成费用(WkCostbudgetcost)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-03 19:16:51
 */
public interface WkCostbudgetcostService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCostbudgetcostPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCostbudgetcostPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkCostbudgetcostPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkCostbudgetcostPojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetcostPojo insert(WkCostbudgetcostPojo wkCostbudgetcostPojo);

    /**
     * 修改数据
     *
     * @param wkCostbudgetcostpojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetcostPojo update(WkCostbudgetcostPojo wkCostbudgetcostpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkCostbudgetcostpojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetcostPojo clearNull(WkCostbudgetcostPojo wkCostbudgetcostpojo);
}
