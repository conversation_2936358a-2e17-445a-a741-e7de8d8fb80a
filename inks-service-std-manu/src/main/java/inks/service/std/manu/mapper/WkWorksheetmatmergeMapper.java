package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWorksheetmatmergeEntity;
import inks.service.std.manu.domain.pojo.WkWorksheetmatmergePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 厂制物料合并表(WkWorksheetmatmerge)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-11 15:16:59
 */
 @Mapper
public interface WkWorksheetmatmergeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetmatmergePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWorksheetmatmergePojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWorksheetmatmergePojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkWorksheetmatmergeEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWorksheetmatmergeEntity wkWorksheetmatmergeEntity);

    
    /**
     * 修改数据
     *
     * @param wkWorksheetmatmergeEntity 实例对象
     * @return 影响行数
     */
    int update(WkWorksheetmatmergeEntity wkWorksheetmatmergeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int deleteAllByPid(@Param("Pid") String key, @Param("tid") String tid);
}

