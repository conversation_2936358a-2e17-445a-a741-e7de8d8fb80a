package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSccompletematEntity;
import inks.service.std.manu.domain.pojo.WkSccompletematPojo;
import inks.service.std.manu.mapper.WkSccompletematMapper;
import inks.service.std.manu.service.WkSccompletematService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 验收物料(WkSccompletemat)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-07 11:01:59
 */
@Service("wkSccompletematService")
public class WkSccompletematServiceImpl implements WkSccompletematService {
    @Resource
    private WkSccompletematMapper wkSccompletematMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSccompletematPojo getEntity(String key, String tid) {
        return this.wkSccompletematMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSccompletematPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSccompletematPojo> lst = wkSccompletematMapper.getPageList(queryParam);
            PageInfo<WkSccompletematPojo> pageInfo = new PageInfo<WkSccompletematPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkSccompletematPojo> getList(String Pid, String tid) {
        try {
            List<WkSccompletematPojo> lst = wkSccompletematMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkSccompletematPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccompletematPojo insert(WkSccompletematPojo wkSccompletematPojo) {
        //初始化item的NULL
        WkSccompletematPojo itempojo = this.clearNull(wkSccompletematPojo);
        WkSccompletematEntity wkSccompletematEntity = new WkSccompletematEntity();
        BeanUtils.copyProperties(itempojo, wkSccompletematEntity);
        //生成雪花id
        wkSccompletematEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkSccompletematEntity.setRevision(1);  //乐观锁
        this.wkSccompletematMapper.insert(wkSccompletematEntity);
        return this.getEntity(wkSccompletematEntity.getId(), wkSccompletematEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSccompletematPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccompletematPojo update(WkSccompletematPojo wkSccompletematPojo) {
        WkSccompletematEntity wkSccompletematEntity = new WkSccompletematEntity();
        BeanUtils.copyProperties(wkSccompletematPojo, wkSccompletematEntity);
        this.wkSccompletematMapper.update(wkSccompletematEntity);
        return this.getEntity(wkSccompletematEntity.getId(), wkSccompletematEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkSccompletematMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkSccompletematPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccompletematPojo clearNull(WkSccompletematPojo wkSccompletematPojo) {
        //初始化NULL字段
        if (wkSccompletematPojo.getPid() == null) wkSccompletematPojo.setPid("");
        if (wkSccompletematPojo.getItemid() == null) wkSccompletematPojo.setItemid("");
        if (wkSccompletematPojo.getGoodsid() == null) wkSccompletematPojo.setGoodsid("");
        if (wkSccompletematPojo.getItemcode() == null) wkSccompletematPojo.setItemcode("");
        if (wkSccompletematPojo.getItemname() == null) wkSccompletematPojo.setItemname("");
        if (wkSccompletematPojo.getItemspec() == null) wkSccompletematPojo.setItemspec("");
        if (wkSccompletematPojo.getItemunit() == null) wkSccompletematPojo.setItemunit("");
        if (wkSccompletematPojo.getQuantity() == null) wkSccompletematPojo.setQuantity(0D);
        if (wkSccompletematPojo.getTaxprice() == null) wkSccompletematPojo.setTaxprice(0D);
        if (wkSccompletematPojo.getTaxamount() == null) wkSccompletematPojo.setTaxamount(0D);
        if (wkSccompletematPojo.getPrice() == null) wkSccompletematPojo.setPrice(0D);
        if (wkSccompletematPojo.getAmount() == null) wkSccompletematPojo.setAmount(0D);
        if (wkSccompletematPojo.getTaxtotal() == null) wkSccompletematPojo.setTaxtotal(0D);
        if (wkSccompletematPojo.getItemtaxrate() == null) wkSccompletematPojo.setItemtaxrate(0);
        if (wkSccompletematPojo.getRownum() == null) wkSccompletematPojo.setRownum(0);
        if (wkSccompletematPojo.getRemark() == null) wkSccompletematPojo.setRemark("");
        if (wkSccompletematPojo.getFinishqty() == null) wkSccompletematPojo.setFinishqty(0D);
        if (wkSccompletematPojo.getBomid() == null) wkSccompletematPojo.setBomid("");
        if (wkSccompletematPojo.getBomtype() == null) wkSccompletematPojo.setBomtype(0);
        if (wkSccompletematPojo.getBomitemid() == null) wkSccompletematPojo.setBomitemid("");
        if (wkSccompletematPojo.getWorkitemid() == null) wkSccompletematPojo.setWorkitemid("");
        if (wkSccompletematPojo.getWorkitemmatid() == null) wkSccompletematPojo.setWorkitemmatid("");
        if (wkSccompletematPojo.getCustom1() == null) wkSccompletematPojo.setCustom1("");
        if (wkSccompletematPojo.getCustom2() == null) wkSccompletematPojo.setCustom2("");
        if (wkSccompletematPojo.getCustom3() == null) wkSccompletematPojo.setCustom3("");
        if (wkSccompletematPojo.getCustom4() == null) wkSccompletematPojo.setCustom4("");
        if (wkSccompletematPojo.getCustom5() == null) wkSccompletematPojo.setCustom5("");
        if (wkSccompletematPojo.getCustom6() == null) wkSccompletematPojo.setCustom6("");
        if (wkSccompletematPojo.getCustom7() == null) wkSccompletematPojo.setCustom7("");
        if (wkSccompletematPojo.getCustom8() == null) wkSccompletematPojo.setCustom8("");
        if (wkSccompletematPojo.getCustom9() == null) wkSccompletematPojo.setCustom9("");
        if (wkSccompletematPojo.getCustom10() == null) wkSccompletematPojo.setCustom10("");
        if (wkSccompletematPojo.getTenantid() == null) wkSccompletematPojo.setTenantid("");
        if (wkSccompletematPojo.getRevision() == null) wkSccompletematPojo.setRevision(0);
        return wkSccompletematPojo;
    }
}
