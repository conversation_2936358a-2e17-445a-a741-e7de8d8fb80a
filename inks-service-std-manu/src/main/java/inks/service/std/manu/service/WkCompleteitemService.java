package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkCompleteitemPojo;

import java.util.List;

/**
 * 验收项目(WkCompleteitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-03 18:27:15
 */
public interface WkCompleteitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCompleteitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCompleteitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkCompleteitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkCompleteitemPojo 实例对象
     * @return 实例对象
     */
    WkCompleteitemPojo insert(WkCompleteitemPojo wkCompleteitemPojo);

    /**
     * 修改数据
     *
     * @param wkCompleteitempojo 实例对象
     * @return 实例对象
     */
    WkCompleteitemPojo update(WkCompleteitemPojo wkCompleteitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkCompleteitempojo 实例对象
     * @return 实例对象
     */
    WkCompleteitemPojo clearNull(WkCompleteitemPojo wkCompleteitempojo);
}
