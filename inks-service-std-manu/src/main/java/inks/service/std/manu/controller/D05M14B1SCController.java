package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkSccarryoverPojo;
import inks.service.std.manu.domain.pojo.WkSccarryoveritemPojo;
import inks.service.std.manu.service.WkSccarryoverService;
import inks.service.std.manu.service.WkSccarryoveritemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 加工结转(Wk_ScCarryover)表控制层
 *
 * <AUTHOR>
 * @since 2022-06-09 14:04:49
 */
@RestController
@RequestMapping("D05M14B1SC")
@Api(tags = "D05M14B1SC:委外结转")
public class D05M14B1SCController extends WkSccarryoverController {

    /**
     * 服务对象
     */
    @Resource
    private WkSccarryoverService wkSccarryoverService;

    /**
     * 服务对象Item
     */
    @Resource
    private WkSccarryoveritemService wkSccarryoveritemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增车间结转", notes = "新增车间结转", produces = "application/json")
    @RequestMapping(value = "/createCarry", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ScCarryover.Add")
    public R<WkSccarryoverPojo> createCarry(@RequestBody String json) {
        try {
            WkSccarryoverPojo wkSccarryoverPojo = JSONArray.parseObject(json, WkSccarryoverPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            R r = systemFeignService.getBillCode("D05M16B1", loginUser.getToken());
            if (r.getCode() == 200)
                wkSccarryoverPojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            wkSccarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
            wkSccarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkSccarryoverPojo.setCreatedate(new Date());   // 创建时间
            wkSccarryoverPojo.setLister(loginUser.getRealname());   // 制表
            wkSccarryoverPojo.setListerid(loginUser.getUserid());    // 制表id
            wkSccarryoverPojo.setModifydate(new Date());   //修改时间
            wkSccarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.wkSccarryoverService.createCarry(wkSccarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询Item", notes = "按条件分页查询Item", produces = "application/json")
    @RequestMapping(value = "/getItemPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ScCarryover.List")
    public R<PageInfo<WkSccarryoveritemPojo>> getItemPageList(@RequestBody String json, String key) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_ScCarryoverItem.RowNum");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Wk_ScCarryoverItem.Pid='" + key + "'");
            return R.ok(this.wkSccarryoveritemService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getInitPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ScCarryover.List")
    public R<PageInfo<WkSccarryoverPojo>> getInitPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_ScCarryover.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Wk_ScCarryover.BillType='期初建账'");
            return R.ok(this.wkSccarryoverService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ScCarryover.List")
    public R<PageInfo<WkSccarryoverPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_ScCarryover.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSccarryoverService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

