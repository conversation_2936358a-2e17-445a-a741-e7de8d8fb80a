package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSteppricePojo;
import inks.service.std.manu.domain.pojo.WkSteppriceitemdetailPojo;

import java.util.List;
import java.util.Map;

/**
 * 阶梯工价(WkStepprice)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-26 16:21:18
 */
public interface WkSteppriceService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSteppricePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSteppriceitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSteppricePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSteppricePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSteppricePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSteppricePojo 实例对象
     * @return 实例对象
     */
    WkSteppricePojo insert(WkSteppricePojo wkSteppricePojo);

    /**
     * 修改数据
     *
     * @param wkSteppricepojo 实例对象
     * @return 实例对象
     */
    WkSteppricePojo update(WkSteppricePojo wkSteppricepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkSteppricePojo 实例对象
     * @return 实例对象
     */
    WkSteppricePojo approval(WkSteppricePojo wkSteppricePojo);

    List<Map<String, Object>> getProductionCostBudget(Map<String, Object> jsonMap, String progroupid, String spu_configValue, String tid);

    List<Map<String, Object>> getMachItemList(String id, String tenantid);

    void getProductionCostBudgetStart(String redisKey, String id, String progroupid,String spu_configValue, String tid);

}
