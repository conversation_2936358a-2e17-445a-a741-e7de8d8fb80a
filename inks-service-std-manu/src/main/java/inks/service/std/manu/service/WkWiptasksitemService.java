package inks.service.std.manu.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWiptasksitemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 派工单子表(WkWiptasksitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-06 14:34:14
 */
public interface WkWiptasksitemService {

    WkWiptasksitemPojo getEntity(String key,String tid);

    PageInfo<WkWiptasksitemPojo> getPageList(QueryParam queryParam);

    List<WkWiptasksitemPojo> getList(String Pid,String tid);  

    WkWiptasksitemPojo insert(WkWiptasksitemPojo wkWiptasksitemPojo);

    WkWiptasksitemPojo update(WkWiptasksitemPojo wkWiptasksitempojo);

    int delete(String key,String tid);

    WkWiptasksitemPojo clearNull(WkWiptasksitemPojo wkWiptasksitempojo);
}
