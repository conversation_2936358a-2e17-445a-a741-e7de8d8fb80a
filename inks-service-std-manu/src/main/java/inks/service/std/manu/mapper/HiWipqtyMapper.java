package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.HiWipqtyEntity;
import inks.service.std.manu.domain.pojo.HiWipqtyPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产过数(HiWipqty)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-16 16:20:44
 */
@Mapper
public interface HiWipqtyMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    HiWipqtyPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<HiWipqtyPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param hiWipqtyEntity 实例对象
     * @return 影响行数
     */
    int insert(HiWipqtyEntity hiWipqtyEntity);


    /**
     * 修改数据
     *
     * @param hiWipqtyEntity 实例对象
     * @return 影响行数
     */
    int update(HiWipqtyEntity hiWipqtyEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    int copyNowToHi(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("onlineIds") List<String> onlineIds, @Param("tid") String tid);

    int deleteNow(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("onlineIds") List<String> onlineIds, @Param("tid") String tid);

    int copyHiToNow(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("ids") List<String> ids, @Param("tid") String tenantid);

    int deleteHi(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("ids") List<String> ids, @Param("tid") String tenantid);

    List<String> getOnlineNowWipNoteRefNos(@Param("startdate") String startdate, @Param("enddate") String enddate, @Param("tid") String tid);

    List<String> getAllMigratableNowIds(String startdate, String enddate, List<String> onlineRefNos, String tid);

    int deleteNowByIds(List<String> ids, String tid);

    int copyNowToHiByIds(List<String> ids, String tid);

    List<String> getAllMigratableHiIds(String startdate, String enddate, String tid);

    int copyHiToNowByIds(List<String> ids, String tid);

    int deleteHiByIds(List<String> ids, String tid);
}

