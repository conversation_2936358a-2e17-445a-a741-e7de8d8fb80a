package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkStationstatePojo;

/**
 * 工位状态(WkStationstate)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-17 15:34:48
 */
public interface WkStationstateService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStationstatePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkStationstatePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkStationstatePojo 实例对象
     * @return 实例对象
     */
    WkStationstatePojo insert(WkStationstatePojo wkStationstatePojo);

    /**
     * 修改数据
     *
     * @param wkStationstatepojo 实例对象
     * @return 实例对象
     */
    WkStationstatePojo update(WkStationstatePojo wkStationstatepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
