package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkMainplanitemPojo;

import java.util.List;

/**
 * 主计划项目(WkMainplanitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-26 10:04:31
 */
public interface WkMainplanitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMainplanitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMainplanitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMainplanitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkMainplanitemPojo 实例对象
     * @return 实例对象
     */
    WkMainplanitemPojo insert(WkMainplanitemPojo wkMainplanitemPojo);

    /**
     * 修改数据
     *
     * @param wkMainplanitempojo 实例对象
     * @return 实例对象
     */
    WkMainplanitemPojo update(WkMainplanitemPojo wkMainplanitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkMainplanitempojo 实例对象
     * @return 实例对象
     */
    WkMainplanitemPojo clearNull(WkMainplanitemPojo wkMainplanitempojo);
}
