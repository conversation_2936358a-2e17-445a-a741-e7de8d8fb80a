package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkCostbudgetitemPojo;

import java.util.List;

/**
 * 成本预测Item(WkCostbudgetitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-03 19:16:38
 */
public interface WkCostbudgetitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCostbudgetitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCostbudgetitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkCostbudgetitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkCostbudgetitemPojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetitemPojo insert(WkCostbudgetitemPojo wkCostbudgetitemPojo);

    /**
     * 修改数据
     *
     * @param wkCostbudgetitempojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetitemPojo update(WkCostbudgetitemPojo wkCostbudgetitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkCostbudgetitempojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetitemPojo clearNull(WkCostbudgetitemPojo wkCostbudgetitempojo);
}
