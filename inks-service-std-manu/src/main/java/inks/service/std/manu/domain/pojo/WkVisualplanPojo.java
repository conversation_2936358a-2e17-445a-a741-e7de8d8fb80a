package inks.service.std.manu.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 可视化排程(WkVisualplan)实体类
 *
 * <AUTHOR>
 * @since 2022-08-01 15:10:03
 */
public class WkVisualplanPojo implements Serializable {
    private static final long serialVersionUID = -35929880400723487L;
         // id
         @Excel(name = "id") 
    private String id;
         // 通用分组
         @Excel(name = "通用分组") 
    private String gengroupid;
         // 单据编码
         @Excel(name = "单据编码") 
    private String billid;
         // 单据编码
         @Excel(name = "单据编码") 
    private String billcode;
         // 单据日期
         @Excel(name = "单据日期") 
    private Date billdate;
         // 单据类型
         @Excel(name = "单据类型") 
    private String billtype;
         // 生产车间id
         @Excel(name = "生产车间id") 
    private String workshopid;
         // 生产车间
         @Excel(name = "生产车间") 
    private String workshop;
         // 工序id
         @Excel(name = "工序id") 
    private String wpid;
         // 工序编码
         @Excel(name = "工序编码") 
    private String wpcode;
         // 工序名称
         @Excel(name = "工序名称") 
    private String wpname;
         // 货品id
         @Excel(name = "货品id") 
    private String goodsid;
         // 产品编码
         @Excel(name = "产品编码") 
    private String itemcode;
         // 产品名称
         @Excel(name = "产品名称") 
    private String itemname;
         // 产品规格
         @Excel(name = "产品规格") 
    private String itemspec;
         // 产品单位
         @Excel(name = "产品单位") 
    private String itemunit;
         // 数量
         @Excel(name = "数量") 
    private Double quantity;
         // 开工
         @Excel(name = "开工") 
    private Date startdate;
         // 完工
         @Excel(name = "完工") 
    private Date enddate;
         // 日产量
         @Excel(name = "日产量") 
    private Double efficiency;
         // 已排入
         @Excel(name = "已排入") 
    private Double finishqty;
         // 备注
         @Excel(name = "备注") 
    private String remark;
         // json内容
         @Excel(name = "json内容") 
    private String jsoncontent;
         // 关闭
         @Excel(name = "关闭") 
    private Integer closed;
         // 创建者
         @Excel(name = "创建者") 
    private String createby;
         // 创建者id
         @Excel(name = "创建者id") 
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期") 
    private Date createdate;
         // 制表
         @Excel(name = "制表") 
    private String lister;
         // 制表id
         @Excel(name = "制表id") 
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期") 
    private Date modifydate;
         // 自定义1
         @Excel(name = "自定义1") 
    private String custom1;
         // 自定义2
         @Excel(name = "自定义2") 
    private String custom2;
         // 自定义3
         @Excel(name = "自定义3") 
    private String custom3;
         // 自定义4
         @Excel(name = "自定义4") 
    private String custom4;
         // 自定义5
         @Excel(name = "自定义5") 
    private String custom5;
         // 自定义6
         @Excel(name = "自定义6") 
    private String custom6;
         // 自定义7
         @Excel(name = "自定义7") 
    private String custom7;
         // 自定义8
         @Excel(name = "自定义8") 
    private String custom8;
         // 自定义9
         @Excel(name = "自定义9") 
    private String custom9;
         // 自定义10
         @Excel(name = "自定义10") 
    private String custom10;
         // 租户id
         @Excel(name = "租户id") 
    private String tenantid;
         // 租户名称
         @Excel(name = "租户名称") 
    private String tenantname;
         // 乐观锁
         @Excel(name = "乐观锁") 
    private Integer revision;

     // id
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // 通用分组
       public String getGengroupid() {
        return gengroupid;
    }
    
    public void setGengroupid(String gengroupid) {
        this.gengroupid = gengroupid;
    }
        
     // 单据编码
       public String getBillid() {
        return billid;
    }
    
    public void setBillid(String billid) {
        this.billid = billid;
    }
        
     // 单据编码
       public String getBillcode() {
        return billcode;
    }
    
    public void setBillcode(String billcode) {
        this.billcode = billcode;
    }
        
     // 单据日期
       public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
     // 单据类型
       public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
     // 生产车间id
       public String getWorkshopid() {
        return workshopid;
    }
    
    public void setWorkshopid(String workshopid) {
        this.workshopid = workshopid;
    }
        
     // 生产车间
       public String getWorkshop() {
        return workshop;
    }
    
    public void setWorkshop(String workshop) {
        this.workshop = workshop;
    }
        
     // 工序id
       public String getWpid() {
        return wpid;
    }
    
    public void setWpid(String wpid) {
        this.wpid = wpid;
    }
        
     // 工序编码
       public String getWpcode() {
        return wpcode;
    }
    
    public void setWpcode(String wpcode) {
        this.wpcode = wpcode;
    }
        
     // 工序名称
       public String getWpname() {
        return wpname;
    }
    
    public void setWpname(String wpname) {
        this.wpname = wpname;
    }
        
     // 货品id
       public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
     // 产品编码
       public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
     // 产品名称
       public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
     // 产品规格
       public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
     // 产品单位
       public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
     // 数量
       public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
     // 开工
       public Date getStartdate() {
        return startdate;
    }
    
    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }
        
     // 完工
       public Date getEnddate() {
        return enddate;
    }
    
    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }
        
     // 日产量
       public Double getEfficiency() {
        return efficiency;
    }
    
    public void setEfficiency(Double efficiency) {
        this.efficiency = efficiency;
    }
        
     // 已排入
       public Double getFinishqty() {
        return finishqty;
    }
    
    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }
        
     // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // json内容
       public String getJsoncontent() {
        return jsoncontent;
    }
    
    public void setJsoncontent(String jsoncontent) {
        this.jsoncontent = jsoncontent;
    }
        
     // 关闭
       public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
     // 创建者
       public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
     // 创建者id
       public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
     // 新建日期
       public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
     // 制表
       public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
     // 制表id
       public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
     // 修改日期
       public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
     // 自定义1
       public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
     // 自定义2
       public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
     // 自定义3
       public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
     // 自定义4
       public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
     // 自定义5
       public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
     // 自定义6
       public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
     // 自定义7
       public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
     // 自定义8
       public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
     // 自定义9
       public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
     // 自定义10
       public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
     // 租户id
       public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
     // 租户名称
       public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

