package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkProcessEntity;
import inks.service.std.manu.domain.WkProcessitemEntity;
import inks.service.std.manu.domain.WkProcessstatEntity;
import inks.service.std.manu.domain.pojo.WkProcessPojo;
import inks.service.std.manu.domain.pojo.WkProcessitemPojo;
import inks.service.std.manu.domain.pojo.WkProcessitemdetailPojo;
import inks.service.std.manu.domain.pojo.WkProcessstatPojo;
import inks.service.std.manu.mapper.WkProccostMapper;
import inks.service.std.manu.mapper.WkProcessMapper;
import inks.service.std.manu.mapper.WkProcessitemMapper;
import inks.service.std.manu.mapper.WkProcessstatMapper;
import inks.service.std.manu.service.WkProcessService;
import inks.service.std.manu.service.WkProcessitemService;
import inks.service.std.manu.service.WkProcessstatService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 生产工序(WkProcess)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-14 20:42:51
 */
@Service("wkProcessService")
public class WkProcessServiceImpl implements WkProcessService {
    @Resource
    private WkProcessMapper wkProcessMapper;

    @Resource
    private WkProcessitemMapper wkProcessitemMapper;
    @Resource
    private WkProcessstatMapper wkProcessstatMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private WkProcessitemService wkProcessitemService;
    @Resource
    private WkProccostMapper wkProccostMapper;
    @Resource
    private WkProcessstatService wkProcessstatService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkProcessPojo getEntity(String key, String tid) {
        return this.wkProcessMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkProcessitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProcessitemdetailPojo> lst = wkProcessMapper.getPageList(queryParam);
            PageInfo<WkProcessitemdetailPojo> pageInfo = new PageInfo<WkProcessitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkProcessPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkProcessPojo wkProcessPojo = this.wkProcessMapper.getEntity(key, tid);
            //读取子表
            if (wkProcessPojo != null) {
                wkProcessPojo.setItem(wkProcessitemMapper.getList(wkProcessPojo.getId(), wkProcessPojo.getTenantid()));
                wkProcessPojo.setStat(wkProcessstatMapper.getList(wkProcessPojo.getId(), wkProcessPojo.getTenantid()));
            }
            return wkProcessPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkProcessPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProcessPojo> lst = wkProcessMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkProcessitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setStat(wkProcessstatMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkProcessPojo> pageInfo = new PageInfo<WkProcessPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkProcessPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProcessPojo> lst = wkProcessMapper.getPageTh(queryParam);
            PageInfo<WkProcessPojo> pageInfo = new PageInfo<WkProcessPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkProcessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkProcessPojo insert(WkProcessPojo wkProcessPojo) {
//初始化NULL字段
        if (wkProcessPojo.getWpcode() == null) wkProcessPojo.setWpcode("");
        if (wkProcessPojo.getWpname() == null) wkProcessPojo.setWpname("");
        if (wkProcessPojo.getPost() == null) wkProcessPojo.setPost(0);
        if (wkProcessPojo.getOutsourcing() == null) wkProcessPojo.setOutsourcing(0);
        if (wkProcessPojo.getBalanceunit() == null) wkProcessPojo.setBalanceunit("");
        if (wkProcessPojo.getCapacity() == null) wkProcessPojo.setCapacity(0D);
        if (wkProcessPojo.getMintime() == null) wkProcessPojo.setMintime(0D);
        if (wkProcessPojo.getSubqty() == null) wkProcessPojo.setSubqty(0D);
        if (wkProcessPojo.getSubunit() == null) wkProcessPojo.setSubunit("");
        if (wkProcessPojo.getSubunituse() == null) wkProcessPojo.setSubunituse("");
        if (wkProcessPojo.getAimmrb() == null) wkProcessPojo.setAimmrb(0D);
        if (wkProcessPojo.getAimcost() == null) wkProcessPojo.setAimcost(0D);
        if (wkProcessPojo.getSplitqty() == null) wkProcessPojo.setSplitqty(0);
        if (wkProcessPojo.getRownum() == null) wkProcessPojo.setRownum(0);
        if (wkProcessPojo.getSummary() == null) wkProcessPojo.setSummary("");
        if (wkProcessPojo.getEnabledmark() == null) wkProcessPojo.setEnabledmark(0);
        if (wkProcessPojo.getCreateby() == null) wkProcessPojo.setCreateby("");
        if (wkProcessPojo.getCreatebyid() == null) wkProcessPojo.setCreatebyid("");
        if (wkProcessPojo.getCreatedate() == null) wkProcessPojo.setCreatedate(new Date());
        if (wkProcessPojo.getLister() == null) wkProcessPojo.setLister("");
        if (wkProcessPojo.getListerid() == null) wkProcessPojo.setListerid("");
        if (wkProcessPojo.getModifydate() == null) wkProcessPojo.setModifydate(new Date());
        if (wkProcessPojo.getDeletemark() == null) wkProcessPojo.setDeletemark(0);
        if (wkProcessPojo.getDeletelisterid() == null) wkProcessPojo.setDeletelisterid("");
        if (wkProcessPojo.getDeletelister() == null) wkProcessPojo.setDeletelister("");
        if (wkProcessPojo.getDeletedate() == null) wkProcessPojo.setDeletedate(new Date());
        if (wkProcessPojo.getBackcolorargb() == null) wkProcessPojo.setBackcolorargb("");
        if (wkProcessPojo.getForecolorargb() == null) wkProcessPojo.setForecolorargb("");
        if (wkProcessPojo.getTargetqty() == null) wkProcessPojo.setTargetqty(0D);
        if (wkProcessPojo.getTargethours() == null) wkProcessPojo.setTargethours(0D);
        if (wkProcessPojo.getTargetamt() == null) wkProcessPojo.setTargetamt(0D);
        if (wkProcessPojo.getFrontphoto() == null) wkProcessPojo.setFrontphoto("");
        if (wkProcessPojo.getWorkcontent() == null) wkProcessPojo.setWorkcontent("");
        if (wkProcessPojo.getLeadernamea() == null) wkProcessPojo.setLeadernamea("");
        if (wkProcessPojo.getLeadertitlea() == null) wkProcessPojo.setLeadertitlea("");
        if (wkProcessPojo.getLeaderavatara() == null) wkProcessPojo.setLeaderavatara("");
        if (wkProcessPojo.getLeadernameb() == null) wkProcessPojo.setLeadernameb("");
        if (wkProcessPojo.getLeadertitleb() == null) wkProcessPojo.setLeadertitleb("");
        if (wkProcessPojo.getLeaderavatarb() == null) wkProcessPojo.setLeaderavatarb("");
        if (wkProcessPojo.getWorkparam() == null) wkProcessPojo.setWorkparam("");
        if (wkProcessPojo.getLastmark() == null) wkProcessPojo.setLastmark(0);
        if (wkProcessPojo.getOnlinebatch() == null) wkProcessPojo.setOnlinebatch(0);
        if (wkProcessPojo.getKeymark() == null) wkProcessPojo.setKeymark(0);
        if (wkProcessPojo.getCustom1() == null) wkProcessPojo.setCustom1("");
        if (wkProcessPojo.getCustom2() == null) wkProcessPojo.setCustom2("");
        if (wkProcessPojo.getCustom3() == null) wkProcessPojo.setCustom3("");
        if (wkProcessPojo.getCustom4() == null) wkProcessPojo.setCustom4("");
        if (wkProcessPojo.getCustom5() == null) wkProcessPojo.setCustom5("");
        if (wkProcessPojo.getCustom6() == null) wkProcessPojo.setCustom6("");
        if (wkProcessPojo.getCustom7() == null) wkProcessPojo.setCustom7("");
        if (wkProcessPojo.getCustom8() == null) wkProcessPojo.setCustom8("");
        if (wkProcessPojo.getCustom9() == null) wkProcessPojo.setCustom9("");
        if (wkProcessPojo.getCustom10() == null) wkProcessPojo.setCustom10("");
        if (wkProcessPojo.getTenantid() == null) wkProcessPojo.setTenantid("");
        if (wkProcessPojo.getRevision() == null) wkProcessPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkProcessEntity wkProcessEntity = new WkProcessEntity();
        BeanUtils.copyProperties(wkProcessPojo, wkProcessEntity);
        //设置id和新建日期
        wkProcessEntity.setId(id);
        wkProcessEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkProcessMapper.insert(wkProcessEntity);
        //Item子表处理
        List<WkProcessitemPojo> lst = wkProcessPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkProcessitemPojo itemPojo = this.wkProcessitemService.clearNull(lst.get(i));
                WkProcessitemEntity wkProcessitemEntity = new WkProcessitemEntity();
                BeanUtils.copyProperties(itemPojo, wkProcessitemEntity);
                //设置id和Pid
                wkProcessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkProcessitemEntity.setPid(id);
                wkProcessitemEntity.setTenantid(wkProcessPojo.getTenantid());
                wkProcessitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkProcessitemMapper.insert(wkProcessitemEntity);
            }
        }

        //stat子表处理
        List<WkProcessstatPojo> lstStat = wkProcessPojo.getStat();
        if (lstStat != null) {
            //循环每个stat子表
            for (int i = 0; i < lstStat.size(); i++) {
                //初始化stat的NULL
                WkProcessstatPojo statPojo = this.wkProcessstatService.clearNull(lstStat.get(i));
                WkProcessstatEntity wkProcessstatEntity = new WkProcessstatEntity();
                BeanUtils.copyProperties(statPojo, wkProcessstatEntity);
                //设置id和Pid
                wkProcessstatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkProcessstatEntity.setPid(id);
                wkProcessstatEntity.setTenantid(wkProcessPojo.getTenantid());
                wkProcessstatEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkProcessstatMapper.insert(wkProcessstatEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkProcessEntity.getId(), wkProcessEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkProcessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkProcessPojo update(WkProcessPojo wkProcessPojo) {
        //主表更改
        WkProcessEntity wkProcessEntity = new WkProcessEntity();
        BeanUtils.copyProperties(wkProcessPojo, wkProcessEntity);
        this.wkProcessMapper.update(wkProcessEntity);
        //Item子表处理
        List<WkProcessitemPojo> lst = wkProcessPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = wkProcessMapper.getDelItemIds(wkProcessPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.wkProcessitemMapper.delete(lstDelIds.get(i), wkProcessEntity.getTenantid());
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                WkProcessitemEntity wkProcessitemEntity = new WkProcessitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    WkProcessitemPojo itemPojo = this.wkProcessitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, wkProcessitemEntity);
                    //设置id和Pid
                    wkProcessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    wkProcessitemEntity.setPid(wkProcessEntity.getId());  // 主表 id
                    wkProcessitemEntity.setTenantid(wkProcessPojo.getTenantid());   // 租户id
                    wkProcessitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.wkProcessitemMapper.insert(wkProcessitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), wkProcessitemEntity);
                    wkProcessitemEntity.setTenantid(wkProcessPojo.getTenantid());
                    this.wkProcessitemMapper.update(wkProcessitemEntity);
                }
            }
        }
        if (wkProcessPojo.getStat() != null) {
            //stat子表处理
            List<WkProcessstatPojo> lstStat = wkProcessPojo.getStat();
            //获取被删除的stat
            List<String> lstDelStatIds = wkProcessMapper.getDelStatIds(wkProcessPojo);
            if (lstDelStatIds != null) {
                //循环每个删除stat子表
                for (int i = 0; i < lstDelStatIds.size(); i++) {
                    this.wkProcessstatMapper.delete(lstDelStatIds.get(i), wkProcessEntity.getTenantid());
                }
            }
            if (lstStat != null) {
                //循环每个stat子表
                for (int i = 0; i < lstStat.size(); i++) {
                    WkProcessstatEntity wkProcessstatEntity = new WkProcessstatEntity();
                    if ("".equals(lstStat.get(i).getId()) || lstStat.get(i).getId() == null) {
                        //初始化stat的NULL
                        WkProcessstatPojo statPojo = this.wkProcessstatService.clearNull(lstStat.get(i));
                        BeanUtils.copyProperties(statPojo, wkProcessstatEntity);
                        //设置id和Pid
                        wkProcessstatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // stat id
                        wkProcessstatEntity.setPid(wkProcessEntity.getId());  // 主表 id
                        wkProcessstatEntity.setTenantid(wkProcessPojo.getTenantid());   // 租户id
                        wkProcessstatEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkProcessstatMapper.insert(wkProcessstatEntity);
                    } else {
                        BeanUtils.copyProperties(lstStat.get(i), wkProcessstatEntity);
                        wkProcessstatEntity.setTenantid(wkProcessPojo.getTenantid());
                        this.wkProcessstatMapper.update(wkProcessstatEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkProcessEntity.getId(), wkProcessEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        // 检查若工段添加了本工序，则禁止删除
        String sectName = wkProcessMapper.checkSectByWkid(key, tid);
        if (StringUtils.isNotBlank(sectName)) {
            throw new RuntimeException("禁止删除，本工序被以下工段引用：" + sectName);
        }
        WkProcessPojo wkProcessPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkProcessitemPojo> lst = wkProcessPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkProcessitemPojo wkProcessitemPojo : lst) {
                this.wkProcessitemMapper.delete(wkProcessitemPojo.getId(), tid);
            }
        }
        //Stat子表处理
        List<WkProcessstatPojo> lstStat = wkProcessPojo.getStat();
        if (lstStat != null) {
            //循环每个删除stat子表
            for (WkProcessstatPojo wkProcessstatPojo : lstStat) {
                this.wkProcessstatMapper.delete(wkProcessstatPojo.getId(), tid);
            }
        }
        // 级联删除工序成本
        this.wkProccostMapper.delete(key, tid);
        this.wkProcessMapper.delete(key, tid);
        return wkProcessPojo.getWpname();
    }

    @Override
    public List<WkProcessPojo> getPostList(String tenantid) {
        return wkProcessMapper.getPostList(tenantid);
    }
    @Override
    public WkProcessPojo getEntityByWpName(String wpname, String tenantid) {
        return this.wkProcessMapper.getEntityByWpName(wpname, tenantid);
    }
}
