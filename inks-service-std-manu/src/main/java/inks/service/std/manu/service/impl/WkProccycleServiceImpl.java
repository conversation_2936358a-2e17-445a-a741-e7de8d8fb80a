package inks.service.std.manu.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.manu.domain.pojo.WkProccyclePojo;
import inks.service.std.manu.domain.WkProccycleEntity;
import inks.service.std.manu.mapper.WkProccycleMapper;
import inks.service.std.manu.service.WkProccycleService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 工序周期(WkProccycle)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-06 16:32:32
 */
@Service("wkProccycleService")
public class WkProccycleServiceImpl implements WkProccycleService {
    @Resource
    private WkProccycleMapper wkProccycleMapper;

    @Override
    public WkProccyclePojo getEntity(String key, String tid) {
        return this.wkProccycleMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<WkProccyclePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProccyclePojo> lst = wkProccycleMapper.getPageList(queryParam);
            PageInfo<WkProccyclePojo> pageInfo = new PageInfo<WkProccyclePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public WkProccyclePojo insert(WkProccyclePojo wkProccyclePojo) {
        //初始化NULL字段
        cleanNull(wkProccyclePojo);
        WkProccycleEntity wkProccycleEntity = new WkProccycleEntity(); 
        BeanUtils.copyProperties(wkProccyclePojo,wkProccycleEntity);
          //生成雪花id
          wkProccycleEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          wkProccycleEntity.setRevision(1);  //乐观锁
          this.wkProccycleMapper.insert(wkProccycleEntity);
        return this.getEntity(wkProccycleEntity.getId(),wkProccycleEntity.getTenantid());
    }


    @Override
    public WkProccyclePojo update(WkProccyclePojo wkProccyclePojo) {
        WkProccycleEntity wkProccycleEntity = new WkProccycleEntity(); 
        BeanUtils.copyProperties(wkProccyclePojo,wkProccycleEntity);
        this.wkProccycleMapper.update(wkProccycleEntity);
        return this.getEntity(wkProccycleEntity.getId(),wkProccycleEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.wkProccycleMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(WkProccyclePojo wkProccyclePojo) {
        if(wkProccyclePojo.getWpid()==null) wkProccyclePojo.setWpid("");
        if(wkProccyclePojo.getWpcode()==null) wkProccyclePojo.setWpcode("");
        if(wkProccyclePojo.getWpname()==null) wkProccyclePojo.setWpname("");
        if(wkProccyclePojo.getCycletype()==null) wkProccyclePojo.setCycletype(0);
        if(wkProccyclePojo.getCycleunit()==null) wkProccyclePojo.setCycleunit(0);
        if(wkProccyclePojo.getCyclevalue()==null) wkProccyclePojo.setCyclevalue(0);
        if(wkProccyclePojo.getLefttole()==null) wkProccyclePojo.setLefttole(0);
        if(wkProccyclePojo.getRighttole()==null) wkProccyclePojo.setRighttole(0);
        if(wkProccyclePojo.getRownum()==null) wkProccyclePojo.setRownum(0);
        if(wkProccyclePojo.getRemark()==null) wkProccyclePojo.setRemark("");
        if(wkProccyclePojo.getCreateby()==null) wkProccyclePojo.setCreateby("");
        if(wkProccyclePojo.getCreatebyid()==null) wkProccyclePojo.setCreatebyid("");
        if(wkProccyclePojo.getCreatedate()==null) wkProccyclePojo.setCreatedate(new Date());
        if(wkProccyclePojo.getLister()==null) wkProccyclePojo.setLister("");
        if(wkProccyclePojo.getListerid()==null) wkProccyclePojo.setListerid("");
        if(wkProccyclePojo.getModifydate()==null) wkProccyclePojo.setModifydate(new Date());
        if(wkProccyclePojo.getCustom1()==null) wkProccyclePojo.setCustom1("");
        if(wkProccyclePojo.getCustom2()==null) wkProccyclePojo.setCustom2("");
        if(wkProccyclePojo.getCustom3()==null) wkProccyclePojo.setCustom3("");
        if(wkProccyclePojo.getCustom4()==null) wkProccyclePojo.setCustom4("");
        if(wkProccyclePojo.getCustom5()==null) wkProccyclePojo.setCustom5("");
        if(wkProccyclePojo.getCustom6()==null) wkProccyclePojo.setCustom6("");
        if(wkProccyclePojo.getCustom7()==null) wkProccyclePojo.setCustom7("");
        if(wkProccyclePojo.getCustom8()==null) wkProccyclePojo.setCustom8("");
        if(wkProccyclePojo.getCustom9()==null) wkProccyclePojo.setCustom9("");
        if(wkProccyclePojo.getCustom10()==null) wkProccyclePojo.setCustom10("");
        if(wkProccyclePojo.getTenantid()==null) wkProccyclePojo.setTenantid("");
        if(wkProccyclePojo.getTenantname()==null) wkProccyclePojo.setTenantname("");
        if(wkProccyclePojo.getRevision()==null) wkProccyclePojo.setRevision(0);
   }

    /**
     * @return WkProccyclePojo
     * @Description 通过工序id和周期类型查询工序周期
     * <AUTHOR>
     * @param[1] wpid 工序id
     * @param[2] type 周期类型 0全程 1前置 2后置
     * @time 2023/7/20 13:14
     */
    @Override
    public WkProccyclePojo getEntityByWpidAndType(String wpid, int type, String tid) {
        return this.wkProccycleMapper.getEntityByWpidAndType(wpid, type, tid);
    }
}
