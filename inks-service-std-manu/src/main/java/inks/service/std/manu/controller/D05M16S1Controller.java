package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkProcessPojo;
import inks.service.std.manu.domain.pojo.WkSteppricesetPojo;
import inks.service.std.manu.mapper.WkProcessMapper;
import inks.service.std.manu.service.WkSteppricesetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 阶梯工价设置(Wk_StepPriceSet)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-26 16:21:54
 */
@RestController
@RequestMapping("D05M16S1")
@Api(tags = "D05M16S1:阶梯工价设置")
public class D05M16S1Controller extends WkSteppricesetController {
    @Resource
    private WkSteppricesetService wkSteppricesetService;
    @Resource
    private TokenService tokenService;
    @Resource
    private WkProcessMapper wkProcessMapper;

    @ApiOperation(value = " 获取工序下唯一Spus", notes = "获取工序下唯一Spus", produces = "application/json")
    @RequestMapping(value = "/getSpusByWpid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_StepPriceSet.List")
    public R<String> getSpusByWpid(String wpid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkSteppricesetService.getSpusByWpid(wpid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "保存工序下唯一Spus", notes = "保存工序下唯一Spus", produces = "application/json")
    @RequestMapping(value = "/saveSpus", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_StepPriceSet.Add")
    public R<WkSteppricesetPojo> saveSpus(@RequestBody String json, String wpid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tenantid = loginUser.getTenantid();
            String spus = JSONArray.parseObject(json, String.class);
            WkSteppricesetPojo spusPojoDB = this.wkSteppricesetService.getSpusEntityByWpid(wpid, tenantid);
            if (spusPojoDB == null) {
                WkSteppricesetPojo wkSteppricesetPojo = new WkSteppricesetPojo();
                wkSteppricesetPojo.setSpus(spus);
                wkSteppricesetPojo.setWpid(wpid);
                WkProcessPojo wkProcessPojo = wkProcessMapper.getEntity(wpid, tenantid);
                wkSteppricesetPojo.setWpname(wkProcessPojo.getWpname());
                wkSteppricesetPojo.setWpcode(wkProcessPojo.getWpcode());
                wkSteppricesetPojo.setCreateby(loginUser.getRealName());   // 创建者
                wkSteppricesetPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                wkSteppricesetPojo.setCreatedate(new Date());   // 创建时间
                wkSteppricesetPojo.setLister(loginUser.getRealname());   // 制表
                wkSteppricesetPojo.setListerid(loginUser.getUserid());    // 制表id
                wkSteppricesetPojo.setModifydate(new Date());   //修改时间
                wkSteppricesetPojo.setTenantid(tenantid);   //租户id
                return R.ok(this.wkSteppricesetService.insert(wkSteppricesetPojo));
            } else {
                spusPojoDB.setSpus(spus);
                spusPojoDB.setModifydate(new Date());   //修改时间
                spusPojoDB.setLister(loginUser.getRealname());   // 制表
                spusPojoDB.setListerid(loginUser.getUserid());    // 制表id
                return R.ok(this.wkSteppricesetService.update(spusPojoDB));
            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取所有阶梯工价设置(工序id)", notes = "获取所有阶梯工价设置(工序id)", produces = "application/json")
    @RequestMapping(value = "/getAllList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_StepPriceSet.List")
    public R<List<WkSteppricesetPojo>> getAllList() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkSteppricesetService.getAllList(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
