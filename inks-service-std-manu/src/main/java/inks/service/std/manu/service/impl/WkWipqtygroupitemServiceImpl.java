package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipqtygroupitemEntity;
import inks.service.std.manu.domain.pojo.WkWipqtygroupitemPojo;
import inks.service.std.manu.mapper.WkWipqtygroupitemMapper;
import inks.service.std.manu.service.WkWipqtygroupitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 过数小组人员(WkWipqtygroupitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-10 11:08:19
 */
@Service("wkWipqtygroupitemService")
public class WkWipqtygroupitemServiceImpl implements WkWipqtygroupitemService {
    @Resource
    private WkWipqtygroupitemMapper wkWipqtygroupitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipqtygroupitemPojo getEntity(String key, String tid) {
        return this.wkWipqtygroupitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipqtygroupitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtygroupitemPojo> lst = wkWipqtygroupitemMapper.getPageList(queryParam);
            PageInfo<WkWipqtygroupitemPojo> pageInfo = new PageInfo<WkWipqtygroupitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkWipqtygroupitemPojo> getList(String Pid, String tid) {
        try {
            List<WkWipqtygroupitemPojo> lst = wkWipqtygroupitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkWipqtygroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipqtygroupitemPojo insert(WkWipqtygroupitemPojo wkWipqtygroupitemPojo) {
        //初始化item的NULL
        WkWipqtygroupitemPojo itempojo = this.clearNull(wkWipqtygroupitemPojo);
        WkWipqtygroupitemEntity wkWipqtygroupitemEntity = new WkWipqtygroupitemEntity();
        BeanUtils.copyProperties(itempojo, wkWipqtygroupitemEntity);
        //生成雪花id
        wkWipqtygroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipqtygroupitemEntity.setRevision(1);  //乐观锁
        this.wkWipqtygroupitemMapper.insert(wkWipqtygroupitemEntity);
        return this.getEntity(wkWipqtygroupitemEntity.getId(), wkWipqtygroupitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipqtygroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipqtygroupitemPojo update(WkWipqtygroupitemPojo wkWipqtygroupitemPojo) {
        WkWipqtygroupitemEntity wkWipqtygroupitemEntity = new WkWipqtygroupitemEntity();
        BeanUtils.copyProperties(wkWipqtygroupitemPojo, wkWipqtygroupitemEntity);
        this.wkWipqtygroupitemMapper.update(wkWipqtygroupitemEntity);
        return this.getEntity(wkWipqtygroupitemEntity.getId(), wkWipqtygroupitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWipqtygroupitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkWipqtygroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipqtygroupitemPojo clearNull(WkWipqtygroupitemPojo wkWipqtygroupitemPojo) {
        //初始化NULL字段
        if (wkWipqtygroupitemPojo.getPid() == null) wkWipqtygroupitemPojo.setPid("");
        if (wkWipqtygroupitemPojo.getUserid() == null) wkWipqtygroupitemPojo.setUserid("");
        if (wkWipqtygroupitemPojo.getUsername() == null) wkWipqtygroupitemPojo.setUsername("");
        if (wkWipqtygroupitemPojo.getRealname() == null) wkWipqtygroupitemPojo.setRealname("");
        if (wkWipqtygroupitemPojo.getRownum() == null) wkWipqtygroupitemPojo.setRownum(0);
        if (wkWipqtygroupitemPojo.getCustom1() == null) wkWipqtygroupitemPojo.setCustom1("");
        if (wkWipqtygroupitemPojo.getCustom2() == null) wkWipqtygroupitemPojo.setCustom2("");
        if (wkWipqtygroupitemPojo.getCustom3() == null) wkWipqtygroupitemPojo.setCustom3("");
        if (wkWipqtygroupitemPojo.getCustom4() == null) wkWipqtygroupitemPojo.setCustom4("");
        if (wkWipqtygroupitemPojo.getCustom5() == null) wkWipqtygroupitemPojo.setCustom5("");
        if (wkWipqtygroupitemPojo.getCustom6() == null) wkWipqtygroupitemPojo.setCustom6("");
        if (wkWipqtygroupitemPojo.getCustom7() == null) wkWipqtygroupitemPojo.setCustom7("");
        if (wkWipqtygroupitemPojo.getCustom8() == null) wkWipqtygroupitemPojo.setCustom8("");
        if (wkWipqtygroupitemPojo.getCustom9() == null) wkWipqtygroupitemPojo.setCustom9("");
        if (wkWipqtygroupitemPojo.getCustom10() == null) wkWipqtygroupitemPojo.setCustom10("");
        if (wkWipqtygroupitemPojo.getTenantid() == null) wkWipqtygroupitemPojo.setTenantid("");
        if (wkWipqtygroupitemPojo.getRevision() == null) wkWipqtygroupitemPojo.setRevision(0);
        return wkWipqtygroupitemPojo;
    }
}
