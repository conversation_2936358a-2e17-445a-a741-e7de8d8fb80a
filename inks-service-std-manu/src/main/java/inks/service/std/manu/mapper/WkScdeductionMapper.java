package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkScdeductionEntity;
import inks.service.std.manu.domain.pojo.WkScdeductionPojo;
import inks.service.std.manu.domain.pojo.WkScdeductionitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 委制扣款(WkScdeduction)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-12 08:46:28
 */
@Mapper
public interface WkScdeductionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkScdeductionPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkScdeductionitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkScdeductionPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param wkScdeductionEntity 实例对象
     * @return 影响行数
     */
    int insert(WkScdeductionEntity wkScdeductionEntity);

    
    /**
     * 修改数据
     *
     * @param wkScdeductionEntity 实例对象
     * @return 影响行数
     */
    int update(WkScdeductionEntity wkScdeductionEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param wkScdeductionPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(WkScdeductionPojo wkScdeductionPojo);
    /**
     * 修改数据
     *
     * @param wkScdeductionEntity 实例对象
     * @return 影响行数
     */
    int approval(WkScdeductionEntity wkScdeductionEntity);
}

