package inks.service.std.manu.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.manu.domain.pojo.WkWipfinishitemPojo;
import inks.service.std.manu.domain.WkWipfinishitemEntity;
import inks.service.std.manu.mapper.WkWipfinishitemMapper;
import inks.service.std.manu.service.WkWipfinishitemService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 完工单子表(WkWipfinishitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-06 14:34:14
 */
@Service("wkWipfinishitemService")
public class WkWipfinishitemServiceImpl implements WkWipfinishitemService {
    @Resource
    private WkWipfinishitemMapper wkWipfinishitemMapper;

    @Override
    public WkWipfinishitemPojo getEntity(String key,String tid) {
        return this.wkWipfinishitemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<WkWipfinishitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipfinishitemPojo> lst = wkWipfinishitemMapper.getPageList(queryParam);
            PageInfo<WkWipfinishitemPojo> pageInfo = new PageInfo<WkWipfinishitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<WkWipfinishitemPojo> getList(String Pid,String tid) { 
        try {
            List<WkWipfinishitemPojo> lst = wkWipfinishitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public WkWipfinishitemPojo insert(WkWipfinishitemPojo wkWipfinishitemPojo) {
        //初始化item的NULL
        WkWipfinishitemPojo itempojo =this.clearNull(wkWipfinishitemPojo);
        WkWipfinishitemEntity wkWipfinishitemEntity = new WkWipfinishitemEntity(); 
        BeanUtils.copyProperties(itempojo,wkWipfinishitemEntity);
          //生成雪花id
          wkWipfinishitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          wkWipfinishitemEntity.setRevision(1);  //乐观锁      
          this.wkWipfinishitemMapper.insert(wkWipfinishitemEntity);
        return this.getEntity(wkWipfinishitemEntity.getId(),wkWipfinishitemEntity.getTenantid());
  
    }

    @Override
    public WkWipfinishitemPojo update(WkWipfinishitemPojo wkWipfinishitemPojo) {
        WkWipfinishitemEntity wkWipfinishitemEntity = new WkWipfinishitemEntity(); 
        BeanUtils.copyProperties(wkWipfinishitemPojo,wkWipfinishitemEntity);
        this.wkWipfinishitemMapper.update(wkWipfinishitemEntity);
        return this.getEntity(wkWipfinishitemEntity.getId(),wkWipfinishitemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.wkWipfinishitemMapper.delete(key,tid) ;
    }

     @Override
     public WkWipfinishitemPojo clearNull(WkWipfinishitemPojo wkWipfinishitemPojo){
     //初始化NULL字段
     if(wkWipfinishitemPojo.getPid()==null) wkWipfinishitemPojo.setPid("");
     if(wkWipfinishitemPojo.getWipitemid()==null) wkWipfinishitemPojo.setWipitemid("");
     if(wkWipfinishitemPojo.getGoodsid()==null) wkWipfinishitemPojo.setGoodsid("");
     if(wkWipfinishitemPojo.getItemcode()==null) wkWipfinishitemPojo.setItemcode("");
     if(wkWipfinishitemPojo.getItemname()==null) wkWipfinishitemPojo.setItemname("");
     if(wkWipfinishitemPojo.getItemspec()==null) wkWipfinishitemPojo.setItemspec("");
     if(wkWipfinishitemPojo.getItemunit()==null) wkWipfinishitemPojo.setItemunit("");
     if(wkWipfinishitemPojo.getItemlabel()==null) wkWipfinishitemPojo.setItemlabel("");
     if(wkWipfinishitemPojo.getMoldid()==null) wkWipfinishitemPojo.setMoldid("");
     if(wkWipfinishitemPojo.getMoldcode()==null) wkWipfinishitemPojo.setMoldcode("");
     if(wkWipfinishitemPojo.getMoldname()==null) wkWipfinishitemPojo.setMoldname("");
     if(wkWipfinishitemPojo.getWorktype()==null) wkWipfinishitemPojo.setWorktype("");
     if(wkWipfinishitemPojo.getWorkshopid()==null) wkWipfinishitemPojo.setWorkshopid("");
     if(wkWipfinishitemPojo.getWorkshop()==null) wkWipfinishitemPojo.setWorkshop("");
     if(wkWipfinishitemPojo.getQuantity()==null) wkWipfinishitemPojo.setQuantity(0D);
     if(wkWipfinishitemPojo.getWkpcsqty()==null) wkWipfinishitemPojo.setWkpcsqty(0D);
     if(wkWipfinishitemPojo.getWksecqty()==null) wkWipfinishitemPojo.setWksecqty(0D);
     if(wkWipfinishitemPojo.getMrbpcsqty()==null) wkWipfinishitemPojo.setMrbpcsqty(0D);
     if(wkWipfinishitemPojo.getMrbsecqty()==null) wkWipfinishitemPojo.setMrbsecqty(0D);
     if(wkWipfinishitemPojo.getFinishqty()==null) wkWipfinishitemPojo.setFinishqty(0D);
     if(wkWipfinishitemPojo.getCustomer()==null) wkWipfinishitemPojo.setCustomer("");
     if(wkWipfinishitemPojo.getCustpo()==null) wkWipfinishitemPojo.setCustpo("");
     if(wkWipfinishitemPojo.getMachuid()==null) wkWipfinishitemPojo.setMachuid("");
     if(wkWipfinishitemPojo.getMachitemid()==null) wkWipfinishitemPojo.setMachitemid("");
     if(wkWipfinishitemPojo.getMachgroupid()==null) wkWipfinishitemPojo.setMachgroupid("");
     if(wkWipfinishitemPojo.getMainplanuid()==null) wkWipfinishitemPojo.setMainplanuid("");
     if(wkWipfinishitemPojo.getMainplanitemid()==null) wkWipfinishitemPojo.setMainplanitemid("");
     if(wkWipfinishitemPojo.getWorkuid()==null) wkWipfinishitemPojo.setWorkuid("");
     if(wkWipfinishitemPojo.getWorkitemid()==null) wkWipfinishitemPojo.setWorkitemid("");
     if(wkWipfinishitemPojo.getTasksuid()==null) wkWipfinishitemPojo.setTasksuid("");
     if(wkWipfinishitemPojo.getTasksitemid()==null) wkWipfinishitemPojo.setTasksitemid("");
     if(wkWipfinishitemPojo.getWorkdate()==null) wkWipfinishitemPojo.setWorkdate(new Date());
     if(wkWipfinishitemPojo.getRemark()==null) wkWipfinishitemPojo.setRemark("");
     if(wkWipfinishitemPojo.getAttributejson()==null) wkWipfinishitemPojo.setAttributejson("");
     if(wkWipfinishitemPojo.getAttributestr()==null) wkWipfinishitemPojo.setAttributestr("");
     if(wkWipfinishitemPojo.getClosed()==null) wkWipfinishitemPojo.setClosed(0);
     if(wkWipfinishitemPojo.getSourcetype()==null) wkWipfinishitemPojo.setSourcetype(0);
     if(wkWipfinishitemPojo.getRownum()==null) wkWipfinishitemPojo.setRownum(0);
     if(wkWipfinishitemPojo.getCustom1()==null) wkWipfinishitemPojo.setCustom1("");
     if(wkWipfinishitemPojo.getCustom2()==null) wkWipfinishitemPojo.setCustom2("");
     if(wkWipfinishitemPojo.getCustom3()==null) wkWipfinishitemPojo.setCustom3("");
     if(wkWipfinishitemPojo.getCustom4()==null) wkWipfinishitemPojo.setCustom4("");
     if(wkWipfinishitemPojo.getCustom5()==null) wkWipfinishitemPojo.setCustom5("");
     if(wkWipfinishitemPojo.getCustom6()==null) wkWipfinishitemPojo.setCustom6("");
     if(wkWipfinishitemPojo.getCustom7()==null) wkWipfinishitemPojo.setCustom7("");
     if(wkWipfinishitemPojo.getCustom8()==null) wkWipfinishitemPojo.setCustom8("");
     if(wkWipfinishitemPojo.getCustom9()==null) wkWipfinishitemPojo.setCustom9("");
     if(wkWipfinishitemPojo.getCustom10()==null) wkWipfinishitemPojo.setCustom10("");
     if(wkWipfinishitemPojo.getTenantid()==null) wkWipfinishitemPojo.setTenantid("");
     if(wkWipfinishitemPojo.getRevision()==null) wkWipfinishitemPojo.setRevision(0);
     return wkWipfinishitemPojo;
     }
}
