package inks.service.std.manu.service.impl;

import org.junit.Test;
import java.util.*;

public class A_WkMrpitemTest {

    public class WkMrpitemPojo {
        private String goodsid;
        private Double grossqty;
        private Integer mergemark;

        public WkMrpitemPojo(String goodsid, Double grossqty) {
            this.goodsid = goodsid;
            this.grossqty = grossqty;
            this.mergemark = 0; // 初始值为 0
        }

        public String getGoodsid() {
            return goodsid;
        }

        public Double getGrossqty() {
            return grossqty;
        }

        public void setGrossqty(Double grossqty) {
            this.grossqty = grossqty;
        }

        public Integer getMergemark() {
            return mergemark;
        }

        public void setMergemark(Integer mergemark) {
            this.mergemark = mergemark;
        }

        @Override
        public String toString() {
            return "WkMrpitemPojo{" +
                    "goodsid='" + goodsid + '\'' +
                    ", grossqty=" + grossqty +
                    ", mergemark=" + mergemark +
                    '}';
        }
    }

    @Test
    public void testMergeGoodsid() {
        // 模拟数据
        List<WkMrpitemPojo> list = Arrays.asList(
                new WkMrpitemPojo("A", 10.0),
                new WkMrpitemPojo("A", 10.0),
                new WkMrpitemPojo("B", 100.0),
                new WkMrpitemPojo("B", 200.0),
                new WkMrpitemPojo("B", 300.0),
                new WkMrpitemPojo("D", 1000.0)
        );

        // 执行合并逻辑
        mergeItems(list);

        // 输出结果
        list.forEach(System.out::println);
    }

    private void mergeItems(List<WkMrpitemPojo> list) {
        // 检查 goodsid 是否有重复
        Set<String> uniqueGoodsIds = new HashSet<>();
        Set<String> duplicateGoodsIds = new HashSet<>();
        for (WkMrpitemPojo item : list) {
            String goodsid = item.getGoodsid();
            if (!uniqueGoodsIds.add(goodsid)) {
                duplicateGoodsIds.add(goodsid);
            }
        }

        // 对于重复的 goodsid 进行合并
        Map<String, WkMrpitemPojo> mergedMap = new HashMap<>();
        for (WkMrpitemPojo item : list) {
            String goodsid = item.getGoodsid();
            if (duplicateGoodsIds.contains(goodsid)) {
                if (mergedMap.containsKey(goodsid)) {
                    // 如果 goodsid 已存在，说明需要合并
                    WkMrpitemPojo mergedItem = mergedMap.get(goodsid);

                    // 先累加 GrossQty
                    mergedItem.setGrossqty(mergedItem.getGrossqty() + item.getGrossqty());

                    // 再设置被合并的行的标志
                    item.setMergemark(1); // 被合并的行 MergeMark 置为 1
                    item.setGrossqty(0D); // 被合并的行 GrossQty 置 0
                } else {
                    // 第一次遇到该 goodsid 的行
                    item.setMergemark(2); // 合并为的行 MergeMark 置为 2
                    mergedMap.put(goodsid, item); // 保存到合并 Map 中
                }
            }
        }
    }

}
