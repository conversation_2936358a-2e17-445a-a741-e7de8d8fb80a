package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSccompleteEntity;
import inks.service.std.manu.domain.WkSccompleteitemEntity;
import inks.service.std.manu.domain.WkSccompletematEntity;
import inks.service.std.manu.domain.pojo.WkSccompletePojo;
import inks.service.std.manu.domain.pojo.WkSccompleteitemPojo;
import inks.service.std.manu.domain.pojo.WkSccompleteitemdetailPojo;
import inks.service.std.manu.domain.pojo.WkSccompletematPojo;
import inks.service.std.manu.mapper.*;
import inks.service.std.manu.service.WkSccompleteService;
import inks.service.std.manu.service.WkSccompleteitemService;
import inks.service.std.manu.service.WkSccompletematService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 委制验收(WkSccomplete)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-09 12:50:11
 */
@Service("wkSccompleteService")
public class WkSccompleteServiceImpl implements WkSccompleteService {
    @Resource
    private WkSccompleteMapper wkSccompleteMapper;

    @Resource
    private WkSccompleteitemMapper wkSccompleteitemMapper;
    @Resource
    private WkSccompletematMapper wkSccompletematMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private WkSccompleteitemService wkSccompleteitemService;
    @Resource
    private WkSccompletematService wkSccompletematService;
    @Resource
    private WkSubcontractitemMapper wkSubcontractitemMapper;
    @Resource
    private manu_SyncMapper manuSyncMapper;

    @Resource
    private WkMrpMapper wkMrpMapper;

    private static void calculateAmount(List<WkSccompleteitemPojo> lst, WkSccompleteEntity wkSccompleteEntity) {
        // 设置主表.未税金额,税额,含税金额=子表累加
        double billTaxAmount = 0.0;
        double billAmount = 0.0;
        double billTaxTotal = 0.0;
        for (WkSccompleteitemPojo item : lst) {
            billTaxAmount += Optional.ofNullable(item.getTaxamount()).orElse(0.0);
            billAmount += Optional.ofNullable(item.getAmount()).orElse(0.0);
            billTaxTotal += Optional.ofNullable(item.getTaxtotal()).orElse(0.0);
        }
        wkSccompleteEntity.setBilltaxamount(billTaxAmount);
        wkSccompleteEntity.setBillamount(billAmount);
        wkSccompleteEntity.setBilltaxtotal(billTaxTotal);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSccompletePojo getEntity(String key, String tid) {
        return this.wkSccompleteMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSccompleteitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSccompleteitemdetailPojo> lst = wkSccompleteMapper.getPageList(queryParam);
            PageInfo<WkSccompleteitemdetailPojo> pageInfo = new PageInfo<WkSccompleteitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSccompletePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkSccompletePojo wkSccompletePojo = this.wkSccompleteMapper.getEntity(key, tid);
            //读取子表
            wkSccompletePojo.setItem(wkSccompleteitemMapper.getList(wkSccompletePojo.getId(), wkSccompletePojo.getTenantid()));
            //读取mat子表
            wkSccompletePojo.setMat(wkSccompletematMapper.getList(wkSccompletePojo.getId(), wkSccompletePojo.getTenantid()));
            return wkSccompletePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSccompletePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSccompletePojo> lst = wkSccompleteMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表,mat子表
            for (WkSccompletePojo wkSccompletePojo : lst) {
                wkSccompletePojo.setItem(wkSccompleteitemMapper.getList(wkSccompletePojo.getId(), wkSccompletePojo.getTenantid()));
                wkSccompletePojo.setMat(wkSccompletematMapper.getList(wkSccompletePojo.getId(), wkSccompletePojo.getTenantid()));
            }
            PageInfo<WkSccompletePojo> pageInfo = new PageInfo<WkSccompletePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSccompletePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSccompletePojo> lst = wkSccompleteMapper.getPageTh(queryParam);
            PageInfo<WkSccompletePojo> pageInfo = new PageInfo<WkSccompletePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkSccompletePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSccompletePojo insert(WkSccompletePojo wkSccompletePojo) {
        String tid = wkSccompletePojo.getTenantid();
//初始化NULL字段
        if (wkSccompletePojo.getRefno() == null) wkSccompletePojo.setRefno("");
        if (wkSccompletePojo.getBilltype() == null) wkSccompletePojo.setBilltype("");
        if (wkSccompletePojo.getBilldate() == null) wkSccompletePojo.setBilldate(new Date());
        if (wkSccompletePojo.getBilltitle() == null) wkSccompletePojo.setBilltitle("");
        if (wkSccompletePojo.getOperator() == null) wkSccompletePojo.setOperator("");
        if (wkSccompletePojo.getGroupid() == null) wkSccompletePojo.setGroupid("");
        if (wkSccompletePojo.getSummary() == null) wkSccompletePojo.setSummary("");
        if (wkSccompletePojo.getCreateby() == null) wkSccompletePojo.setCreateby("");
        if (wkSccompletePojo.getCreatebyid() == null) wkSccompletePojo.setCreatebyid("");
        if (wkSccompletePojo.getCreatedate() == null) wkSccompletePojo.setCreatedate(new Date());
        if (wkSccompletePojo.getLister() == null) wkSccompletePojo.setLister("");
        if (wkSccompletePojo.getListerid() == null) wkSccompletePojo.setListerid("");
        if (wkSccompletePojo.getModifydate() == null) wkSccompletePojo.setModifydate(new Date());
        if (wkSccompletePojo.getAssessor() == null) wkSccompletePojo.setAssessor("");
        if (wkSccompletePojo.getAssessorid() == null) wkSccompletePojo.setAssessorid("");
        if (wkSccompletePojo.getAssessdate() == null) wkSccompletePojo.setAssessdate(new Date());
        if (wkSccompletePojo.getItemcount() == null) wkSccompletePojo.setItemcount(0);
        if (wkSccompletePojo.getDisannulcount() == null) wkSccompletePojo.setDisannulcount(0);
        if (wkSccompletePojo.getFinishcount() == null) wkSccompletePojo.setFinishcount(0);
        if (wkSccompletePojo.getPrintcount() == null) wkSccompletePojo.setPrintcount(0);
        if (wkSccompletePojo.getCustom1() == null) wkSccompletePojo.setCustom1("");
        if (wkSccompletePojo.getBillstatecode() == null) wkSccompletePojo.setBillstatecode("");
        if (wkSccompletePojo.getBillstatedate() == null) wkSccompletePojo.setBillstatedate(new Date());
        if (wkSccompletePojo.getBilltaxamount() == null) wkSccompletePojo.setBilltaxamount(0D);
        if (wkSccompletePojo.getBillamount() == null) wkSccompletePojo.setBillamount(0D);
        if (wkSccompletePojo.getBilltaxtotal() == null) wkSccompletePojo.setBilltaxtotal(0D);
        if (wkSccompletePojo.getBillpaid() == null) wkSccompletePojo.setBillpaid(0D);
        if (wkSccompletePojo.getCustom2() == null) wkSccompletePojo.setCustom2("");
        if (wkSccompletePojo.getCustom3() == null) wkSccompletePojo.setCustom3("");
        if (wkSccompletePojo.getCustom4() == null) wkSccompletePojo.setCustom4("");
        if (wkSccompletePojo.getCustom5() == null) wkSccompletePojo.setCustom5("");
        if (wkSccompletePojo.getCustom6() == null) wkSccompletePojo.setCustom6("");
        if (wkSccompletePojo.getCustom7() == null) wkSccompletePojo.setCustom7("");
        if (wkSccompletePojo.getCustom8() == null) wkSccompletePojo.setCustom8("");
        if (wkSccompletePojo.getCustom9() == null) wkSccompletePojo.setCustom9("");
        if (wkSccompletePojo.getCustom10() == null) wkSccompletePojo.setCustom10("");
        if (tid == null) wkSccompletePojo.setTenantid("");
        if (wkSccompletePojo.getTenantname() == null) wkSccompletePojo.setTenantname("");
        if (wkSccompletePojo.getRevision() == null) wkSccompletePojo.setRevision(0);
        // 设置itemcount
        wkSccompletePojo.setItemcount(wkSccompletePojo.getItem().size());
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkSccompleteEntity wkSccompleteEntity = new WkSccompleteEntity();
        BeanUtils.copyProperties(wkSccompletePojo, wkSccompleteEntity);

        //设置id和新建日期
        wkSccompleteEntity.setId(id);
        wkSccompleteEntity.setRevision(1);  //乐观锁
        //Item子表处理
        List<WkSccompleteitemPojo> lst = wkSccompletePojo.getItem();
        if (lst == null) throw new RuntimeException("单据内容不能为空");
        // 设置主表.未税金额,税额,含税金额=子表累加
        calculateAmount(lst, wkSccompleteEntity);
        //插入主表
        this.wkSccompleteMapper.insert(wkSccompleteEntity);

        //循环每个item子表
        for (WkSccompleteitemPojo wkSccompleteitemPojo : lst) {
            //初始化item的NULL
            WkSccompleteitemPojo itemPojo = this.wkSccompleteitemService.clearNull(wkSccompleteitemPojo);
            WkSccompleteitemEntity wkSccompleteitemEntity = new WkSccompleteitemEntity();
            BeanUtils.copyProperties(itemPojo, wkSccompleteitemEntity);
            //设置id和Pid
            wkSccompleteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkSccompleteitemEntity.setPid(id);
            wkSccompleteitemEntity.setTenantid(tid);
            wkSccompleteitemEntity.setRevision(1);  //乐观锁
            //插入子表
            this.wkSccompleteitemMapper.insert(wkSccompleteitemEntity);
            // 同步Wk_MrpItem.WkFinishQty
            String mrpitemid = wkSccompleteitemEntity.getMrpitemid();
            if (StringUtils.isNotBlank(mrpitemid)) {
                this.wkMrpMapper.syncMrpItemWkFinishQty(mrpitemid, tid);
            }
        }
        //mat子表处理
        List<WkSccompletematPojo> lstMat = wkSccompletePojo.getMat();
        if (lstMat != null) {
            //循环每个mat子表
            for (WkSccompletematPojo wkSccompletematPojo : lstMat) {
                //初始化mat的NULL
                WkSccompletematPojo matPojo = this.wkSccompletematService.clearNull(wkSccompletematPojo);
                WkSccompletematEntity wkSccompletematEntity = new WkSccompletematEntity();
                BeanUtils.copyProperties(matPojo, wkSccompletematEntity);
                //设置id和Pid
                wkSccompletematEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkSccompletematEntity.setPid(id);
                wkSccompletematEntity.setTenantid(tid);
                wkSccompletematEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkSccompletematMapper.insert(wkSccompletematEntity);
            }
        }

        for (WkSccompleteitemPojo wkSccompleteitemPojo : lst) {
            // 同步委制单据子表Wk_SubcontractItem的验收数量CompQty   Citeitemid是委制单据子表id
            if ("委制验收".equals(wkSccompletePojo.getBilltype()) || "委制退货".equals(wkSccompletePojo.getBilltype())) {
                if (StringUtils.isNotBlank(wkSccompleteitemPojo.getCiteitemid())) {
                    // 同步验收数量
                    wkSccompleteitemMapper.updateCompQty(wkSccompleteitemPojo.getCiteitemid(), tid);
                    // 同步完工数量 照抄store服务的matAccessCiteMapper.updateWsAcceFinish方法
                    wkSccompleteitemMapper.updateWsAcceFinish(wkSccompleteitemPojo.getCiteitemid(), wkSccompleteitemPojo.getCiteuid(), tid);
                    // 同步委制单加工单主表的完工款数FinishCount
                    wkSubcontractitemMapper.updateWkSubcontractFinishCount(wkSccompleteitemPojo.getCiteitemid(), tid);
                }
            }
        }
        // 更新本主表的完工款数
        this.wkSccompleteMapper.updateFinishCount(wkSccompleteEntity.getId(), tid);

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(WkSccompleteitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkScRemQty(goodsid, tid);
        });

        //返回Bill实例
        return this.getBillEntity(wkSccompleteEntity.getId(), wkSccompleteEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSccompletePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSccompletePojo update(WkSccompletePojo wkSccompletePojo) {
        String tid = wkSccompletePojo.getTenantid();
        // 设置itemcount
        wkSccompletePojo.setItemcount(wkSccompletePojo.getItem().size());
        //主表更改
        WkSccompleteEntity wkSccompleteEntity = new WkSccompleteEntity();
        BeanUtils.copyProperties(wkSccompletePojo, wkSccompleteEntity);
        List<WkSccompleteitemPojo> lst = wkSccompletePojo.getItem();
        if (lst == null) throw new RuntimeException("单据内容不能为空");
        // 设置主表.未税金额,税额,含税金额=子表累加
        calculateAmount(lst, wkSccompleteEntity);
        this.wkSccompleteMapper.update(wkSccompleteEntity);
        //Item子表处理
        //获取被删除的Item
        List<String> lstDelIds = wkSccompleteMapper.getDelItemIds(wkSccompletePojo);
        // 所有去重的Goodsid Set
        Set<String> goodsidLstDelSet = new HashSet<>();
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (String lstDelId : lstDelIds) {
                WkSccompleteitemPojo delPojoDB = wkSccompleteitemMapper.getEntity(lstDelId, wkSccompleteEntity.getTenantid());
                goodsidLstDelSet.add(delPojoDB.getGoodsid());
                this.wkSccompleteitemMapper.delete(lstDelId, wkSccompleteEntity.getTenantid());
                // 同步委制单据子表Wk_SubcontractItem的验收数量CompQty   Citeitemid是委制单据子表id
                wkSccompleteitemMapper.updateCompQty(delPojoDB.getCiteitemid(), tid);
                // 同步完工数量 照抄store服务的matAccessCiteMapper.updateWsAcceFinish方法
                wkSccompleteitemMapper.updateWsAcceFinish(delPojoDB.getCiteitemid(), delPojoDB.getCiteuid(), tid);
                // 同步委制单加工单主表的完工款数FinishCount
                wkSubcontractitemMapper.updateWkSubcontractFinishCount(delPojoDB.getCiteitemid(), tid);
                // 同步Wk_MrpItem.WkFinishQty
                String mrpitemid = delPojoDB.getMrpitemid();
                if (StringUtils.isNotBlank(mrpitemid)) {
                    this.wkMrpMapper.syncMrpItemWkFinishQty(mrpitemid, tid);
                }
            }
        }

        //循环每个item子表
        for (WkSccompleteitemPojo wkSccompleteitemPojo : lst) {
            WkSccompleteitemEntity wkSccompleteitemEntity = new WkSccompleteitemEntity();
            if ("".equals(wkSccompleteitemPojo.getId()) || wkSccompleteitemPojo.getId() == null) {
                //初始化item的NULL
                WkSccompleteitemPojo itemPojo = this.wkSccompleteitemService.clearNull(wkSccompleteitemPojo);
                BeanUtils.copyProperties(itemPojo, wkSccompleteitemEntity);
                //设置id和Pid
                wkSccompleteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                wkSccompleteitemEntity.setPid(wkSccompleteEntity.getId());  // 主表 id
                wkSccompleteitemEntity.setTenantid(tid);   // 租户id
                wkSccompleteitemEntity.setRevision(1);  // 乐观锁
                //插入子表
                this.wkSccompleteitemMapper.insert(wkSccompleteitemEntity);
            } else {
                BeanUtils.copyProperties(wkSccompleteitemPojo, wkSccompleteitemEntity);
                wkSccompleteitemEntity.setTenantid(tid);
                this.wkSccompleteitemMapper.update(wkSccompleteitemEntity);
            }
            // 同步Wk_MrpItem.WkFinishQty
            String mrpitemid = wkSccompleteitemEntity.getMrpitemid();
            if (StringUtils.isNotBlank(mrpitemid)) {
                this.wkMrpMapper.syncMrpItemWkFinishQty(mrpitemid, tid);
            }
        }

        for (WkSccompleteitemPojo wkSccompleteitemPojo : lst) {
            // 同步委制单据子表Wk_SubcontractItem的验收数量CompQty   Citeitemid是委制单据子表id
            if ("委制验收".equals(wkSccompletePojo.getBilltype()) || "委制退货".equals(wkSccompletePojo.getBilltype())) {
                if (StringUtils.isNotBlank(wkSccompleteitemPojo.getCiteitemid())) {
                    wkSccompleteitemMapper.updateCompQty(wkSccompleteitemPojo.getCiteitemid(), tid);
                    // 同步完工数量 照抄store服务的matAccessCiteMapper.updateWsAcceFinish方法
                    wkSccompleteitemMapper.updateWsAcceFinish(wkSccompleteitemPojo.getCiteitemid(), wkSccompleteitemPojo.getCiteuid(), tid);
                    // 同步委制单加工单主表的完工款数FinishCount
                    wkSubcontractitemMapper.updateWkSubcontractFinishCount(wkSccompleteitemPojo.getCiteitemid(), tid);
                }
            }
        }
        //mat子表处理
        if (wkSccompletePojo.getMat() != null) {
            List<WkSccompletematPojo> lstMat = wkSccompletePojo.getMat();
            //获取被删除的Item
            List<String> lstDelMatIds = wkSccompleteMapper.getDelMatIds(wkSccompletePojo);
            if (lstDelMatIds != null) {
                //循环每个删除item子表
                for (String lstDelMatId : lstDelMatIds) {
                    this.wkSccompletematMapper.delete(lstDelMatId, wkSccompleteEntity.getTenantid());
                }
            }
            if (lstMat != null) {
                //循环每个mat子表
                for (WkSccompletematPojo wkSccompletematPojo : lstMat) {
                    WkSccompletematEntity wkSccompletematEntity = new WkSccompletematEntity();
                    if ("".equals(wkSccompletematPojo.getId()) || wkSccompletematPojo.getId() == null) {
                        //初始化mat的NULL
                        WkSccompletematPojo matPojo = this.wkSccompletematService.clearNull(wkSccompletematPojo);
                        BeanUtils.copyProperties(matPojo, wkSccompletematEntity);
                        //设置id和Pid
                        wkSccompletematEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkSccompletematEntity.setPid(wkSccompleteEntity.getId());  // 主表 id
                        wkSccompletematEntity.setTenantid(wkSccompletePojo.getTenantid());   // 租户id
                        wkSccompletematEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkSccompletematMapper.insert(wkSccompletematEntity);
                    } else {
                        BeanUtils.copyProperties(wkSccompletematPojo, wkSccompletematEntity);
                        wkSccompletematEntity.setTenantid(wkSccompletePojo.getTenantid());
                        this.wkSccompletematMapper.update(wkSccompletematEntity);
                    }
                }
            }
        }
        // 更新本主表的完工款数
        this.wkSccompleteMapper.updateFinishCount(wkSccompleteEntity.getId(), tid);


        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(WkSccompleteitemPojo::getGoodsid).collect(Collectors.toSet());
        goodsidLstSet.addAll(goodsidLstDelSet);
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkScRemQty(goodsid, tid);
        });

        //返回Bill实例
        return this.getBillEntity(wkSccompleteEntity.getId(), wkSccompleteEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkSccompletePojo wkSccompletePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkSccompleteitemPojo> lst = wkSccompletePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkSccompleteitemPojo wkSccompleteitemPojo : lst) {
                this.wkSccompleteitemMapper.delete(wkSccompleteitemPojo.getId(), tid);
                if ("委制验收".equals(wkSccompletePojo.getBilltype()) || "委制退货".equals(wkSccompletePojo.getBilltype())) {
                    if (StringUtils.isNotBlank(wkSccompleteitemPojo.getCiteitemid())) {
                        // 同步委制单据子表Wk_SubcontractItem的验收数量CompQty   Citeitemid是委制单据子表id
                        wkSccompleteitemMapper.updateCompQty(wkSccompleteitemPojo.getCiteitemid(), tid);
                        // 同步完工数量 照抄store服务的matAccessCiteMapper.updateWsAcceFinish方法
                        wkSccompleteitemMapper.updateWsAcceFinish(wkSccompleteitemPojo.getCiteitemid(), wkSccompleteitemPojo.getCiteuid(), tid);
                        // 同步委制单加工单主表的完工款数FinishCount
                        wkSubcontractitemMapper.updateWkSubcontractFinishCount(wkSccompleteitemPojo.getCiteitemid(), tid);
                    }
                }
                // 同步Wk_MrpItem.WkFinishQty
                String mrpitemid = wkSccompleteitemPojo.getMrpitemid();
                if (StringUtils.isNotBlank(mrpitemid)) {
                    this.wkMrpMapper.syncMrpItemWkFinishQty(mrpitemid, tid);
                }
            }
        }
        //mat子表处理
        List<WkSccompletematPojo> lstMat = wkSccompletePojo.getMat();
        if (lstMat != null) {
            //循环每个删除item子表
            for (WkSccompletematPojo wkSccompletematPojo : lstMat) {
                this.wkSccompletematMapper.delete(wkSccompletematPojo.getId(), tid);
            }
        }
        this.wkSccompleteMapper.delete(key, tid);
        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(WkSccompleteitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            manuSyncMapper.updateGoodsWkScRemQty(goodsid, tid);
        });
        return wkSccompletePojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkSccompletePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkSccompletePojo approval(WkSccompletePojo wkSccompletePojo) {
        //主表更改
        WkSccompleteEntity wkSccompleteEntity = new WkSccompleteEntity();
        BeanUtils.copyProperties(wkSccompletePojo, wkSccompleteEntity);
        this.wkSccompleteMapper.approval(wkSccompleteEntity);
        //返回Bill实例
        return this.getBillEntity(wkSccompleteEntity.getId(), wkSccompleteEntity.getTenantid());
    }

    @Override
    public WkSccompletePojo disannul(List<WkSccompleteitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            WkSccompleteitemPojo Pojo = lst.get(i);
            WkSccompleteitemPojo dbPojo = this.wkSccompleteitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    WkSccompleteitemEntity entity = new WkSccompleteitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setTenantid(tid);
                    this.wkSccompleteitemMapper.update(entity);
                    disNum++;

                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }


        if (disNum > 0) {
            for (WkSccompleteitemPojo wkSccompleteitemPojo : lst) {
                // 同步委制单据子表Wk_SubcontractItem的验收数量CompQty   Citeitemid是委制单据子表id
                if (StringUtils.isNotBlank(wkSccompleteitemPojo.getCiteitemid())) {
                    // 同步验收数量
                    wkSccompleteitemMapper.updateCompQty(wkSccompleteitemPojo.getCiteitemid(), tid);
                    // 同步完工数量 照抄store服务的matAccessCiteMapper.updateWsAcceFinish方法
                    wkSccompleteitemMapper.updateWsAcceFinish(wkSccompleteitemPojo.getCiteitemid(), wkSccompleteitemPojo.getCiteuid(), tid);
                    // 同步委制单加工单主表的完工款数FinishCount
                    wkSubcontractitemMapper.updateWkSubcontractFinishCount(wkSccompleteitemPojo.getCiteitemid(), tid);
                }
            }

            // 收集去重的所有goodsid
            Set<String> goodsIdSetDelete = lst.stream()
                    .map(WkSccompleteitemPojo::getGoodsid)
                    .collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsIdSetDelete.forEach(goodsid -> {
                manuSyncMapper.updateGoodsWkScRemQty(goodsid, tid);
            });
            this.wkSccompleteMapper.updateDisannulCount(Pid, tid);
            //主表更改
            WkSccompleteEntity entity = new WkSccompleteEntity();
            entity.setId(Pid);
            entity.setLister(loginUser.getRealname());
            entity.setListerid(loginUser.getUserid());
            entity.setModifydate(new Date());
            entity.setTenantid(tid);
            this.wkSccompleteMapper.update(entity);
            //返回Bill实例
            return this.getBillEntity(entity.getId(), entity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    public WkSccompletePojo closed(List<WkSccompleteitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            WkSccompleteitemPojo Pojo = lst.get(i);
            WkSccompleteitemPojo dbPojo = this.wkSccompleteitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    WkSccompleteitemEntity entity = new WkSccompleteitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(tid);
                    this.wkSccompleteitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }


        if (disNum > 0) {
            for (WkSccompleteitemPojo wkSccompleteitemPojo : lst) {
                // 同步委制单据子表Wk_SubcontractItem的验收数量CompQty   Citeitemid是委制单据子表id
                if (StringUtils.isNotBlank(wkSccompleteitemPojo.getCiteitemid())) {
                    // 同步验收数量
                    wkSccompleteitemMapper.updateCompQty(wkSccompleteitemPojo.getCiteitemid(), tid);
                    // 同步完工数量 照抄store服务的matAccessCiteMapper.updateWsAcceFinish方法
                    wkSccompleteitemMapper.updateWsAcceFinish(wkSccompleteitemPojo.getCiteitemid(), wkSccompleteitemPojo.getCiteuid(), tid);
                    // 同步委制单加工单主表的完工款数FinishCount
                    wkSubcontractitemMapper.updateWkSubcontractFinishCount(wkSccompleteitemPojo.getCiteitemid(), tid);
                }
            }

            // 收集去重的所有goodsid
            Set<String> goodsIdSetDelete = lst.stream()
                    .map(WkSccompleteitemPojo::getGoodsid)
                    .collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsIdSetDelete.forEach(goodsid -> {
                manuSyncMapper.updateGoodsWkScRemQty(goodsid, tid);
            });
            this.wkSccompleteMapper.updateFinishCount(Pid, tid);
            //主表更改
            WkSccompleteEntity entity = new WkSccompleteEntity();
            entity.setId(Pid);
            entity.setLister(loginUser.getRealname());
            entity.setListerid(loginUser.getUserid());
            entity.setModifydate(new Date());
            entity.setTenantid(tid);
            this.wkSccompleteMapper.update(entity);
            //返回Bill实例
            return this.getBillEntity(entity.getId(), tid);
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    public void updatePrintcount(WkSccompletePojo billPrintPojo) {
        this.wkSccompleteMapper.updatePrintcount(billPrintPojo);
    }
}
