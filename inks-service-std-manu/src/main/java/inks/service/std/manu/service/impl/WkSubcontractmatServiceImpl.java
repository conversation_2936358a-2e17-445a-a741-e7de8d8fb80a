package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSubcontractmatEntity;
import inks.service.std.manu.domain.pojo.WkSubcontractmatPojo;
import inks.service.std.manu.mapper.WkSubcontractmatMapper;
import inks.service.std.manu.service.WkSubcontractmatService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 委制物料(WkSubcontractmat)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-04 10:40:58
 */
@Service("wkSubcontractmatService")
public class WkSubcontractmatServiceImpl implements WkSubcontractmatService {
    @Resource
    private WkSubcontractmatMapper wkSubcontractmatMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSubcontractmatPojo getEntity(String key, String tid) {
        return this.wkSubcontractmatMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSubcontractmatPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSubcontractmatPojo> lst = wkSubcontractmatMapper.getPageList(queryParam);
            PageInfo<WkSubcontractmatPojo> pageInfo = new PageInfo<WkSubcontractmatPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkSubcontractmatPojo> getList(String Pid, String tid) {
        try {
            List<WkSubcontractmatPojo> lst = wkSubcontractmatMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkSubcontractmatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSubcontractmatPojo insert(WkSubcontractmatPojo wkSubcontractmatPojo) {
        //初始化item的NULL
        WkSubcontractmatPojo itempojo = this.clearNull(wkSubcontractmatPojo);
        WkSubcontractmatEntity wkSubcontractmatEntity = new WkSubcontractmatEntity();
        BeanUtils.copyProperties(itempojo, wkSubcontractmatEntity);

        wkSubcontractmatEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkSubcontractmatEntity.setRevision(1);  //乐观锁
        this.wkSubcontractmatMapper.insert(wkSubcontractmatEntity);
        return this.getEntity(wkSubcontractmatEntity.getId(), wkSubcontractmatEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSubcontractmatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSubcontractmatPojo update(WkSubcontractmatPojo wkSubcontractmatPojo) {
        WkSubcontractmatEntity wkSubcontractmatEntity = new WkSubcontractmatEntity();
        BeanUtils.copyProperties(wkSubcontractmatPojo, wkSubcontractmatEntity);
        this.wkSubcontractmatMapper.update(wkSubcontractmatEntity);
        return this.getEntity(wkSubcontractmatEntity.getId(), wkSubcontractmatEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkSubcontractmatMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkSubcontractmatPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSubcontractmatPojo clearNull(WkSubcontractmatPojo wkSubcontractmatPojo) {
        //初始化NULL字段
        if (wkSubcontractmatPojo.getPid() == null) wkSubcontractmatPojo.setPid("");
        if (wkSubcontractmatPojo.getItemid() == null) wkSubcontractmatPojo.setItemid("");
        if (wkSubcontractmatPojo.getGoodsid() == null) wkSubcontractmatPojo.setGoodsid("");
        if (wkSubcontractmatPojo.getQuantity() == null) wkSubcontractmatPojo.setQuantity(0D);
        if (wkSubcontractmatPojo.getFinishqty() == null) wkSubcontractmatPojo.setFinishqty(0D);
        if (wkSubcontractmatPojo.getMrpuid() == null) wkSubcontractmatPojo.setMrpuid("");
        if (wkSubcontractmatPojo.getMrpitemid() == null) wkSubcontractmatPojo.setMrpitemid("");
        if (wkSubcontractmatPojo.getSubqty() == null) wkSubcontractmatPojo.setSubqty(0D);
        if (wkSubcontractmatPojo.getMainqty() == null) wkSubcontractmatPojo.setMainqty(0D);
        if (wkSubcontractmatPojo.getLossrate() == null) wkSubcontractmatPojo.setLossrate(0D);
        if (wkSubcontractmatPojo.getBomid() == null) wkSubcontractmatPojo.setBomid("");
        if (wkSubcontractmatPojo.getBomtype() == null) wkSubcontractmatPojo.setBomtype(0);
        if (wkSubcontractmatPojo.getBomitemid() == null) wkSubcontractmatPojo.setBomitemid("");
        if (wkSubcontractmatPojo.getItemrowcode() == null) wkSubcontractmatPojo.setItemrowcode("");
        if (wkSubcontractmatPojo.getRownum() == null) wkSubcontractmatPojo.setRownum(0);
        if (wkSubcontractmatPojo.getClosed() == null) wkSubcontractmatPojo.setClosed(0);
        if (wkSubcontractmatPojo.getBomqty() == null) wkSubcontractmatPojo.setBomqty(0D);
        if (wkSubcontractmatPojo.getAvaiqty() == null) wkSubcontractmatPojo.setAvaiqty(0D);
        if (wkSubcontractmatPojo.getNeedqty() == null) wkSubcontractmatPojo.setNeedqty(0D);
        if (wkSubcontractmatPojo.getStoplanqty() == null) wkSubcontractmatPojo.setStoplanqty(0D);
        if (wkSubcontractmatPojo.getRealqty() == null) wkSubcontractmatPojo.setRealqty(0D);
        if (wkSubcontractmatPojo.getFlowcode() == null) wkSubcontractmatPojo.setFlowcode("");
        if (wkSubcontractmatPojo.getAttributejson() == null) wkSubcontractmatPojo.setAttributejson("");
        if (wkSubcontractmatPojo.getCustom1() == null) wkSubcontractmatPojo.setCustom1("");
        if (wkSubcontractmatPojo.getCustom2() == null) wkSubcontractmatPojo.setCustom2("");
        if (wkSubcontractmatPojo.getCustom3() == null) wkSubcontractmatPojo.setCustom3("");
        if (wkSubcontractmatPojo.getCustom4() == null) wkSubcontractmatPojo.setCustom4("");
        if (wkSubcontractmatPojo.getCustom5() == null) wkSubcontractmatPojo.setCustom5("");
        if (wkSubcontractmatPojo.getCustom6() == null) wkSubcontractmatPojo.setCustom6("");
        if (wkSubcontractmatPojo.getCustom7() == null) wkSubcontractmatPojo.setCustom7("");
        if (wkSubcontractmatPojo.getCustom8() == null) wkSubcontractmatPojo.setCustom8("");
        if (wkSubcontractmatPojo.getCustom9() == null) wkSubcontractmatPojo.setCustom9("");
        if (wkSubcontractmatPojo.getCustom10() == null) wkSubcontractmatPojo.setCustom10("");
        if (wkSubcontractmatPojo.getTenantid() == null) wkSubcontractmatPojo.setTenantid("");
        if (wkSubcontractmatPojo.getRevision() == null) wkSubcontractmatPojo.setRevision(0);
        return wkSubcontractmatPojo;
    }

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkSubcontractmatPojo> getListByPid(String key, String tid) {
        try {
            return this.wkSubcontractmatMapper.getListByPid(key, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
