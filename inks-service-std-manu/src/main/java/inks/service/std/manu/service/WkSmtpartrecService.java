package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSmtpartrecPojo;

/**
 * 上料记录(WkSmtpartrec)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-21 11:14:05
 */
public interface WkSmtpartrecService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSmtpartrecPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSmtpartrecPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSmtpartrecPojo 实例对象
     * @return 实例对象
     */
    WkSmtpartrecPojo insert(WkSmtpartrecPojo wkSmtpartrecPojo);

    /**
     * 修改数据
     *
     * @param wkSmtpartrecpojo 实例对象
     * @return 实例对象
     */
    WkSmtpartrecPojo update(WkSmtpartrecPojo wkSmtpartrecpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
