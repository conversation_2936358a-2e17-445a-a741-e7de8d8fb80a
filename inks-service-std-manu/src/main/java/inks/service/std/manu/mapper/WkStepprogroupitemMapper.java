package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkStepprogroupitemEntity;
import inks.service.std.manu.domain.pojo.WkStepprogroupitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阶梯项目(WkStepprogroupitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-01 16:33:50
 */
 @Mapper
public interface WkStepprogroupitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStepprogroupitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkStepprogroupitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkStepprogroupitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkStepprogroupitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkStepprogroupitemEntity wkStepprogroupitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkStepprogroupitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkStepprogroupitemEntity wkStepprogroupitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int checkGoodsidSpuvalue(@Param("goodsid") String goodsid, @Param("spuValue") String spuValue, @Param("tenantid") String tenantid);
}

