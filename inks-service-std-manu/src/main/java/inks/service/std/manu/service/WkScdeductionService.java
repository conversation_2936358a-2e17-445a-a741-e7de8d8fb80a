package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkScdeductionPojo;
import inks.service.std.manu.domain.pojo.WkScdeductionitemdetailPojo;

/**
 * 委制扣款(WkScdeduction)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-12 08:46:28
 */
public interface WkScdeductionService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkScdeductionPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkScdeductionitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkScdeductionPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkScdeductionPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkScdeductionPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkScdeductionPojo 实例对象
     * @return 实例对象
     */
    WkScdeductionPojo insert(WkScdeductionPojo wkScdeductionPojo);

    /**
     * 修改数据
     *
     * @param wkScdeductionpojo 实例对象
     * @return 实例对象
     */
    WkScdeductionPojo update(WkScdeductionPojo wkScdeductionpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkScdeductionPojo 实例对象
     * @return 实例对象
     */
    WkScdeductionPojo approval(WkScdeductionPojo wkScdeductionPojo);
}
