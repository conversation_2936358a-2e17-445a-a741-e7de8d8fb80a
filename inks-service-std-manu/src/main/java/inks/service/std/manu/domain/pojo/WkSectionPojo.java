package inks.service.std.manu.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;

/**
 * 生产工段(WkSection)实体类
 *
 * <AUTHOR>
 * @since 2024-10-28 11:21:19
 */
public class WkSectionPojo implements Serializable {
    private static final long serialVersionUID = -38994653971906370L;
     // id
     @Excel(name = "id")
    private String id;
     // 工段类型
     @Excel(name = "工段类型")
    private String secttype;
     // 工段编码
     @Excel(name = "工段编码")
    private String sectcode;
     // 工段名称
     @Excel(name = "工段名称")
    private String sectname;
     // 流程描述
     @Excel(name = "流程描述")
    private String flowdesc;
     // 备注
     @Excel(name = "备注")
    private String summary;
     // 有效标识
     @Excel(name = "有效标识")
    private Integer enabledmark;
     // 排序
     @Excel(name = "排序")
    private Integer rownum;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建者id
     @Excel(name = "创建者id")
    private String createbyid;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // item行数
     @Excel(name = "item行数")
    private Integer itemcount;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 自定义6
     @Excel(name = "自定义6")
    private String custom6;
     // 自定义7
     @Excel(name = "自定义7")
    private String custom7;
     // 自定义8
     @Excel(name = "自定义8")
    private String custom8;
     // 自定义9
     @Excel(name = "自定义9")
    private String custom9;
     // 自定义10
     @Excel(name = "自定义10")
    private String custom10;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 租户名称
     @Excel(name = "租户名称")
    private String tenantname;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<WkSectionitemPojo> item;
    // 关联工位List
    private List<WkStationPojo> stations;
  // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public List<WkStationPojo> getStations() {
        return stations;
    }

    public void setStations(List<WkStationPojo> stations) {
        this.stations = stations;
    }

    // 工段类型
    public String getSecttype() {
        return secttype;
    }
    
    public void setSecttype(String secttype) {
        this.secttype = secttype;
    }
        
  // 工段编码
    public String getSectcode() {
        return sectcode;
    }
    
    public void setSectcode(String sectcode) {
        this.sectcode = sectcode;
    }
        
  // 工段名称
    public String getSectname() {
        return sectname;
    }
    
    public void setSectname(String sectname) {
        this.sectname = sectname;
    }
        
  // 流程描述
    public String getFlowdesc() {
        return flowdesc;
    }
    
    public void setFlowdesc(String flowdesc) {
        this.flowdesc = flowdesc;
    }
        
  // 备注
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
  // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
  // 排序
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
  // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
  // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
  // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
  // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
  // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
  // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
  // item行数
    public Integer getItemcount() {
        return itemcount;
    }
    
    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }
        
  // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
  // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
  // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
  // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
  // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
  // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
  // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
  // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
  // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
  // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
  // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
  // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
  // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

    public List<WkSectionitemPojo> getItem() {
        return item;
    }

    public void setItem(List<WkSectionitemPojo> item) {
        this.item = item;
    }


}

