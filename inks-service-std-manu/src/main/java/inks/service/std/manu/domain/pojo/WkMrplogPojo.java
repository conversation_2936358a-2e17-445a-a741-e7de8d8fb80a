package inks.service.std.manu.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志(WkMrplog)Pojo
 *
 * <AUTHOR>
 * @since 2024-01-22 12:35:01
 */
public class WkMrplogPojo implements Serializable {
    private static final long serialVersionUID = 950553580093793406L;
         // id
       @Excel(name = "id")    
  private String id;
         // Pid
       @Excel(name = "Pid")    
  private String pid;
         // 摘要的JSON
       @Excel(name = "摘要的JSON")    
  private String remarkjson;
         // 模块标题
       @Excel(name = "模块标题")    
  private String opertitle;
         // 业务类型（0其它 1新增 2修改 3删除）
       @Excel(name = "业务类型（0其它 1新增 2修改 3删除）")    
  private Integer businesstype;
         // 方法名称
       @Excel(name = "方法名称")    
  private String method;
         // 请求方式
       @Excel(name = "请求方式")    
  private String requestmethod;
         // 操作类别（0其它 1后台用户 2手机端用户）
       @Excel(name = "操作类别（0其它 1后台用户 2手机端用户）")    
  private Integer operatortype;
         // 操作人员id
       @Excel(name = "操作人员id")    
  private String operuserid;
         // 操作人员
       @Excel(name = "操作人员")    
  private String opername;
         // 部门名称
       @Excel(name = "部门名称")    
  private String deptname;
         // 请求URL
       @Excel(name = "请求URL")    
  private String operurl;
         // 主机地址
       @Excel(name = "主机地址")    
  private String operip;
         // 操作地点
       @Excel(name = "操作地点")    
  private String operlocation;
         // 请求参数
       @Excel(name = "请求参数")    
  private String operparam;
         // 返回参数
       @Excel(name = "返回参数")    
  private String jsonresult;
         // 操作状态（0正常 1异常）
       @Excel(name = "操作状态（0正常 1异常）")    
  private Integer status;
         // 错误消息
       @Excel(name = "错误消息")    
  private String errormsg;
         // 操作时间
       @Excel(name = "操作时间")    
  private Date opertime;
         // 租户id
       @Excel(name = "租户id")    
  private String tenantid;

     // id
   
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
     // Pid
   
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
     // 摘要的JSON
   
    public String getRemarkjson() {
        return remarkjson;
    }

    public void setRemarkjson(String remarkjson) {
        this.remarkjson = remarkjson;
    }
     // 模块标题
   
    public String getOpertitle() {
        return opertitle;
    }

    public void setOpertitle(String opertitle) {
        this.opertitle = opertitle;
    }
     // 业务类型（0其它 1新增 2修改 3删除）
   
    public Integer getBusinesstype() {
        return businesstype;
    }

    public void setBusinesstype(Integer businesstype) {
        this.businesstype = businesstype;
    }
     // 方法名称
   
    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }
     // 请求方式
   
    public String getRequestmethod() {
        return requestmethod;
    }

    public void setRequestmethod(String requestmethod) {
        this.requestmethod = requestmethod;
    }
     // 操作类别（0其它 1后台用户 2手机端用户）
   
    public Integer getOperatortype() {
        return operatortype;
    }

    public void setOperatortype(Integer operatortype) {
        this.operatortype = operatortype;
    }
     // 操作人员id
   
    public String getOperuserid() {
        return operuserid;
    }

    public void setOperuserid(String operuserid) {
        this.operuserid = operuserid;
    }
     // 操作人员
   
    public String getOpername() {
        return opername;
    }

    public void setOpername(String opername) {
        this.opername = opername;
    }
     // 部门名称
   
    public String getDeptname() {
        return deptname;
    }

    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }
     // 请求URL
   
    public String getOperurl() {
        return operurl;
    }

    public void setOperurl(String operurl) {
        this.operurl = operurl;
    }
     // 主机地址
   
    public String getOperip() {
        return operip;
    }

    public void setOperip(String operip) {
        this.operip = operip;
    }
     // 操作地点
   
    public String getOperlocation() {
        return operlocation;
    }

    public void setOperlocation(String operlocation) {
        this.operlocation = operlocation;
    }
     // 请求参数
   
    public String getOperparam() {
        return operparam;
    }

    public void setOperparam(String operparam) {
        this.operparam = operparam;
    }
     // 返回参数
   
    public String getJsonresult() {
        return jsonresult;
    }

    public void setJsonresult(String jsonresult) {
        this.jsonresult = jsonresult;
    }
     // 操作状态（0正常 1异常）
   
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
     // 错误消息
   
    public String getErrormsg() {
        return errormsg;
    }

    public void setErrormsg(String errormsg) {
        this.errormsg = errormsg;
    }
     // 操作时间
   
    public Date getOpertime() {
        return opertime;
    }

    public void setOpertime(Date opertime) {
        this.opertime = opertime;
    }
     // 租户id
   
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

}

