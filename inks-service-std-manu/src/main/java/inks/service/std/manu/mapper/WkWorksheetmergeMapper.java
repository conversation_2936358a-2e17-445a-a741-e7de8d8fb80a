package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWorksheetmergeEntity;
import inks.service.std.manu.domain.pojo.WkWorksheetmergePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 加工单合并记录(WkWorksheetmerge)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-15 10:20:45
 */
@Mapper
public interface WkWorksheetmergeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetmergePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWorksheetmergePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param wkWorksheetmergeEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWorksheetmergeEntity wkWorksheetmergeEntity);

    
    /**
     * 修改数据
     *
     * @param wkWorksheetmergeEntity 实例对象
     * @return 影响行数
     */
    int update(WkWorksheetmergeEntity wkWorksheetmergeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int updateMergeItemid(@Param("mergeItemid") String mergeItemid, @Param("itemids")String itemids, @Param("tid")String tid);

    List<WkWorksheetmergePojo> getMergeListByItemid( @Param("key")String key,  @Param("tid")String tid);
}

