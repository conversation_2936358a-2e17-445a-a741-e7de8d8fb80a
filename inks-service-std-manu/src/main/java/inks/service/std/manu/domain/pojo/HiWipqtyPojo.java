package inks.service.std.manu.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产过数(HiWipqty)实体类
 *
 * <AUTHOR>
 * @since 2023-10-16 16:20:43
 */
public class HiWipqtyPojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = 901224333355174741L;
    // id
    @Excel(name = "id")
    private String id;
    // 编码
    @Excel(name = "编码")
    private String refno;
    // 工序id
    @Excel(name = "工序id")
    private String wpid;
    // 工序编码
    @Excel(name = "工序编码")
    private String wpcode;
    // 工序名称
    @Excel(name = "工序名称")
    private String wpname;
    // 操作日期
    @Excel(name = "操作日期")
    private Date wkdate;
    // 方向
    @Excel(name = "方向")
    private String direction;
    // 货品id
    @Excel(name = "货品id")
    private String goodsid;
    // 工人
    @Excel(name = "工人")
    private String worker;
    // Pcs数量
    @Excel(name = "Pcs数量")
    private Double pcsqty;
    // Sec数量
    @Excel(name = "Sec数量")
    private Double secqty;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 加工单号
    @Excel(name = "加工单号")
    private String workuid;
    // WIP单号
    @Excel(name = "WIP单号")
    private String wipuid;
    // Wip行号
    @Excel(name = "Wip行号")
    private Integer wiprownum;
    // Wipitemid
    @Excel(name = "Wipitemid")
    private String wipitemid;
    // 报废Pcs数
    @Excel(name = "报废Pcs数")
    private Double mrbpcsqty;
    // 报废Pcs数
    @Excel(name = "报废Pcs数")
    private Double mrbsecqty;
    // Mrbid
    @Excel(name = "Mrbid")
    private String mrbid;
    // 出入库单id
    @Excel(name = "出入库单id")
    private String acceid;
    // 检验员
    @Excel(name = "检验员")
    private String inspector;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 销售单号
    @Excel(name = "销售单号")
    private String machuid;
    // 销售子项id
    @Excel(name = "销售子项id")
    private String machitemid;
    // 销售客户id
    @Excel(name = "销售客户id")
    private String machgroupid;
    // 主计划号
    @Excel(name = "主计划号")
    private String mainplanuid;
    // 主计划Itemid
    @Excel(name = "主计划Itemid")
    private String mainplanitemid;
    // 加工单Itemid
    @Excel(name = "加工单Itemid")
    private String workitemid;
    @Excel(name = "")
    private String attributejson;
    // 当前规格JSON
    @Excel(name = "当前规格JSON")
    private String specjson;
    // 长
    @Excel(name = "长")
    private Double sizex;
    // 宽
    @Excel(name = "宽")
    private Double sizey;
    // 厚
    @Excel(name = "厚")
    private Double sizez;
    // 工作参数
    @Excel(name = "工作参数")
    private String workparam;
    // 工位id
    @Excel(name = "工位id")
    private String statid;
    // 工位编码
    @Excel(name = "工位编码")
    private String statcode;
    // 工位名称
    @Excel(name = "工位名称")
    private String statname;
    // 本次工时
    @Excel(name = "本次工时")
    private Double worktime;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // 编码
    private String groupuid;
    // 名称
    private String groupname;
    // 缩写
    private String abbreviate;
    // 面积
    private Double area;
    //成本分类
    private String costgroupjson;
    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 编码
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 工序id
    public String getWpid() {
        return wpid;
    }

    public void setWpid(String wpid) {
        this.wpid = wpid;
    }

    public String getCostgroupjson() {
        return costgroupjson;
    }

    public void setCostgroupjson(String costgroupjson) {
        this.costgroupjson = costgroupjson;
    }

    // 工序编码
    public String getWpcode() {
        return wpcode;
    }

    public void setWpcode(String wpcode) {
        this.wpcode = wpcode;
    }

    // 工序名称
    public String getWpname() {
        return wpname;
    }

    public void setWpname(String wpname) {
        this.wpname = wpname;
    }

    // 操作日期
    public Date getWkdate() {
        return wkdate;
    }

    public void setWkdate(Date wkdate) {
        this.wkdate = wkdate;
    }

    public String getStatid() {
        return statid;
    }

    public void setStatid(String statid) {
        this.statid = statid;
    }

    public String getStatcode() {
        return statcode;
    }

    public void setStatcode(String statcode) {
        this.statcode = statcode;
    }

    public String getStatname() {
        return statname;
    }

    public void setStatname(String statname) {
        this.statname = statname;
    }

    public Double getWorktime() {
        return worktime;
    }

    public void setWorktime(Double worktime) {
        this.worktime = worktime;
    }

    // 方向
    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    // 货品id
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 工人
    public String getWorker() {
        return worker;
    }

    public void setWorker(String worker) {
        this.worker = worker;
    }

    // Pcs数量
    public Double getPcsqty() {
        return pcsqty;
    }

    public void setPcsqty(Double pcsqty) {
        this.pcsqty = pcsqty;
    }

    // Sec数量
    public Double getSecqty() {
        return secqty;
    }

    public void setSecqty(Double secqty) {
        this.secqty = secqty;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 加工单号
    public String getWorkuid() {
        return workuid;
    }

    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }

    // WIP单号
    public String getWipuid() {
        return wipuid;
    }

    public void setWipuid(String wipuid) {
        this.wipuid = wipuid;
    }

    // Wip行号
    public Integer getWiprownum() {
        return wiprownum;
    }

    public void setWiprownum(Integer wiprownum) {
        this.wiprownum = wiprownum;
    }

    // Wipitemid
    public String getWipitemid() {
        return wipitemid;
    }

    public void setWipitemid(String wipitemid) {
        this.wipitemid = wipitemid;
    }

    // 报废Pcs数
    public Double getMrbpcsqty() {
        return mrbpcsqty;
    }

    public void setMrbpcsqty(Double mrbpcsqty) {
        this.mrbpcsqty = mrbpcsqty;
    }

    // 报废Pcs数
    public Double getMrbsecqty() {
        return mrbsecqty;
    }

    public void setMrbsecqty(Double mrbsecqty) {
        this.mrbsecqty = mrbsecqty;
    }

    // Mrbid
    public String getMrbid() {
        return mrbid;
    }

    public void setMrbid(String mrbid) {
        this.mrbid = mrbid;
    }

    // 出入库单id
    public String getAcceid() {
        return acceid;
    }

    public void setAcceid(String acceid) {
        this.acceid = acceid;
    }

    // 检验员
    public String getInspector() {
        return inspector;
    }

    public void setInspector(String inspector) {
        this.inspector = inspector;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 销售单号
    public String getMachuid() {
        return machuid;
    }

    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }

    // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }

    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }

    // 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }

    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }

    // 主计划号
    public String getMainplanuid() {
        return mainplanuid;
    }

    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }

    // 主计划Itemid
    public String getMainplanitemid() {
        return mainplanitemid;
    }

    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }

    // 加工单Itemid
    public String getWorkitemid() {
        return workitemid;
    }

    public void setWorkitemid(String workitemid) {
        this.workitemid = workitemid;
    }

    public String getAttributejson() {
        return attributejson;
    }

    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }

    // 当前规格JSON
    public String getSpecjson() {
        return specjson;
    }

    public void setSpecjson(String specjson) {
        this.specjson = specjson;
    }

    // 长
    public Double getSizex() {
        return sizex;
    }

    public void setSizex(Double sizex) {
        this.sizex = sizex;
    }

    // 宽
    public Double getSizey() {
        return sizey;
    }

    public void setSizey(Double sizey) {
        this.sizey = sizey;
    }

    // 厚
    public Double getSizez() {
        return sizez;
    }

    public void setSizez(Double sizez) {
        this.sizez = sizez;
    }

    public String getWorkparam() {
        return workparam;
    }

    public void setWorkparam(String workparam) {
        this.workparam = workparam;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getGroupuid() {
        return groupuid;
    }

    public void setGroupuid(String groupuid) {
        this.groupuid = groupuid;
    }

    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    public String getAbbreviate() {
        return abbreviate;
    }

    public void setAbbreviate(String abbreviate) {
        this.abbreviate = abbreviate;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }
}

