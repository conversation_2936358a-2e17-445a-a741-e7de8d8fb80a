package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWorksheetitemPojo;
import inks.service.std.manu.domain.pojo.WkWorksheetitemdetailPojo;

import java.util.List;

/**
 * 厂制项目(WkWorksheetitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 13:28:01
 */
public interface WkWorksheetitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWorksheetitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWorksheetitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkWorksheetitemPojo 实例对象
     * @return 实例对象
     */
    WkWorksheetitemPojo insert(WkWorksheetitemPojo wkWorksheetitemPojo);

    /**
     * 修改数据
     *
     * @param wkWorksheetitempojo 实例对象
     * @return 实例对象
     */
    WkWorksheetitemPojo update(WkWorksheetitemPojo wkWorksheetitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkWorksheetitempojo 实例对象
     * @return 实例对象
     */
    WkWorksheetitemPojo clearNull(WkWorksheetitemPojo wkWorksheetitempojo);

    int updateMergemark1(String itemids, String tid);

    WkWorksheetitemdetailPojo getEntityDetail(String key, String tid);

    int updateMergemark0(String key, String tid);

    List<WkWorksheetitemPojo> getItemListByIds(String ids, String key, String tid);
}
