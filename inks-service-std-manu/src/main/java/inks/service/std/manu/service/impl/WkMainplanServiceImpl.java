package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkMainplanEntity;
import inks.service.std.manu.domain.WkMainplanitemEntity;
import inks.service.std.manu.domain.pojo.WkMainplanPojo;
import inks.service.std.manu.domain.pojo.WkMainplanitemPojo;
import inks.service.std.manu.domain.pojo.WkMainplanitemdetailPojo;
import inks.service.std.manu.mapper.WkMainplanMapper;
import inks.service.std.manu.mapper.WkMainplanitemMapper;
import inks.service.std.manu.service.WkMainplanService;
import inks.service.std.manu.service.WkMainplanitemService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 生产主计划(WkMainplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-16 07:35:57
 */
@Service("wkMainplanService")
public class WkMainplanServiceImpl implements WkMainplanService {
    @Resource
    private WkMainplanMapper wkMainplanMapper;

    @Resource
    private WkMainplanitemMapper wkMainplanitemMapper;
    @Resource
    private SystemFeignService systemFeignService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkMainplanitemService wkMainplanitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMainplanPojo getEntity(String key, String tid) {
        return this.wkMainplanMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMainplanitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMainplanitemdetailPojo> lst = wkMainplanMapper.getPageList(queryParam);
            PageInfo<WkMainplanitemdetailPojo> pageInfo = new PageInfo<WkMainplanitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMainplanPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkMainplanPojo wkMainplanPojo = this.wkMainplanMapper.getEntity(key, tid);
            //读取子表
            wkMainplanPojo.setItem(wkMainplanitemMapper.getList(wkMainplanPojo.getId(), wkMainplanPojo.getTenantid()));
            return wkMainplanPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMainplanPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMainplanPojo> lst = wkMainplanMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkMainplanitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkMainplanPojo> pageInfo = new PageInfo<WkMainplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMainplanPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMainplanPojo> lst = wkMainplanMapper.getPageTh(queryParam);
            PageInfo<WkMainplanPojo> pageInfo = new PageInfo<WkMainplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkMainplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkMainplanPojo insert(WkMainplanPojo wkMainplanPojo) {
        String tid = wkMainplanPojo.getTenantid();
//初始化NULL字段
        if (wkMainplanPojo.getRefno() == null) wkMainplanPojo.setRefno("");
        if (wkMainplanPojo.getBilltype() == null) wkMainplanPojo.setBilltype("");
        if (wkMainplanPojo.getBilldate() == null) wkMainplanPojo.setBilldate(new Date());
        if (wkMainplanPojo.getBilltitle() == null) wkMainplanPojo.setBilltitle("");
        if (wkMainplanPojo.getOperator() == null) wkMainplanPojo.setOperator("");
        if (wkMainplanPojo.getSummary() == null) wkMainplanPojo.setSummary("");
        if (wkMainplanPojo.getCreateby() == null) wkMainplanPojo.setCreateby("");
        if (wkMainplanPojo.getCreatebyid() == null) wkMainplanPojo.setCreatebyid("");
        if (wkMainplanPojo.getCreatedate() == null) wkMainplanPojo.setCreatedate(new Date());
        if (wkMainplanPojo.getLister() == null) wkMainplanPojo.setLister("");
        if (wkMainplanPojo.getListerid() == null) wkMainplanPojo.setListerid("");
        if (wkMainplanPojo.getModifydate() == null) wkMainplanPojo.setModifydate(new Date());
        if (wkMainplanPojo.getAssessor() == null) wkMainplanPojo.setAssessor("");
        if (wkMainplanPojo.getAssessorid() == null) wkMainplanPojo.setAssessorid("");
        if (wkMainplanPojo.getAssessdate() == null) wkMainplanPojo.setAssessdate(new Date());
        if (wkMainplanPojo.getBillstatecode() == null) wkMainplanPojo.setBillstatecode("");
        if (wkMainplanPojo.getBillstatedate() == null) wkMainplanPojo.setBillstatedate(new Date());
        if (wkMainplanPojo.getBillstartdate() == null) wkMainplanPojo.setBillstartdate(new Date());
        if (wkMainplanPojo.getBillplandate() == null) wkMainplanPojo.setBillplandate(new Date());
        if (wkMainplanPojo.getItemcount() == null) wkMainplanPojo.setItemcount(wkMainplanPojo.getItem().size());
        if (wkMainplanPojo.getMrpcount() == null) wkMainplanPojo.setMrpcount(0);
        if (wkMainplanPojo.getStartcount() == null) wkMainplanPojo.setStartcount(0);
        if (wkMainplanPojo.getDisannulcount() == null) wkMainplanPojo.setDisannulcount(0);
        if (wkMainplanPojo.getFinishcount() == null) wkMainplanPojo.setFinishcount(0);
        if (wkMainplanPojo.getPrintcount() == null) wkMainplanPojo.setPrintcount(0);
        if (wkMainplanPojo.getOaflowmark() == null) wkMainplanPojo.setOaflowmark(0);
        if (wkMainplanPojo.getBillwkwpid() == null) wkMainplanPojo.setBillwkwpid("");
        if (wkMainplanPojo.getBillwkwpcode() == null) wkMainplanPojo.setBillwkwpcode("");
        if (wkMainplanPojo.getBillwkwpname() == null) wkMainplanPojo.setBillwkwpname("");
        if (wkMainplanPojo.getMergemark() == null) wkMainplanPojo.setMergemark(0);
        if (wkMainplanPojo.getCustom1() == null) wkMainplanPojo.setCustom1("");
        if (wkMainplanPojo.getCustom2() == null) wkMainplanPojo.setCustom2("");
        if (wkMainplanPojo.getCustom3() == null) wkMainplanPojo.setCustom3("");
        if (wkMainplanPojo.getCustom4() == null) wkMainplanPojo.setCustom4("");
        if (wkMainplanPojo.getCustom5() == null) wkMainplanPojo.setCustom5("");
        if (wkMainplanPojo.getCustom6() == null) wkMainplanPojo.setCustom6("");
        if (wkMainplanPojo.getCustom7() == null) wkMainplanPojo.setCustom7("");
        if (wkMainplanPojo.getCustom8() == null) wkMainplanPojo.setCustom8("");
        if (wkMainplanPojo.getCustom9() == null) wkMainplanPojo.setCustom9("");
        if (wkMainplanPojo.getCustom10() == null) wkMainplanPojo.setCustom10("");
        if (tid == null) wkMainplanPojo.setTenantid("");
        if (wkMainplanPojo.getTenantname() == null) wkMainplanPojo.setTenantname("");
        if (wkMainplanPojo.getRevision() == null) wkMainplanPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkMainplanEntity wkMainplanEntity = new WkMainplanEntity();
        BeanUtils.copyProperties(wkMainplanPojo, wkMainplanEntity);
        //设置id和新建日期
        wkMainplanEntity.setId(id);
        wkMainplanEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkMainplanMapper.insert(wkMainplanEntity);
        //Item子表处理
        List<WkMainplanitemPojo> lst = wkMainplanPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (WkMainplanitemPojo wkMainplanitemPojo : lst) {
                //初始化item的NULL
                WkMainplanitemPojo itemPojo = this.wkMainplanitemService.clearNull(wkMainplanitemPojo);
                WkMainplanitemEntity wkMainplanitemEntity = new WkMainplanitemEntity();
                BeanUtils.copyProperties(itemPojo, wkMainplanitemEntity);
                //设置id和Pid
                wkMainplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkMainplanitemEntity.setPid(id);
                wkMainplanitemEntity.setTenantid(tid);
                wkMainplanitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkMainplanitemMapper.insert(wkMainplanitemEntity);
                // 同步订单子表的MainPlanQty,MainPlanClosed 订单主表的MainPlanCount
                String machitemid = wkMainplanitemPojo.getMachitemid();
                if (isNotBlank(machitemid)) {
                    this.wkMainplanitemMapper.syncBusMachItemMainPlanQty(machitemid, tid);
                    this.wkMainplanitemMapper.syncBusMachMainPlanCount(machitemid, tid);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkMainplanEntity.getId(), wkMainplanEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkMainplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkMainplanPojo update(WkMainplanPojo wkMainplanPojo) {
        String tid = wkMainplanPojo.getTenantid();
        //主表更改
        WkMainplanEntity wkMainplanEntity = new WkMainplanEntity();
        // 更新款数
        wkMainplanPojo.setItemcount(wkMainplanPojo.getItem().size());
        BeanUtils.copyProperties(wkMainplanPojo, wkMainplanEntity);
        this.wkMainplanMapper.update(wkMainplanEntity);
        if (wkMainplanPojo.getItem() != null) {
            //Item子表处理
            List<WkMainplanitemPojo> lst = wkMainplanPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkMainplanMapper.getDelItemIds(wkMainplanPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                // 删除前先存储Mergeitems字段
                List<String> mergeItemIds = Collections.emptyList();
                for (String lstDelId : lstDelIds) {
                    WkMainplanitemPojo mainplanitemDel = wkMainplanitemMapper.getEntity(lstDelId, tid);
                    // 确保 getMergeitems() 不为 null
                    String mergeItems = mainplanitemDel.getMergeitems();
                    if (isNotBlank(mergeItems)) {
                        mergeItemIds = Arrays.asList(mergeItems.split(","));
                    }
                    this.wkMainplanitemMapper.delete(lstDelId, wkMainplanEntity.getTenantid());
                    // 同步订单子表的MainPlanQty,MainPlanClosed 订单主表的MainPlanCount
                    String machitemid = mainplanitemDel.getMachitemid();
                    if (isNotBlank(machitemid)) {
                        this.wkMainplanitemMapper.syncBusMachItemMainPlanQty(machitemid, tid);
                        this.wkMainplanitemMapper.syncBusMachMainPlanCount(machitemid, tid);
                    }
                    // 如果 mergemark为1，执行mergeItem方法的反向操作
                    if (Objects.equals(wkMainplanPojo.getMergemark(), 1)&& CollectionUtils.isNotEmpty(mergeItemIds)) {

                        // 1.mergeItemIds：即合并前的主计划子表原单id集合 将 MergeMark 都置回为初始状态0
                        wkMainplanitemMapper.syncMergeMarkInIds(mergeItemIds, 0, tid);

                        // 2.将销售订单子表的WkMegerMark置为0,WkMergeItem置为空
                        List<String> machItemids = wkMainplanitemMapper.getMachItemidsInPlanItemids(mergeItemIds, tid);
                        wkMainplanMapper.syncMachingItemWkMergeInIds(machItemids, 0, "", tid);

                    }

                }
            }
            if (lst != null) {
                //循环每个item子表
                for (WkMainplanitemPojo wkMainplanitemPojo : lst) {
                    WkMainplanitemEntity wkMainplanitemEntity = new WkMainplanitemEntity();
                    if ("".equals(wkMainplanitemPojo.getId()) || wkMainplanitemPojo.getId() == null) {
                        //初始化item的NULL
                        WkMainplanitemPojo itemPojo = this.wkMainplanitemService.clearNull(wkMainplanitemPojo);
                        BeanUtils.copyProperties(itemPojo, wkMainplanitemEntity);
                        //设置id和Pid
                        wkMainplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkMainplanitemEntity.setPid(wkMainplanEntity.getId());  // 主表 id
                        wkMainplanitemEntity.setTenantid(tid);   // 租户id
                        wkMainplanitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkMainplanitemMapper.insert(wkMainplanitemEntity);
                    } else {
                        BeanUtils.copyProperties(wkMainplanitemPojo, wkMainplanitemEntity);
                        wkMainplanitemEntity.setTenantid(tid);
                        this.wkMainplanitemMapper.update(wkMainplanitemEntity);
                    }
                    // 同步订单子表的MainPlanQty,MainPlanClosed 订单主表的MainPlanCount
                    String machitemid = wkMainplanitemPojo.getMachitemid();
                    if (isNotBlank(machitemid)) {
                        this.wkMainplanitemMapper.syncBusMachItemMainPlanQty(machitemid, tid);
                        this.wkMainplanitemMapper.syncBusMachMainPlanCount(machitemid, tid);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkMainplanEntity.getId(), wkMainplanEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkMainplanPojo wkMainplanPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkMainplanitemPojo> lst = wkMainplanPojo.getItem();
        // 删除前先存储Mergeitems字段
        List<String> mergeItemIds = Collections.emptyList();
        if (lst != null) {
            //循环每个删除item子表
            for (WkMainplanitemPojo wkMainplanitemPojo : lst) {
                // 确保 getMergeitems() 不为 null
                String mergeItems = wkMainplanitemPojo.getMergeitems();
                if (isNotBlank(mergeItems)) {
                    mergeItemIds = Arrays.asList(mergeItems.split(","));
                }
                this.wkMainplanitemMapper.delete(wkMainplanitemPojo.getId(), tid);
                // 同步订单子表的MainPlanQty,MainPlanClosed 订单主表的MainPlanCount
                String machitemid = wkMainplanitemPojo.getMachitemid();
                if (isNotBlank(machitemid)) {
                    this.wkMainplanitemMapper.syncBusMachItemMainPlanQty(machitemid, tid);
                    this.wkMainplanitemMapper.syncBusMachMainPlanCount(machitemid, tid);
                }
            }
        }
        this.wkMainplanMapper.delete(key, tid);

        // 如果 mergemark为1，执行mergeItem方法的反向操作
        if (Objects.equals(wkMainplanPojo.getMergemark(), 1)&& CollectionUtils.isNotEmpty(mergeItemIds)) {

            // 1.mergeItemIds：即合并前的主计划子表原单id集合 将 MergeMark 都置回为初始状态0
            wkMainplanitemMapper.syncMergeMarkInIds(mergeItemIds, 0, tid);

            // 2.将销售订单子表的WkMegerMark置为0,WkMergeItem置为空
            List<String> machItemids = wkMainplanitemMapper.getMachItemidsInPlanItemids(mergeItemIds, tid);
            wkMainplanMapper.syncMachingItemWkMergeInIds(machItemids, 0, "", tid);

        }


        return wkMainplanPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param wkMainplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkMainplanPojo approval(WkMainplanPojo wkMainplanPojo) {
        //主表更改
        WkMainplanEntity wkMainplanEntity = new WkMainplanEntity();
        BeanUtils.copyProperties(wkMainplanPojo, wkMainplanEntity);
        this.wkMainplanMapper.approval(wkMainplanEntity);
        //返回Bill实例
        return this.getBillEntity(wkMainplanEntity.getId(), wkMainplanEntity.getTenantid());
    }

    // 查询Item是否被引用
    @Override
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.wkMainplanMapper.getItemCiteBillName(key, pid, tid);
    }

    @Override
    public void updatePrintcount(WkMainplanPojo billPrintPojo) {
        this.wkMainplanMapper.updatePrintcount(billPrintPojo);
    }

//    @Override
//    @Transactional
//    public void mergeItem(List<String> mainPlanItemIds, LoginUser loginUser) {
//        String tid = loginUser.getTenantid();
//        List<WkMainplanitemdetailPojo> planItems = wkMainplanMapper.getMainPlanItem(mainPlanItemIds, tid);
//        // 主计划子表 合并相关字段：id、pid、goodsid、quantity、machuid
//        // 1.goodsid相同合并数量，生成新生产计划单
//
//        // 使用 Map 以 goodsid 作为 key，合并相同 goodsid 的数量
//        Map<String, WkMainplanitemdetailPojo> mergedItems = new HashMap<>();
//        for (WkMainplanitemdetailPojo item : planItems) {
//            String goodsId = item.getGoodsid();
//            if (mergedItems.containsKey(goodsId)) {
//                WkMainplanitemdetailPojo existingItem = mergedItems.get(goodsId);
//                existingItem.setQuantity(existingItem.getQuantity() + item.getQuantity());
//            } else {
//                mergedItems.put(goodsId, item);
//            }
//        }
//
//        // 生成新的生产计划单
//        // 主表
//        WkMainplanPojo newPlan = new WkMainplanPojo();
//        newPlan.setBilltype(planItems.get(0).getBilltype());
//        // 收集所有 planItems 的 refno 并拼接成一个字符串，设置为单据标题
//        String refnos = planItems.stream()
//                .map(WkMainplanitemdetailPojo::getRefno)
//                .collect(Collectors.joining(", "));
//        newPlan.setBilltitle("合并来自【" + refnos + "】");
//        //生成单据编码
//        R r = systemFeignService.getBillCode("D05M04B1", loginUser.getToken());
//        if (r.getCode() == 200)
//            newPlan.setRefno(r.getData().toString());
//        else {
//            throw new BaseBusinessException("单据编码读取出错" + r);
//        }
//        newPlan.setCreateby(loginUser.getRealname());   // 创建者
//        newPlan.setCreatebyid(loginUser.getUserid());  // 创建者id
//        newPlan.setCreatedate(new Date());   // 创建时间
//        newPlan.setLister(loginUser.getRealname());   // 制表
//        newPlan.setListerid(loginUser.getUserid());    // 制表id
//        newPlan.setModifydate(new Date());   //修改时间
//        newPlan.setTenantid(tid);   //租户id
//        // 子表
//        List<WkMainplanitemPojo> newPlanItems = new ArrayList<>();
//        for (WkMainplanitemdetailPojo mergedItem : mergedItems.values()) {
//            WkMainplanitemPojo newPlanItem = new WkMainplanitemPojo();
//            BeanUtils.copyProperties(mergedItem, newPlanItem);
//            newPlanItems.add(newPlanItem);
//        }
//        newPlan.setItem(newPlanItems);
//        // 插入计划单主子表
//        WkMainplanPojo insertPlan = insert(newPlan);
//
//        // 2.通过machuid更新Bus_MachingItem表的WkMergeMark、WkMergeItemid
//        // WkMergeMark； 2被合主单 3被合副单
//        // 获取主单号的 machuid 列表
//        List<String> machuidsMaster = newPlanItems.stream()
//                .map(WkMainplanitemPojo::getMachuid)
//                .collect(Collectors.toList());
//        // 获取副单号的 machuid 列表，即排除主单号中已有的 machuid
//        List<String> machuidsSlave = planItems.stream()
//                .map(WkMainplanitemdetailPojo::getMachuid)
//                .filter(machuid -> !machuidsMaster.contains(machuid)) // 过滤掉主单号中已有的 machuid
//                .collect(Collectors.toList());
//        // 刷新 Bus_MachingItem 表的 WkMergeMark、WkMergeItemid
//        wkMainplanMapper.syncMachingItemWkMerge(machuidMaster, 2, newPlanId, tid);
//
//
//    }


    @Override
    @Transactional
    public void mergeItem(List<String> mainPlanItemIds, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        List<WkMainplanitemdetailPojo> planItems = wkMainplanMapper.getMainPlanItem(mainPlanItemIds, tid);
        // ----1.goodsid相同合并数量，生成新生产计划单
        // 检查 goodsid 是否相同
        String goodsId = planItems.get(0).getGoodsid();
        boolean allGoodsIdSame = planItems.stream()
                .allMatch(item -> item.getGoodsid().equals(goodsId));
        if (!allGoodsIdSame) {
            throw new IllegalArgumentException("请选择相同的物料");
        }
        // 因为所有的 goodsid 都相同，可以直接合并数量
        double totalQuantity = planItems.stream()
                .mapToDouble(WkMainplanitemdetailPojo::getQuantity)
                .sum();
        // 生成新的生产计划单
        WkMainplanPojo newPlan = new WkMainplanPojo();
        newPlan.setMergemark(1);//合并后的单据标识 // 1合单“备用”
        newPlan.setBilltype(planItems.get(0).getBilltype());

        // 收集所有 planItems 的 refno 并拼接成一个字符串，设置为单据标题
        String refnos = planItems.stream()
                .map(WkMainplanitemdetailPojo::getRefno)
                .collect(Collectors.joining(", "));
        newPlan.setBilltitle("合并来自【" + refnos + "】");

        // 生成单据编码
        R r = systemFeignService.getBillCode("D05M04B1", loginUser.getToken());
        if (r.getCode() == 200) {
            newPlan.setRefno(r.getData().toString());
        } else {
            throw new BaseBusinessException("单据编码读取出错" + r);
        }

        newPlan.setCreateby(loginUser.getRealname());
        newPlan.setCreatebyid(loginUser.getUserid());
        newPlan.setCreatedate(new Date());
        newPlan.setLister(loginUser.getRealname());
        newPlan.setListerid(loginUser.getUserid());
        newPlan.setModifydate(new Date());
        newPlan.setTenantid(tid);

        // 创建新的计划单子表
        WkMainplanitemPojo newPlanItem = new WkMainplanitemPojo();
        BeanUtils.copyProperties(planItems.get(0), newPlanItem);
        newPlanItem.setQuantity(totalQuantity);
        newPlanItem.setMergemark(1);//主计划子表 合并后的单据标识 // 0默认/1合单/2被合主/3被合
        newPlanItem.setMergeitems(String.join(",", mainPlanItemIds));
        newPlan.setItem(Collections.singletonList(newPlanItem));
        // 插入计划单主子表
        WkMainplanPojo insertPlan = insert(newPlan);

        // ----2.通过 machitemid 更新 Bus_MachingItem 表的 WkMergeMark、WkMergeItemid
        String masterMachItemid = planItems.get(0).getMachitemid();//主单machuid
        List<String> salveMachItemids = planItems.stream()
                .map(WkMainplanitemdetailPojo::getMachitemid)
                .filter(machuid -> !machuid.equals(masterMachItemid))  // 过滤掉主单的machuid
                .collect(Collectors.toList());

        String insertPlanItemid = insertPlan.getItem().get(0).getId();
        wkMainplanMapper.syncMachingItemWkMergeInIds(Collections.singletonList(masterMachItemid), 2, insertPlanItemid, tid);
        wkMainplanMapper.syncMachingItemWkMergeInIds(salveMachItemids, 3, insertPlanItemid, tid);

        // ----3.更新被合并的Wk_MainPlanItem.MergeMark=2/3 (被合主单2，副单3)
        String masterPlanItemid = planItems.get(0).getId();//主单
        List<String> salvePlanItemids = mainPlanItemIds.stream()
                .filter(id -> !id.equals(masterPlanItemid))  // 过滤掉主单的id
                .collect(Collectors.toList());
        wkMainplanitemMapper.syncMergeMarkInIds(Collections.singletonList(masterPlanItemid), 2, tid);
        wkMainplanitemMapper.syncMergeMarkInIds(salvePlanItemids, 3, tid);


    }

}
