package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.InksConfig;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.service.WkWorksheetService;
import inks.service.std.manu.service.WkWorksheetitemService;
import inks.service.std.manu.service.WkWorksheetmatService;
import inks.service.std.manu.service.WkWorksheetmatmergeService;
import inks.service.std.manu.utils.PrintColor;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;
import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 厂制工单(WkWorksheet)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 15:25:54
 */

public class WkWorksheetController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(WkWorksheetController.class);
    private final String moduleCode = "D05M01B1";
    /**
     * 服务对象
     */
    @Resource
    private WkWorksheetService wkWorksheetService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkWorksheetitemService wkWorksheetitemService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkWorksheetmatService wkWorksheetmatService;
    @Resource
    private WkWorksheetmatmergeService wkWorksheetmatmergeService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private UtilsFeignService utilsFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取厂制工单详细信息", notes = "获取厂制工单详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<WkWorksheetPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkWorksheetService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<PageInfo<WkWorksheetitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWorksheetService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取厂制工单详细信息", notes = "获取厂制工单详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<WkWorksheetPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkWorksheetService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取厂制工单详细信息+MatMerge子表", notes = "获取采购计划详细信息", produces = "application/json")
    @RequestMapping(value = "/getMergeBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<WkWorksheetPojo> getMergeBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkWorksheetService.getMergeBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<PageInfo<WkWorksheetPojo>> getBillList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWorksheetService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.List")
    public R<PageInfo<WkWorksheetPojo>> getPageTh(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkWorksheetService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增厂制工单", notes = "新增厂制工单", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Add")
    @InksConfig
    public R<WkWorksheetPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkWorksheetPojo wkWorksheetPojo = JSONArray.parseObject(json, WkWorksheetPojo.class);
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Wk_Worksheet", null, loginUser.getTenantid());
            wkWorksheetPojo.setRefno(refno);
            if ("销售订单".equals(wkWorksheetPojo.getBilltype())) {
                for (WkWorksheetitemPojo wkWorksheetitemPojo : wkWorksheetPojo.getItem()) {
                    Map<String, Object> map = wkWorksheetService.getMachitemWKqty(wkWorksheetitemPojo.getMachitemid(), loginUser.getTenantid());
                    if (map != null) {
                        //BigDecimal wkQtyBigDecimal = (BigDecimal) map.get("WkQty");
                        BigDecimal wkQtyBigDecimal = map.get("WkQty") != null ? (BigDecimal) map.get("WkQty") : BigDecimal.ZERO;
                        double wkqty = wkQtyBigDecimal.doubleValue();
                        //BigDecimal wkQuantityBigDecimal = (BigDecimal) map.get("WkQuantity");
                        BigDecimal wkQuantityBigDecimal = map.get("WkQuantity") != null ? (BigDecimal) map.get("WkQuantity") : BigDecimal.ZERO;
                        double wkQuantity = wkQuantityBigDecimal.doubleValue();
                        if (wkWorksheetitemPojo.getQuantity() > wkqty - wkQuantity) {
                            return R.fail("生产数:" + wkWorksheetitemPojo.getQuantity() + "超出订单余数:" + (wkqty - wkQuantity));
                        }
                    }
                }
            }
            wkWorksheetPojo.setCreateby(loginUser.getRealname());   // 创建者
            wkWorksheetPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkWorksheetPojo.setCreatedate(new Date());   // 创建时间
            wkWorksheetPojo.setLister(loginUser.getRealname());   // 制表
            wkWorksheetPojo.setListerid(loginUser.getUserid());    // 制表id            
            wkWorksheetPojo.setModifydate(new Date());   //修改时间
            wkWorksheetPojo.setTenantid(loginUser.getTenantid());   //租户id
            if (loginUser.getTenantinfo() != null) {
                wkWorksheetPojo.setTenantname(loginUser.getTenantinfo().getTenantname() == null ? "" : loginUser.getTenantinfo().getTenantname());
            }
            wkWorksheetPojo.setItemcount(wkWorksheetPojo.getItem().size());
            WkWorksheetPojo insert = this.wkWorksheetService.insert(wkWorksheetPojo, loginUser.getToken());
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());// 保存单据编码RefNoUtils
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 导入厂制工单", notes = "新增厂制工单", produces = "application/json")
    @RequestMapping(value = "/syncCreate", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Add")
    public R<WkWorksheetPojo> syncCreate(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        synchronized (tid.intern()) {
            try {
                WkWorksheetPojo wkWorksheetPojo = JSONArray.parseObject(json, WkWorksheetPojo.class);
                //生成单据编码
                String refno = RefNoUtils.generateRefNo("D05M01B1", "Wk_Worksheet", null, tid);
                wkWorksheetPojo.setRefno(refno);
                if ("销售订单".equals(wkWorksheetPojo.getBilltype())) {
                    for (WkWorksheetitemPojo wkWorksheetitemPojo : wkWorksheetPojo.getItem()) {
                        Map<String, Object> map = wkWorksheetService.getMachitemWKqty(wkWorksheetitemPojo.getMachitemid(), loginUser.getTenantid());
                        if (map != null) {
                            //BigDecimal wkQtyBigDecimal = (BigDecimal) map.get("WkQty");
                            BigDecimal wkQtyBigDecimal = map.get("WkQty") != null ? (BigDecimal) map.get("WkQty") : BigDecimal.ZERO;
                            double wkqty = wkQtyBigDecimal.doubleValue();
                            //BigDecimal wkQuantityBigDecimal = (BigDecimal) map.get("WkQuantity");
                            BigDecimal wkQuantityBigDecimal = map.get("WkQuantity") != null ? (BigDecimal) map.get("WkQuantity") : BigDecimal.ZERO;
                            double wkQuantity = wkQuantityBigDecimal.doubleValue();
                            if (wkWorksheetitemPojo.getQuantity() > wkqty - wkQuantity) {
                                return R.fail("生产数:" + wkWorksheetitemPojo.getQuantity() + "超出订单余数:" + (wkqty - wkQuantity));
                            }
                        }
                    }
                }
                wkWorksheetPojo.setCreateby(loginUser.getRealname());   // 创建者
                wkWorksheetPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                wkWorksheetPojo.setCreatedate(new Date());   // 创建时间
                wkWorksheetPojo.setLister(loginUser.getRealname());   // 制表
                wkWorksheetPojo.setListerid(loginUser.getUserid());    // 制表id
                wkWorksheetPojo.setModifydate(new Date());   //修改时间
                wkWorksheetPojo.setTenantid(loginUser.getTenantid());   //租户id
                wkWorksheetPojo.setItemcount(wkWorksheetPojo.getItem().size());
                WkWorksheetPojo insertDB = this.wkWorksheetService.insert(wkWorksheetPojo, loginUser.getToken());

                RefNoUtils.saveRedisRefNo(refno, "D05M01B1", tid);
                return R.ok(insertDB);
            } catch (Exception e) {
                return R.fail(e.getMessage());
            }
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改厂制工单", notes = "修改厂制工单", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Edit")
    @InksConfig
    public R<WkWorksheetPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkWorksheetPojo wkWorksheetPojo = JSONArray.parseObject(json, WkWorksheetPojo.class);
            if ("销售订单".equals(wkWorksheetPojo.getBilltype())) {
                for (WkWorksheetitemPojo wkWorksheetitemPojo : wkWorksheetPojo.getItem()) {
                    // 不是合并单：才进行生产数和订单余数的对比校验
                    if (wkWorksheetitemPojo.getMergemark() == 0) {
                        Map<String, Object> map = wkWorksheetService.getMachitemWKqty(wkWorksheetitemPojo.getMachitemid(), loginUser.getTenantid());
                        WkWorksheetitemPojo wkWorksheetitemOld = wkWorksheetitemService.getEntity(wkWorksheetitemPojo.getId(), loginUser.getTenantid());
                        if (map != null) {
                            BigDecimal wkQtyBigDecimal = map.get("WkQty") != null ? (BigDecimal) map.get("WkQty") : BigDecimal.ZERO;
                            double wkqty = wkQtyBigDecimal.doubleValue();
                            BigDecimal wkQuantityBigDecimal = map.get("WkQuantity") != null ? (BigDecimal) map.get("WkQuantity") : BigDecimal.ZERO;
                            double wkQuantity = wkQuantityBigDecimal.doubleValue();
                            if (wkWorksheetitemPojo.getQuantity() > wkqty - (wkQuantity - wkWorksheetitemOld.getQuantity())) {
                                return R.fail("生产数:" + wkWorksheetitemPojo.getQuantity() + "超出订单余数:" + (wkqty - (wkQuantity - wkWorksheetitemOld.getQuantity())));
                            }
                        }
                    }
                }
            }
            wkWorksheetPojo.setLister(loginUser.getRealname());   // 制表
            wkWorksheetPojo.setListerid(loginUser.getUserid());    // 制表id   
            wkWorksheetPojo.setModifydate(new Date());   //修改时间
            wkWorksheetPojo.setAssessor(""); //审核员
            wkWorksheetPojo.setAssessorid(""); //审核员
            wkWorksheetPojo.setAssessdate(new Date()); //审核时间
            wkWorksheetPojo.setTenantid(loginUser.getTenantid());   //租户id
            wkWorksheetPojo.setItemcount(wkWorksheetPojo.getItem().size());
            return R.ok(this.wkWorksheetService.update(wkWorksheetPojo, loginUser.getToken()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除厂制工单", notes = "删除厂制工单", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Delete")
    @OperLog(title = "删除生产工单")
    @InksConfig
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //检查引用
            List<WkWorksheetitemPojo> lst = this.wkWorksheetitemService.getList(key, loginUser.getTenantid());
            for (WkWorksheetitemPojo item : lst) {
                List<String> lstcite = this.wkWorksheetService.getItemCiteBillName(item.getId(), item.getPid(), loginUser.getTenantid());
                if (!lstcite.isEmpty()) {
                    return R.fail(403, "禁止删除,被以下单据引用:" + lstcite);
                }
            }
            String refno = this.wkWorksheetService.delete(key, loginUser.getTenantid(), loginUser.getToken());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增厂制工单Item", notes = "新增厂制工单Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Add")
    public R<WkWorksheetitemPojo> createItem(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            WkWorksheetitemPojo wkWorksheetitemPojo = JSONArray.parseObject(json, WkWorksheetitemPojo.class);
            wkWorksheetitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkWorksheetitemService.insert(wkWorksheetitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除厂制工单Item", notes = "删除厂制工单Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWorksheetitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核厂制工单", notes = "审核厂制工单", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Approval")
    public R<WkWorksheetPojo> approval(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetService.getEntity(key, loginUser.getTenantid());
            if (wkWorksheetPojo.getAssessor().isEmpty()) {
                wkWorksheetPojo.setAssessor(loginUser.getRealname()); //审核员
                wkWorksheetPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                wkWorksheetPojo.setAssessor(""); //审核员
                wkWorksheetPojo.setAssessorid(""); //审核员
            }
            wkWorksheetPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.wkWorksheetService.approval(wkWorksheetPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "作废厂制工单", notes = "作废厂制工单,?type=1作废，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Edit")
    public R<WkWorksheetPojo> disannul(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<WkWorksheetitemPojo> lst = JSONArray.parseArray(json, WkWorksheetitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            return R.ok(this.wkWorksheetService.disannul(lst, type, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "中止厂制工单", notes = "中止厂制工单,?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Edit")
    public R<WkWorksheetPojo> closed(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<WkWorksheetitemPojo> lst = JSONArray.parseArray(json, WkWorksheetitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWorksheetService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = wkWorksheetPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkWorksheetitemPojo wkWorksheetitemPojo = new WkWorksheetitemPojo();
                    wkWorksheetPojo.getItem().add(wkWorksheetitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(wkWorksheetPojo.getItem());

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            //获取单据信息
            WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetService.getEntity(key, tid);

            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, tid, loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                String printapproved = tencfg.get("system.bill.printapproved");
                if (printapproved != null && printapproved.equals("true") && wkWorksheetPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========  Eric 20240218
            List<WkWorksheetitemPojo> lstitem = this.wkWorksheetitemService.getList(key, tid);
            //=========获取单据mat信息========
            List<WkWorksheetmatPojo> lstmat = this.wkWorksheetmatService.getList(key, tid);
            //=========获取单据matMerge信息========
            List<WkWorksheetmatmergePojo> lismatmerge = wkWorksheetmatmergeService.getList(key, tid);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("Item", attrListToMaps(lstitem));
            mapreport.put("Mat", attrListToMaps(lstmat));
            mapreport.put("MatMerge", attrListToMaps(lismatmerge));
            mapreport.put("Master", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("xml", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "生产加工单" + wkWorksheetPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            mapPrint.put("data", ptJson);   //  打印数据
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "一式两份打印(仅Item无Mat)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),cmd=1为预览", produces = "application/json")
    @RequestMapping(value = "/printWebBillMulti", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebBillMulti(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetService.getEntity(key, loginUser.getTenantid());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                String printapproved = tencfg.get("system.bill.printapproved");
                if (printapproved != null && printapproved.equals("true") && wkWorksheetPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            QueryParam queryParam = new QueryParam();
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Wk_WorksheetItem.Pid='" + key + "'");
            queryParam.setOrderBy("Wk_WorksheetItem.RowNum");
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            List<WkWorksheetitemdetailPojo> lstitem = this.wkWorksheetService.getPageList(queryParam).getList();
            List<WkWorksheetitemdetailPojo> lstCopy = new ArrayList<>();
            for (WkWorksheetitemdetailPojo map2 : lstitem) {
                WkWorksheetitemdetailPojo newPojo = new WkWorksheetitemdetailPojo();
                BeanUtils.copyProperties(map2, newPojo);
                lstCopy.add(newPojo);
            }
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrcostListToMaps(lstitem);
            List<Map<String, Object>> lstMap = attrcostListToMaps(lstCopy);

            // 给第一个 lst 中的每个 map 添加字段 ToWho=1，PageNo=1
            lst.forEach(m -> {
                m.put("ToWho", 1);
                m.put("PageNo", 1);
            });
            // 给第二个 lstCopy 中的每个 map 添加字段 ToWho=2，PageNo=1
            lstMap.forEach(m -> {
                m.put("ToWho", 2);
                m.put("PageNo", 1);
            });

            // 合并两个 lst
            lst.addAll(lstMap);


            PrintColor.red("lst:" + lst);
            PrintColor.red("lst.size():" + lst.size());


            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            // 创建一个空的Map
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "送货单据" + wkWorksheetPojo.getRefno());
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());

            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印WkWorksheet明细报表(分页PageList)", notes = "打印明细报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public void printPageList(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Wk_Worksheet.CreateDate");
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<WkWorksheetitemdetailPojo> lst = this.wkWorksheetService.getPageList(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkWorksheetitemdetailPojo itemdetailPojo = new WkWorksheetitemdetailPojo();
                    lst.add(itemdetailPojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "云打印WkWorksheet明细报表(分页PageList)", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<WkWorksheetitemdetailPojo> lstitem = this.wkWorksheetService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // 拆开costgroupjson:[{"key":"qitadedanjia","name":"其他的单价","value":"10"},{"key":"lasijiage","name":"拉丝价格","value":"12"}]
            for (Map<String, Object> itemMap : lst) {
                if (itemMap.get("costgroupjson") != null) {
                    String costgroupjson = itemMap.get("costgroupjson").toString();
                    if (StringUtils.isNotBlank(costgroupjson)) {
                        // 将 JSON 字符串解析为 List<Map<String, Object>>
                        List<Map<String, Object>> costinfoList = JSON.parseObject(costgroupjson, new TypeReference<List<Map<String, Object>>>() {
                        });

                        // 创建新的 Map 用于存储键值对
                        Map<String, Object> costinfoMap = new HashMap<>();

                        // 遍历列表，提取键值对并添加到新的 Map 中
                        for (Map<String, Object> map2 : costinfoList) {
                            String key = (String) map2.get("key");
                            Object value = map2.get("value");
                            if (key != null && value != null) {
                                costinfoMap.put(key, value);
                            }
                        }
                        // 将新的 Map 合并到 itemMap 中
                        itemMap.putAll(costinfoMap);
                    }
                }
            }

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "WkWorksheet明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印WkWorksheet单据报表(分页PageTh)", notes = "打印PageTh报表", produces = "application/json")
    @RequestMapping(value = "/printPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public void printPageTh(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Wk_Worksheet.CreateDate");
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<WkWorksheetPojo> lst = this.wkWorksheetService.getPageTh(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkWorksheetPojo pojo = new WkWorksheetPojo();
                    lst.add(pojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "批量云打印报表(List<WkWorksheetPojo>不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebPageTh(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Worksheet.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Wk_Worksheet.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<WkWorksheetPojo> lstTh = this.wkWorksheetService.getPageTh(queryParam).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstTh.size() > 0) {
                map.put("groupname", lstTh.get(0).getGroupname());
                map.put("abbreviate", lstTh.get(0).getAbbreviate());
                map.put("groupuid", lstTh.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "WkWorksheet批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "批量打印单据,传入ids", notes = "json={KEY,KEY},ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBatchBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public void printBatchBill(@RequestBody String json, String ptid) throws IOException, JRException {
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            String content;
            if (reportsPojo != null) {
                content = reportsPojo.getRptdata();
            } else {
                throw new BaseBusinessException("未找到报表");
            }
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");

            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            //数据填充
            JasperPrint printAll = new JasperPrint();
            for (int a = 0; a < lstkeys.size(); a++) {
                String key = lstkeys.get(a);
                //=========获取单据表头信息========
                WkWorksheetPojo pojo = this.wkWorksheetService.getBillEntity(key, loginUser.getTenantid());
                if (pojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
                if (printapproved != null && printapproved.equals("true") && pojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
                //表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(pojo);
                // 加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
                // 判定是否需要追行
                if (reportsPojo.getPagerow() > 0) {
                    int index = 0;
                    // 取行余数
                    index = pojo.getItem().size() % reportsPojo.getPagerow();
                    if (index > 0) {
                        // 补全空白行
                        for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                            WkWorksheetitemPojo itemPojo = new WkWorksheetitemPojo();
                            pojo.getItem().add(itemPojo);
                        }
                    }
                }

                // 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = BeanUtils.attrcostListToMaps(pojo.getItem());
                //item转数据源
                JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

                // 刷入打印Num++
//                WkWorksheetPojo billPrintPojo = new WkWorksheetPojo();
//                billPrintPojo.setId(pojo.getId());
//                billPrintPojo.setPrintcount(pojo.getPrintcount() + 1);
//                billPrintPojo.setTenantid(pojo.getTenantid());
//                this.wkWorksheetService.update(billPrintPojo);

                //报表生成
                InputStream stream = new ByteArrayInputStream(content.getBytes());
                //编译报表
                JasperReport jasperReport = JasperCompileManager.compileReport(stream);

                //数据填充
                JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
                if (a == 0) {
                    printAll = print;
                } else {
                    List<JRPrintPage> pages = print.getPages();
                    for (JRPrintPage page : pages) {
                        printAll.addPage(page);
                    }
                }
            }
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(printAll, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "批量云打印WkWorksheet单据(ids)", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printBatchWebBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printBatchWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            for (String key : lstkeys) {
                //=========获取单据表头信息========
                WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetService.getEntity(key, loginUser.getTenantid());
                if (wkWorksheetPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
                if (printapproved != null && printapproved.equals("true") && wkWorksheetPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetPojo);

                // 获取单据表头.加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
                //=========获取单据Item信息========
                List<WkWorksheetitemPojo> lstitem = this.wkWorksheetitemService.getList(key, loginUser.getTenantid());
                // 单据Item. 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = BeanUtils.attrcostListToMaps(lstitem);
                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
                maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);
                ptRefNoMain += wkWorksheetPojo.getRefno() + ",";
                // 刷入打印Num++
                WkWorksheetPojo billPrintPojo = new WkWorksheetPojo();
                billPrintPojo.setId(wkWorksheetPojo.getId());
                billPrintPojo.setPrintcount(wkWorksheetPojo.getPrintcount() + 1);
                billPrintPojo.setTenantid(wkWorksheetPojo.getTenantid());
                this.wkWorksheetService.updatePrintcount(billPrintPojo);
            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "WkWorksheet：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),{ids:xxx}", produces = "application/json")
    @RequestMapping(value = "/printWebItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Worksheet.Print")
    public R<String> printWebItem(String key, String ptid, String sn, Integer cmd, @RequestBody String json, Integer redis) {
        try {
            Map<String, Object> mapids = JSONArray.parseObject(json, Map.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //获取单据信息
            WkWorksheetPojo wkWorksheetPojo = this.wkWorksheetService.getEntity(key, loginUser.getTenantid());
//            //=========获取单据表头信息========
//            BusMachiningitemPojo busMachiningitemPojo = this.busMachiningitemService.getEntity(key, loginUser.getTenantid());
//            //=========获取单据表头信息========
//            BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(busMachiningitemPojo.getPid(), loginUser.getTenantid());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData(); // redisService.getCacheObject("tenant_config:" + loginUser.getTenantid());
                String printapproved = tencfg.get("system.bill.printapproved");
                if (printapproved != null && printapproved.equals("true") && wkWorksheetPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(wkWorksheetPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<WkWorksheetitemPojo> lstitem = this.wkWorksheetitemService.getItemListByIds(mapids.get("ids").toString(), key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);
            // 将costgroupjson转为Map
            for (Map<String, Object> map2 : lst) {
                if (map2.get("costgroupjson") != null && !map2.get("costgroupjson").toString().isEmpty()) {
                    List<Map<String, Object>> listObjectSec = JSONArray.parseObject(map2.get("costgroupjson").toString(), List.class);
                    for (Map<String, Object> mapList : listObjectSec) {
                        if (mapList.get("key") != null && mapList.get("value") != null)
                            map2.put(mapList.get("key").toString(), mapList.get("value").toString());
                    }
                }
            }
            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "销售订单" + wkWorksheetPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

