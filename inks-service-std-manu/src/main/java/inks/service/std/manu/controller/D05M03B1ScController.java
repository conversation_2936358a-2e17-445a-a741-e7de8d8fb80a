package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.FieldFilter;
import inks.common.security.annotation.InksConfig;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkSccompletePojo;
import inks.service.std.manu.domain.pojo.WkSccompleteitemdetailPojo;
import inks.service.std.manu.service.WkSccompleteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 委外加工单验收(Wk_ScComplete)表控制层
 *
 * <AUTHOR>
 * @since 2023-10-09 12:50:11
 */
@RestController
@RequestMapping("D05M03B1Sc")
@Api(tags = "D05M03B1Sc:委制加工单验收")
public class D05M03B1ScController extends WkSccompleteController {

    @Resource
    private TokenService tokenService;
    @Resource
    private WkSccompleteService wkSccompleteService;

    @ApiOperation(value = "委制验收未开票(WkSccomplete)按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineInvoPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ScComplete.List")
    @InksConfig({"system.bill.amountfilter"})
    @FieldFilter(permission = "Wk_ScComplete.Amount")
    public R<PageInfo<WkSccompleteitemdetailPojo>> getOnlineInvoPageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_ScComplete.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and ABS(Wk_ScCompleteItem.InvoQty)<ABS(Wk_ScCompleteItem.Quantity)";
            qpfilter += " and Wk_ScCompleteItem.Closed=0 and Wk_ScCompleteItem.InvoClosed=0";
            qpfilter += " and Wk_ScComplete.Assessorid<>''";
            if (groupid != null) {
                qpfilter += " and Wk_ScComplete.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSccompleteService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "委制验收getOnlinePageList按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ScComplete.List")
    @InksConfig({"system.bill.amountfilter"})
    @FieldFilter(permission = "Wk_ScComplete.Amount")
    public R<PageInfo<WkSccompleteitemdetailPojo>> getOnlinePageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_ScComplete.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Wk_ScCompleteItem.FinishQty<Wk_ScCompleteItem.Quantity";
            qpfilter += " and Wk_ScCompleteItem.Closed=0 ";  // 未关闭、未注销

            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkSccompleteService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "委制验收getOnlinePageList按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_ScComplete.List")
    @InksConfig({"system.bill.amountfilter"})
    @FieldFilter(permission = "Wk_ScComplete.Amount")
    public R<PageInfo<WkSccompletePojo>> getOnlinePageTh(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_ScComplete.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Wk_ScComplete.ItemCount>Wk_ScComplete.FinishCount+Wk_ScComplete.DisannulCount";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkSccompleteService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
