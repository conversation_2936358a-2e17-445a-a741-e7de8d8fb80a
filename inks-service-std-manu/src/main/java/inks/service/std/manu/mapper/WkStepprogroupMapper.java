package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkStepprogroupEntity;
import inks.service.std.manu.domain.pojo.WkStepprogroupPojo;
import inks.service.std.manu.domain.pojo.WkStepprogroupitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阶梯制程(WkStepprogroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-01 16:33:33
 */
@Mapper
public interface WkStepprogroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStepprogroupPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkStepprogroupitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkStepprogroupPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param wkStepprogroupEntity 实例对象
     * @return 影响行数
     */
    int insert(WkStepprogroupEntity wkStepprogroupEntity);

    
    /**
     * 修改数据
     *
     * @param wkStepprogroupEntity 实例对象
     * @return 影响行数
     */
    int update(WkStepprogroupEntity wkStepprogroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param wkStepprogroupPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(WkStepprogroupPojo wkStepprogroupPojo);
                                                                                    /**
     * 修改数据
     *
     * @param wkStepprogroupEntity 实例对象
     * @return 影响行数
     */
    int approval(WkStepprogroupEntity wkStepprogroupEntity);

    String getidBySpuJson(@Param("goodsid") String goodsid, @Param("sql") String string, @Param("size") int spusSize, @Param("tid") String tid);

    String getFlowJson(@Param("wkStepprogroupId") String wkStepprogroupId, @Param("quantity") String quantity, @Param("tid") String tid);
}

