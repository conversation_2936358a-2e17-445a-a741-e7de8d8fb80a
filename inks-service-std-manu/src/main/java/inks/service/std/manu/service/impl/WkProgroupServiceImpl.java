package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkProgroupEntity;
import inks.service.std.manu.domain.WkProgroupitemEntity;
import inks.service.std.manu.domain.pojo.WkProgroupPojo;
import inks.service.std.manu.domain.pojo.WkProgroupitemPojo;
import inks.service.std.manu.domain.pojo.WkProgroupitemdetailPojo;
import inks.service.std.manu.mapper.WkProgroupMapper;
import inks.service.std.manu.mapper.WkProgroupitemMapper;
import inks.service.std.manu.service.WkProgroupService;
import inks.service.std.manu.service.WkProgroupitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 生产制程(WkProgroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-17 16:36:50
 */
@Service("wkProgroupService")
public class WkProgroupServiceImpl implements WkProgroupService {
    @Resource
    private WkProgroupMapper wkProgroupMapper;

    @Resource
    private WkProgroupitemMapper wkProgroupitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkProgroupitemService wkProgroupitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkProgroupPojo getEntity(String key, String tid) {
        return this.wkProgroupMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkProgroupitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProgroupitemdetailPojo> lst = wkProgroupMapper.getPageList(queryParam);
            PageInfo<WkProgroupitemdetailPojo> pageInfo = new PageInfo<WkProgroupitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkProgroupPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkProgroupPojo wkProgroupPojo = this.wkProgroupMapper.getEntity(key, tid);
            //读取子表
            wkProgroupPojo.setItem(wkProgroupitemMapper.getList(wkProgroupPojo.getId(), wkProgroupPojo.getTenantid()));
            return wkProgroupPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkProgroupPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProgroupPojo> lst = wkProgroupMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(wkProgroupitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<WkProgroupPojo> pageInfo = new PageInfo<WkProgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkProgroupPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkProgroupPojo> lst = wkProgroupMapper.getPageTh(queryParam);
            PageInfo<WkProgroupPojo> pageInfo = new PageInfo<WkProgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkProgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkProgroupPojo insert(WkProgroupPojo wkProgroupPojo) {
//初始化NULL字段
        if (wkProgroupPojo.getGrouptype() == null) wkProgroupPojo.setGrouptype("");
        if (wkProgroupPojo.getGroupcode() == null) wkProgroupPojo.setGroupcode("");
        if (wkProgroupPojo.getGroupname() == null) wkProgroupPojo.setGroupname("");
        if (wkProgroupPojo.getFlowdesc() == null) wkProgroupPojo.setFlowdesc("");
        if (wkProgroupPojo.getSummary() == null) wkProgroupPojo.setSummary("");
        if (wkProgroupPojo.getEnabledmark() == null) wkProgroupPojo.setEnabledmark(0);
        if (wkProgroupPojo.getRownum() == null) wkProgroupPojo.setRownum(0);
        if (wkProgroupPojo.getCreateby() == null) wkProgroupPojo.setCreateby("");
        if (wkProgroupPojo.getCreatebyid() == null) wkProgroupPojo.setCreatebyid("");
        if (wkProgroupPojo.getCreatedate() == null) wkProgroupPojo.setCreatedate(new Date());
        if (wkProgroupPojo.getLister() == null) wkProgroupPojo.setLister("");
        if (wkProgroupPojo.getListerid() == null) wkProgroupPojo.setListerid("");
        if (wkProgroupPojo.getModifydate() == null) wkProgroupPojo.setModifydate(new Date());
        if (wkProgroupPojo.getItemcount() == null) wkProgroupPojo.setItemcount(0);
        if (wkProgroupPojo.getCustom1() == null) wkProgroupPojo.setCustom1("");
        if (wkProgroupPojo.getCustom2() == null) wkProgroupPojo.setCustom2("");
        if (wkProgroupPojo.getCustom3() == null) wkProgroupPojo.setCustom3("");
        if (wkProgroupPojo.getCustom4() == null) wkProgroupPojo.setCustom4("");
        if (wkProgroupPojo.getCustom5() == null) wkProgroupPojo.setCustom5("");
        if (wkProgroupPojo.getCustom6() == null) wkProgroupPojo.setCustom6("");
        if (wkProgroupPojo.getCustom7() == null) wkProgroupPojo.setCustom7("");
        if (wkProgroupPojo.getCustom8() == null) wkProgroupPojo.setCustom8("");
        if (wkProgroupPojo.getCustom9() == null) wkProgroupPojo.setCustom9("");
        if (wkProgroupPojo.getCustom10() == null) wkProgroupPojo.setCustom10("");
        if (wkProgroupPojo.getTenantid() == null) wkProgroupPojo.setTenantid("");
        if (wkProgroupPojo.getTenantname() == null) wkProgroupPojo.setTenantname("");
        if (wkProgroupPojo.getRevision() == null) wkProgroupPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        WkProgroupEntity wkProgroupEntity = new WkProgroupEntity();
        BeanUtils.copyProperties(wkProgroupPojo, wkProgroupEntity);
        //设置id和新建日期
        wkProgroupEntity.setId(id);
        wkProgroupEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkProgroupMapper.insert(wkProgroupEntity);
        //Item子表处理
        List<WkProgroupitemPojo> lst = wkProgroupPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                WkProgroupitemPojo itemPojo = this.wkProgroupitemService.clearNull(lst.get(i));
                WkProgroupitemEntity wkProgroupitemEntity = new WkProgroupitemEntity();
                BeanUtils.copyProperties(itemPojo, wkProgroupitemEntity);
                //设置id和Pid
                wkProgroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkProgroupitemEntity.setPid(id);
                wkProgroupitemEntity.setTenantid(wkProgroupPojo.getTenantid());
                wkProgroupitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkProgroupitemMapper.insert(wkProgroupitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkProgroupEntity.getId(), wkProgroupEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkProgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkProgroupPojo update(WkProgroupPojo wkProgroupPojo) {
        //主表更改
        WkProgroupEntity wkProgroupEntity = new WkProgroupEntity();
        BeanUtils.copyProperties(wkProgroupPojo, wkProgroupEntity);
        this.wkProgroupMapper.update(wkProgroupEntity);
        if (wkProgroupPojo.getItem() != null) {
            //Item子表处理
            List<WkProgroupitemPojo> lst = wkProgroupPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = wkProgroupMapper.getDelItemIds(wkProgroupPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.wkProgroupitemMapper.delete(lstDelIds.get(i), wkProgroupEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    WkProgroupitemEntity wkProgroupitemEntity = new WkProgroupitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        WkProgroupitemPojo itemPojo = this.wkProgroupitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, wkProgroupitemEntity);
                        //设置id和Pid
                        wkProgroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        wkProgroupitemEntity.setPid(wkProgroupEntity.getId());  // 主表 id
                        wkProgroupitemEntity.setTenantid(wkProgroupPojo.getTenantid());   // 租户id
                        wkProgroupitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.wkProgroupitemMapper.insert(wkProgroupitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), wkProgroupitemEntity);
                        wkProgroupitemEntity.setTenantid(wkProgroupPojo.getTenantid());
                        this.wkProgroupitemMapper.update(wkProgroupitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkProgroupEntity.getId(), wkProgroupEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkProgroupPojo wkProgroupPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkProgroupitemPojo> lst = wkProgroupPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkProgroupitemPojo wkProgroupitemPojo : lst) {
                this.wkProgroupitemMapper.delete(wkProgroupitemPojo.getId(), tid);
            }
        }
        this.wkProgroupMapper.delete(key, tid);
        return wkProgroupPojo.getGroupname();
    }


}
