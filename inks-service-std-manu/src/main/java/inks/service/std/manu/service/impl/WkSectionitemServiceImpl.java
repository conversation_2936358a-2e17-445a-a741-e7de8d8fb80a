package inks.service.std.manu.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.manu.domain.pojo.WkSectionitemPojo;
import inks.service.std.manu.domain.WkSectionitemEntity;
import inks.service.std.manu.mapper.WkSectionitemMapper;
import inks.service.std.manu.service.WkSectionitemService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 生产工段工序子表(WkSectionitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-28 11:21:43
 */
@Service("wkSectionitemService")
public class WkSectionitemServiceImpl implements WkSectionitemService {
    @Resource
    private WkSectionitemMapper wkSectionitemMapper;

    @Override
    public WkSectionitemPojo getEntity(String key,String tid) {
        return this.wkSectionitemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<WkSectionitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSectionitemPojo> lst = wkSectionitemMapper.getPageList(queryParam);
            PageInfo<WkSectionitemPojo> pageInfo = new PageInfo<WkSectionitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<WkSectionitemPojo> getList(String Pid,String tid) { 
        try {
            List<WkSectionitemPojo> lst = wkSectionitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public WkSectionitemPojo insert(WkSectionitemPojo wkSectionitemPojo) {
        //初始化item的NULL
        WkSectionitemPojo itempojo =this.clearNull(wkSectionitemPojo);
        WkSectionitemEntity wkSectionitemEntity = new WkSectionitemEntity(); 
        BeanUtils.copyProperties(itempojo,wkSectionitemEntity);
          //生成雪花id
          wkSectionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          wkSectionitemEntity.setRevision(1);  //乐观锁      
          this.wkSectionitemMapper.insert(wkSectionitemEntity);
        return this.getEntity(wkSectionitemEntity.getId(),wkSectionitemEntity.getTenantid());
  
    }

    @Override
    public WkSectionitemPojo update(WkSectionitemPojo wkSectionitemPojo) {
        WkSectionitemEntity wkSectionitemEntity = new WkSectionitemEntity(); 
        BeanUtils.copyProperties(wkSectionitemPojo,wkSectionitemEntity);
        this.wkSectionitemMapper.update(wkSectionitemEntity);
        return this.getEntity(wkSectionitemEntity.getId(),wkSectionitemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.wkSectionitemMapper.delete(key,tid) ;
    }

     @Override
     public WkSectionitemPojo clearNull(WkSectionitemPojo wkSectionitemPojo){
     //初始化NULL字段
     if(wkSectionitemPojo.getPid()==null) wkSectionitemPojo.setPid("");
     if(wkSectionitemPojo.getWpid()==null) wkSectionitemPojo.setWpid("");
     if(wkSectionitemPojo.getWpcode()==null) wkSectionitemPojo.setWpcode("");
     if(wkSectionitemPojo.getWpname()==null) wkSectionitemPojo.setWpname("");
     if(wkSectionitemPojo.getKeymark()==null) wkSectionitemPojo.setKeymark(0);
     if(wkSectionitemPojo.getRownum()==null) wkSectionitemPojo.setRownum(0);
     if(wkSectionitemPojo.getRemark()==null) wkSectionitemPojo.setRemark("");
     if(wkSectionitemPojo.getCustom1()==null) wkSectionitemPojo.setCustom1("");
     if(wkSectionitemPojo.getCustom2()==null) wkSectionitemPojo.setCustom2("");
     if(wkSectionitemPojo.getCustom3()==null) wkSectionitemPojo.setCustom3("");
     if(wkSectionitemPojo.getCustom4()==null) wkSectionitemPojo.setCustom4("");
     if(wkSectionitemPojo.getCustom5()==null) wkSectionitemPojo.setCustom5("");
     if(wkSectionitemPojo.getCustom6()==null) wkSectionitemPojo.setCustom6("");
     if(wkSectionitemPojo.getCustom7()==null) wkSectionitemPojo.setCustom7("");
     if(wkSectionitemPojo.getCustom8()==null) wkSectionitemPojo.setCustom8("");
     if(wkSectionitemPojo.getCustom9()==null) wkSectionitemPojo.setCustom9("");
     if(wkSectionitemPojo.getCustom10()==null) wkSectionitemPojo.setCustom10("");
     if(wkSectionitemPojo.getTenantid()==null) wkSectionitemPojo.setTenantid("");
     if(wkSectionitemPojo.getRevision()==null) wkSectionitemPojo.setRevision(0);
     return wkSectionitemPojo;
     }
}
