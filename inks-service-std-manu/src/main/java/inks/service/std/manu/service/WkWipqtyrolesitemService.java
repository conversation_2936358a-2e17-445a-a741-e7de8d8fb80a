package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesitemPojo;

import java.util.List;

/**
 * 过数角色子表(WkWipqtyrolesitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-26 10:08:49
 */
public interface WkWipqtyrolesitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtyrolesitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipqtyrolesitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipqtyrolesitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkWipqtyrolesitemPojo 实例对象
     * @return 实例对象
     */
    WkWipqtyrolesitemPojo insert(WkWipqtyrolesitemPojo wkWipqtyrolesitemPojo);

    /**
     * 修改数据
     *
     * @param wkWipqtyrolesitempojo 实例对象
     * @return 实例对象
     */
    WkWipqtyrolesitemPojo update(WkWipqtyrolesitemPojo wkWipqtyrolesitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkWipqtyrolesitempojo 实例对象
     * @return 实例对象
     */
    WkWipqtyrolesitemPojo clearNull(WkWipqtyrolesitemPojo wkWipqtyrolesitempojo);
}
