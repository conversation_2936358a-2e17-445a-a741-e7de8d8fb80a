package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkCostbudgetPojo;
import inks.service.std.manu.domain.pojo.WkCostbudgetitemdetailPojo;

/**
 * 成本预测(WkCostbudget)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-03 19:16:26
 */
public interface WkCostbudgetService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCostbudgetPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCostbudgetitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkCostbudgetPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCostbudgetPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkCostbudgetPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkCostbudgetPojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetPojo insert(WkCostbudgetPojo wkCostbudgetPojo);

    /**
     * 修改数据
     *
     * @param wkCostbudgetpojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetPojo update(WkCostbudgetPojo wkCostbudgetpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param wkCostbudgetPojo 实例对象
     * @return 实例对象
     */
    WkCostbudgetPojo approval(WkCostbudgetPojo wkCostbudgetPojo);
}
