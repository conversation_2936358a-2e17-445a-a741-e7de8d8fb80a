package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWscarryoveritemEntity;
import inks.service.std.manu.domain.pojo.WkWscarryoveritemPojo;
import inks.service.std.manu.mapper.WkWscarryoveritemMapper;
import inks.service.std.manu.service.WkWscarryoveritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 结转子表(WkWscarryoveritem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-09 10:53:29
 */
@Service("wkWscarryoveritemService")
public class WkWscarryoveritemServiceImpl implements WkWscarryoveritemService {
    @Resource
    private WkWscarryoveritemMapper wkWscarryoveritemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWscarryoveritemPojo getEntity(String key, String tid) {
        return this.wkWscarryoveritemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWscarryoveritemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWscarryoveritemPojo> lst = wkWscarryoveritemMapper.getPageList(queryParam);
            PageInfo<WkWscarryoveritemPojo> pageInfo = new PageInfo<WkWscarryoveritemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkWscarryoveritemPojo> getList(String Pid, String tid) {
        try {
            List<WkWscarryoveritemPojo> lst = wkWscarryoveritemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkWscarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWscarryoveritemPojo insert(WkWscarryoveritemPojo wkWscarryoveritemPojo) {
        //初始化item的NULL
        WkWscarryoveritemPojo itempojo = this.clearNull(wkWscarryoveritemPojo);
        WkWscarryoveritemEntity wkWscarryoveritemEntity = new WkWscarryoveritemEntity();
        BeanUtils.copyProperties(itempojo, wkWscarryoveritemEntity);

        wkWscarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWscarryoveritemEntity.setRevision(1);  //乐观锁
        this.wkWscarryoveritemMapper.insert(wkWscarryoveritemEntity);
        return this.getEntity(wkWscarryoveritemEntity.getId(), wkWscarryoveritemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWscarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWscarryoveritemPojo update(WkWscarryoveritemPojo wkWscarryoveritemPojo) {
        WkWscarryoveritemEntity wkWscarryoveritemEntity = new WkWscarryoveritemEntity();
        BeanUtils.copyProperties(wkWscarryoveritemPojo, wkWscarryoveritemEntity);
        this.wkWscarryoveritemMapper.update(wkWscarryoveritemEntity);
        return this.getEntity(wkWscarryoveritemEntity.getId(), wkWscarryoveritemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWscarryoveritemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkWscarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWscarryoveritemPojo clearNull(WkWscarryoveritemPojo wkWscarryoveritemPojo) {
        //初始化NULL字段
        if (wkWscarryoveritemPojo.getPid() == null) wkWscarryoveritemPojo.setPid("");
        if (wkWscarryoveritemPojo.getGoodsid() == null) wkWscarryoveritemPojo.setGoodsid("");
        if (wkWscarryoveritemPojo.getItemcode() == null) wkWscarryoveritemPojo.setItemcode("");
        if (wkWscarryoveritemPojo.getItemname() == null) wkWscarryoveritemPojo.setItemname("");
        if (wkWscarryoveritemPojo.getItemspec() == null) wkWscarryoveritemPojo.setItemspec("");
        if (wkWscarryoveritemPojo.getItemunit() == null) wkWscarryoveritemPojo.setItemunit("");
        if (wkWscarryoveritemPojo.getOpenqty() == null) wkWscarryoveritemPojo.setOpenqty(0D);
        if (wkWscarryoveritemPojo.getOpenamount() == null) wkWscarryoveritemPojo.setOpenamount(0D);
        if (wkWscarryoveritemPojo.getInqty() == null) wkWscarryoveritemPojo.setInqty(0D);
        if (wkWscarryoveritemPojo.getInamount() == null) wkWscarryoveritemPojo.setInamount(0D);
        if (wkWscarryoveritemPojo.getOutqty() == null) wkWscarryoveritemPojo.setOutqty(0D);
        if (wkWscarryoveritemPojo.getOutamount() == null) wkWscarryoveritemPojo.setOutamount(0D);
        if (wkWscarryoveritemPojo.getCloseqty() == null) wkWscarryoveritemPojo.setCloseqty(0D);
        if (wkWscarryoveritemPojo.getCloseamount() == null) wkWscarryoveritemPojo.setCloseamount(0D);
        if (wkWscarryoveritemPojo.getSkuid() == null) wkWscarryoveritemPojo.setSkuid("");
        if (wkWscarryoveritemPojo.getAttributejson() == null) wkWscarryoveritemPojo.setAttributejson("");
        if (wkWscarryoveritemPojo.getRownum() == null) wkWscarryoveritemPojo.setRownum(0);
        if (wkWscarryoveritemPojo.getCustom1() == null) wkWscarryoveritemPojo.setCustom1("");
        if (wkWscarryoveritemPojo.getCustom2() == null) wkWscarryoveritemPojo.setCustom2("");
        if (wkWscarryoveritemPojo.getCustom3() == null) wkWscarryoveritemPojo.setCustom3("");
        if (wkWscarryoveritemPojo.getCustom4() == null) wkWscarryoveritemPojo.setCustom4("");
        if (wkWscarryoveritemPojo.getCustom5() == null) wkWscarryoveritemPojo.setCustom5("");
        if (wkWscarryoveritemPojo.getCustom6() == null) wkWscarryoveritemPojo.setCustom6("");
        if (wkWscarryoveritemPojo.getCustom7() == null) wkWscarryoveritemPojo.setCustom7("");
        if (wkWscarryoveritemPojo.getCustom8() == null) wkWscarryoveritemPojo.setCustom8("");
        if (wkWscarryoveritemPojo.getCustom9() == null) wkWscarryoveritemPojo.setCustom9("");
        if (wkWscarryoveritemPojo.getCustom10() == null) wkWscarryoveritemPojo.setCustom10("");
        if (wkWscarryoveritemPojo.getTenantid() == null) wkWscarryoveritemPojo.setTenantid("");
        if (wkWscarryoveritemPojo.getRevision() == null) wkWscarryoveritemPojo.setRevision(0);
        return wkWscarryoveritemPojo;
    }
}
