package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkGrouplossratePojo;

/**
 * 车间预损率(WkGrouplossrate)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-16 10:56:19
 */
public interface WkGrouplossrateService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkGrouplossratePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkGrouplossratePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkGrouplossratePojo 实例对象
     * @return 实例对象
     */
    WkGrouplossratePojo insert(WkGrouplossratePojo wkGrouplossratePojo);

    /**
     * 修改数据
     *
     * @param wkGrouplossratepojo 实例对象
     * @return 实例对象
     */
    WkGrouplossratePojo update(WkGrouplossratePojo wkGrouplossratepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
