package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWipqtygroupPojo;
import inks.service.std.manu.domain.pojo.WkWipqtygroupitemdetailPojo;

/**
 * 过数小组(WkWipqtygroup)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-10 11:07:59
 */
public interface WkWipqtygroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtygroupPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipqtygroupitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipqtygroupPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipqtygroupPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWipqtygroupPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWipqtygroupPojo 实例对象
     * @return 实例对象
     */
    WkWipqtygroupPojo insert(WkWipqtygroupPojo wkWipqtygroupPojo);

    /**
     * 修改数据
     *
     * @param wkWipqtygrouppojo 实例对象
     * @return 实例对象
     */
    WkWipqtygroupPojo update(WkWipqtygroupPojo wkWipqtygrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

}
