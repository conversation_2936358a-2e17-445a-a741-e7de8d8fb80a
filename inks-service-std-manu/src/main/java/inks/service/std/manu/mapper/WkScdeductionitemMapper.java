package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkScdeductionitemEntity;
import inks.service.std.manu.domain.pojo.WkScdeductionitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 委制扣款Item(WkScdeductionitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-12 08:46:42
 */
 @Mapper
public interface WkScdeductionitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkScdeductionitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkScdeductionitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkScdeductionitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkScdeductionitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkScdeductionitemEntity wkScdeductionitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkScdeductionitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkScdeductionitemEntity wkScdeductionitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

