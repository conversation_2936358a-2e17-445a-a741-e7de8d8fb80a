package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkSteppricesetPojo;

import java.util.List;

/**
 * 阶梯工价设置(WkSteppriceset)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-26 16:21:54
 */
public interface WkSteppricesetService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSteppricesetPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkSteppricesetPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSteppricesetPojo 实例对象
     * @return 实例对象
     */
    WkSteppricesetPojo insert(WkSteppricesetPojo wkSteppricesetPojo);

    /**
     * 修改数据
     *
     * @param wkSteppricesetpojo 实例对象
     * @return 实例对象
     */
    WkSteppricesetPojo update(WkSteppricesetPojo wkSteppricesetpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    String getSpusByWpid(String wpid, String tid);

    WkSteppricesetPojo getSpusEntityByWpid(String wpid, String tid);

    List<WkSteppricesetPojo> getAllList(String tid);
}
