package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.NoRepeatSubmit;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkWipgroupitemPojo;
import inks.service.std.manu.domain.pojo.WkWipnotePojo;
import inks.service.std.manu.domain.pojo.WkWipnoteitemPojo;
import inks.service.std.manu.domain.pojo.WkWipnoteitemdetailPojo;
import inks.service.std.manu.mapper.WkWipgroupitemMapper;
import inks.service.std.manu.mapper.WkWipnoteMapper;
import inks.service.std.manu.service.WkWipnoteService;
import inks.service.std.manu.service.WkWipnoteitemService;
import inks.service.std.manu.utils.DeepCopyUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;
import static org.apache.commons.lang3.StringUtils.*;

/**
 * WIP记录(Wk_WipNote)表控制层
 *
 * <AUTHOR>
 * @since 2022-01-06 13:57:44
 */
@RestController
@RequestMapping("D05M05B1")
@Api(tags = "D05M05B1:生产WIP")
public class D05M05B1Controller extends WkWipnoteController {
    private final String moduleCode = "D05M05B1";
    @Resource
    private WkWipgroupitemMapper wkWipgroupitemMapper;
    /**
     * 服务对象
     */
    @Resource
    private WkWipnoteService wkWipnoteService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkWipnoteitemService wkWipnoteitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private WkWipnoteMapper wkWipnoteMapper;
    @Resource
    private UtilsFeignService utilsFeignService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询,结存工单（在制+待制）", notes = "参数：wpid=工序id", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnoteitemdetailPojo>> getOnlinePageList(@RequestBody String json, String wpid, String wkwpid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNoteItem.PlanDate");
            String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and (Wk_WipNoteItem.inpcsqty=0 or Wk_WipNoteItem.inpcsqty>Wk_WipNoteItem.outpcsqty+Wk_WipNoteItem.mrbpcsqty)";
            if (wpid != null) {
                qpfilter += " and Wk_WipNoteItem.Wpid='" + wpid + "'";
            }
            if (wkwpid != null) {
                qpfilter += " and Wk_WipNote.WkWpid='" + wkwpid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询,结存工单（在制+待制）getOnlinePageList基础上加入销售订单/加工单的Summary查询", notes = "参数：wpid=工序id", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageListBySummary", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnoteitemdetailPojo>> getOnlinePageListBySummary(@RequestBody String json, String wpid, String wkwpid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNoteItem.PlanDate");
            String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and (Wk_WipNoteItem.inpcsqty=0 or Wk_WipNoteItem.inpcsqty>Wk_WipNoteItem.outpcsqty+Wk_WipNoteItem.mrbpcsqty)";
            if (wpid != null) {
                qpfilter += " and Wk_WipNoteItem.Wpid='" + wpid + "'";
            }
            if (wkwpid != null) {
                qpfilter += " and Wk_WipNote.WkWpid='" + wkwpid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getOnlinePageListBySummary(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询,结存工单（在制+待制）", notes = "参数：wpid=工序id", produces = "application/json")
    @RequestMapping(value = "/getInputOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnoteitemdetailPojo>> getInputOnlinePageList(@RequestBody String json, String wpid, String wkwpid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNoteItem.PlanDate");
            String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and (Wk_WipNoteItem.inpcsqty=0 or (Wk_WipNoteItem.inpcsqty<Wk_WipNote.WkPcsQty - Wk_WipNote.mrbpcsqty and Wk_WipNoteItem.inpcsQty>0))";
            if (wpid != null) {
                qpfilter += " and Wk_WipNoteItem.Wpid='" + wpid + "'";
            }
            if (wkwpid != null) {
                qpfilter += " and Wk_WipNote.WkWpid='" + wkwpid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询,结存工单（在制+待制）", notes = "参数：wpid=工序id", produces = "application/json")
    @RequestMapping(value = "/getOutputOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnoteitemdetailPojo>> getOutputOnlinePageList(@RequestBody String json, String wpid, String wkwpid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNoteItem.PlanDate");
            String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Wk_WipNoteItem.inpcsqty>Wk_WipNoteItem.outpcsqty + Wk_WipNoteItem.mrbpcsqty";
            if (wpid != null) {
                qpfilter += " and Wk_WipNoteItem.Wpid='" + wpid + "'";
            }
            if (wkwpid != null) {
                qpfilter += " and Wk_WipNote.WkWpid='" + wkwpid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param wpid,workuid
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询获取平行出组数据", notes = "按条件分页查询获取平行出组数据", produces = "application/json")
    @RequestMapping(value = "/getOtherItemList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnoteitemdetailPojo>> getOtherItemList(String wpid, String workuid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            int index = workuid.lastIndexOf("_");
            // 字符串中不存在下划线"_"，则返回-1
            if (index == -1) {
                workuid += "_";
            } else {
                workuid = workuid.substring(0, index + 1);
            }
            QueryParam queryParam = new QueryParam();
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            queryParam.setOrderBy("Wk_WipNoteItem.RowNum");
            String qpfilter = " and Wk_WipNote.WkWpid='" + wpid + "'";
            qpfilter += " and Wk_WipNoteItem.Wpid='" + wpid + "'";
            qpfilter += " and Wk_WipNote.WorkUid like '" + workuid + "%'";
            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and (Wk_WipNoteItem.inpcsqty=0 or Wk_WipNoteItem.inpcsqty>Wk_WipNoteItem.outpcsqty+Wk_WipNoteItem.mrbpcsqty)";
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<PageInfo < WkWipnoteitemdetailPojo>>
     * @Description 按条件分页查询获取平行出组数据
     * <AUTHOR>
     * @param[1] wpid 工序id
     * @param[2] workuid 加工单uid
     * @time 2023/3/31 15:44
     */
    @ApiOperation(value = "按条件分页查询获取平行出组数据", notes = "按条件分页查询获取平行出组数据", produces = "application/json")
    @RequestMapping(value = "/getItemListByWpid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnoteitemdetailPojo>> getItemListByWpid(String wpid, String workuid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            int index = workuid.lastIndexOf("_");
            // 字符串中不存在下划线"_"，则返回-1
            if (index == -1) {
                workuid += "_";
            } else {
                workuid = workuid.substring(0, index + 1);
            }
            QueryParam queryParam = new QueryParam();
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            queryParam.setOrderBy("Wk_WipNoteItem.RowNum");
            String qpfilter = " and Wk_WipNote.WkWpid='" + wpid + "'";
            qpfilter += " and Wk_WipNoteItem.Wpid='" + wpid + "'";
            qpfilter += " and Wk_WipNote.WorkUid like '" + workuid + "%'";
            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and (Wk_WipNoteItem.inpcsqty=0 or Wk_WipNoteItem.inpcsqty>Wk_WipNoteItem.outpcsqty+Wk_WipNoteItem.mrbpcsqty)";
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
//    @ApiOperation(value = "按条件分页查询", notes = "参数：wpid 工序id; percent: 有的租户 总投数会默认乘以1.2,统计完成时需要总投数除以1.2 wipgroupid：分管表id", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnotePojo>> getOnlinePageTh(@RequestBody String json, String wpid, Double percent, String wipgroupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNote.WorkRefNo");
            String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            if (percent != null) qpfilter += "/" + percent; //必须紧更上一行的条件 总投数除以1.2

            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            if (isNotBlank(wpid)) {
                qpfilter += " and Wk_WipNote.WkWpid='" + wpid + "'";
            }
            if (isNotBlank(wipgroupid)) {
                qpfilter += " and Wk_WipNote.WipGroupid='" + wipgroupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "快捷领料按条件分页查询", notes = "参数：wpid 工序id", produces = "application/json")
    @RequestMapping(value = "/getMatPageTh", method = RequestMethod.POST)
    public R<PageInfo<WkWipnotePojo>> getMatPageTh(@RequestBody String json, String wpid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNote.CreateDate");
            String qpfilter = " and Wk_WipNote.MatCode!=''";
            if (wpid != null) {
                qpfilter += " and Wk_WipNote.WkWpid='" + wpid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "快捷领料按条件分页查询", notes = "参数：wpid 工序id", produces = "application/json")
    @RequestMapping(value = "/getMatOnlinePageTh", method = RequestMethod.POST)
    public R<PageInfo<WkWipnotePojo>> getMatOnlinePageTh(@RequestBody String json, String wpid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNote.CreateDate");
            String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            qpfilter += " and Wk_WipNote.MatCode!='' and Wk_WipNote.MatUsed=0";
            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            if (wpid != null) {
                qpfilter += " and Wk_WipNote.WkWpid='" + wpid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnotePojo>> getOnlineBillList(@RequestBody String json, String wpid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNote.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            if (wpid != null) {
                qpfilter += " and Wk_WipNote.WkWpid='" + wpid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " ByWorkUid获取WIP记录详细信息", notes = "获取WIP记录详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByWorkUid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<WkWipnotePojo> getBillEntityByWorkUid(String workuid) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWipnoteService.getBillEntityByWorkUid(workuid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增WIP记录", notes = "新增WIP记录", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.Add")
    @NoRepeatSubmit(timeout = 100)
    public R<WkWipnotePojo> create(@RequestBody String json, @RequestParam(required = false) Integer infirst) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            WkWipnotePojo wkWipnotePojo = JSONArray.parseObject(json, WkWipnotePojo.class);
            int mergemark = wkWipnotePojo.getMergemark() == null ? 0 : wkWipnotePojo.getMergemark();
            if (mergemark == 1) return R.fail("被合并加工单子项禁止转WIP");
            WkWipnotePojo dbPojo = this.wkWipnoteService.getEntityByWorkUid(wkWipnotePojo.getWorkuid(), tid);
            if (dbPojo != null) {
                return R.fail(wkWipnotePojo.getWorkuid() + "单据已导入WIP,禁止重复导入");
            }
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Wk_WipNote", null, loginUser.getTenantid());
            wkWipnotePojo.setRefno(refno);
            wkWipnotePojo.setCreateby(loginUser.getRealname());   // 创建者
            wkWipnotePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkWipnotePojo.setCreatedate(new Date());   // 创建时间
            wkWipnotePojo.setLister(loginUser.getRealname());   // 制表
            wkWipnotePojo.setListerid(loginUser.getUserid());    // 制表id
            wkWipnotePojo.setModifydate(new Date());   //修改时间
            wkWipnotePojo.setTenantid(tid);   //租户id
            wkWipnotePojo.setItemcount(wkWipnotePojo.getItem().size());
            if (infirst == null) infirst = 0;
            WkWipnotePojo insertDB = this.wkWipnoteService.insert(wkWipnotePojo, infirst);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok(insertDB);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 批量新增WIP记录", notes = "新增WIP记录", produces = "application/json")
    @RequestMapping(value = "/createBatch", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.Add")
    public R<String> createBatch(@RequestBody String json, @RequestParam(required = false) Integer infirst) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            if (infirst == null) infirst = 0;
            List<WkWipnotePojo> wkWipnotePojoList = JSONArray.parseArray(json, WkWipnotePojo.class);
            if (CollectionUtils.isEmpty(wkWipnotePojoList)) return R.fail("传入数据为空");
            // 批量生成单据编码size()个  ---->refNoList
            List<String> refNoList;
            R r = systemFeignService.getBillCodeList("D05M05B1", wkWipnotePojoList.size(), loginUser.getToken());
            if (r.getCode() == 200) {
                refNoList = (List<String>) r.getData();
            } else {
                return R.fail("单据编码读取出错" + r);
            }
            for (WkWipnotePojo wkWipnotePojo : wkWipnotePojoList) {
                int mergemark = wkWipnotePojo.getMergemark() == null ? 0 : wkWipnotePojo.getMergemark();
                if (mergemark == 1) return R.fail("被合并加工单子项禁止转WIP");

                WkWipnotePojo dbPojo = this.wkWipnoteService.getEntityByWorkUid(wkWipnotePojo.getWorkuid(), loginUser.getTenantid());
                if (dbPojo != null) {
                    return R.fail(wkWipnotePojo.getWorkuid() + "单据已导入WIP,禁止重复导入");
                }
                //设置单据编码
                wkWipnotePojo.setRefno(refNoList.get(wkWipnotePojoList.indexOf(wkWipnotePojo)));
                wkWipnotePojo.setCreateby(loginUser.getRealname());   // 创建者
                wkWipnotePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                wkWipnotePojo.setCreatedate(new Date());   // 创建时间
                wkWipnotePojo.setLister(loginUser.getRealname());   // 制表
                wkWipnotePojo.setListerid(loginUser.getUserid());    // 制表id
                wkWipnotePojo.setModifydate(new Date());   //修改时间
                wkWipnotePojo.setTenantid(loginUser.getTenantid());   //租户id
                wkWipnotePojo.setItemcount(wkWipnotePojo.getItem().size());
                this.wkWipnoteService.insert(wkWipnotePojo, infirst);
            }
            return R.ok("成功导入" + wkWipnotePojoList.size() + "条数据");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "生产加工单转WIP流程ByMI", notes = "", produces = "application/json")
    @RequestMapping(value = "/batchCreateByWsMi", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.Add")
    public R<WkWipnotePojo> batchCreateByWsMi(@RequestBody String jsonArrayString, @RequestParam(required = false) Integer infirst) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            // 将JSON数组字符串转为List<WkWipnotePojo>
            List<WkWipnotePojo> wkWipnoteList = JSONArray.parseArray(jsonArrayString, WkWipnotePojo.class);
            for (WkWipnotePojo wkWipnotePojo : wkWipnoteList) {
                WkWipnotePojo dbPojo = this.wkWipnoteService.getEntityByWorkUid(wkWipnotePojo.getWorkuid(), tid);
                if (dbPojo != null) {
                    return R.fail(wkWipnotePojo.getWorkuid() + "单据已导入WIP,禁止重复导入");
                }
                // 生成单据编码RefNoUtils
                String refno = RefNoUtils.generateRefNo(moduleCode, "Wk_WipNote", null, loginUser.getTenantid());
                wkWipnotePojo.setRefno(refno);
                wkWipnotePojo.setCreateby(loginUser.getRealname());   // 创建者
                wkWipnotePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                wkWipnotePojo.setCreatedate(new Date());   // 创建时间
                wkWipnotePojo.setLister(loginUser.getRealname());   // 制表
                wkWipnotePojo.setListerid(loginUser.getUserid());    // 制表id
                wkWipnotePojo.setModifydate(new Date());   //修改时间
                wkWipnotePojo.setTenantid(tid);   //租户id
                // 查询goodsid对应的Pcb工艺子表(Mat_SpecPcbItem)的工序List,根据工序表(Wk_Process)只要post!=0的工序(0免过1过数2随过),加入到WkWipnotePojo的item中
                List<WkWipnoteitemPojo> wkWipnoteitemPojoList = this.wkWipnoteService.getSpecPcbItemListByGoodsid(wkWipnotePojo.getGoodsid(), tid);
                // 判空
                if (CollectionUtils.isEmpty(wkWipnoteitemPojoList)) {
                    return R.fail("未找到货品关联的工艺下工序(Goodsid:" + wkWipnotePojo.getGoodsid() + ")");
                }

                // 20240813 会传入分管表id,查出分管子表的工序List;和wkWipnoteitemPojoList对比取二者交集(wpid同时有的留下)
                String wipgroupid = wkWipnotePojo.getWipgroupid();
                if (isNotBlank(wipgroupid)) {
                    List<WkWipgroupitemPojo> wipgroupitemList = wkWipgroupitemMapper.getList(wipgroupid, tid);
                    // 提取wipgroupitemList中的wpid列表
                    List<String> wipIdsFromGroup = wipgroupitemList.stream()
                            .map(WkWipgroupitemPojo::getWpid)
                            .collect(Collectors.toList());
                    // 取交集
                    //wkWipnoteitemPojoList.removeIf(item -> !wipIdsFromGroup.contains(item.getWpid()));
                    // 使用 Iterator 进行安全删除
                    wkWipnoteitemPojoList.removeIf(item -> item == null || item.getWpid() == null || !wipIdsFromGroup.contains(item.getWpid()));
                }

                // 手动设置RowNum字段，从1开始递增
                int rowNum = 1;
                for (WkWipnoteitemPojo wkWipnoteitemPojo : wkWipnoteitemPojoList) {
                    wkWipnoteitemPojo.setRownum(rowNum++);
                }
                wkWipnotePojo.setItem(wkWipnoteitemPojoList);
                wkWipnotePojo.setItemcount(wkWipnotePojo.getItem().size());
                if (infirst == null) infirst = 0;
                this.wkWipnoteService.insert(wkWipnotePojo, infirst);
                RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            }
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //3. 生产工序之间工序差额预警(前道工序和后道工序差额超过3%时工序红色预警)
    //
    //解决方案：生产WIP加入生产结算功能
    //
    //生产完工后将生产WIP子表中出入组数量同时为0的的工序记录删除
    //按照过数时间重新排序
    //排序完成后，前到工序的出组数量，写入到下一个工序的入组数量
    //根据出入组的差额进行工序预警，超过3%即预警红色
    @ApiOperation(value = "WIP生产结算功能(生产完工后将生产WIP子表中出入组数量同时为0的工序记录删除,按照过数时间结束日期重新排序,排序完成后，前到工序的出组数量，写入到下一个工序的入组数量,根据出入组的差额进行工序预警，超过3%即预警红色)", notes = "", produces = "application/json")
    @RequestMapping(value = "/wipSettlement", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.Add")
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public R<WkWipnotePojo> wipSettlement(String key) { // key: Wk_WipNot.id
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            Date now = new Date();
            // 获取原始的WIP子表数据
            List<WkWipnoteitemPojo> wipnoteitemList = wkWipnoteitemService.getList(key, loginUser.getTenantid());


            // 步骤 0: 校验是否所有记录的InPcsQty和OutPcsQty都为0
            boolean allZeroQuantities = wipnoteitemList.stream().allMatch(item -> item.getInpcsqty() == 0 && item.getOutpcsqty() == 0);

            if (allZeroQuantities) {
                // 如果所有记录的Inpcsqty和Outpcsqty都为0，抛出异常禁止结算
                throw new BaseBusinessException("禁止结算,出入组数量都为0");
            }

            // 步骤 1: 删除出入组数量同时为0的工序记录
            List<String> deletedIds = new ArrayList<>();
            // 找到要删除的记录并保存 id
            wipnoteitemList.removeIf(item -> {
                boolean shouldRemove = item.getInpcsqty() == 0 && item.getOutpcsqty() == 0;
                if (shouldRemove) {
                    deletedIds.add(item.getId());
                }
                return shouldRemove;
            });
            // 执行删除操作
            if (!deletedIds.isEmpty()) {
                wkWipnoteitemService.batchDeleteByIds(deletedIds);
            }

            // 步骤 2: 按照结束日期时间重新排序 (升序)
            wipnoteitemList.sort(Comparator.comparing(WkWipnoteitemPojo::getEnddate));

            // 步骤 3: 遍历排序后的WIP子表，更新入组数量和rownum
            int rownum = 1; // 从1开始递增的rownum
            for (int i = 0; i < wipnoteitemList.size(); i++) {
                // 更新rownum字段 modifydate字段
                wipnoteitemList.get(i).setRownum(rownum);
                wipnoteitemList.get(i).setModifydate(now);
                // 将前一工序的出组数量写入到下一个工序的入组数量
                if (i > 0) {
                    WkWipnoteitemPojo preItem = wipnoteitemList.get(i - 1);
                    wipnoteitemList.get(i).setInpcsqty(preItem.getOutpcsqty());
                }
                rownum++;
            }
            // 执行更新操作 只update3个字段Byid: outpcsqty, rownum, modifydate
            for (WkWipnoteitemPojo wkWipnoteitemPojo : wipnoteitemList) {
//                wkWipnoteitemService.update3Field(wkWipnoteitemPojo);
                wkWipnoteitemService.update(wkWipnoteitemPojo);
            }

            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "WIP:预累加实际投料数Wk_WipNote.WkPcsQty (addqty:本次想要累加的数量)", notes = "", produces = "application/json")
    @RequestMapping(value = "/wipSyncWkPcsQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_WipNote.Add")
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public R<WkWipnotePojo> wipSyncWkPcsQty(String wipitemid, Double addqty) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //此wipitemid已出组的数量
            wkWipnoteMapper.wipSyncWkPcsQty(wipitemid, addqty, loginUser.getTenantid());
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.Print")
    public void printOnlinePageList(@RequestBody String json, String wpid, String ptid) throws IOException {

        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Wk_WipNoteItem.PlanDate");
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
        qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
        qpfilter += " and (Wk_WipNoteItem.inpcsqty=0 or Wk_WipNoteItem.inpcsqty>Wk_WipNoteItem.outpcsqty+Wk_WipNoteItem.mrbpcsqty)";
        if (wpid != null) {
            qpfilter += " and Wk_WipNoteItem.Wpid='" + wpid + "'";
        }
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<WkWipnoteitemdetailPojo> lst = this.wkWipnoteService.getPageList(queryParam).getList();


        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkWipnoteitemdetailPojo wkWipnoteitemPojo = new WkWipnoteitemdetailPojo();
                    lst.add(wkWipnoteitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 打印单据
     *
     * @param json 实体 List<WkWipnotePojo>
     * @return 编辑结果
     */
    @ApiOperation(value = "批量云打印报表(List<WkWipnotePojo>不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Wk_WipNote.Print")
    public R<String> printWebList(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表！");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //获取单据信息
            List<WkWipnotePojo> lstitem = JSONArray.parseArray(json, WkWipnotePojo.class);
//            WkWipnotePojo wkWipnotePojo = this.wkWipnoteService.getEntity(json, loginUser.getTenantid());
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "wip批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @Description 批量普通打印报表(List < WkWipnotePojo >)
     * <AUTHOR>
     * @param[1] json
     * @param[2] ptid
     * @time 2023/6/12 16:16
     */
    @ApiOperation(value = "批量普通打印报表(List<WkWipnotePojo>不带item)", notes = "", produces = "application/json")
    @RequestMapping(value = "/printList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.Print")
    public void printList(@RequestBody String json, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        map.put("printer", loginUser.getRealname());
        map.put("printdate", org.apache.http.client.utils.DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

        //=========获取单据Item信息========
        List<WkWipnotePojo> lst = JSONArray.parseArray(json, WkWipnotePojo.class);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkWipnotePojo wkWipnotePojo = new WkWipnotePojo();
                    lst.add(wkWipnotePojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改WIP记录Item", notes = "修改WIP记录Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.Edit")
    public R<WkWipnoteitemPojo> updateItem(@RequestBody String json) {
        try {
            WkWipnoteitemPojo wkWipnoteitemPojo = JSONArray.parseObject(json, WkWipnoteitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            wkWipnoteitemPojo.setLister(loginUser.getRealname());   // 制表
            // wkWipnoteitemPojo.setListerid(loginUser.getUserid());    // 制表id
            wkWipnoteitemPojo.setModifydate(new Date());   //修改时间
            wkWipnoteitemPojo.setTenantid(loginUser.getTenantid());   //租户id
            // wkWipnoteitemPojo.setItemcount(wkWipnoteitemPojo.getItem().size());
            wkWipnoteitemPojo = this.wkWipnoteitemService.update(wkWipnoteitemPojo);
            if (!wkWipnoteitemPojo.getPid().isEmpty()) {
                WkWipnotePojo wipnotePojo = this.wkWipnoteService.getEntity(wkWipnoteitemPojo.getPid(), loginUser.getTenantid());
                // 是否最后一工序
                if (wipnotePojo != null && wipnotePojo.getItemcount() == wkWipnoteitemPojo.getRownum()) {
                    wipnotePojo.setPlandate(wkWipnoteitemPojo.getPlandate());
                    this.wkWipnoteService.update(wipnotePojo);
                }
            }
            return R.ok(wkWipnoteitemPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<String>
     * @Description 拆分WipNote
     * <AUTHOR>
     * @param[1] id WipNote.id
     * @param[2] wkrownum  WipNote.WkRowNum当前工序行号
     * @param[3] qty 拆分数量
     * @time 2023/4/19 13:26
     */
    @ApiOperation(value = "拆分WipNote", notes = "拆分WipNote", produces = "application/json")
    @RequestMapping(value = "/split", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.Add")
    public R<String> split(@RequestBody String json) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String id = jsonObject.getString("id");
            Integer wkrownum = jsonObject.getInteger("wkrownum");
            Double qty = jsonObject.getDouble("qty");
            Double secqty = jsonObject.getDouble("secqty"); // 新增secqty
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 获取WipNote主子表DB数据
            WkWipnotePojo wipOrg = wkWipnoteService.getBillEntity(id, loginUser.getTenantid());
            List<WkWipnoteitemPojo> wipnoteitemListOrg = wipOrg.getItem();
            WkWipnoteitemPojo wipItemOrg = wipnoteitemListOrg.stream()
                    .filter(item -> item.getRownum().equals(wkrownum))
                    .findFirst().get();
            // 拆分的新wip主子表 （深拷贝）
            WkWipnotePojo wipNew = DeepCopyUtil.deepCopy(wipOrg);
            //WkWipnoteitemPojo wipItemNew = DeepCopyUtil.deepCopy(wipItemOrg);

            // 1. 老WipNote主表的总投数WkPcsQty、WkSecQty减去拆分的数量并更新
            wipOrg.setWkpcsqty(wipOrg.getWkpcsqty() - qty);
            wipOrg.setWksecqty(wipOrg.getWksecqty() - secqty);

            // 2. 老WipNote子表的入组数InPcsQty、InSecQty减去拆分的数量并更新
            wipItemOrg.setInpcsqty(wipItemOrg.getInpcsqty() - qty);
            wipItemOrg.setInsecqty(wipItemOrg.getInsecqty() - secqty);
            this.wkWipnoteitemService.update(wipItemOrg);

            // 3. 克隆WipNote主子表(主表生成新WorkUid,Quantity=secqty,WkPcsQty=拆分的数量
            String newWorkUid = findMaxWorkuid(wipOrg.getWorkuid(), loginUser.getTenantid());
            wipNew.setWorkuid(newWorkUid);
            wipNew.setQuantity(0D);
            wipNew.setWkpcsqty(qty);
            wipNew.setWksecqty(secqty);

            // 3.2 拆单的newWorkUid赋值给原单SubUid
            String subuidOrg = wipOrg.getSubuid();
            wipOrg.setSubuid(isBlank(subuidOrg) ? newWorkUid : subuidOrg + "," + newWorkUid);// 检查 Subuid 是否为空，并根据情况决定是否加逗号
            this.wkWipnoteService.update(wipOrg);


            // 获取传入的工序行号以及之后的所有工序
            List<WkWipnoteitemPojo> wipnoteitemListNew = wipnoteitemListOrg.stream()
                    .filter(item -> item.getRownum() >= wkrownum)
                    .collect(Collectors.toList());
            wipnoteitemListNew.get(0).setInpcsqty(qty);
            wipnoteitemListNew.get(0).setInsecqty(secqty);
            wipNew.setItem(wipnoteitemListNew);

            // 4. 保存新WipNote主子表
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Wk_WipNote", null, loginUser.getTenantid());
            wipOrg.setRefno(refno);
            wipNew.setBilldate(new Date());
            wipNew.setCreateby(loginUser.getRealname());
            wipNew.setCreatebyid(loginUser.getUserid());
            wipNew.setCreatedate(new Date());
            wipNew.setLister(loginUser.getRealname());
            wipNew.setListerid(loginUser.getUserid());
            wipNew.setModifydate(new Date());
            wipNew.setTenantid(loginUser.getTenantid());
            wipNew.setItemcount(wipNew.getItem().size());
            this.wkWipnoteService.insert(wipNew, 0);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //获取新的WorkUid 原加工单号格式：BM-2023-04-0073_1
    private String findMaxWorkuid(String workuid, String tid) {
        // 在workuid后面加上下划线，再进行like查询
        String workuid_ = workuid + "_";
        String maxWorkUid = wkWipnoteMapper.getByWorkuidLike(workuid_, tid);
        //查不到,默认加后缀1 ; 查到了,累加1
        int newNumber = 1;
        if (isNotBlank(maxWorkUid)) {
            String numString = maxWorkUid.substring(maxWorkUid.lastIndexOf("_") + 1);
            newNumber = Integer.parseInt(numString) + 1;
        }
        return workuid + "_" + newNumber;
    }

    /**
     * @return R<WkWorksheetitemdetailPojo>
     * @Description 合并销售订单子项物料转为WipNote
     * <AUTHOR>
     * @param[1] json  List<WkWipnotePojo>
     * @param[2] inFirst 是否首工序入组
     * @param[3] progroupid 生产制程id
     * @time 2023/6/13 14:11
     */
    @ApiOperation(value = "合并销售订单子项物料转为WipNote", notes = "json  List<WkWipnotePojo>;inFirst 是否首工序入组;progroupid 生产制程id", produces = "application/json")
    @RequestMapping(value = "/mergeItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_WipNote.Merge")
    public R<WkWipnotePojo> mergeItem(@RequestBody String json, int inFirst, String progroupid) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<WkWipnotePojo> lstitem = JSONArray.parseArray(json, WkWipnotePojo.class);
            WkWipnotePojo wkWipnotePojo = wkWipnoteService.mergeItem(lstitem, inFirst, progroupid, loginUser);
            return R.ok(wkWipnotePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//--------------------------------------------------------------------以下为Scm用户查询-----------------------------------------------------------------------------------------

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "Scm按条件分页查询,结存工单（在制+待制）", notes = "参数：wpid=工序id", produces = "application/json")
    @RequestMapping(value = "/getScmOnlinePageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Wk_WipNote.List")
    public R<PageInfo<WkWipnoteitemdetailPojo>> getScmOnlinePageList(@RequestBody String json, String wpid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNoteItem.PlanDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and (Wk_WipNoteItem.inpcsqty=0 or Wk_WipNoteItem.inpcsqty>Wk_WipNoteItem.outpcsqty+Wk_WipNoteItem.mrbpcsqty)";
            if (wpid != null) {
                qpfilter += " and Wk_WipNoteItem.Wpid='" + wpid + "'";
            }
            // 过滤Scm用户的Groupids
            qpfilter += " and Wk_WipNote.MachGroupid in (" + loginUser.getGroupids() + ")";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "参数：wpid 工序id", produces = "application/json")
    @RequestMapping(value = "/getScmOnlinePageTh", method = RequestMethod.POST)
    public R<PageInfo<WkWipnotePojo>> getScmOnlinePageTh(@RequestBody String json, String wpid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNote.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = " and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty<Wk_WipNote.WkPcsQty";
            qpfilter += " and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0 ";  // 未关闭、未注销
            if (wpid != null) {
                qpfilter += " and Wk_WipNote.WkWpid='" + wpid + "'";
            }
            // 过滤Scm用户的Groupids
            qpfilter += " and Wk_WipNote.MachGroupid in (" + loginUser.getGroupids() + ")";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getScmPageTh", method = RequestMethod.POST)
    public R<PageInfo<WkWipnotePojo>> getScmPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_WipNote.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 过滤Scm用户的Groupids
            qpfilter += " and Wk_WipNote.MachGroupid in (" + loginUser.getGroupids() + ")";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkWipnoteService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取WIP记录详细信息", notes = "获取WIP记录详细信息", produces = "application/json")
    @RequestMapping(value = "/getScmBillEntity", method = RequestMethod.GET)
    public R<WkWipnotePojo> getScmBillEntity(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWipnoteService.getScmBillEntity(key, loginUser.getGroupids(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    // 用来一次性刷新租户下所有WipNote表的AttributeStr(本地连接公网数据库刷新)
//    @RequestMapping(value = "/updateAttrStr", method = RequestMethod.GET)
//    public int updateAttrStr() {
//        String gongShi="${spuchang}*${spukuan}*${spuhou}*${spuwaijing}*${spunajing}*${spugaodu}";
//        String tid="ceb5ae04-ea5f-4ba8-81af-0c8ec1ad112c";
////        String tid="b842c7ca-a02b-4dc6-af43-e4d3e84af592";
////        String tid="12138";
//        // id,AttributeJson
//        List<Map<String,String>> lst = this.wkWipnoteMapper.getAllByTid(tid);
//        int count=0;
//        for (Map<String, String> map : lst) {
//            String id = map.get("id");
//            String attributejson = map.get("AttributeJson");
//            System.out.println("id = " + id);
//            System.out.println("attributejson = " + attributejson);
//
//            if (StringUtils.isNotBlank(attributejson)) {
//                String attrStr = BeanUtils.calculateAttrStr(attributejson, gongShi);
//                int i = this.wkWipnoteMapper.upateAttrStr(id, attrStr, tid);
//                count+=i;
//            }
//        }
//        System.out.println("共刷新: " + count);
//        return count;
//    }

}
