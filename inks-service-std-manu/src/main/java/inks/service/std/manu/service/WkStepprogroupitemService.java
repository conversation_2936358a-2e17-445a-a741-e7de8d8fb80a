package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkStepprogroupitemPojo;

import java.util.List;

/**
 * 阶梯项目(WkStepprogroupitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-01 16:33:50
 */
public interface WkStepprogroupitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkStepprogroupitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkStepprogroupitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkStepprogroupitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkStepprogroupitemPojo 实例对象
     * @return 实例对象
     */
    WkStepprogroupitemPojo insert(WkStepprogroupitemPojo wkStepprogroupitemPojo);

    /**
     * 修改数据
     *
     * @param wkStepprogroupitempojo 实例对象
     * @return 实例对象
     */
    WkStepprogroupitemPojo update(WkStepprogroupitemPojo wkStepprogroupitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkStepprogroupitempojo 实例对象
     * @return 实例对象
     */
    WkStepprogroupitemPojo clearNull(WkStepprogroupitemPojo wkStepprogroupitempojo);
}
