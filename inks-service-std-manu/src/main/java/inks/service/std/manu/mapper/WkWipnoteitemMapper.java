package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipnoteitemEntity;
import inks.service.std.manu.domain.pojo.WkWipnoteitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Wip记录子表(WkWipnoteitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-26 10:08:23
 */
@Mapper
public interface WkWipnoteitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipnoteitemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipnoteitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkWipnoteitemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param wkWipnoteitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipnoteitemEntity wkWipnoteitemEntity);


    /**
     * 修改数据
     *
     * @param wkWipnoteitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipnoteitemEntity wkWipnoteitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);


    /**
     * 查询 所有Item
     *
     * @param Wpid 筛选条件
     * @return 查询结果
     */
    List<WkWipnoteitemPojo> getListByWpid(@Param("Wpid") String Wpid, @Param("Pid") String Pid, @Param("tid") String tid);

    /**
     * 查询 所有Item
     *
     * @param Wpid 筛选条件
     * @return 查询结果
     */
    List<WkWipnoteitemPojo> getRemListByWpid(@Param("Wpid") String Wpid, @Param("Pid") String Pid, @Param("qty") Double qty, @Param("tid") String tid);

    List<WkWipnoteitemPojo> getRemListByWpid2(@Param("Wpid") String Wpid, @Param("Pid") String Pid, @Param("qty") Double qty, @Param("tid") String tid);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    WkWipnoteitemPojo getEntityByRownum(@Param("Rownum") Integer Rownum, @Param("Pid") String Pid, @Param("tid") String tid);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    WkWipnoteitemPojo getPrevPostEntityByRownum(@Param("Rownum") Integer Rownum, @Param("Pid") String Pid, @Param("tid") String tid);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    WkWipnoteitemPojo getNextPostEntityByRownum(@Param("Rownum") Integer Rownum, @Param("Pid") String Pid, @Param("tid") String tid);



    int updateInQty(@Param("key") String key, @Param("pcsqty") Double pcsqty, @Param("secqty") Double secqty,
                    @Param("startdate") Date startdate, @Param("inworker") String inworker, @Param("tid") String tid);


    int updateOutQty(@Param("key") String key, @Param("pcsqty") Double pcsqty, @Param("secqty") Double secqty,
                     @Param("enddate") Date enddate, @Param("tid") String tid);

    int updateMrbQty(String key, String tid);

    int updateJobPcsQty(@Param("itemid") String key, @Param("pid") String pid, @Param("tid") String tid);

    int updateOutQtyAndOutWorker(@Param("key") String key, @Param("pcsqty") Double pcsqty, @Param("secqty") Double secqty,
                                 @Param("enddate") Date enddate, @Param("outworker") String outworker, @Param("tid") String tid);


    int updateStartDate(@Param("key") String key, @Param("startdate") Date date, @Param("tid") String tid);

    int hasNextByRownum(@Param("Rownum") Integer rownum, @Param("Pid") String wipid, @Param("tid") String tid);

    void updateWipItemRowNum(@Param("maxWipItemId") String maxWipItemId, @Param("maxWipItemRowNum") int maxWipItemRowNum, @Param("tid") String tid);

    int update3Field(@Param("wkWipnoteitemPojo") WkWipnoteitemPojo wkWipnoteitemPojo);

    int batchDeleteByIds(@Param("deletedIds") List<String> deletedIds);

    int checkKeyMark(@Param("wpid") String wpid, @Param("tid") String tid);

    int countWipItemWpid(@Param("wpid") String wpid, @Param("wipid") String wipid, @Param("tid") String tid);

    int updateStartdateAndInworker(WkWipnoteitemPojo wkWipnoteitemPojo);
}

