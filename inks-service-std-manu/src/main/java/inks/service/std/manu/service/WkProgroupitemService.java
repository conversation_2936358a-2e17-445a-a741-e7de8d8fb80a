package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkProgroupitemPojo;

import java.util.List;

/**
 * 工序清单(WkProgroupitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-26 10:05:55
 */
public interface WkProgroupitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkProgroupitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkProgroupitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkProgroupitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkProgroupitemPojo 实例对象
     * @return 实例对象
     */
    WkProgroupitemPojo insert(WkProgroupitemPojo wkProgroupitemPojo);

    /**
     * 修改数据
     *
     * @param wkProgroupitempojo 实例对象
     * @return 实例对象
     */
    WkProgroupitemPojo update(WkProgroupitemPojo wkProgroupitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkProgroupitempojo 实例对象
     * @return 实例对象
     */
    WkProgroupitemPojo clearNull(WkProgroupitemPojo wkProgroupitempojo);
}
