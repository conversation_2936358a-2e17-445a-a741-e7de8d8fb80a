package inks.service.std.manu.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkProcessPojo;
import inks.service.std.manu.service.WkProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 生产工序(Wk_Process)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:42:48
 */
@RestController
@RequestMapping("D05M21S1")
@Api(tags = "D05M21S1:生产工序")
public class D05M21S1Controller extends WkProcessController {

    @Resource
    private TokenService tokenService;
    @Resource
    private WkProcessService wkProcessService;

    @ApiOperation(value = "Post=1的工序", notes = "", produces = "application/json")
    @RequestMapping(value = "/getPostList", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Wk_Process.List")
    public R<List<WkProcessPojo>> getPostList() {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkProcessService.getPostList(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     * esay poi导入导出 时间2021-12-13 song
     * */
    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        List<WkProcessPojo> list = new ArrayList<>();
        //创建表格
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("生产工序信息", ""),
                WkProcessPojo.class, list);
        try {
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "生产工序模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<WkProcessPojo>> importExecl(MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<WkProcessPojo> list = POIUtil.importExcel(file.getInputStream(), WkProcessPojo.class, importParams);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "识别编码", notes = "识别编码", produces = "application/json")
    @RequestMapping(value = "/getEntityByWpName", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Process.List")
    public R<WkProcessPojo> getEntityByWpName(@RequestBody String json) {
        try {
            WkProcessPojo processPojo = JSONArray.parseObject(json, WkProcessPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            WkProcessPojo wkProcessPojoDB = wkProcessService.getEntityByWpName(processPojo.getWpname(), loginUser.getTenantid());
            return R.ok(wkProcessPojoDB);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "导入工序信息", notes = "导入货品信息", produces = "application/json")
    @RequestMapping(value = "/importEntity", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Process.Add")
    public R<WkProcessPojo> importEntity(@RequestBody String json) {
        try {
            WkProcessPojo processPojo = JSONArray.parseObject(json, WkProcessPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            //查询货品信息是否存在
            WkProcessPojo pojo = wkProcessService.getEntityByWpName(processPojo.getWpname(), loginUser.getTenantid());
            //如果存在不做操作
            if (pojo != null) {
                return R.ok(pojo);
            } else {
                processPojo.setCreateby(loginUser.getRealname());   //创建者
                processPojo.setCreatedate(new Date());   //创建时间
                processPojo.setLister(loginUser.getRealname());   //用户名
                processPojo.setModifydate(new Date());   //修改时间
                processPojo.setTenantid(loginUser.getTenantid());   //租户id
                return R.ok(wkProcessService.insert(processPojo));
            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
