package inks.service.std.manu.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesPojo;
import inks.service.std.manu.domain.pojo.WkWipqtyrolesitemdetailPojo;
import inks.service.std.manu.service.WkWipqtyrolesService;
import inks.service.std.manu.service.WkWipqtyrolesitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 过数角色(Wk_WipQtyRoles)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:53:26
 */
@RestController
@RequestMapping("D05M21S4")
@Api(tags = "D05M21S4:过数角色")
public class D05M21S4Controller extends WkWipqtyrolesController {
    @Resource
    private WkWipqtyrolesService wkWipqtyrolesService;
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取过数角色详细信息", notes = "获取过数角色详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByUserid", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Wk_WipQtyRoles.List")
    public R<WkWipqtyrolesPojo> getBillEntityByUserid(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.wkWipqtyrolesService.getBillEntityByUserid(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getListByWpid", method = RequestMethod.GET)
    //  @PreAuthorize(hasPermi = "Wk_WipQtyRoles.List")
    public R<List<WkWipqtyrolesPojo>> getListByWpid(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkWipqtyrolesService.getListByWpid(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }



}
