package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSccompleteEntity;
import inks.service.std.manu.domain.pojo.WkSccompletePojo;
import inks.service.std.manu.domain.pojo.WkSccompleteitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 委制验收(WkSccomplete)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-09 12:50:11
 */
@Mapper
public interface WkSccompleteMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccompletePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSccompleteitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSccompletePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkSccompleteEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSccompleteEntity wkSccompleteEntity);


    /**
     * 修改数据
     *
     * @param wkSccompleteEntity 实例对象
     * @return 影响行数
     */
    int update(WkSccompleteEntity wkSccompleteEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param wkSccompletePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(WkSccompletePojo wkSccompletePojo);

    /**
     * 修改数据
     *
     * @param wkSccompleteEntity 实例对象
     * @return 影响行数
     */
    int approval(WkSccompleteEntity wkSccompleteEntity);

    List<String> getDelMatIds(WkSccompletePojo wkSccompletePojo);

    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    void updatePrintcount(WkSccompletePojo billPrintPojo);

    void updateDisannulCount(String key, String tid);
}

