package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkSccosttypeEntity;
import inks.service.std.manu.domain.pojo.WkSccosttypePojo;
import inks.service.std.manu.mapper.WkSccosttypeMapper;
import inks.service.std.manu.service.WkSccosttypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 委外费用类型(WkSccosttype)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-13 15:12:07
 */
@Service("wkSccosttypeService")
public class WkSccosttypeServiceImpl implements WkSccosttypeService {
    @Resource
    private WkSccosttypeMapper wkSccosttypeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkSccosttypePojo getEntity(String key, String tid) {
        return this.wkSccosttypeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkSccosttypePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkSccosttypePojo> lst = wkSccosttypeMapper.getPageList(queryParam);
            PageInfo<WkSccosttypePojo> pageInfo = new PageInfo<WkSccosttypePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkSccosttypePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccosttypePojo insert(WkSccosttypePojo wkSccosttypePojo) {
        //初始化NULL字段
        if (wkSccosttypePojo.getItemcode() == null) wkSccosttypePojo.setItemcode("");
        if (wkSccosttypePojo.getItemname() == null) wkSccosttypePojo.setItemname("");
        if (wkSccosttypePojo.getRownum() == null) wkSccosttypePojo.setRownum(0);
        if (wkSccosttypePojo.getRemark() == null) wkSccosttypePojo.setRemark("");
        if (wkSccosttypePojo.getCreateby() == null) wkSccosttypePojo.setCreateby("");
        if (wkSccosttypePojo.getCreatebyid() == null) wkSccosttypePojo.setCreatebyid("");
        if (wkSccosttypePojo.getCreatedate() == null) wkSccosttypePojo.setCreatedate(new Date());
        if (wkSccosttypePojo.getLister() == null) wkSccosttypePojo.setLister("");
        if (wkSccosttypePojo.getListerid() == null) wkSccosttypePojo.setListerid("");
        if (wkSccosttypePojo.getModifydate() == null) wkSccosttypePojo.setModifydate(new Date());
        if (wkSccosttypePojo.getCustom1() == null) wkSccosttypePojo.setCustom1("");
        if (wkSccosttypePojo.getCustom2() == null) wkSccosttypePojo.setCustom2("");
        if (wkSccosttypePojo.getCustom3() == null) wkSccosttypePojo.setCustom3("");
        if (wkSccosttypePojo.getCustom4() == null) wkSccosttypePojo.setCustom4("");
        if (wkSccosttypePojo.getCustom5() == null) wkSccosttypePojo.setCustom5("");
        if (wkSccosttypePojo.getCustom6() == null) wkSccosttypePojo.setCustom6("");
        if (wkSccosttypePojo.getCustom7() == null) wkSccosttypePojo.setCustom7("");
        if (wkSccosttypePojo.getCustom8() == null) wkSccosttypePojo.setCustom8("");
        if (wkSccosttypePojo.getCustom9() == null) wkSccosttypePojo.setCustom9("");
        if (wkSccosttypePojo.getCustom10() == null) wkSccosttypePojo.setCustom10("");
        if (wkSccosttypePojo.getTenantid() == null) wkSccosttypePojo.setTenantid("");
        if (wkSccosttypePojo.getTenantname() == null) wkSccosttypePojo.setTenantname("");
        if (wkSccosttypePojo.getRevision() == null) wkSccosttypePojo.setRevision(0);
        WkSccosttypeEntity wkSccosttypeEntity = new WkSccosttypeEntity();
        BeanUtils.copyProperties(wkSccosttypePojo, wkSccosttypeEntity);
        //生成雪花id
        wkSccosttypeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkSccosttypeEntity.setRevision(1);  //乐观锁
        this.wkSccosttypeMapper.insert(wkSccosttypeEntity);
        return this.getEntity(wkSccosttypeEntity.getId(), wkSccosttypeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkSccosttypePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkSccosttypePojo update(WkSccosttypePojo wkSccosttypePojo) {
        WkSccosttypeEntity wkSccosttypeEntity = new WkSccosttypeEntity();
        BeanUtils.copyProperties(wkSccosttypePojo, wkSccosttypeEntity);
        this.wkSccosttypeMapper.update(wkSccosttypeEntity);
        return this.getEntity(wkSccosttypeEntity.getId(), wkSccosttypeEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkSccosttypeMapper.delete(key, tid);
    }


}
