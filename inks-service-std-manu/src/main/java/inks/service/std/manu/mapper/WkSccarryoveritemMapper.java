package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkSccarryoveritemEntity;
import inks.service.std.manu.domain.pojo.WkSccarryoveritemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 结转子表(WkSccarryoveritem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-09 14:05:01
 */
 @Mapper
public interface WkSccarryoveritemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkSccarryoveritemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkSccarryoveritemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkSccarryoveritemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkSccarryoveritemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkSccarryoveritemEntity wkSccarryoveritemEntity);

    
    /**
     * 修改数据
     *
     * @param wkSccarryoveritemEntity 实例对象
     * @return 影响行数
     */
    int update(WkSccarryoveritemEntity wkSccarryoveritemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

