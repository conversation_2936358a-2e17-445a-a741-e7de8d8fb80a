package inks.service.std.manu.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import inks.common.core.domain.LoginUser;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.service.std.manu.domain.pojo.WkCostbudgetPojo;
import inks.service.std.manu.domain.pojo.WkCostbudgetitemPojo;
import inks.service.std.manu.service.WkCostbudgetService;
import inks.service.std.manu.service.WkCostbudgetitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 成本预测(Wk_CostBudget)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-03 19:16:26
 */
@RestController
@RequestMapping("D05M13B1")
@Api(tags = "D05M13B1:成本预测")
public class D05M13B1Controller extends WkCostbudgetController {
    /**
     * 服务对象
     */
    @Resource
    private WkCostbudgetService wkCostbudgetService;

    /**
     * 服务对象Item
     */
    @Resource
    private WkCostbudgetitemService wkCostbudgetitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "导出带图模板数据", notes = "导出带单据模板数据", produces = "application/json")
    @RequestMapping(value = "/exportBill", method = RequestMethod.GET)
    public void exportBiil(String key, HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            WkCostbudgetPojo wkCostbudgetPojo = this.wkCostbudgetService.getEntity(key, loginUser.getTenantid());
            Map<String, Object> map = BeanUtils.beanToMap(wkCostbudgetPojo);

            List<WkCostbudgetitemPojo> lstitem = this.wkCostbudgetitemService.getList(key, loginUser.getTenantid());
            List<Map<String, Object>> mapsitem = BeanUtils.attrListToMaps(lstitem);
            map.put("item", mapsitem);


            // File file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "static/poitemp/co1m01b1edit.xls");
            //            TemplateExportParams exportParams = new TemplateExportParams(
//                    file.getPath());

            TemplateExportParams exportParams = new TemplateExportParams(
                    "http://oss.oms.inksyun.com/C01/poitemp/co1m01b1edit.xls");

            if (exportParams != null) {

                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, map);
                POIUtil.downloadWorkbook(workbook, request, response, "生产成本预算" + wkCostbudgetPojo.getRefno());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
