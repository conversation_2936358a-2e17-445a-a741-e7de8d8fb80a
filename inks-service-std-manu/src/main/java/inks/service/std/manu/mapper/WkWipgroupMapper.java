package inks.service.std.manu.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.WkWipgroupEntity;
import inks.service.std.manu.domain.pojo.WkWipgroupPojo;
import inks.service.std.manu.domain.pojo.WkWipgroupitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * WIP设定(WkWipgroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-14 20:45:44
 */
@Mapper
public interface WkWipgroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWipgroupPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipgroupitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkWipgroupPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param wkWipgroupEntity 实例对象
     * @return 影响行数
     */
    int insert(WkWipgroupEntity wkWipgroupEntity);

    
    /**
     * 修改数据
     *
     * @param wkWipgroupEntity 实例对象
     * @return 影响行数
     */
    int update(WkWipgroupEntity wkWipgroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param wkWipgroupPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(WkWipgroupPojo wkWipgroupPojo);
                                                                                                                        }

