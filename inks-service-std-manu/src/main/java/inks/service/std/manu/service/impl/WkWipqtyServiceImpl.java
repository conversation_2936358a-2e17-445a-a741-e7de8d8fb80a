package inks.service.std.manu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.std.manu.config.Exception.ExpiredException;
import inks.service.std.manu.domain.WkWipnoteitemEntity;
import inks.service.std.manu.domain.WkWipqtyEntity;
import inks.service.std.manu.domain.exception.WipNoneProcessException;
import inks.service.std.manu.domain.pojo.*;
import inks.service.std.manu.mapper.*;
import inks.service.std.manu.service.*;
import inks.service.std.manu.utils.MyDateUtils;
import inks.service.std.manu.utils.PrintColor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;


/**
 * 生产过数(WkWipqty)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-04 16:30:01
 */
@Service("wkWipqtyService")
public class WkWipqtyServiceImpl implements WkWipqtyService {
    @Resource
    private WkSectionMapper wkSectionMapper;
    @Resource
    private WkProccycleMapper wkProccycleMapper;
    @Resource
    private WkWorksheetService wkWorksheetService;
    @Resource
    private WkWipqtyMapper wkWipqtyMapper;

    @Resource
    private WkWipnoteMapper wkWipnoteMapper;

    @Resource
    private WkWipnoteitemMapper wkWipnoteitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private WkWipnoteitemService wkWipnoteitemService;

    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private WkProcessMapper wkProcessMapper;
    @Resource
    private WkWipfinishService wkWipfinishService;
    @Resource
    private WkWipnoteService wkWipnoteService;
    private final static Logger logger = LoggerFactory.getLogger(WkWipqtyServiceImpl.class);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipqtyPojo getEntity(String key, String tid) {
        return this.wkWipqtyMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipqtyPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtyPojo> lst = wkWipqtyMapper.getPageList(queryParam);
            PageInfo<WkWipqtyPojo> pageInfo = new PageInfo<WkWipqtyPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<WkWipqtyLastPojo> getOnlinePageListByLastMark(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtyLastPojo> lst = wkWipqtyMapper.getOnlinePageListByLastMark(queryParam);
            PageInfo<WkWipqtyLastPojo> pageInfo = new PageInfo<WkWipqtyLastPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWipqtyPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipqtyPojo insert(WkWipqtyPojo wkWipqtyPojo) {

        WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
        BeanUtils.copyProperties(wkWipqtyPojo, wkWipqtyEntity);

        wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipqtyEntity.setRevision(1);  //乐观锁
        this.wkWipqtyMapper.insert(wkWipqtyEntity);
        return this.getEntity(wkWipqtyEntity.getId(), wkWipqtyEntity.getTenantid());

    }

    public List<Map<String, Object>> getidAndDateAndCus(String table, String ordertype) {
        return wkWipqtyMapper.getidAndDateAndCus(table, ordertype);
    }

    @Override
    public int updateSnowflakeId(String table, String newSnowflakeId, String custom1) {
        return wkWipqtyMapper.updateSnowflakeId(table, newSnowflakeId, custom1);
    }

    @Override
    public int copyIdToCustom(String table) {
        return wkWipqtyMapper.copyIdToCustom(table);
    }

    @Override
    public int copyCiteItemidToCustom(String table, String tgcolumn) {
        return wkWipqtyMapper.copyCiteItemidToCustom(table, tgcolumn);
    }

    @Override
    public int updateCiteItemid(String tgtable, String tgcolumn, String orgtable) {
        return wkWipqtyMapper.updateCiteItemid(tgtable, tgcolumn, orgtable);
    }

    /**
     * 修改数据
     *
     * @param wkWipqtyPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipqtyPojo update(WkWipqtyPojo wkWipqtyPojo) {
        WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
        BeanUtils.copyProperties(wkWipqtyPojo, wkWipqtyEntity);
        this.wkWipqtyMapper.update(wkWipqtyEntity);
        return this.getEntity(wkWipqtyEntity.getId(), wkWipqtyEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public WkWipqtyPojo delete(String key, String tid) {
        WkWipqtyPojo wkWipqtyPojo = this.wkWipqtyMapper.getEntity(key, tid);

        this.wkWipqtyMapper.delete(key, tid);
        String direction = wkWipqtyPojo.getDirection();
        Double pcsQty = wkWipqtyPojo.getPcsqty();
        Double secQty = wkWipqtyPojo.getSecqty();
        switch (direction) {
            case "入组":
                this.wkWipnoteitemMapper.updateInQty(wkWipqtyPojo.getWipitemid(), -pcsQty, -secQty, null, null, tid);
                break;
            case "出组":
                this.wkWipnoteitemMapper.updateOutQty(wkWipqtyPojo.getWipitemid(), -pcsQty, -secQty, null, tid);
                break;
            default:
                break;
        }
        // 更新完成数
        //  this.wkWipqtyMapper.updateWipComp(wkWipqtyPojo.getWipitemid(), tid);
        return wkWipqtyPojo;
    }


    //快速入组(上工序自动出)
    @Override
    //@Transactional
    public WkWipnotePojo quickInput(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        // 查询是否有工单
        WkWipnotePojo wkWipnotePojo = this.wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
        if (wkWipnotePojo == null) {
            throw new BaseBusinessException("没找到WIP记录 " + quickPojo.getWorkuid());
        }
        // 当前关联的加工单子表
        String workitemid = wkWipnotePojo.getWorkitemid();

        // 读取指定系统参数 加工单按序入库
        String configValue = systemFeignService.getConfigValue("module.manu.finishbyorder", loginUser.getTenantid(), loginUser.getToken()).getData();
        // true 按序入库 且不是补单 才检查
        // 判断是否按序入库，且不是补单
        if ("true".equals(configValue) && wkWipnotePojo.getSupplement() != 1 && isNotBlank(workitemid)) {
            WkWorksheetPojo sheetBill = wkWorksheetService.getBillEntityByItemid(workitemid, tid);
            List<WkWorksheetitemPojo> sheetItems = sheetBill.getItem();

            // 先找到当前 workitemid 对应的对象
            WkWorksheetitemPojo currentSheetItem = sheetItems.stream()
                    .filter(item -> item.getId().equals(workitemid))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("未找到与 workitemid 对应的工序行"));

            // 获取当前工序行号 item行号
            int currentWkRowNum = Integer.parseInt(currentSheetItem.getWkrownum());
            int rowNum = currentSheetItem.getRownum();

            // 检查所有在当前工序行之前的行号是否符合条件，并收集不符合条件的行号
            Optional<WkWorksheetitemPojo> invalidItem = sheetItems.stream()
                    .filter(item -> item.getRownum() < rowNum)
                    .filter(item -> Integer.parseInt(item.getWkrownum()) > currentWkRowNum)
                    .findFirst();
            if (invalidItem.isPresent()) {
                WkWorksheetitemPojo sheetitem = invalidItem.get();
                throw new IllegalStateException("禁止超过前单工序：" + sheetitem.getWkwpname() + "(加工单号" + sheetBill.getRefno() + "的第" + sheetitem.getRownum() + 1 + "行)");
            }
        }
        quickPojo.setWipid(wkWipnotePojo.getId());
        Double remQty = wkWipnotePojo.getWkpcsqty() - wkWipnotePojo.getMrbpcsqty();
        // 查询工单中是否操作工序
        List<WkWipnoteitemPojo> list = this.wkWipnoteitemMapper.getRemListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), remQty, tid);
        WkWipnoteitemPojo wkWipnoteitemPojo;
        if (!list.isEmpty()) {
            wkWipnoteitemPojo = list.get(0);
        } else {
            wkWipnoteitemPojo = new WkWipnoteitemPojo();
        }
        if (list.isEmpty()) {
            throw new BaseBusinessException("未找到可过数的记录 " + quickPojo.getWorkuid() + "单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达前工序");
        }

        // 20241206 读取指定系统参数 wip按序报工检查 为“goods”： 为goodsid查询，workrefno、workrownum小于当前工前的，并把wkrownum<当前工单的。报错；
        //                                       为“mach”：  为“goods”基础上加个machitemid也相同
        String wipfinishbyorder = systemFeignService.getConfigValue("module.manu.wipfinishbyorder", loginUser.getTenantid(), loginUser.getToken()).getData();
        if (isNotBlank(wipfinishbyorder)) {
            String id = wkWipnotePojo.getId();
            String goodsid = wkWipnotePojo.getGoodsid();
            Integer wkrownum = wkWipnoteitemPojo.getRownum();

            // 根据wipfinishbyorder判断类型并获取前置工序
            if ("goods".equals(wipfinishbyorder) || "mach".equals(wipfinishbyorder)) {
                // 获取machitemid，仅在"mach"情况下才有值
                String machitemid = "mach".equals(wipfinishbyorder) ? wkWipnotePojo.getMachitemid() : null;
                // 查询前置工序
                WkWipnotePojo wipFirstDB = wkWipnoteMapper.getPreWipNoteByGoodsid(goodsid, wkrownum, machitemid, tid);
                if (wipFirstDB != null && !wipFirstDB.getId().equals(id)) {//查詢出来的第一个不是当前要过数的，就报错
                    throw new BaseBusinessException("请先过数【" + wipFirstDB.getWorkuid() + "】批次");
                }
            }
        }


        // 是第一行Item
        if (wkWipnoteitemPojo.getRownum() == 1) {
            //创建QRY对象
            WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
            wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyEntity.setWkdate(new Date());
            wkWipqtyEntity.setDirection("入组");
            wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyEntity.setPcsqty(wkWipnotePojo.getWkpcsqty());
            wkWipqtyEntity.setSecqty(wkWipnotePojo.getWksecqty());
            wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyEntity.setMrbpcsqty(0D);
            wkWipqtyEntity.setMrbsecqty(0D);
            wkWipqtyEntity.setMrbid("");
            wkWipqtyEntity.setAcceid("");
            wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
            wkWipqtyEntity.setListerid(quickPojo.getListerid());
            wkWipqtyEntity.setCreateby(loginUser.getRealName());
            wkWipqtyEntity.setLister(quickPojo.getLister());
            wkWipqtyEntity.setCreatedate(new Date());
            wkWipqtyEntity.setModifydate(new Date());
            wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyEntity.setWorkitemid(workitemid);
            wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyEntity.setRemark("扫码过数入组");
            wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyEntity.setWorker(quickPojo.getWorker()); //工人
            wkWipqtyEntity.setRevision(1);
            // 加入尺寸 Eric20221008
            wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
            transactionTemplate.execute((status) -> {
                this.wkWipqtyMapper.insert(wkWipqtyEntity);
                // 同步Item数量  20231103记录入组操作人(前端传入)
                wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), quickPojo.getWorker(), tid);
                //更新Wk_WipNote表
                WkWipnotePojo upWipPojo = new WkWipnotePojo();
                upWipPojo.setId(wkWipnotePojo.getId());
                upWipPojo.setWkrownum(wkWipnoteitemPojo.getRownum());
                upWipPojo.setWkwpid(wkWipnoteitemPojo.getWpid());
                upWipPojo.setWkwpcode(wkWipnoteitemPojo.getWpcode());
                upWipPojo.setWkwpname(wkWipnoteitemPojo.getWpname());
                upWipPojo.setWkspecjson(quickPojo.getSpecjson());
                upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
                upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
                upWipPojo.setWorkitemid(workitemid);
                upWipPojo.setTenantid(loginUser.getTenantid());
                upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
                upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
                this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                // 加工单
                if (!workitemid.isEmpty()) {
                    this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateWorkWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateWorkBillWkwpByMerge(upWipPojo);
                    }
                }
                //更新bus_machiningitem表
                if (!wkWipnotePojo.getMachitemid().isEmpty()) {
                    this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateMachWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateMachBillWkwpByMerge(upWipPojo);
                    }
                }
                // 同步Wk_MainPlan生产主计划主子表工序状态
                if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                    this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateMainPlanWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateMainPlanBillWkwpByMerge(upWipPojo);
                    }
                }
                return Boolean.TRUE;
            });
        }
        if (wkWipnoteitemPojo.getRownum() > 1) {
            // 拿到前一个行号
            quickPojo.setRownum(wkWipnoteitemPojo.getRownum() - 1);
            // 拿到前一条记录
            WkWipnoteitemPojo wkWipnoteitemPojoPre = wkWipnoteitemMapper.getPrevPostEntityByRownum(quickPojo.getRownum(), quickPojo.getWipid(), tid);
            if (wkWipnoteitemPojoPre == null) {
                throw new BaseBusinessException("没找到前工序记录" + quickPojo.getWorkuid() + wkWipnoteitemPojo.getWpname());
            }
            logger.info("查询出的前工序POST=1:{}，入组数:{}", wkWipnoteitemPojoPre.getWpname(), wkWipnoteitemPojoPre.getInpcsqty());
            if (wkWipnoteitemPojoPre.getInpcsqty() == 0) {
                //throw new BaseBusinessException("单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达前工序");
                wkWipnotePojo.setWkrownum(wkWipnoteitemPojo.getRownum());
                throw new WipNoneProcessException(JSON.toJSONString(wkWipnotePojo));
            }
            // 如果前一条记录中还有结余数量
            double RemQty = wkWipnoteitemPojoPre.getInpcsqty() - wkWipnoteitemPojoPre.getOutpcsqty() - wkWipnoteitemPojoPre.getMrbpcsqty();
            if (quickPojo.getQty() != null && remQty < quickPojo.getQty() + wkWipnoteitemPojo.getInpcsqty()) {
                throw new BaseBusinessException("结余数量" + (remQty - wkWipnoteitemPojo.getInpcsqty()) + "不足本次过数量" + quickPojo.getQty());
            }
            if (quickPojo.getSecqty() != null && remQty < quickPojo.getSecqty() + wkWipnoteitemPojo.getInpcsqty()) {
                throw new BaseBusinessException("结余数量" + (remQty - wkWipnoteitemPojo.getInpcsqty()) + "不足本次过数量Secqty:" + quickPojo.getSecqty());
            }
            Double CrtQty = quickPojo.getQty() == null ? RemQty : quickPojo.getQty();
            if (RemQty > 0) {
                //创建QRY对象
                WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
                wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
                wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
                wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
                wkWipqtyEntity.setWkdate(new Date());
                wkWipqtyEntity.setDirection("入组");
                wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
                wkWipqtyEntity.setPcsqty(CrtQty);
                wkWipqtyEntity.setSecqty(quickPojo.getSecqty() == null ? 0 : quickPojo.getSecqty());
                wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
                wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
                wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
                wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
                wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
                wkWipqtyEntity.setMrbpcsqty(0D);
                wkWipqtyEntity.setMrbsecqty(0D);
                wkWipqtyEntity.setMrbid("");
                wkWipqtyEntity.setAcceid("");
                wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
                wkWipqtyEntity.setListerid(quickPojo.getListerid());
                wkWipqtyEntity.setCreateby(loginUser.getRealName());
                wkWipqtyEntity.setLister(quickPojo.getLister());
                wkWipqtyEntity.setCreatedate(new Date());
                wkWipqtyEntity.setModifydate(new Date());
                wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
                wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
                wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
                wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
                wkWipqtyEntity.setWorkitemid(workitemid);
                wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
                wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
                wkWipqtyEntity.setRemark("扫码过数入组");
                wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
                wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
                wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
                wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
                wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
                wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
                wkWipqtyEntity.setWorker(quickPojo.getWorker()); //工人
                wkWipqtyEntity.setRevision(1);
                // 加入尺寸 Eric20221008
                wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
                wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
                wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
                transactionTemplate.execute((status) -> {

                    this.wkWipqtyMapper.insert(wkWipqtyEntity);
//                    20231103记录入组操作人(前端传入)
                    wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), quickPojo.getWorker(), tid);
                    //更新Wk_WipNote表
                    WkWipnotePojo upWipPojo = new WkWipnotePojo();
                    upWipPojo.setId(wkWipnotePojo.getId());
                    upWipPojo.setWkrownum(wkWipnoteitemPojo.getRownum());
                    upWipPojo.setWkwpid(wkWipnoteitemPojo.getWpid());
                    upWipPojo.setWkwpcode(wkWipnoteitemPojo.getWpcode());
                    upWipPojo.setWkwpname(wkWipnoteitemPojo.getWpname());
                    upWipPojo.setWkspecjson(quickPojo.getSpecjson());
                    upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
                    upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
                    upWipPojo.setWorkitemid(workitemid);
                    upWipPojo.setTenantid(loginUser.getTenantid());
                    upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
                    upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
                    this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                    // 加工单
                    if (!workitemid.isEmpty()) {
                        this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                    }
                    //更新bus_machiningitem表
                    if (!wkWipnotePojo.getMachitemid().isEmpty()) {
                        this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                    }
                    // 同步Wk_MainPlan生产主计划主子表工序状态
                    if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                        this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                    }
                    // 前工序出组
                    wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                    wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    wkWipqtyEntity.setWiprownum(wkWipnoteitemPojoPre.getRownum());
                    wkWipqtyEntity.setWipitemid(wkWipnoteitemPojoPre.getId());
                    wkWipqtyEntity.setDirection("出组");
                    wkWipqtyEntity.setWpid(wkWipnoteitemPojoPre.getWpid());
                    wkWipqtyEntity.setWpcode(wkWipnoteitemPojoPre.getWpcode());
                    wkWipqtyEntity.setWpname(wkWipnoteitemPojoPre.getWpname());
                    wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
                    // 加入尺寸 Eric20221008
                    wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
                    wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
                    wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
                    this.wkWipqtyMapper.insert(wkWipqtyEntity);
                    // 20231103记录出组操作人(前端传入)
                    this.wkWipnoteitemMapper.updateOutQtyAndOutWorker(wkWipnoteitemPojoPre.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), quickPojo.getWorker(), tid);
                    // 加入随过功能 Eric 20220915
                    if (wkWipnoteitemPojo.getRownum() - wkWipnoteitemPojoPre.getRownum() > 1) {
                        FollowPost(wkWipnotePojo.getId(), wkWipnoteitemPojoPre.getRownum() + 1, wkWipnoteitemPojo.getRownum() - 1, wkWipqtyEntity);
                    }
                    // 同步 WorkParam eric 20230304
                    if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                        this.mergeWorkparam(wkWipnoteitemPojo.getId(), tid);
                    }
                    // 同步 SPCEJSON
                    if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                        this.sumSpecjson(wkWipnoteitemPojoPre.getId(), tid);
                    }
                    return Boolean.TRUE;
                });
            } else {
                if (wkWipnoteitemPojoPre.getInpcsqty() > 0) {
                    throw new BaseBusinessException("前工序【" + wkWipnoteitemPojoPre.getWpname() + "】已入组数量" + wkWipnoteitemPojoPre.getInpcsqty() + "，请勿重复过数");
                } else {
                    throw new BaseBusinessException("请勿跳工序过数，在制工序为：" + wkWipnotePojo.getWkwpname());
                }
            }

        }
        return this.wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);
    }

    //仅入组
    @Override
    //@Transactional
    public WkWipnotePojo quickStart(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = this.wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录 " + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());
            // wipNote主表总数量
            Double remQty = wkWipnotePojo.getWkpcsqty() - wkWipnotePojo.getMrbpcsqty();
            // quickPojo.getQty()如果未输入数量，则默认为剩余数量全数入组;否则按输入数量入组
            Double crtQty = quickPojo.getQty() == null ? remQty : quickPojo.getQty();
            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = this.wkWipnoteitemMapper.getRemListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), remQty, tid);
            WkWipnoteitemPojo wkWipnoteitemPojo;
            if (!list.isEmpty()) {
                wkWipnoteitemPojo = list.get(0);
            } else {
                wkWipnoteitemPojo = new WkWipnoteitemPojo();
            }
            if (list.isEmpty()) {
                throw new BaseBusinessException("未找到可过数的记录 " + quickPojo.getWorkuid());
            }
            // 20230830 校验是否禁止入组
            if (wkWipnoteitemPojo.getDisablein() != null && wkWipnoteitemPojo.getDisablein() == 1) {
                throw new BaseBusinessException("当前工序已设定为禁止入组");
            }
            // 20230830 校验在线批次数  获取当前工序在线单据数 和 工序里设定的在线批次数
            int onlineCountByWp = wkWipnoteMapper.getOnlineCountByWp(wkWipnoteitemPojo.getWpid(), tid);
            int onlineBatchCountDB = wkProcessMapper.getOnlineBatchCount(wkWipnoteitemPojo.getWpid(), tid);
            PrintColor.color("onlineCountByWp:" + onlineCountByWp + " onlineBatchCountDB:" + onlineBatchCountDB);
            if (onlineBatchCountDB <= onlineCountByWp) { // 当前在线单据数禁止大于设定的在线批次数
                throw new BaseBusinessException("当前" + wkWipnoteitemPojo.getWpname() + "工序在线单据数(" + onlineCountByWp + ")禁止超过设定的在线批次数(" + onlineBatchCountDB + ")");
            }
            // 20230904 记录入组操作人(前端传入) 在updateStartdateAndInworker方法中更新
            wkWipnoteitemPojo.setInworker(quickPojo.getWorker());

            // 【工序周期操作】上料(入组)时间Startdate:当前时间;  下料计划时间PlanDate：当前时间加上(全程QC)周期时间                 0全程1前置2后置
            WkProccyclePojo wkProccyclePojoQC = wkProccycleMapper.getEntityByWpidAndType(wkWipnoteitemPojo.getWpid(), 0, tid);
            if (wkProccyclePojoQC == null) {
                throw new BaseBusinessException("未找到工序周期信息");
            }
            Date startDate = new Date();
            wkWipnoteitemPojo.setStartdate(startDate);
            LocalDateTime addlocalDateTime = MyDateUtils.addTime(LocalDateTime.now(), wkProccyclePojoQC.getCyclevalue(), wkProccyclePojoQC.getCycleunit());
            Date adddate = Date.from(addlocalDateTime.atZone(ZoneId.systemDefault()).toInstant());// LocalDateTime转为Date
            PrintColor.red("adddate" + adddate);
            wkWipnoteitemPojo.setPlandate(adddate);
            // 上料时间过期检查(第一道工序或者当isadmin=1(不为空)时，不检查)  20240425加入或者isMock为true也不检查
            if (wkWipnoteitemPojo.getRownum() != 1 && quickPojo.getIsadmin() == null) {
                // 检查早于StartDate必须在StartPlan左右分钟误差范围内 读取当前工序(前置QZ)的左右误差
                WkProccyclePojo wkProccyclePojoQZ = wkProccycleMapper.getEntityByWpidAndType(wkWipnoteitemPojo.getWpid(), 1, tid);
                if (startDate.before(MyDateUtils.subMinutesOnDate(wkWipnoteitemPojo.getStartplan(), wkProccyclePojoQZ.getLefttole()))) {
                    throw new ExpiredException("当前上料时间过早");
                } else if (startDate.after(MyDateUtils.addMinutesOnDate(wkWipnoteitemPojo.getStartplan(), wkProccyclePojoQZ.getRighttole()))) {
                    throw new ExpiredException("当前上料时间已过期");
                }
            }
            this.wkWipnoteitemMapper.updateStartdateAndInworker(wkWipnoteitemPojo);

            // 是第一行Item 第一道工序：
            if (wkWipnoteitemPojo.getRownum() == 1) {
                // 超数检查：检查第一道工序【已入组数量+本次入组数量】不能大于【wipNote主表总数】
                if (remQty < wkWipnoteitemPojo.getInpcsqty() + crtQty) {
                    throw new BaseBusinessException("本次入组数量" + crtQty + "超出wip结余数量 " + (remQty - wkWipnoteitemPojo.getInpcsqty()));
                }
                //创建QRY对象
                WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
                wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
                wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
                wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
                wkWipqtyEntity.setWkdate(new Date());
                wkWipqtyEntity.setDirection("入组");
                wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
                wkWipqtyEntity.setWorker(quickPojo.getWorker());
                wkWipqtyEntity.setPcsqty(wkWipnotePojo.getQuantity());
                wkWipqtyEntity.setSecqty(wkWipnotePojo.getQuantity());
                wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
                wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
                wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
                wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
                wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
                wkWipqtyEntity.setMrbpcsqty(0D);
                wkWipqtyEntity.setMrbsecqty(0D);
                wkWipqtyEntity.setMrbid("");
                wkWipqtyEntity.setAcceid("");
                wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
                wkWipqtyEntity.setListerid(quickPojo.getListerid());
                wkWipqtyEntity.setCreateby(loginUser.getRealName());
                wkWipqtyEntity.setLister(quickPojo.getLister());
                wkWipqtyEntity.setCreatedate(new Date());
                wkWipqtyEntity.setModifydate(new Date());
                wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
                wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
                wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
                wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
                wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
                wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
                wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
                wkWipqtyEntity.setRemark("仅过数入组");
                wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
                wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
                wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
                wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
                wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
                wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
                wkWipqtyEntity.setRevision(1);
                // 加入尺寸 Eric20221008
                wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
                wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
                wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
                transactionTemplate.execute((status) -> {
                    this.wkWipqtyMapper.insert(wkWipqtyEntity);
                    // 同步Item数量
                    wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), null, tid);
                    //更新Wk_WipNote表
                    WkWipnotePojo upWipPojo = new WkWipnotePojo();
                    upWipPojo.setId(wkWipnotePojo.getId());
                    upWipPojo.setWkrownum(wkWipnoteitemPojo.getRownum());
                    upWipPojo.setWkwpid(wkWipnoteitemPojo.getWpid());
                    upWipPojo.setWkwpcode(wkWipnoteitemPojo.getWpcode());
                    upWipPojo.setWkwpname(wkWipnoteitemPojo.getWpname());
                    upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
                    upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
                    upWipPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
                    upWipPojo.setTenantid(loginUser.getTenantid());
                    upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
                    upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
                    this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                    // 加工单
                    if (isNotBlank(wkWipnotePojo.getWorkitemid())) {
                        this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                        if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                            this.wkWipqtyMapper.updateWorkWkwpByMerge(upWipPojo);
                            this.wkWipqtyMapper.updateWorkBillWkwpByMerge(upWipPojo);
                        }
                    }
                    //更新bus_machiningitem表
                    if (isNotBlank(wkWipnotePojo.getMachitemid())) {
                        this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                        if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                            this.wkWipqtyMapper.updateMachWkwpByMerge(upWipPojo);
                            this.wkWipqtyMapper.updateMachBillWkwpByMerge(upWipPojo);
                        }
                    }
                    // 同步Wk_MainPlan生产主计划主子表工序状态
                    if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                        this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                        if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                            this.wkWipqtyMapper.updateMainPlanWkwpByMerge(upWipPojo);
                            this.wkWipqtyMapper.updateMainPlanBillWkwpByMerge(upWipPojo);
                        }
                    }
                    return Boolean.TRUE;
                });
            }
            if (wkWipnoteitemPojo.getRownum() > 1) {
                // 拿到前一个行号
                quickPojo.setRownum(wkWipnoteitemPojo.getRownum() - 1);
                // 拿到前一条记录
                WkWipnoteitemPojo wkWipnoteitemPojoPre = wkWipnoteitemMapper.getPrevPostEntityByRownum(quickPojo.getRownum(), quickPojo.getWipid(), tid);
                if (wkWipnoteitemPojoPre == null) {
                    throw new BaseBusinessException("没找到前工序记录" + quickPojo.getWorkuid() + wkWipnoteitemPojo.getWpname());
                }
                logger.info("查询出的前工序POST=1:{}，入组数:{}", wkWipnoteitemPojoPre.getWpname(), wkWipnoteitemPojoPre.getInpcsqty());
                if (wkWipnoteitemPojoPre.getInpcsqty() == 0) {
                    throw new BaseBusinessException("单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达前工序");
                }
                if (wkWipnoteitemPojoPre.getOutpcsqty() == 0) {
                    throw new BaseBusinessException("单据在" + wkWipnotePojo.getWkwpname() + ",前工序尚未出组");
                }
                // 超数检查：检查本工序【已入组数量+本次入组数量】不能大於上一道工序的出组数量
                if (wkWipnoteitemPojoPre.getOutpcsqty() < wkWipnoteitemPojo.getInpcsqty() + crtQty) {
                    throw new BaseBusinessException("本次入组数量" + crtQty + "超出上一道工序结余出组数量" + (wkWipnoteitemPojoPre.getOutpcsqty() - wkWipnoteitemPojo.getInpcsqty()));
                }
//                // 如果前一条记录中还有结余数量
//                Double RemQty = wkWipnoteitemPojoPre.getInpcsqty() - wkWipnoteitemPojoPre.getOutpcsqty() - wkWipnoteitemPojoPre.getMrbpcsqty();
//                if (quickPojo.getQty() != null && remQty < quickPojo.getQty()+wkWipnoteitemPojo.getInpcsqty()) {
//                    throw new BaseBusinessException("结余数量" + (remQty-wkWipnoteitemPojo.getInpcsqty()) + "不足本次过数量" + quickPojo.getQty());
//                }
//                Double CrtQty = quickPojo.getQty() == null ? remQty : quickPojo.getQty();
//                if (RemQty > 0) {
                //创建QRY对象
                WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
                wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
                wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
                wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
                wkWipqtyEntity.setWkdate(new Date());
                wkWipqtyEntity.setDirection("入组");
                wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
                wkWipqtyEntity.setWorker(quickPojo.getWorker());
                wkWipqtyEntity.setPcsqty(crtQty);
                Double SecQty = wkWipnoteitemPojoPre.getInsecqty() - wkWipnoteitemPojoPre.getOutsecqty() - wkWipnoteitemPojoPre.getMrbsecqty();
                wkWipqtyEntity.setSecqty(SecQty);
                wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
                wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
                wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
                wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
                wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
                wkWipqtyEntity.setMrbpcsqty(0D);
                wkWipqtyEntity.setMrbsecqty(0D);
                wkWipqtyEntity.setMrbid("");
                wkWipqtyEntity.setAcceid("");
                wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
                wkWipqtyEntity.setListerid(quickPojo.getListerid());
                wkWipqtyEntity.setCreateby(loginUser.getRealName());
                wkWipqtyEntity.setLister(quickPojo.getLister());
                wkWipqtyEntity.setCreatedate(new Date());
                wkWipqtyEntity.setModifydate(new Date());
                wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
                wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
                wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
                wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
                wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
                wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
                wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
                wkWipqtyEntity.setRemark("仅过数入组");
                wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
                wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
                wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
                wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
                wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
                wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
                wkWipqtyEntity.setRevision(1);
                // 加入尺寸 Eric20221008
                wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
                wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
                wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
                transactionTemplate.execute((status) -> {

                    this.wkWipqtyMapper.insert(wkWipqtyEntity);
                    wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), null, tid);
                    //更新Wk_WipNote表
                    WkWipnotePojo upWipPojo = new WkWipnotePojo();
                    upWipPojo.setId(wkWipnotePojo.getId());
                    upWipPojo.setWkrownum(wkWipnoteitemPojo.getRownum());
                    upWipPojo.setWkwpid(wkWipnoteitemPojo.getWpid());
                    upWipPojo.setWkwpcode(wkWipnoteitemPojo.getWpcode());
                    upWipPojo.setWkwpname(wkWipnoteitemPojo.getWpname());
                    upWipPojo.setWkspecjson(quickPojo.getSpecjson());
                    upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
                    upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
                    upWipPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
                    upWipPojo.setTenantid(loginUser.getTenantid());
                    upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
                    upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
                    this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                    // 加工单
                    if (isNotBlank(wkWipnotePojo.getWorkitemid())) {
                        this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                    }
                    //更新bus_machiningitem表
                    if (isNotBlank(wkWipnotePojo.getMachitemid())) {
                        this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                    }
                    // 同步Wk_MainPlan生产主计划主子表工序状态
                    if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                        this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                    }
                    // 同步 WorkParam eric 20230304
                    if (isNotBlank(quickPojo.getWorkparam())) {
                        this.mergeWorkparam(wkWipnoteitemPojo.getId(), tid);
                    }
                    return Boolean.TRUE;
                });
                // --------------------------------前工序出组--------------------------
//                wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
//                wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
//                wkWipqtyEntity.setWiprownum(wkWipnoteitemPojoPre.getRownum());
//                wkWipqtyEntity.setWipitemid(wkWipnoteitemPojoPre.getId());
//                wkWipqtyEntity.setDirection("出组");
//                wkWipqtyEntity.setWpid(wkWipnoteitemPojoPre.getWpid());
//                wkWipqtyEntity.setWpcode(wkWipnoteitemPojoPre.getWpcode());
//                wkWipqtyEntity.setWpname(wkWipnoteitemPojoPre.getWpname());
//                wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
//                // 加入尺寸 Eric20221008
//                wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
//                wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
//                wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
//                this.wkWipqtyMapper.insert(wkWipqtyEntity);
//                this.wkWipnoteitemMapper.updateOutQty(wkWipnoteitemPojoPre.getId(), wkWipqtyEntity.getWkdate(), tid);
//                // 加入随过功能 Eric 20220915
//                if (wkWipnoteitemPojo.getRownum() - wkWipnoteitemPojoPre.getRownum() > 1) {
//                    FollowPost(wkWipnotePojo.getId(), wkWipnoteitemPojoPre.getRownum() + 1, wkWipnoteitemPojo.getRownum() - 1, wkWipqtyEntity);
//                }
//                // 同步 SPCEJSON
//                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
//                    this.sumSpecjson(wkWipnoteitemPojoPre.getId(), tid);
//                }
                // --------------------------------前工序出组結束--------------------------
            }
//                else {
//                    if (wkWipnoteitemPojoPre.getInpcsqty() > 0) {
//                      throw new BaseBusinessException("前工序【" + wkWipnoteitemPojoPre.getWpname() + "】已入组数量" + wkWipnoteitemPojoPre.getInpcsqty() + "，请勿重复过数");
//                    } else {
//                        throw new BaseBusinessException("请勿跳工序过数，在制工序为：" + wkWipnotePojo.getWkwpname());
//                    }
//                }
//            }
            return this.wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    @Transactional
    public WkWipnotePojo quickStartAdd(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = this.wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录 " + quickPojo.getWorkuid());
            }
            //20240313 生产加工单主表总投数数量更新,需累加传入的数量
            wkWipnoteMapper.updateWkpcsqty(wkWipnotePojo.getWkpcsqty() + quickPojo.getQty(), wkWipnotePojo.getId(), tid);
            //20241105 同步加工单子表总投数
            wkWipnoteMapper.syncSheetItemWkpsqty(wkWipnotePojo.getWkpcsqty() + quickPojo.getQty(), wkWipnotePojo.getWorkitemid(), tid);

            quickPojo.setWipid(wkWipnotePojo.getId());
            // wipNote主表总数量
            double remQty = wkWipnotePojo.getWkpcsqty() - wkWipnotePojo.getMrbpcsqty();
            // quickPojo.getQty()如果未输入数量，则默认为剩余数量全数入组;否则按输入数量入组
            Double crtQty = quickPojo.getQty() == null ? remQty : quickPojo.getQty();
            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = this.wkWipnoteitemMapper.getRemListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), null, tid);
            WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
            if (!list.isEmpty()) {
                wkWipnoteitemPojo = list.get(0);
            }
            if (list.isEmpty()) {
                throw new BaseBusinessException("未找到可过数的记录 " + quickPojo.getWorkuid());
            }
            // 是第一行Item 第一道工序：
//                // 超数检查：检查第一道工序【已入组数量+本次入组数量】不能大于【wipNote主表总数】
//                if (remQty < wkWipnoteitemPojo.getInpcsqty() + crtQty) {
//                    throw new BaseBusinessException("本次入组数量" + crtQty + "超出wip结余数量 " + (remQty - wkWipnoteitemPojo.getInpcsqty()));
//                }
            //创建QRY对象
            WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
            // 标记为增减料过数 StoreMark=1
            wkWipqtyEntity.setStoremark(1);
            wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyEntity.setWkdate(new Date());
            wkWipqtyEntity.setDirection("入组");
            wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyEntity.setWorker(quickPojo.getWorker());
            wkWipqtyEntity.setPcsqty(crtQty);
            wkWipqtyEntity.setSecqty(crtQty);
            wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyEntity.setMrbpcsqty(0D);
            wkWipqtyEntity.setMrbsecqty(0D);
            wkWipqtyEntity.setMrbid("");
            wkWipqtyEntity.setAcceid("");
            wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
            wkWipqtyEntity.setListerid(quickPojo.getListerid());
            wkWipqtyEntity.setCreateby(loginUser.getRealName());
            wkWipqtyEntity.setLister(quickPojo.getLister());
            wkWipqtyEntity.setCreatedate(new Date());
            wkWipqtyEntity.setModifydate(new Date());
            wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyEntity.setRemark("仅过数入组");
            wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyEntity.setRevision(1);
            // 加入尺寸 Eric20221008
            wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
            this.wkWipqtyMapper.insert(wkWipqtyEntity);
            // 同步Item数量
            wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), null, tid);
//            //更新Wk_WipNote表
//            WkWipnotePojo upWipPojo = new WkWipnotePojo();
//            upWipPojo.setId(wkWipnotePojo.getId());
//            upWipPojo.setWkrownum(wkWipnoteitemPojo.getRownum());
//            upWipPojo.setWkwpid(wkWipnoteitemPojo.getWpid());
//            upWipPojo.setWkwpcode(wkWipnoteitemPojo.getWpcode());
//            upWipPojo.setWkwpname(wkWipnoteitemPojo.getWpname());
//            upWipPojo.setWkspecjson(quickPojo.getSpecjson());
//            upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
//            upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
//            upWipPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
//            upWipPojo.setTenantid(loginUser.getTenantid());
//            upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
//            upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
//            this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
            return this.wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);
        } catch (BaseBusinessException e) {
            throw new BaseBusinessException(e.getMessage());
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    // 仅入组【任意工序下的】
    @Override
    @Transactional
    public WkWipnotePojo anyQuickStart(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();

        // 查询是否有工单
        WkWipnotePojo wkWipnotePojo = this.wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
        if (wkWipnotePojo == null) {
            throw new BaseBusinessException("没找到WIP记录 " + quickPojo.getWorkuid());
        }
        quickPojo.setWipid(wkWipnotePojo.getId());
        // wipNote主表总数量
        Double remQty = wkWipnotePojo.getWkpcsqty() - wkWipnotePojo.getMrbpcsqty();
        // quickPojo.getQty()如果未输入数量，则默认为剩余数量全数入组;否则按输入数量入组
        Double crtQty = quickPojo.getQty() == null ? remQty : quickPojo.getQty();
        // 查询工单中是否操作工序
        List<WkWipnoteitemPojo> list = this.wkWipnoteitemMapper.getRemListByWpid2(quickPojo.getWpid(), quickPojo.getWipid(), remQty, tid);
        WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
        if (!list.isEmpty()) {
            wkWipnoteitemPojo = list.get(0);
        }
        if (list.isEmpty()) {
//            WkProcessPojo process = wkProcessMapper.getEntity(quickPojo.getWpid(), tid);
//                throw new BaseBusinessException("当前工单中没有["+process.getWpname()+"]工序,请联系生管添加");
            throw new WipNoneProcessException(JSON.toJSONString(wkWipnotePojo));
        }
//            // 202309011 若传入数量为0，需检查此Wipnoteitem.id生成的N条入组记录是否已有PcsQty=0的记录(保证Wipnoteitem.id对应N条入组记录只能有1个PcsQty=0的记录)
//            if (crtQty==0) {
//                WkWipqtyPojo wkWipqtyPojo = wkWipqtyMapper.checkWipQtyPcsQty(wkWipnoteitemPojo.getId(), loginUser.getTenantid());
//                if (wkWipqtyPojo!=null) {
//                    throw new BaseBusinessException("此工序下已存在数量为0的入组记录,请选择覆盖生成");
//                }
//            }

        // 是第一行Item 第一道工序：
        if (wkWipnoteitemPojo.getRownum() == 1) {
            // 超数检查：检查第一道工序【已入组数量+本次入组数量】是否大于【wipNote主表总数】
            if (remQty < wkWipnoteitemPojo.getInpcsqty() + crtQty) {
                throw new BaseBusinessException("本次入组数量" + crtQty + "超出wip结余数量" + (remQty - wkWipnoteitemPojo.getInpcsqty()));
            }
            //创建QRY对象
            WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
            wkWipqtyEntity.setRefno("wqs" + DateUtils.dateTimeNow());
            wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyEntity.setWkdate(new Date());
            wkWipqtyEntity.setDirection("入组");
            wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyEntity.setWorker(quickPojo.getWorker());
            wkWipqtyEntity.setPcsqty(crtQty);
            wkWipqtyEntity.setSecqty(wkWipnotePojo.getQuantity());
            wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyEntity.setMrbpcsqty(0D);
            wkWipqtyEntity.setMrbsecqty(0D);
            wkWipqtyEntity.setMrbid("");
            wkWipqtyEntity.setAcceid("");
            wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
            wkWipqtyEntity.setListerid(quickPojo.getListerid());
            wkWipqtyEntity.setCreateby(loginUser.getRealName());
            wkWipqtyEntity.setLister(quickPojo.getLister());
            wkWipqtyEntity.setCreatedate(new Date());
            wkWipqtyEntity.setModifydate(new Date());
            wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyEntity.setRemark("任意工序仅过数入组");
            wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyEntity.setRevision(1);
            // 加入尺寸 Eric20221008
            wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
            this.wkWipqtyMapper.insert(wkWipqtyEntity);
            // 同步Item数量
            wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), null, tid);
            // 如果传入时间,则更新WkWipnoteItem的入组时间
            Date startDate = quickPojo.getStartdate() == null ? new Date() : quickPojo.getStartdate();
            wkWipnoteitemMapper.updateStartDate(wkWipnoteitemPojo.getId(), startDate, tid);
            //更新Wk_WipNote表
            WkWipnotePojo upWipPojo = new WkWipnotePojo();
            upWipPojo.setId(wkWipnotePojo.getId());
            upWipPojo.setWkrownum(wkWipnoteitemPojo.getRownum());
            upWipPojo.setWkwpid(wkWipnoteitemPojo.getWpid());
            upWipPojo.setWkwpcode(wkWipnoteitemPojo.getWpcode());
            upWipPojo.setWkwpname(wkWipnoteitemPojo.getWpname());
            upWipPojo.setWkspecjson(quickPojo.getSpecjson());
            upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
            upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
            upWipPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
            upWipPojo.setTenantid(loginUser.getTenantid());
            upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
            upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
            // 加工单
            if (isNotBlank(wkWipnotePojo.getWorkitemid())) {
                this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                    this.wkWipqtyMapper.updateWorkWkwpByMerge(upWipPojo);
                    this.wkWipqtyMapper.updateWorkBillWkwpByMerge(upWipPojo);
                }
            }
            //更新bus_machiningitem表
            if (isNotBlank(wkWipnotePojo.getMachitemid())) {
                this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                    this.wkWipqtyMapper.updateMachWkwpByMerge(upWipPojo);
                    this.wkWipqtyMapper.updateMachBillWkwpByMerge(upWipPojo);
                }
            }
            // 同步Wk_MainPlan生产主计划主子表工序状态
            if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                    this.wkWipqtyMapper.updateMainPlanWkwpByMerge(upWipPojo);
                    this.wkWipqtyMapper.updateMainPlanBillWkwpByMerge(upWipPojo);
                }
            }
        }
        if (wkWipnoteitemPojo.getRownum() > 1) {
            // 超数检查：本工序【已入组数量+本次入组数量】是否大于【wipNote主表总数】
            if (remQty < wkWipnoteitemPojo.getInpcsqty() + crtQty) {
                throw new BaseBusinessException("本次入组数量" + crtQty + "超出wip结余数量" + (remQty - wkWipnoteitemPojo.getInpcsqty()));
            }
            // 如果传入时间,则更新WkWipnoteItem的入组时间
            Date startDate = quickPojo.getStartdate() == null ? new Date() : quickPojo.getStartdate();
            wkWipnoteitemMapper.updateStartDate(wkWipnoteitemPojo.getId(), startDate, tid);
            //创建QRY对象
            WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
            wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyEntity.setWkdate(new Date());
            wkWipqtyEntity.setDirection("入组");
            wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyEntity.setWorker(quickPojo.getWorker());
            wkWipqtyEntity.setPcsqty(crtQty);
//                Double SecQty = wkWipnoteitemPojoPre.getInsecqty() - wkWipnoteitemPojoPre.getOutsecqty() - wkWipnoteitemPojoPre.getMrbsecqty();
            wkWipqtyEntity.setSecqty(0D);
            wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyEntity.setMrbpcsqty(0D);
            wkWipqtyEntity.setMrbsecqty(0D);
            wkWipqtyEntity.setMrbid("");
            wkWipqtyEntity.setAcceid("");
            wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
            wkWipqtyEntity.setListerid(quickPojo.getListerid());
            wkWipqtyEntity.setCreateby(loginUser.getRealName());
            wkWipqtyEntity.setLister(quickPojo.getLister());
            wkWipqtyEntity.setCreatedate(new Date());
            wkWipqtyEntity.setModifydate(new Date());
            wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyEntity.setRemark("任意工序仅过数入组");
            wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyEntity.setRevision(1);
            // 加入尺寸 Eric20221008
            wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
            this.wkWipqtyMapper.insert(wkWipqtyEntity);
            wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), null, tid);
            //更新Wk_WipNote表
            WkWipnotePojo upWipPojo = new WkWipnotePojo();
            upWipPojo.setId(wkWipnotePojo.getId());
            upWipPojo.setWkrownum(wkWipnoteitemPojo.getRownum());
            upWipPojo.setWkwpid(wkWipnoteitemPojo.getWpid());
            upWipPojo.setWkwpcode(wkWipnoteitemPojo.getWpcode());
            upWipPojo.setWkwpname(wkWipnoteitemPojo.getWpname());
            upWipPojo.setWkspecjson(quickPojo.getSpecjson());
            upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
            upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
            upWipPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
            upWipPojo.setTenantid(loginUser.getTenantid());
            upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
            upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
            // 加工单
            if (isNotBlank(wkWipnotePojo.getWorkitemid())) {
                this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
            }
            //更新bus_machiningitem表
            if (isNotBlank(wkWipnotePojo.getMachitemid())) {
                this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
            }
            // 同步Wk_MainPlan生产主计划主子表工序状态
            if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
            }
            // 同步 WorkParam eric 20230304
            if (isNotBlank(quickPojo.getWorkparam())) {
                this.mergeWorkparam(wkWipnoteitemPojo.getId(), tid);
            }
        }
        return this.wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);

    }


    @Override  //quickPojo里只传入了wpid(工序id),wipid,remark
    @Transactional
    public WkWipnoteitemPojo anyQuickAddWipItem(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        String wpid = quickPojo.getWpid();
        String wipid = quickPojo.getWipid();
        String remark = quickPojo.getRemark();

        // 检查wip下的Item是否已存在wpid
        int i = this.wkWipnoteitemMapper.countWipItemWpid(wpid, wipid, tid);
        if (i > 0) {
            throw new BaseBusinessException("工序已存在");
        }


        // 查询工序
        WkProcessPojo wkProcessPojo = wkProcessMapper.getEntity(wpid, tid);
        // 查询DB中WipNoteItem最大RowNum(将工序加入到WipNoteItem表倒数第二行(即RowNum),原来的最后一行变为RowNum+1)
        Map<String, Object> maxWipItemMap = this.wkWipnoteMapper.getMaxWipItemRowNumAndId(wipid, tid);
        if (maxWipItemMap == null) {
            maxWipItemMap = new HashMap<>();
            maxWipItemMap.put("RowNum", 0);
            maxWipItemMap.put("id", wipid);
        }
        // DB中最大行号,id
        Integer maxWipItemRowNum = (Integer) maxWipItemMap.get("RowNum");
        String maxWipItemId = (String) maxWipItemMap.get("id");
        // 将原来的最后一行变为RowNum+1
        this.wkWipnoteitemMapper.updateWipItemRowNum(maxWipItemId, maxWipItemRowNum + 1, tid);
        // 构建WipNoteItem对象(工序id,code,name,remark,rownum,lister)
        WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
        wkWipnoteitemPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipnoteitemPojo.setPid(wipid);
        wkWipnoteitemPojo.setRownum(maxWipItemRowNum);
        wkWipnoteitemPojo.setWpid(wpid);
        wkWipnoteitemPojo.setWpcode(wkProcessPojo.getWpcode());
        wkWipnoteitemPojo.setWpname(wkProcessPojo.getWpname());
        wkWipnoteitemPojo.setLister(loginUser.getRealname());
        wkWipnoteitemPojo.setRemark(remark);
        wkWipnoteitemPojo.setTenantid(tid);
        return wkWipnoteitemService.insert(wkWipnoteitemPojo);
    }


    // 只传入wipid,rownum
    @Override
    //@Transactional
    public String adminQuickInput(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        try {
            // 只传入wipid,rownum
            String wipid = quickPojo.getWipid();
            Integer rownum = quickPojo.getRownum();
            // 查询是否有工单
            WkWipnotePojo wkWipnoteBillPojo = wkWipnoteService.getBillEntity(wipid, tid);
            if (wkWipnoteBillPojo == null) {
                throw new BaseBusinessException("没找到WIP记录" + quickPojo.getWorkuid());
            }

            List<WkWipnoteitemPojo> wipItemAllList = wkWipnoteBillPojo.getItem();
            // 按行号从小到大排序，拿到所有行号【小于等于】当前行号的工序
            List<WkWipnoteitemPojo> wipItemPreList = wipItemAllList.stream()
                    .filter(item -> item.getRownum() <= rownum)
                    .sorted(Comparator.comparingInt(WkWipnoteitemPojo::getRownum))
                    .collect(Collectors.toList());
            // 找到入组数量减去出组数量大于 0 的首个符合条件的 WkWipnoteitemPojo 对象
            WkWipnoteitemPojo firstRemItem = wipItemPreList.stream()
                    .filter(item -> item.getInpcsqty() - item.getOutpcsqty() - item.getMrbpcsqty() > 0)
                    .findFirst()
                    .orElseThrow(() -> new BaseBusinessException("没有找到可过数行"));

            // 最终需要循环过数的wipitem集合
            // 循环开始的行号 ，过滤出从 rownumRem 开始的行号的 WkWipnoteitemPojo 列表
            Integer rownumRem = firstRemItem.getRownum();
            List<WkWipnoteitemPojo> wipItemList = wipItemPreList.stream()
                    .filter(item -> item.getRownum() >= rownumRem)
                    .collect(Collectors.toList());

            // 可过数数量 (用于循环过速的数量)
            double firstRemPcsQty = firstRemItem.getInpcsqty() - firstRemItem.getOutpcsqty() - firstRemItem.getMrbpcsqty();
            double firstRemSecQty = firstRemItem.getInsecqty() - firstRemItem.getOutsecqty() - firstRemItem.getMrbsecqty();

            //---------------------------------------批量过速开始-----------------------------------------
            //用/quickOutPut逻辑先出组当前工序再入组下道工序，所以循环的最后一次是传入行的前一行  wipItemList.size若为3，则需循环2次
            for (int i = 0; i < wipItemList.size() - 1; i++) {

                // 当前记录（必须从数据库读取，上次循环中可能改变了这次的值）
                WkWipnoteitemPojo wkWipnoteitemPojo = wkWipnoteitemMapper.getEntity(wipItemList.get(i).getId(), tid);
                // 拿到后一条记录
                WkWipnoteitemPojo wkWipnoteitemPojoNext = wipItemList.get(i + 1);
                // 检查当前工序是否已出组完成；是否能过数firstRemQty个数量
                double RemQty = wkWipnoteitemPojo.getInpcsqty() - wkWipnoteitemPojo.getOutpcsqty() - wkWipnoteitemPojo.getMrbpcsqty();
                if (RemQty <= 0) {
                    throw new BaseBusinessException("工序【" + wkWipnoteitemPojo.getWpname() + "】已出组完成");
                }
                if (RemQty < firstRemPcsQty) {
                    throw new BaseBusinessException("工序【" + wkWipnoteitemPojo.getWpname() + "】结余数量" + RemQty + "不足本次过数量" + firstRemPcsQty);
                }
                // 过数记录
                WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
                wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
                wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
                wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
                wkWipqtyEntity.setWkdate(new Date());
                wkWipqtyEntity.setDirection("出组");
                wkWipqtyEntity.setGoodsid(wkWipnoteBillPojo.getGoodsid());
                wkWipqtyEntity.setPcsqty(firstRemPcsQty);
                wkWipqtyEntity.setSecqty(firstRemSecQty);
                wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
                wkWipqtyEntity.setWorkuid(wkWipnoteBillPojo.getWorkuid());
                wkWipqtyEntity.setWipuid(wkWipnoteBillPojo.getRefno());
                wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
                wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
                wkWipqtyEntity.setMrbpcsqty(0D);
                wkWipqtyEntity.setMrbsecqty(0D);
                wkWipqtyEntity.setMrbid("");
                wkWipqtyEntity.setAcceid("");
                wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
                wkWipqtyEntity.setListerid(quickPojo.getListerid());
                wkWipqtyEntity.setCreateby(loginUser.getRealName());
                wkWipqtyEntity.setLister(quickPojo.getLister());
                wkWipqtyEntity.setCreatedate(new Date());
                wkWipqtyEntity.setModifydate(new Date());
                wkWipqtyEntity.setMachuid(wkWipnoteBillPojo.getMachuid());
                wkWipqtyEntity.setMachitemid(wkWipnoteBillPojo.getMachitemid());
                wkWipqtyEntity.setMainplanuid(wkWipnoteBillPojo.getMainplanuid());
                wkWipqtyEntity.setMainplanitemid(wkWipnoteBillPojo.getMainplanitemid());
                wkWipqtyEntity.setWorkitemid(wkWipnoteBillPojo.getWorkitemid());
                wkWipqtyEntity.setMachgroupid(wkWipnoteBillPojo.getMachgroupid());
                wkWipqtyEntity.setTenantid(wkWipnoteBillPojo.getTenantid());
                wkWipqtyEntity.setRemark("扫码过数出组");
                wkWipqtyEntity.setAttributejson(wkWipnoteBillPojo.getAttributejson());
                wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
                wkWipqtyEntity.setWorkparam(quickPojo.getWorkparam());  // 完工参数
                wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
                wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
                wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
                wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
                wkWipqtyEntity.setWorker(quickPojo.getWorker()); //工人
                wkWipqtyEntity.setRevision(1);
                // 加入尺寸 Eric20221008
                wkWipqtyEntity.setSizex(wkWipnoteBillPojo.getSizex());
                wkWipqtyEntity.setSizey(wkWipnoteBillPojo.getSizey());
                wkWipqtyEntity.setSizez(wkWipnoteBillPojo.getSizez());
                transactionTemplate.execute((status) -> {
                    this.wkWipqtyMapper.insert(wkWipqtyEntity);
                    //20231103记录出组操作人(前端传入)
                    this.wkWipnoteitemMapper.updateOutQtyAndOutWorker(wkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), quickPojo.getWorker(), tid);
                    // 20240402读取指定系统参数 判断是否需要同步生产WIP的JobPcsQty字段。na 不控制 first 首工序 key 关键工序
                    String updatejobqty = systemFeignService.getConfigValue("module.manu.updatejobqty", loginUser.getTenantid(), loginUser.getToken()).getData();
                    if ("first".equals(updatejobqty) && wkWipnoteitemPojo.getRownum() == 1) {
                        this.wkWipnoteitemMapper.updateJobPcsQty(wkWipnoteitemPojo.getId(), wkWipnoteitemPojo.getPid(), tid);
                    } else if ("key".equals(updatejobqty) && wkWipnoteitemMapper.checkKeyMark(quickPojo.getWpid(), tid) == 1) {
                        this.wkWipnoteitemMapper.updateJobPcsQty(wkWipnoteitemPojo.getId(), wkWipnoteitemPojo.getPid(), tid);
                    }
                    // 加入随过功能 Eric 20220915
                    if (wkWipnoteitemPojoNext.getRownum() - wkWipnoteitemPojo.getRownum() > 1) {
                        FollowPost(wkWipnoteBillPojo.getId(), wkWipnoteitemPojo.getRownum() + 1, wkWipnoteitemPojoNext.getRownum() - 1, wkWipqtyEntity);
                    }
                    // 同步 WorkParam eric 20230304
                    if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                        this.mergeWorkparam(wkWipnoteitemPojo.getId(), tid);
                    }
                    // 同步 SPCEJSON eric 20220903
                    if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                        this.sumSpecjson(wkWipnoteitemPojo.getId(), tid);
                    }
                    // 后记录入组
                    wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                    wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    wkWipqtyEntity.setWiprownum(wkWipnoteitemPojoNext.getRownum());
                    wkWipqtyEntity.setWipitemid(wkWipnoteitemPojoNext.getId());
                    wkWipqtyEntity.setDirection("入组");
                    wkWipqtyEntity.setWpid(wkWipnoteitemPojoNext.getWpid());
                    wkWipqtyEntity.setWpcode(wkWipnoteitemPojoNext.getWpcode());
                    wkWipqtyEntity.setWpname(wkWipnoteitemPojoNext.getWpname());
                    wkWipqtyEntity.setAttributejson(wkWipnoteBillPojo.getAttributejson());
                    // 加入尺寸 Eric20221008
                    wkWipqtyEntity.setSizex(wkWipnoteBillPojo.getSizex());
                    wkWipqtyEntity.setSizey(wkWipnoteBillPojo.getSizey());
                    wkWipqtyEntity.setSizez(wkWipnoteBillPojo.getSizez());
                    this.wkWipqtyMapper.insert(wkWipqtyEntity);
                    // 20231103记录入组操作人(前端传入)
                    this.wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojoNext.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), quickPojo.getWorker(), tid);

                    //更新Wk_WipNote表
                    WkWipnotePojo upWipPojo = new WkWipnotePojo();
                    upWipPojo.setId(wkWipnoteBillPojo.getId());
                    upWipPojo.setWkrownum(wkWipnoteitemPojoNext.getRownum());
                    upWipPojo.setWkwpid(wkWipnoteitemPojoNext.getWpid());
                    upWipPojo.setWkwpcode(wkWipnoteitemPojoNext.getWpcode());
                    upWipPojo.setWkwpname(wkWipnoteitemPojoNext.getWpname());
                    upWipPojo.setWkspecjson(quickPojo.getSpecjson());
                    upWipPojo.setMachitemid(wkWipnoteBillPojo.getMachitemid());
                    upWipPojo.setMachuid(wkWipnoteBillPojo.getMachuid());
                    upWipPojo.setWorkitemid(wkWipnoteBillPojo.getWorkitemid());
                    upWipPojo.setTenantid(loginUser.getTenantid());
                    upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
                    upWipPojo.setMainplanitemid(wkWipnoteBillPojo.getMainplanitemid());

                    this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                    // 加工单
                    if (!wkWipnoteBillPojo.getWorkitemid().isEmpty()) {
                        this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                        if (wkWipnoteBillPojo.getMergemark() != null && wkWipnoteBillPojo.getMergemark() == 2) {
                            this.wkWipqtyMapper.updateWorkWkwpByMerge(upWipPojo);
                            this.wkWipqtyMapper.updateWorkBillWkwpByMerge(upWipPojo);
                        }
                    }
                    //更新bus_machiningitem表
                    if (!wkWipnoteBillPojo.getMachitemid().isEmpty()) {
                        this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                        if (wkWipnoteBillPojo.getMergemark() != null && wkWipnoteBillPojo.getMergemark() == 2) {
                            this.wkWipqtyMapper.updateMachWkwpByMerge(upWipPojo);
                            this.wkWipqtyMapper.updateMachBillWkwpByMerge(upWipPojo);
                        }
                    }
                    // TODO 同步Wk_MainPlan生产主计划主子表工序状态
                    if (!wkWipnoteBillPojo.getMainplanitemid().isEmpty()) {
                        this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                        if (wkWipnoteBillPojo.getMergemark() != null && wkWipnoteBillPojo.getMergemark() == 2) {
                            this.wkWipqtyMapper.updateMainPlanWkwpByMerge(upWipPojo);
                            this.wkWipqtyMapper.updateMainPlanBillWkwpByMerge(upWipPojo);
                        }
                    }
                    return Boolean.TRUE;
                });
            }//-------------批量过速结束-----------------
            return "ok";
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    // 快速出组(下工序自动入)
    @Override
    //@Transactional
    public WkWipnotePojo quickOutput(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录" + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());

            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = wkWipnoteitemMapper.getListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), tid);
            WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
            if (!list.isEmpty()) {
                wkWipnoteitemPojo = list.get(0);
            }
            if (list.isEmpty()) {
                throw new BaseBusinessException("未找到WIP工序记录" + quickPojo.getWorkuid());
            }
            if (wkWipnoteitemPojo.getInpcsqty() == 0) {
                throw new BaseBusinessException("单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达本工序");
            }
            double RemQty = wkWipnoteitemPojo.getInpcsqty() - wkWipnoteitemPojo.getOutpcsqty() - wkWipnoteitemPojo.getMrbpcsqty();
            if (RemQty <= 0) {
                throw new BaseBusinessException("本工序已出组完成");
            }

            if (quickPojo.getQty() != null && RemQty < quickPojo.getQty()) {
                throw new BaseBusinessException("结余数量" + RemQty + "不足本次过数量" + quickPojo.getQty());
            }
            Double CrtQty = quickPojo.getQty() == null ? RemQty : quickPojo.getQty();

            // 拿到后一个行号
            quickPojo.setRownum(wkWipnoteitemPojo.getRownum() + 1);
            // 拿到后一条记录
            WkWipnoteitemPojo wkWipnoteitemPojoNext = wkWipnoteitemMapper.getNextPostEntityByRownum(quickPojo.getRownum(), quickPojo.getWipid(), tid);
            // 无后一条记录
            if (wkWipnoteitemPojoNext == null) {
                throw new BaseBusinessException("最后工序请用完工入库操作");
            }
            // 过数记录
            WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
            wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyEntity.setWkdate(new Date());
            wkWipqtyEntity.setDirection("出组");
            wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyEntity.setPcsqty(CrtQty);
            wkWipqtyEntity.setSecqty(0D);
            wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyEntity.setMrbpcsqty(0D);
            wkWipqtyEntity.setMrbsecqty(0D);
            wkWipqtyEntity.setMrbid("");
            wkWipqtyEntity.setAcceid("");
            wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
            wkWipqtyEntity.setListerid(quickPojo.getListerid());
            wkWipqtyEntity.setCreateby(loginUser.getRealName());
            wkWipqtyEntity.setLister(quickPojo.getLister());
            wkWipqtyEntity.setCreatedate(new Date());
            wkWipqtyEntity.setModifydate(new Date());
            wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyEntity.setRemark("扫码过数出组");
            wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyEntity.setWorkparam(quickPojo.getWorkparam());  // 完工参数
            wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyEntity.setWorker(quickPojo.getWorker()); //工人
            wkWipqtyEntity.setRevision(1);
            // 加入尺寸 Eric20221008
            wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
            WkWipnoteitemPojo finalWkWipnoteitemPojo = wkWipnoteitemPojo;
            transactionTemplate.execute((status) -> {
                this.wkWipqtyMapper.insert(wkWipqtyEntity);
                //20231103记录出组操作人(前端传入)
                this.wkWipnoteitemMapper.updateOutQtyAndOutWorker(finalWkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), quickPojo.getWorker(), tid);
                // 20240402读取指定系统参数 判断是否需要同步生产WIP的JobPcsQty字段。na 不控制 first 首工序 key 关键工序
                String updatejobqty = systemFeignService.getConfigValue("module.manu.updatejobqty", loginUser.getTenantid(), loginUser.getToken()).getData();
                if ("first".equals(updatejobqty) && finalWkWipnoteitemPojo.getRownum() == 1) {
                    this.wkWipnoteitemMapper.updateJobPcsQty(finalWkWipnoteitemPojo.getId(), finalWkWipnoteitemPojo.getPid(), tid);
                } else if ("key".equals(updatejobqty) && wkWipnoteitemMapper.checkKeyMark(quickPojo.getWpid(), tid) == 1) {
                    this.wkWipnoteitemMapper.updateJobPcsQty(finalWkWipnoteitemPojo.getId(), finalWkWipnoteitemPojo.getPid(), tid);
                }
                // 加入随过功能 Eric 20220915
                if (wkWipnoteitemPojoNext.getRownum() - finalWkWipnoteitemPojo.getRownum() > 1) {
                    FollowPost(wkWipnotePojo.getId(), finalWkWipnoteitemPojo.getRownum() + 1, wkWipnoteitemPojoNext.getRownum() - 1, wkWipqtyEntity);
                }
                // 同步 WorkParam eric 20230304
                if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                    this.mergeWorkparam(finalWkWipnoteitemPojo.getId(), tid);
                }
                // 同步 SPCEJSON eric 20220903
                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                    this.sumSpecjson(finalWkWipnoteitemPojo.getId(), tid);
                }
                // 后记录入组
                wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipqtyEntity.setWiprownum(wkWipnoteitemPojoNext.getRownum());
                wkWipqtyEntity.setWipitemid(wkWipnoteitemPojoNext.getId());
                wkWipqtyEntity.setDirection("入组");
                wkWipqtyEntity.setWpid(wkWipnoteitemPojoNext.getWpid());
                wkWipqtyEntity.setWpcode(wkWipnoteitemPojoNext.getWpcode());
                wkWipqtyEntity.setWpname(wkWipnoteitemPojoNext.getWpname());
                wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
                // 加入尺寸 Eric20221008
                wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
                wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
                wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
                this.wkWipqtyMapper.insert(wkWipqtyEntity);
                // 20231103记录入组操作人(前端传入)
                this.wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojoNext.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), quickPojo.getWorker(), tid);

                //更新Wk_WipNote表
                WkWipnotePojo upWipPojo = new WkWipnotePojo();
                upWipPojo.setId(wkWipnotePojo.getId());
                upWipPojo.setWkrownum(wkWipnoteitemPojoNext.getRownum());
                upWipPojo.setWkwpid(wkWipnoteitemPojoNext.getWpid());
                upWipPojo.setWkwpcode(wkWipnoteitemPojoNext.getWpcode());
                upWipPojo.setWkwpname(wkWipnoteitemPojoNext.getWpname());
                upWipPojo.setWkspecjson(quickPojo.getSpecjson());
                upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
                upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
                upWipPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
                upWipPojo.setTenantid(loginUser.getTenantid());
                upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
                upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());

                this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                // 加工单
                if (!wkWipnotePojo.getWorkitemid().isEmpty()) {
                    this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateWorkWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateWorkBillWkwpByMerge(upWipPojo);
                    }
                }
                //更新bus_machiningitem表
                if (!wkWipnotePojo.getMachitemid().isEmpty()) {
                    this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateMachWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateMachBillWkwpByMerge(upWipPojo);
                    }
                }
                // TODO 同步Wk_MainPlan生产主计划主子表工序状态
                if (!wkWipnotePojo.getMainplanitemid().isEmpty()) {
                    this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateMainPlanWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateMainPlanBillWkwpByMerge(upWipPojo);
                    }
                }
                return Boolean.TRUE;
            });
            return this.wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);

        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    // 仅出组
    @Override
    //@Transactional
    public WkWipnotePojo quickFinish(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录" + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());

            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = wkWipnoteitemMapper.getListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), tid);
            WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
            if (!list.isEmpty()) {
                wkWipnoteitemPojo = list.get(0);
            }
            if (list.isEmpty()) {
                throw new BaseBusinessException("未找到WIP工序记录" + quickPojo.getWorkuid());
            }
            if (wkWipnoteitemPojo.getInpcsqty() == 0) {
                throw new BaseBusinessException("单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达本工序");
            }
            Double remQty = wkWipnoteitemPojo.getInpcsqty() - wkWipnoteitemPojo.getOutpcsqty() - wkWipnoteitemPojo.getMrbpcsqty();
            if (remQty <= 0) {
                throw new BaseBusinessException("本工序已出组完成");
            }

            if (quickPojo.getQty() != null && remQty < quickPojo.getQty()) {
                throw new BaseBusinessException("结余数量" + remQty + "不足本次过数量" + quickPojo.getQty());
            }
            Double crtQty = quickPojo.getQty() == null ? remQty : quickPojo.getQty();

            // 拿到后一个行号
            quickPojo.setRownum(wkWipnoteitemPojo.getRownum() + 1);
            // 拿到后一条记录
            WkWipnoteitemPojo wkWipnoteitemPojoNext = wkWipnoteitemMapper.getNextPostEntityByRownum(quickPojo.getRownum(), quickPojo.getWipid(), tid);
            // 无后一条记录
            if (wkWipnoteitemPojoNext == null) {
                throw new BaseBusinessException("最后工序请用完工入库操作");
            }
            // 20230830 校验是否禁止出组
            if (wkWipnoteitemPojo.getDisableout() != null && wkWipnoteitemPojo.getDisableout() == 1) {
                throw new BaseBusinessException("当前工序已设定为禁止出组");
            }
            // 【工序周期操作】当前工序下料(出组)时间Enddate:当前时间;  下道工序上料计划时间StartPlan：当前时间加上(下一道工序的前置QZ)周期时间    0全程1前置2后置
            WkProccyclePojo wkProccyclePojoNextQZ = wkProccycleMapper.getEntityByWpidAndType(wkWipnoteitemPojoNext.getWpid(), 1, tid);
            if (wkProccyclePojoNextQZ == null) {
                throw new BaseBusinessException("未找到工序周期信息");
            }
            Date endDate = new Date();
            wkWipnoteitemPojo.setEnddate(endDate);
            LocalDateTime addlocalDateTime = MyDateUtils.addTime(LocalDateTime.now(), wkProccyclePojoNextQZ.getCyclevalue(), wkProccyclePojoNextQZ.getCycleunit());
            Date addDate = Date.from(addlocalDateTime.atZone(ZoneId.systemDefault()).toInstant());// LocalDateTime转为Date
            PrintColor.red("addDate：" + addDate);
            wkWipnoteitemPojoNext.setStartplan(addDate);
            // 下料时间过期检查(当isadmin=1时，不检查) 20240425加入或者isMock为true也不检查
            if (quickPojo.getIsadmin() == null) {
                // 检查EndDate必须在PlanDate左右分钟误差范围内 读取当前工序(全程QC)的左右误差
                WkProccyclePojo wkProccyclePojoQC = wkProccycleMapper.getEntityByWpidAndType(wkWipnoteitemPojo.getWpid(), 0, tid);
                if (endDate.before(MyDateUtils.subMinutesOnDate(wkWipnoteitemPojo.getPlandate(), wkProccyclePojoQC.getLefttole()))) {
                    throw new ExpiredException("当前下料时间过早");
                } else if (endDate.after(MyDateUtils.addMinutesOnDate(wkWipnoteitemPojo.getPlandate(), wkProccyclePojoQC.getRighttole()))) {
                    throw new ExpiredException("当前下料时间已过期");
                }
            }


            // 过数记录
            WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
            wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyEntity.setWkdate(new Date());
            wkWipqtyEntity.setDirection("出组");
            wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyEntity.setWorker(quickPojo.getWorker());
            wkWipqtyEntity.setPcsqty(crtQty);
            wkWipqtyEntity.setSecqty(0D);
            wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyEntity.setMrbpcsqty(0D);
            wkWipqtyEntity.setMrbsecqty(0D);
            wkWipqtyEntity.setMrbid("");
            wkWipqtyEntity.setAcceid("");
            wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
            wkWipqtyEntity.setListerid(quickPojo.getListerid());
            wkWipqtyEntity.setCreateby(loginUser.getRealName());
            wkWipqtyEntity.setLister(quickPojo.getLister());
            wkWipqtyEntity.setCreatedate(new Date());
            wkWipqtyEntity.setModifydate(new Date());
            wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyEntity.setRemark("仅过数出组");
            wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyEntity.setWorkparam(quickPojo.getWorkparam());  // 完工参数
            wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyEntity.setRevision(1);
            // 加入尺寸 Eric20221008
            wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
            WkWipnoteitemPojo finalWkWipnoteitemPojo = wkWipnoteitemPojo;
            transactionTemplate.execute((status) -> {
                this.wkWipqtyMapper.insert(wkWipqtyEntity);
                // 20230904 记录出组操作人(前端传入quickPojo.getWorker())
                this.wkWipnoteitemMapper.updateOutQtyAndOutWorker(finalWkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), quickPojo.getWorker(), tid);
                // 同步 WorkParam eric 20230304
                if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                    this.mergeWorkparam(finalWkWipnoteitemPojo.getId(), tid);
                }
                // 同步 SPCEJSON eric 20220903
                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                    this.sumSpecjson(finalWkWipnoteitemPojo.getId(), tid);
                }

                // ---------------------------后记录入组-----------------------
                this.wkWipnoteitemService.update(wkWipnoteitemPojoNext);
//                wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
//                wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
//                wkWipqtyEntity.setWiprownum(wkWipnoteitemPojoNext.getRownum());
//                wkWipqtyEntity.setWipitemid(wkWipnoteitemPojoNext.getId());
//                wkWipqtyEntity.setDirection("入组");
//                wkWipqtyEntity.setWpid(wkWipnoteitemPojoNext.getWpid());
//                wkWipqtyEntity.setWpcode(wkWipnoteitemPojoNext.getWpcode());
//                wkWipqtyEntity.setWpname(wkWipnoteitemPojoNext.getWpname());
//                wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
//                // 加入尺寸 Eric20221008
//                wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
//                wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
//                wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
//                this.wkWipqtyMapper.insert(wkWipqtyEntity);
//                this.wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojoNext.getId(), wkWipqtyEntity.getWkdate(), tid);
//                // 加入随过功能 Eric 20220915
//                if (wkWipnoteitemPojoNext.getRownum() - finalWkWipnoteitemPojo.getRownum() > 1) {
//                    FollowPost(wkWipnotePojo.getId(), finalWkWipnoteitemPojo.getRownum() + 1, wkWipnoteitemPojoNext.getRownum() - 1, wkWipqtyEntity);
//                }
                // ---------------------------后记录入组结束-----------------------

                //更新Wk_WipNote表
                WkWipnotePojo upWipPojo = new WkWipnotePojo();
                upWipPojo.setId(wkWipnotePojo.getId());
                // 仅过数出组，不用更新工序了
//                upWipPojo.setWkrownum(wkWipnoteitemPojoNext.getRownum());
//                upWipPojo.setWkwpid(wkWipnoteitemPojoNext.getWpid());
//                upWipPojo.setWkwpcode(wkWipnoteitemPojoNext.getWpcode());
//                upWipPojo.setWkwpname(wkWipnoteitemPojoNext.getWpname());
                upWipPojo.setWkrownum(finalWkWipnoteitemPojo.getRownum());
                upWipPojo.setWkwpid(finalWkWipnoteitemPojo.getWpid());
                upWipPojo.setWkwpcode(finalWkWipnoteitemPojo.getWpcode());
                upWipPojo.setWkwpname(finalWkWipnoteitemPojo.getWpname());
                upWipPojo.setWkspecjson(quickPojo.getSpecjson());
                upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
                upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
                upWipPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
                upWipPojo.setTenantid(loginUser.getTenantid());
                upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
                upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());

                this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                // 加工单
                if (isNotBlank(wkWipnotePojo.getWorkitemid())) {
                    this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateWorkWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateWorkBillWkwpByMerge(upWipPojo);
                    }
                }
                //更新bus_machiningitem表
                if (isNotBlank(wkWipnotePojo.getMachitemid())) {
                    this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateMachWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateMachBillWkwpByMerge(upWipPojo);
                    }
                }
                // 同步Wk_MainPlan生产主计划主子表工序状态
                if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                    this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateMainPlanWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateMainPlanBillWkwpByMerge(upWipPojo);
                    }
                }

                return Boolean.TRUE;
            });
            return this.wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);

        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    //@Transactional
    public WkWipnotePojo quickFinishSub(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录" + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());

            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = wkWipnoteitemMapper.getListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), tid);
            WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
            if (!list.isEmpty()) {
                wkWipnoteitemPojo = list.get(0);
            }
            if (list.isEmpty()) {
                throw new BaseBusinessException("未找到WIP工序记录" + quickPojo.getWorkuid());
            }
            if (wkWipnoteitemPojo.getInpcsqty() == 0) {
                throw new BaseBusinessException("单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达本工序");
            }
            double remQty = wkWipnoteitemPojo.getInpcsqty() - wkWipnoteitemPojo.getOutpcsqty() - wkWipnoteitemPojo.getMrbpcsqty();
            if (remQty <= 0) {
                throw new BaseBusinessException("本工序已出组完成");
            }

            if (quickPojo.getQty() != null && remQty < quickPojo.getQty()) {
                throw new BaseBusinessException("结余数量" + remQty + "不足本次过数量" + quickPojo.getQty());
            }
            Double crtQty = quickPojo.getQty() == null ? remQty : quickPojo.getQty();

            // 拿到后一个行号
            quickPojo.setRownum(wkWipnoteitemPojo.getRownum() + 1);
            // 拿到后一条记录
            WkWipnoteitemPojo wkWipnoteitemPojoNext = wkWipnoteitemMapper.getNextPostEntityByRownum(quickPojo.getRownum(), quickPojo.getWipid(), tid);
            // 无后一条记录
            if (wkWipnoteitemPojoNext == null) {
                throw new BaseBusinessException("最后工序请用完工入库操作");
            }
            // 过数记录
            WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
            // 标记为增减料过数 StoreMark=1
            wkWipqtyEntity.setStoremark(1);
            wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyEntity.setWkdate(new Date());
            wkWipqtyEntity.setDirection("出组");
            wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyEntity.setWorker(quickPojo.getWorker());
            wkWipqtyEntity.setPcsqty(crtQty);
            wkWipqtyEntity.setSecqty(0D);
            wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyEntity.setMrbpcsqty(0D);
            wkWipqtyEntity.setMrbsecqty(0D);
            wkWipqtyEntity.setMrbid("");
            wkWipqtyEntity.setAcceid("");
            wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
            wkWipqtyEntity.setListerid(quickPojo.getListerid());
            wkWipqtyEntity.setCreateby(loginUser.getRealName());
            wkWipqtyEntity.setLister(quickPojo.getLister());
            wkWipqtyEntity.setCreatedate(new Date());
            wkWipqtyEntity.setModifydate(new Date());
            wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyEntity.setRemark("仅过数出组");
            wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyEntity.setWorkparam(quickPojo.getWorkparam());  // 完工参数
            wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyEntity.setRevision(1);
            // 加入尺寸 Eric20221008
            wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
            WkWipnoteitemPojo finalWkWipnoteitemPojo = wkWipnoteitemPojo;
            transactionTemplate.execute((status) -> {
                this.wkWipqtyMapper.insert(wkWipqtyEntity);
                this.wkWipnoteitemMapper.updateOutQty(finalWkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), tid);
                // 同步 WorkParam eric 20230304
                if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                    this.mergeWorkparam(finalWkWipnoteitemPojo.getId(), tid);
                }
                // 同步 SPCEJSON eric 20220903
                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                    this.sumSpecjson(finalWkWipnoteitemPojo.getId(), tid);
                }
                return Boolean.TRUE;
            });
            return this.wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);

        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    // 仅出组【任意工序下的】
    @Override
    //@Transactional
    public WkWipnotePojo anyQuickFinish(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录" + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());

            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = wkWipnoteitemMapper.getListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), tid);
            WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
            if (!list.isEmpty()) {
                wkWipnoteitemPojo = list.get(0);
            }
            if (list.isEmpty()) {
                throw new BaseBusinessException("未找到WIP工序记录" + quickPojo.getWorkuid());
            }
//            if (wkWipnoteitemPojo.getInpcsqty() == 0) {
//                throw new BaseBusinessException("单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达本工序");
//            }
            // wipNote主表总数量
            Double remQty = wkWipnotePojo.getWkpcsqty() - wkWipnotePojo.getMrbpcsqty();
            // quickPojo.getQty()如果未输入数量，则默认为剩余数量全数入组;否则按输入数量入组
            Double crtQty = quickPojo.getQty() == null ? remQty : quickPojo.getQty();
            // 超数检查：仅检查当前工序累计出组数量不能大于wipNote主表总数量
            if (remQty < crtQty + wkWipnoteitemPojo.getOutpcsqty()) {
                throw new BaseBusinessException("本次出组数量" + crtQty + "不能大于剩余数量" + (remQty - wkWipnoteitemPojo.getOutpcsqty()));
            }
            // 拿到后一个行号
            quickPojo.setRownum(wkWipnoteitemPojo.getRownum() + 1);
            // 拿到后一条记录
            WkWipnoteitemPojo wkWipnoteitemPojoNext = wkWipnoteitemMapper.getNextPostEntityByRownum(quickPojo.getRownum(), quickPojo.getWipid(), tid);
            // 无后一条记录
            if (wkWipnoteitemPojoNext == null) {
                throw new BaseBusinessException("最后工序请用完工入库操作");
            }
            // 过数记录
            WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
            wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyEntity.setWkdate(new Date());
            wkWipqtyEntity.setDirection("出组");
            wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyEntity.setWorker(quickPojo.getWorker());
            wkWipqtyEntity.setPcsqty(crtQty);
            wkWipqtyEntity.setSecqty(0D);
            wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyEntity.setMrbpcsqty(0D);
            wkWipqtyEntity.setMrbsecqty(0D);
            wkWipqtyEntity.setMrbid("");
            wkWipqtyEntity.setAcceid("");
            wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
            wkWipqtyEntity.setListerid(quickPojo.getListerid());
            wkWipqtyEntity.setCreateby(loginUser.getRealName());
            wkWipqtyEntity.setLister(quickPojo.getLister());
            wkWipqtyEntity.setCreatedate(new Date());
            wkWipqtyEntity.setModifydate(new Date());
            wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyEntity.setRemark("任意工序仅过数出组");
            wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyEntity.setWorkparam(quickPojo.getWorkparam());  // 完工参数
            wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyEntity.setRevision(1);
            // 加入尺寸 Eric20221008
            wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
            WkWipnoteitemPojo finalWkWipnoteitemPojo = wkWipnoteitemPojo;
            transactionTemplate.execute((status) -> {
                this.wkWipqtyMapper.insert(wkWipqtyEntity);
                this.wkWipnoteitemMapper.updateOutQty(finalWkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), tid);
                // 同步 WorkParam eric 20230304
                if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                    this.mergeWorkparam(finalWkWipnoteitemPojo.getId(), tid);
                }
                // 同步 SPCEJSON eric 20220903
                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                    this.sumSpecjson(finalWkWipnoteitemPojo.getId(), tid);
                }
                //更新Wk_WipNote表
                WkWipnotePojo upWipPojo = new WkWipnotePojo();
                upWipPojo.setId(wkWipnotePojo.getId());
                // 仅过数出组，不用更新工序了
//                upWipPojo.setWkrownum(wkWipnoteitemPojoNext.getRownum());
//                upWipPojo.setWkwpid(wkWipnoteitemPojoNext.getWpid());
//                upWipPojo.setWkwpcode(wkWipnoteitemPojoNext.getWpcode());
//                upWipPojo.setWkwpname(wkWipnoteitemPojoNext.getWpname());
                upWipPojo.setWkrownum(finalWkWipnoteitemPojo.getRownum());
                upWipPojo.setWkwpid(finalWkWipnoteitemPojo.getWpid());
                upWipPojo.setWkwpcode(finalWkWipnoteitemPojo.getWpcode());
                upWipPojo.setWkwpname(finalWkWipnoteitemPojo.getWpname());
                upWipPojo.setWkspecjson(quickPojo.getSpecjson());
                upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
                upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
                upWipPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
                upWipPojo.setTenantid(loginUser.getTenantid());
                upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
                upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());

                this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                // 加工单
                if (isNotBlank(wkWipnotePojo.getWorkitemid())) {
                    this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateWorkWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateWorkBillWkwpByMerge(upWipPojo);
                    }
                }
                //更新bus_machiningitem表
                if (isNotBlank(wkWipnotePojo.getMachitemid())) {
                    this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateMachWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateMachBillWkwpByMerge(upWipPojo);
                    }
                }
                // 同步Wk_MainPlan生产主计划主子表工序状态
                if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                    this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                        this.wkWipqtyMapper.updateMainPlanWkwpByMerge(upWipPojo);
                        this.wkWipqtyMapper.updateMainPlanBillWkwpByMerge(upWipPojo);
                    }
                }

                return Boolean.TRUE;
            });
            return this.wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);

        } catch (BaseBusinessException e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 指定一道工序:同时进行入组和出组相同数量(出入组记录的RefNo用同一个便于查找和删除)
     * 两套逻辑: (wipqtyid和startdate只传入一个,前端已保证)
     * 一: 20230908 前端传入wipqtyid,说明此接口调用前已经调用过/anyQuickStart生成了入组为0的记录[入组记录xx],目的只为了记录入组时间(WipQty.CreateDate)
     * ,此接口不再创建入组的过数记录,而是去更新[入组记录xx]的PcsQty为本次传入的数量;  然后计算工时(出组记录.WorkTime)用当前时间减去[入组记录xx]的入组时间
     * (前端已保证在WipQty中,WipNoteItem.id拆解生成的N条过速记录只有一个是PcsQty=0的),同步WipNoteItem数量时也不再更新入组时间
     * <p>
     * 二: 20230914 若前端不传入wipqtyid(改为传入startdate),则说明此接口调用前没有先调用/anyQuickStart,需要在此接口先创建[入组记录],入组时间是前端传入的时间startdate;
     * 计算工时(出组记录.WorkTime)用当前时间减去startdate.
     */
    @Override
//    @Transactional
    public WkWipnotePojo anyQuickOneWk(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        // 查看前端是否传入了wipqtyid
        String wipqtyid = quickPojo.getWipqtyid();
        System.out.println("=========================================quickPojo.getWipqtyid() = " + wipqtyid);
        String tid = loginUser.getTenantid();
        try {
            // [通用]查询是否有工单------------------------------------------------------------
            WkWipnotePojo wkWipnotePojo = this.wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录 " + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());
            // wipNote主表总数量
            double remQty = wkWipnotePojo.getWkpcsqty() - wkWipnotePojo.getMrbpcsqty();
            // quickPojo.getQty()如果未输入数量，则默认为剩余数量全数入组;否则按输入数量入组
            Double crtQty = quickPojo.getQty() == null ? remQty : quickPojo.getQty();
            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = this.wkWipnoteitemMapper.getRemListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), remQty, tid);
            if (CollectionUtils.isEmpty(list)) {
                throw new BaseBusinessException("未找到可过数的记录 " + quickPojo.getWorkuid());
            }
            WkWipnoteitemPojo wkWipnoteitemPojo = list.get(0);
            String itemid = wkWipnoteitemPojo.getId();
            // [出组]超数检查：仅检查当前工序累计出组数量不能大于wipNote主表总数量------------------------
            // 关键工序不做检查!20240223  keyMark=0表示不是关键工序,进入检查
            int keyMark = wkWipnoteitemMapper.checkKeyMark(quickPojo.getWpid(), tid);
            if (remQty > 0 && remQty < crtQty + wkWipnoteitemPojo.getOutpcsqty()) {
                throw new BaseBusinessException(crtQty + "," + (remQty - wkWipnoteitemPojo.getOutpcsqty()));
            }
            // 拿到后一个行号
            quickPojo.setRownum(wkWipnoteitemPojo.getRownum() + 1);
            // 是否有后一条记录
            int i = wkWipnoteitemMapper.hasNextByRownum(quickPojo.getRownum(), quickPojo.getWipid(), tid);
            // 无后一条记录
            if (i == 0) {
                throw new BaseBusinessException("最后工序请用完工入库操作");
            }

            // 没有第一道工序和其他工序的区别了
            // 超数检查：本工序【已入组数量+本次入组数量】是否大于【wipNote主表总数】
            if (remQty > 0 && remQty < wkWipnoteitemPojo.getInpcsqty() + crtQty) {
                throw new BaseBusinessException("本次入组数量" + crtQty + "超出wip结余数量" + (remQty - wkWipnoteitemPojo.getInpcsqty()));
            }
            transactionTemplate.execute((status) -> {
                // 若前端传入了wipqtyid 则更新该[入组记录XX]对象;  若没有传入wipqtyid 则创建[入组记录]对象,时间是前端传入的startdate;
                WkWipqtyEntity inWipqtyEntity = new WkWipqtyEntity(); // 入组记录对象
                Date inWipQtyDate = null; // 入组时间(计算工时用)
                if (isNotBlank(wipqtyid)) {
                    // 查询拿到[入组记录XX]
                    WkWipqtyPojo wipqtyPojo = wkWipqtyMapper.getEntityByWipQtyid(wipqtyid, tid);
                    inWipQtyDate = wipqtyPojo.getCreatedate(); //修改CreateDate前先存储入组时间
                    BeanUtils.copyProperties(wipqtyPojo, inWipqtyEntity);
                    // --20230908改为不创建[入组记录]对象,而是更新[入组记录XX]的数量PcsQty和,ModifyDate
                    wkWipqtyMapper.updateWipQtyPcsQty(wipqtyid, crtQty, new Date(), tid);
                } else {
                    inWipQtyDate = quickPojo.getStartdate(); // 前端传入的入组时间
                    //创建QRY[入组记录]对象----------------------------------------
                    WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
                    wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                    wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
                    wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
                    wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
                    wkWipqtyEntity.setWkdate(inWipQtyDate);//过数时间改为传入时间
                    wkWipqtyEntity.setDirection("入组");
                    wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
                    wkWipqtyEntity.setWorker(quickPojo.getWorker());
                    wkWipqtyEntity.setPcsqty(crtQty);
                    wkWipqtyEntity.setSecqty(0D);
                    wkWipqtyEntity.setRemark(wkWipnoteitemPojo.getRemark());
                    wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
                    wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
                    wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
                    wkWipqtyEntity.setWipitemid(itemid);
                    wkWipqtyEntity.setMrbpcsqty(0D);
                    wkWipqtyEntity.setMrbsecqty(0D);
                    wkWipqtyEntity.setMrbid("");
                    wkWipqtyEntity.setAcceid("");
                    wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
                    wkWipqtyEntity.setListerid(quickPojo.getListerid());
                    wkWipqtyEntity.setCreateby(loginUser.getRealName());
                    wkWipqtyEntity.setLister(quickPojo.getLister());
                    wkWipqtyEntity.setCreatedate(new Date());
                    wkWipqtyEntity.setModifydate(new Date());
                    wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
                    wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
                    wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
                    wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
                    wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
                    wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
                    wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
                    wkWipqtyEntity.setRemark("任意工序仅过数入组");
                    wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
                    wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
                    wkWipqtyEntity.setWorkparam(quickPojo.getWorkparam());  // 完工参数
                    wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
                    wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
                    wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
                    wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
                    wkWipqtyEntity.setRevision(1);
                    // 加入尺寸 Eric20221008
                    wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
                    wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
                    wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
                    this.wkWipqtyMapper.insert(wkWipqtyEntity);
                    // 如果传入时间,则更新WkWipnoteItem的[入组]时间
                    wkWipnoteitemMapper.updateStartDate(itemid, inWipQtyDate, tid);
                    //
                    inWipqtyEntity = wkWipqtyEntity;
                }

                // ------------------------------[入组记录]转为[出组记录],改个出组的方向+计算工时-----------------------------------
                inWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                inWipqtyEntity.setDirection("出组");
                inWipqtyEntity.setPcsqty(crtQty);
                inWipqtyEntity.setRemark("任意工序仅过数出组");
                inWipqtyEntity.setWkdate(new Date());
                // 计算毫秒数差值 转换为小时 设置小时数给wkWipqtyEntity对象的Worktime属性
                // 计算工时:当前时间-当前工序的入组时间
                long timeDifferenceInMillis = new Date().getTime() - inWipQtyDate.getTime();
                double hours = (double) timeDifferenceInMillis / (1000 * 60 * 60);
                inWipqtyEntity.setWorktime(hours);
                this.wkWipqtyMapper.insert(inWipqtyEntity);

                // 同步Item数量 入 (传wipqtyid的不再更新入组时间,/anyQuickStart才会更新入组时间; 不传wipqtyid的更新入组时间)
                wkWipnoteitemMapper.updateInQty(itemid, inWipqtyEntity.getPcsqty(), inWipqtyEntity.getSecqty(), null, null, tid);
                // 同步Item数量 出
                wkWipnoteitemMapper.updateOutQty(itemid, inWipqtyEntity.getPcsqty(), inWipqtyEntity.getSecqty(), new Date(), tid);
                // 关键工序出组时同步 JobPcsQty
                if (Objects.equals(keyMark, 1)) {
                    wkWipnoteitemMapper.updateJobPcsQty(itemid, wkWipnoteitemPojo.getPid(), tid);
                }
                //更新Wk_WipNote表
                WkWipnotePojo upWipPojo = new WkWipnotePojo();
                upWipPojo.setId(wkWipnotePojo.getId());
                upWipPojo.setWkrownum(wkWipnoteitemPojo.getRownum());
                upWipPojo.setWkwpid(wkWipnoteitemPojo.getWpid());
                upWipPojo.setWkwpcode(wkWipnoteitemPojo.getWpcode());
                upWipPojo.setWkwpname(wkWipnoteitemPojo.getWpname());
                upWipPojo.setWkspecjson(quickPojo.getSpecjson());
                upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
                upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
                upWipPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
                upWipPojo.setTenantid(loginUser.getTenantid());
                upWipPojo.setWkspecjson(inWipqtyEntity.getSpecjson());
                upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
                this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                // 加工单
                if (isNotBlank(wkWipnotePojo.getWorkitemid())) {
                    this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                }
                //更新bus_machiningitem表
                if (isNotBlank(wkWipnotePojo.getMachitemid())) {
                    this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                }
                // 同步Wk_MainPlan生产主计划主子表工序状态
                if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                    this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                    this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                }
                // 同步 WorkParam eric 20230304
                if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                    this.mergeWorkparam(itemid, tid);
                }
                // 同步 SPCEJSON eric 20220903
                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                    this.sumSpecjson(itemid, tid);
                }
                return Boolean.TRUE;
            });
            return this.wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    //@Transactional
    public String anyQuickFinishByFinishid(String key, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        WkWipfinishPojo finishDB = wkWipfinishService.getBillEntity(key, tid);
        if (finishDB == null) {
            throw new BaseBusinessException("未找到完工单");
        }
        // 完工单主表的工位
        String statid = finishDB.getStationid();
        Date now = new Date();
        List<WkWipfinishitemPojo> finishItemList = finishDB.getItem();
        for (WkWipfinishitemPojo finishitemDB : finishItemList) {
            String wipitemid = finishitemDB.getWipitemid();
            String tasksitemid = finishitemDB.getTasksitemid();
            // 本次要报工的数量 即完工单子表的数量
            Double quantity_Finish = finishitemDB.getQuantity();
            // 查询是否有工单
            // 查询工单中是否操作工序
            WkWipnoteitemPojo wipitemDB = wkWipnoteitemMapper.getEntity(wipitemid, tid);
            if (wipitemDB == null) {
                throw new BaseBusinessException("未找到WipItem记录 (货品编码：" + finishitemDB.getItemcode() + ")");
            }

            // wipNote主表总可用数量
            WkWipnotePojo wipDB = wkWipnoteMapper.getEntityByitemid(wipitemid, tid);
            double remQty = wipDB.getWkpcsqty() - wipDB.getMrbpcsqty();
            // quickPojo.getQty()如果未输入数量，则默认为剩余数量全数入组;否则按输入数量入组
            Double crtQty = quantity_Finish;
            // 超数检查：仅检查当前工序累计出组数量不能大于wipNote主表总数量
            if (remQty < crtQty + wipitemDB.getOutpcsqty()) {
                throw new BaseBusinessException("本次出组数量" + crtQty + "不能大于剩余数量" + (remQty - wipitemDB.getOutpcsqty()));
            }

            // 过数记录
            WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
            wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWipqtyEntity.setWpid(wipitemDB.getWpid());
            wkWipqtyEntity.setStatid(statid);
            //wkWipqtyEntity.setWorkdate(now);
            wkWipqtyEntity.setPcsqty(crtQty);
            wkWipqtyEntity.setWpcode(finishDB.getWpcode());
            wkWipqtyEntity.setWpname(finishDB.getWpname());
            wkWipqtyEntity.setStatcode(finishDB.getStatcode()); //工位编码
            wkWipqtyEntity.setStatname(finishDB.getStatname()); //工位名称
            wkWipqtyEntity.setWkdate(now);
            wkWipqtyEntity.setDirection("出组");
            wkWipqtyEntity.setGoodsid(finishitemDB.getGoodsid());
            wkWipqtyEntity.setWorker(wipitemDB.getItemworker());
            wkWipqtyEntity.setPcsqty(crtQty);
            wkWipqtyEntity.setSecqty(0D);
            wkWipqtyEntity.setRemark(wipitemDB.getRemark());
            wkWipqtyEntity.setWorkuid(wipDB.getWorkuid());
            //wkWipqtyEntity.setWipid(wipDB.getId());
            wkWipqtyEntity.setWipuid(wipDB.getRefno());
            wkWipqtyEntity.setWiprownum(wipitemDB.getRownum());
            wkWipqtyEntity.setWipitemid(wipitemDB.getId());
            wkWipqtyEntity.setMrbpcsqty(0D);
            wkWipqtyEntity.setMrbsecqty(0D);
            wkWipqtyEntity.setMrbid("");
            wkWipqtyEntity.setAcceid("");
            wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
            wkWipqtyEntity.setListerid(loginUser.getUserid());
            wkWipqtyEntity.setCreateby(loginUser.getRealName());
            wkWipqtyEntity.setLister(loginUser.getRealName());
            wkWipqtyEntity.setCreatedate(now);
            wkWipqtyEntity.setModifydate(now);
            wkWipqtyEntity.setMachuid(finishitemDB.getMachuid());
            wkWipqtyEntity.setMachitemid(finishitemDB.getMachitemid());
            wkWipqtyEntity.setMainplanuid(finishitemDB.getMainplanuid());
            wkWipqtyEntity.setMainplanitemid(finishitemDB.getMainplanitemid());
            wkWipqtyEntity.setWorkitemid(finishitemDB.getWorkitemid());
            wkWipqtyEntity.setMachgroupid(finishitemDB.getMachgroupid());
            wkWipqtyEntity.setTenantid(finishitemDB.getTenantid());
            wkWipqtyEntity.setRemark("manu/anyQuickFinishByFinishid 完工单下所有行仅过数出组");
            wkWipqtyEntity.setAttributejson(finishitemDB.getAttributejson());
            wkWipqtyEntity.setSpecjson(wipitemDB.getSpecjson());
            wkWipqtyEntity.setWorkparam(wipitemDB.getWorkparam());  // 完工参数
            wkWipqtyEntity.setWorktime(0D); //TODO 工时
            wkWipqtyEntity.setRevision(1);
            // 加入尺寸 Eric20221008
            wkWipqtyEntity.setSizex(wipDB.getSizex());
            wkWipqtyEntity.setSizey(wipDB.getSizey());
            wkWipqtyEntity.setSizez(wipDB.getSizez());
            WkWipnoteitemPojo finalWipnoteitemPojo = wipitemDB;
            transactionTemplate.execute((status) -> {
                this.wkWipqtyMapper.insert(wkWipqtyEntity);
                this.wkWipnoteitemMapper.updateOutQty(finalWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), tid);
                //// 同步 WorkParam eric 20230304
                //if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                //    this.mergeWorkparam(finalWipnoteitemPojo.getId(), tid);
                //}
                //// 同步 SPCEJSON eric 20220903
                //if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                //    this.sumSpecjson(finalWipnoteitemPojo.getId(), tid);
                //}
                //更新Wk_WipNote表
                WkWipnotePojo upWipPojo = new WkWipnotePojo();
                upWipPojo.setId(wipDB.getId());
                // 仅过数出组，不用更新工序了
                //upWipPojo.setWkrownum(wkWipnoteitemPojo.getRownum());
                //upWipPojo.setWkwpid(wkWipnoteitemPojo.getWpid());
                //upWipPojo.setWkwpcode(wkWipnoteitemPojo.getWpcode());
                //upWipPojo.setWkwpname(wkWipnoteitemPojo.getWpname());
                upWipPojo.setWkrownum(wipitemDB.getRownum());
                upWipPojo.setWkwpid(wipitemDB.getWpid());
                upWipPojo.setWkwpcode(wipitemDB.getWpcode());
                upWipPojo.setWkwpname(wipitemDB.getWpname());
                upWipPojo.setWkspecjson(wipitemDB.getSpecjson());
                upWipPojo.setMachitemid(finishitemDB.getMachitemid());
                upWipPojo.setMachuid(finishitemDB.getMachuid());
                upWipPojo.setWorkitemid(finishitemDB.getWorkitemid());
                upWipPojo.setTenantid(tid);
                upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
                upWipPojo.setMainplanitemid(finishitemDB.getMainplanitemid());

                this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                //// 加工单
                //if (wkWipnotePojo.getWorkitemid().length() > 0) {
                //    this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                //    this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);
                //    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                //        this.wkWipqtyMapper.updateWorkWkwpByMerge(upWipPojo);
                //        this.wkWipqtyMapper.updateWorkBillWkwpByMerge(upWipPojo);
                //    }
                //}
                ////更新bus_machiningitem表
                //if (wkWipnotePojo.getMachitemid().length() > 0) {
                //    this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                //    this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                //    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                //        this.wkWipqtyMapper.updateMachWkwpByMerge(upWipPojo);
                //        this.wkWipqtyMapper.updateMachBillWkwpByMerge(upWipPojo);
                //    }
                //}
                //// 同步Wk_MainPlan生产主计划主子表工序状态
                //if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                //    this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                //    this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                //    if (wkWipnotePojo.getMergemark() != null && wkWipnotePojo.getMergemark() == 2) {
                //        this.wkWipqtyMapper.updateMainPlanWkwpByMerge(upWipPojo);
                //        this.wkWipqtyMapper.updateMainPlanBillWkwpByMerge(upWipPojo);
                //    }
                //}

                return Boolean.TRUE;
            });
        }

        return "成功生成出组记录条数：" + finishItemList.size();

    }

    // 跟随过数
    private void FollowPost(String Wipid, Integer strownum, Integer endrownum, WkWipqtyEntity wkWipqtyPojo) {
        for (int i = strownum; i <= endrownum; i++) {
            // 拿到后一条记录
            WkWipnoteitemPojo wkWipnoteitemflPojo = wkWipnoteitemMapper.getEntityByRownum(i, Wipid, wkWipqtyPojo.getTenantid());
            if (wkWipnoteitemflPojo != null) {
                WkWipqtyEntity wipqtyEntity = new WkWipqtyEntity();
                BeanUtils.copyProperties(wkWipqtyPojo, wipqtyEntity);
                wipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                wipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wipqtyEntity.setWiprownum(wkWipnoteitemflPojo.getRownum());
                wipqtyEntity.setWipitemid(wkWipnoteitemflPojo.getId());
                wipqtyEntity.setDirection("入组");
                wipqtyEntity.setWpid(wkWipnoteitemflPojo.getWpid());
                wipqtyEntity.setWpcode(wkWipnoteitemflPojo.getWpcode());
                wipqtyEntity.setWpname(wkWipnoteitemflPojo.getWpname());
                wipqtyEntity.setRemark("随过入组");
                this.wkWipqtyMapper.insert(wipqtyEntity);
                wkWipnoteitemMapper.updateInQty(wkWipnoteitemflPojo.getId(), wipqtyEntity.getPcsqty(), wipqtyEntity.getSecqty(), wipqtyEntity.getWkdate(), null, wkWipqtyPojo.getTenantid());

                wipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                wipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wipqtyEntity.setWiprownum(wkWipnoteitemflPojo.getRownum());
                wipqtyEntity.setWipitemid(wkWipnoteitemflPojo.getId());
                wipqtyEntity.setDirection("出组");
                wipqtyEntity.setWpid(wkWipnoteitemflPojo.getWpid());
                wipqtyEntity.setWpcode(wkWipnoteitemflPojo.getWpcode());
                wipqtyEntity.setWpname(wkWipnoteitemflPojo.getWpname());
                wipqtyEntity.setRemark("随过出组");
                this.wkWipqtyMapper.insert(wipqtyEntity);
                wkWipnoteitemMapper.updateOutQty(wkWipnoteitemflPojo.getId(), wipqtyEntity.getPcsqty(), wipqtyEntity.getSecqty(), wipqtyEntity.getWkdate(), wkWipqtyPojo.getTenantid());
            }
        }

    }

    //完工入库(出组+入库)
    @Override
    //@Transactional
    public Map<String, String> quickInStore(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        Map<String, String> m = new HashMap<>();
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录" + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());

            // 当前关联的加工单子表
            String workitemid = wkWipnotePojo.getWorkitemid();
            // 读取指定系统参数 加工单按序入库--
            String configValue = systemFeignService.getConfigValue("module.manu.finishbyorder", loginUser.getTenantid(), loginUser.getToken()).getData();
            // true 按序入库 且不是补单 才检查
            // 判断是否按序入库，且不是补单
            if ("true".equals(configValue) && wkWipnotePojo.getSupplement() != 1 && isNotBlank(workitemid)) {
                WkWorksheetPojo sheetBill = wkWorksheetService.getBillEntityByItemid(workitemid, tid);
                List<WkWorksheetitemPojo> sheetItems = sheetBill.getItem();

                // 先找到当前 workitemid 对应的对象
                WkWorksheetitemPojo currentSheetItem = sheetItems.stream()
                        .filter(item -> item.getId().equals(workitemid))
                        .findFirst()
                        .orElseThrow(() -> new IllegalArgumentException("未找到与 workitemid 对应的工序行"));

                // 获取当前工序行号 item行号
                int currentWkRowNum = Integer.parseInt(currentSheetItem.getWkrownum());
                int rowNum = currentSheetItem.getRownum();

                // 检查所有在当前工序行之前的行号是否符合条件，并收集不符合条件的行号
                Optional<WkWorksheetitemPojo> invalidItem = sheetItems.stream()
                        .filter(item -> item.getRownum() < rowNum)
                        .filter(item -> Integer.parseInt(item.getWkrownum()) > currentWkRowNum)
                        .findFirst();
                if (invalidItem.isPresent()) {
                    WkWorksheetitemPojo sheetitem = invalidItem.get();
                    throw new IllegalStateException("禁止超过前单工序：" + sheetitem.getWkwpname() + "(加工单号" + sheetBill.getRefno() + "的第" + sheetitem.getRownum() + 1 + "行)");
                }
            }

            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = wkWipnoteitemMapper.getListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), tid);
            WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
            if (!list.isEmpty()) {
                wkWipnoteitemPojo = list.get(0);
            }
            if (list.isEmpty()) {
                throw new BaseBusinessException("未找到WIP工序记录" + quickPojo.getWorkuid());
            }

            // 拿到后一个行号
            quickPojo.setRownum(wkWipnoteitemPojo.getRownum() + 1);
            // 拿到后一条记录
            WkWipnoteitemPojo wkWipnoteitemPojoNext = wkWipnoteitemMapper.getEntityByRownum(quickPojo.getRownum(), quickPojo.getWipid(), tid);
            // 无后一条记录
            if (wkWipnoteitemPojoNext != null) {
                throw new BaseBusinessException(wkWipnoteitemPojo.getWpname() + "非最后工序请用出入组功能操作");
            }
            if (wkWipnoteitemPojo.getInpcsqty() == 0) {
                throw new BaseBusinessException("单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达本工序");
            }
            // 检查余数
            Double RemQty = wkWipnoteitemPojo.getInpcsqty() - wkWipnoteitemPojo.getOutpcsqty() - wkWipnoteitemPojo.getMrbpcsqty();
            if (RemQty <= 0 && quickPojo.getQty() > 0) {   // 红冲负数时不检查余数 Eric 20241130
                throw new BaseBusinessException("本工序已入库完成");
            }
            if (quickPojo.getQty() != null && RemQty < quickPojo.getQty()) {
                throw new BaseBusinessException("结余数量" + RemQty + "不足本次过数量" + quickPojo.getQty());
            }
            Double CrtQty = quickPojo.getQty() == null ? RemQty : quickPojo.getQty();

            // 过数记录
            WkWipqtyPojo wkWipqtyPojo = new WkWipqtyPojo();
            wkWipqtyPojo.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyPojo.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyPojo.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyPojo.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyPojo.setWkdate(new Date());
            wkWipqtyPojo.setDirection("出组");
            wkWipqtyPojo.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyPojo.setWorker(quickPojo.getWorker());
            wkWipqtyPojo.setPcsqty(CrtQty);
            wkWipqtyPojo.setSecqty(quickPojo.getSecqty() == null ? 0D : quickPojo.getSecqty());
            wkWipqtyPojo.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyPojo.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyPojo.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyPojo.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyPojo.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyPojo.setMrbpcsqty(0D);
            wkWipqtyPojo.setMrbsecqty(0D);
            wkWipqtyPojo.setMrbid("");
            wkWipqtyPojo.setAcceid("");
            wkWipqtyPojo.setCreatebyid(loginUser.getUserid());
            wkWipqtyPojo.setListerid(quickPojo.getListerid());
            wkWipqtyPojo.setCreateby(loginUser.getRealName());
            wkWipqtyPojo.setLister(quickPojo.getLister());
            wkWipqtyPojo.setCreatedate(new Date());
            wkWipqtyPojo.setModifydate(new Date());
            wkWipqtyPojo.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyPojo.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyPojo.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyPojo.setWorkitemid(workitemid);
            wkWipqtyPojo.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyPojo.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyPojo.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyPojo.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyPojo.setWorkparam(quickPojo.getWorkparam());
            wkWipqtyPojo.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyPojo.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyPojo.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyPojo.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyPojo.setRemark("扫码完工入库");
            // 加入尺寸 Eric20221008
            wkWipqtyPojo.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyPojo.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyPojo.setSizez(wkWipnotePojo.getSizez());
            WkWipnoteitemPojo finalWkWipnoteitemPojo = wkWipnoteitemPojo;
            transactionTemplate.execute((status) -> {
                WkWipqtyPojo finalWkWipqtyPojo = insert(wkWipqtyPojo);
                String wipitemid = finalWkWipnoteitemPojo.getId();
                // Wip记录出入组数量
                this.wkWipnoteitemMapper.updateOutQty(wipitemid, finalWkWipqtyPojo.getPcsqty(), finalWkWipqtyPojo.getSecqty(), finalWkWipqtyPojo.getWkdate(), tid);

                // 同步 WorkParam eric 20230304
                if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                    this.mergeWorkparam(wipitemid, tid);
                }
                // 同步 SPCEPackJSON eric 20230321
                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                    this.sumSpecPackjson(wipitemid, tid);
                }
                this.wkWipqtyMapper.updateWipComp(wipitemid, loginUser.getTenantid());

                quickPojo.setQty(CrtQty);
                // 生产入库单生成
                MatAccessClonePojo matAccessClonePojo = createAccess(quickPojo, wkWipnotePojo, finalWkWipnoteitemPojo, wkWipqtyPojo, loginUser);
                String acceJson = JSONObject.toJSONString(matAccessClonePojo);
                m.put("acceJson", acceJson);
                m.put("billid", finalWkWipqtyPojo.getId());
                m.put("wipitemid", wipitemid);
                return Boolean.TRUE;
            });
            return m;//wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //完工入库(出组+入库)(仅校验必须为最后工序且累计出组不大于Wip总数) 20230922 取消校验最后工序
    @Override
    //@Transactional
    public Map<String, String> anyQuickInStore(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        Map<String, String> m = new HashMap<>();
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录" + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());

            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = wkWipnoteitemMapper.getListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), tid);
            WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
            if (!list.isEmpty()) {
                wkWipnoteitemPojo = list.get(0);
            }
            if (list.isEmpty()) {
                throw new BaseBusinessException("未找到WIP工序记录" + quickPojo.getWorkuid());
            }

            // wipNote主表总数量
            Double remQty = wkWipnotePojo.getWkpcsqty() - wkWipnotePojo.getMrbpcsqty();
            // quickPojo.getQty()如果未输入数量，则默认为剩余remQty数量全数入组;否则按输入数量入组
            Double crtQty = quickPojo.getQty() == null ? remQty : quickPojo.getQty();
            // [出组]超数检查：仅检查当前工序累计出组数量不能大于wipNote主表总数量------------------------
//            // 关键工序不做检查!20240223  keyMark=0表示不是关键工序,进入检查
//            int keyMark = wkWipnoteitemMapper.checkKeyMark(quickPojo.getWpid(), tid);
//            if (keyMark == 0) {
            if (remQty > 0 && remQty < crtQty + wkWipnoteitemPojo.getOutpcsqty()) {
                throw new BaseBusinessException(crtQty + "," + (remQty - wkWipnoteitemPojo.getOutpcsqty()));
            }
//            }
            // 超数检查：检查最后工序【已出组数量+本次出组数量】不能大于【wipNote主表总数】
            if (remQty < wkWipnoteitemPojo.getOutpcsqty() + crtQty) {
                throw new BaseBusinessException("本次出组数量" + crtQty + "超出wip结余数量 " + (remQty - wkWipnoteitemPojo.getOutpcsqty()));
            }
            // 过数记录
            WkWipqtyPojo wkWipqtyPojo = new WkWipqtyPojo();
            wkWipqtyPojo.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyPojo.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyPojo.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyPojo.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyPojo.setWkdate(new Date());
            wkWipqtyPojo.setDirection("出组");
            wkWipqtyPojo.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyPojo.setWorker(quickPojo.getWorker());
            wkWipqtyPojo.setPcsqty(crtQty);
            wkWipqtyPojo.setSecqty(0D);
            wkWipqtyPojo.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyPojo.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyPojo.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyPojo.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyPojo.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyPojo.setMrbpcsqty(0D);
            wkWipqtyPojo.setMrbsecqty(0D);
            wkWipqtyPojo.setMrbid("");
            wkWipqtyPojo.setAcceid("");
            wkWipqtyPojo.setCreatebyid(loginUser.getUserid());
            wkWipqtyPojo.setListerid(quickPojo.getListerid());
            wkWipqtyPojo.setCreateby(loginUser.getRealName());
            wkWipqtyPojo.setLister(quickPojo.getLister());
            wkWipqtyPojo.setCreatedate(new Date());
            wkWipqtyPojo.setModifydate(new Date());
            wkWipqtyPojo.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyPojo.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyPojo.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyPojo.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyPojo.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyPojo.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyPojo.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyPojo.setWorkparam(quickPojo.getWorkparam());
            wkWipqtyPojo.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyPojo.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyPojo.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyPojo.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyPojo.setRemark("扫码完工入库");
            // 加入尺寸 Eric20221008
            wkWipqtyPojo.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyPojo.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyPojo.setSizez(wkWipnotePojo.getSizez());
            WkWipnoteitemPojo finalWkWipnoteitemPojo = wkWipnoteitemPojo;
            transactionTemplate.execute((status) -> {
                WkWipqtyPojo finalWkWipqtyPojo = insert(wkWipqtyPojo);
                String wipitemid = finalWkWipnoteitemPojo.getId();
                // Wip记录出入组数量
                this.wkWipnoteitemMapper.updateOutQty(wipitemid, finalWkWipqtyPojo.getPcsqty(), finalWkWipqtyPojo.getSecqty(), finalWkWipqtyPojo.getWkdate(), tid);

                // 同步 WorkParam eric 20230304
                if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                    this.mergeWorkparam(wipitemid, tid);
                }
                // 同步 SPCEPackJSON eric 20230321
                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                    this.sumSpecPackjson(wipitemid, tid);
                }
                this.wkWipqtyMapper.updateWipComp(wipitemid, loginUser.getTenantid());

                quickPojo.setQty(crtQty);
                // 生产入库单生成
                MatAccessClonePojo matAccessClonePojo = createAccess(quickPojo, wkWipnotePojo, finalWkWipnoteitemPojo, finalWkWipqtyPojo, loginUser);
                String acceJson = JSONObject.toJSONString(matAccessClonePojo);
                m.put("acceJson", acceJson);
                m.put("billid", finalWkWipqtyPojo.getId());
                m.put("wipitemid", wipitemid);
                return Boolean.TRUE;
            });
            return m;//wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * quickPojo.getQty() 想要报废数
     */
    @Override
    //@Transactional
    public Map<String, String> quickMrb(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        Map<String, String> resultMqbMap = new HashMap<>();
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录" + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());

            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = wkWipnoteitemMapper.getListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), tid);
            WkWipnoteitemPojo wkWipnoteitemPojo = new WkWipnoteitemPojo();
            if (!list.isEmpty()) {
                wkWipnoteitemPojo = list.get(0);
            }
            if (list.isEmpty()) {
                throw new BaseBusinessException("未找到WIP工序记录" + quickPojo.getWorkuid());
            }


            // 检查余数
            double RemQty = wkWipnoteitemPojo.getInpcsqty() - wkWipnoteitemPojo.getOutpcsqty() - wkWipnoteitemPojo.getMrbpcsqty();
            if (RemQty <= 0) {
                throw new BaseBusinessException("本工序无可报废数量");
            }
            if (quickPojo.getQty() != null && RemQty < quickPojo.getQty()) {
                throw new BaseBusinessException("结余数量" + RemQty + "不足本次报废数量" + quickPojo.getQty());
            }
            Double CrtQty = quickPojo.getQty() == null ? RemQty : quickPojo.getQty();

            // 过数记录
            WkWipqtyPojo wkWipqtyPojo = new WkWipqtyPojo();
            wkWipqtyPojo.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyPojo.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyPojo.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyPojo.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyPojo.setWkdate(new Date());
            wkWipqtyPojo.setDirection("报废");
            wkWipqtyPojo.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyPojo.setWorker(quickPojo.getWorker());
            wkWipqtyPojo.setPcsqty(CrtQty);
            wkWipqtyPojo.setSecqty(0D);
            wkWipqtyPojo.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyPojo.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyPojo.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyPojo.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyPojo.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyPojo.setMrbpcsqty(0D);
            wkWipqtyPojo.setMrbsecqty(0D);
            wkWipqtyPojo.setMrbid("");
            wkWipqtyPojo.setAcceid("");
            wkWipqtyPojo.setCreatebyid(loginUser.getUserid());
            wkWipqtyPojo.setListerid(quickPojo.getListerid());
            wkWipqtyPojo.setCreateby(loginUser.getRealName());
            wkWipqtyPojo.setLister(quickPojo.getLister());
            wkWipqtyPojo.setCreatedate(new Date());
            wkWipqtyPojo.setModifydate(new Date());
            wkWipqtyPojo.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyPojo.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyPojo.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyPojo.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyPojo.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyPojo.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyPojo.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyPojo.setWorkparam(quickPojo.getWorkparam());
            wkWipqtyPojo.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyPojo.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyPojo.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyPojo.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyPojo.setRemark("报废出组+创建报废单");
            // 加入尺寸 Eric20221008
            wkWipqtyPojo.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyPojo.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyPojo.setSizez(wkWipnotePojo.getSizez());
            // 插入出组记录
            wkWipqtyPojo = insert(wkWipqtyPojo);
            resultMqbMap.put("billid", wkWipqtyPojo.getId());
            WkWipnoteitemPojo finalWkWipnoteitemPojo = wkWipnoteitemPojo;
            WkWipqtyPojo finalWkWipqtyPojo = wkWipqtyPojo;
            transactionTemplate.execute((status) -> {
                // 同步 WorkParam eric 20230304
                if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                    this.mergeWorkparam(finalWkWipnoteitemPojo.getId(), tid);
                }
                // 同步 SPCEPackJSON eric 20230321
                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                    this.sumSpecPackjson(finalWkWipnoteitemPojo.getId(), tid);
                }
                quickPojo.setQty(CrtQty);
                // 生产入库单生成
                QmsMrbClonePojo qmsMrbClonePojo = createQms_Mrb(quickPojo, wkWipnotePojo, finalWkWipnoteitemPojo, finalWkWipqtyPojo, loginUser);
                String mrbJson = JSONObject.toJSONString(qmsMrbClonePojo);
                resultMqbMap.put("mrbJson", mrbJson);
                return Boolean.TRUE;
            });
            return resultMqbMap;//wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    private QmsMrbClonePojo createQms_Mrb(QuickWipqtyPojo quickPojo, WkWipnotePojo wkWipnotePojo,
                                          WkWipnoteitemPojo wkWipnoteitemPojo, WkWipqtyPojo wipQtyPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        QmsMrbClonePojo qmsMrbClonePojo = new QmsMrbClonePojo();
        qmsMrbClonePojo.setLister(loginUser.getUsername());
        qmsMrbClonePojo.setCreateby(loginUser.getUsername());
        qmsMrbClonePojo.setBilldate(new Date());
        qmsMrbClonePojo.setBilltype("在线打报");
        qmsMrbClonePojo.setBilltitle("【" + wkWipnotePojo.getWorkuid() + "】在线快速打报");
        qmsMrbClonePojo.setGroupid(quickPojo.getGroupid());
        String wpid = quickPojo.getWpid() == null ? wkWipnoteitemPojo.getWpid() : quickPojo.getWpid();
        WkProcessPojo process = wkProcessMapper.getEntity(wpid, tid);
        qmsMrbClonePojo.setWpid(wpid);
        qmsMrbClonePojo.setWpname(process.getWpname());

        QmsMrbitemClonePojo qmsMrbitemClonePojo = new QmsMrbitemClonePojo();
        qmsMrbitemClonePojo.setGoodsid(wkWipnotePojo.getGoodsid());
        //               Mat_Goods.GoodsUid,
        //               Mat_Goods.GoodsName,
        //               Mat_Goods.GoodsSpec,
        //               Mat_Goods.GoodsUnit,
        //               Mat_Goods.Partid,
        //               Mat_Goods.Surface,
        //               Mat_Goods.Drawing,
        //               Mat_Goods.BrandName,
        qmsMrbitemClonePojo.setItemcode(wkWipnotePojo.getGoodsuid());
        qmsMrbitemClonePojo.setItemname(wkWipnotePojo.getGoodsname());
        qmsMrbitemClonePojo.setItemspec(wkWipnotePojo.getGoodsspec());
        qmsMrbitemClonePojo.setItemunit(wkWipnotePojo.getGoodsunit());
        // 赋值secqty，workitemid，mainplanuid，mainplanitemid，machuid，machitemid字段
        qmsMrbitemClonePojo.setSecqty(quickPojo.getSecqty());
        qmsMrbitemClonePojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
        qmsMrbitemClonePojo.setMainplanuid(wkWipnotePojo.getMainplanuid());
        qmsMrbitemClonePojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
        qmsMrbitemClonePojo.setMachuid(wkWipnotePojo.getMachuid());
        qmsMrbitemClonePojo.setMachitemid(wkWipnotePojo.getMachitemid());
        // 责任工序id或者是工段id
        String dutywpid = quickPojo.getDutywpid();
        // 可能来自工序，也可能来自工段
        String dutywpcode;
        String dutywpname;
        if (StringUtils.isNotBlank(dutywpid)) {
            WkProcessPojo dutyProcess = wkProcessMapper.getEntity(dutywpid, tid);
            if (dutyProcess != null) {
                dutywpcode = dutyProcess.getWpcode();
                dutywpname = dutyProcess.getWpname();
            } else {
                // 如果差不到工序，说明传入的是工段id 从工段表查询
                WkSectionPojo wkSectionPojo = wkSectionMapper.getEntity(dutywpid, tid);
                if (wkSectionPojo == null) {
                    throw new BaseBusinessException("dutywpid均未找到工序、工段信息");
                }
                dutywpcode = wkSectionPojo.getSectcode();
                dutywpname = wkSectionPojo.getSectname();
            }
            qmsMrbitemClonePojo.setDutywpid(dutywpid);
            qmsMrbitemClonePojo.setDutywpcode(dutywpcode);
            qmsMrbitemClonePojo.setDutywpname(dutywpname);
        } else {
            qmsMrbitemClonePojo.setDutywpid(wpid);
            qmsMrbitemClonePojo.setDutywpcode(process.getWpcode());
            qmsMrbitemClonePojo.setDutywpname(process.getWpname());
        }
        // 疵点名称
        String defectid = quickPojo.getDefectid();
        PrintColor.red("=======defectid:" + defectid);
        if (StringUtils.isNotBlank(defectid)) {//        select DefCode,DefName from Qms_Defect
            Map<String, String> defNameCode = wkProcessMapper.getDefNameCode(defectid, tid);
            qmsMrbitemClonePojo.setDefectid(defectid);
            qmsMrbitemClonePojo.setDefcode(defNameCode.get("DefCode"));
            qmsMrbitemClonePojo.setDefname(defNameCode.get("DefName"));
        }
        qmsMrbitemClonePojo.setQuantity(quickPojo.getQty());
        qmsMrbitemClonePojo.setWorkuid(quickPojo.getWorkuid());
        qmsMrbitemClonePojo.setWipitemid(wkWipnoteitemPojo.getId());

        List<QmsMrbitemClonePojo> items = new ArrayList<>();
        items.add(qmsMrbitemClonePojo);
        qmsMrbClonePojo.setItem(items);

        return qmsMrbClonePojo;
    }


    // 同步出组信息中的规格json
    private void sumSpecjson(String itemid, String tid) {
        List<WkWipqtyPojo> lstqty = this.wkWipqtyMapper.getOutListByWipItemid(itemid, tid);
        WkWipnoteitemEntity wkWipnoteitemEntity = new WkWipnoteitemEntity();
        wkWipnoteitemEntity.setId(itemid);
        wkWipnoteitemEntity.setTenantid(tid);
        if (lstqty.size() > 1) {
            List<SpecjsonPojo> lstspec = new ArrayList<>();
            // 循环记录行
            for (WkWipqtyPojo pojo : lstqty) {
                if (pojo.getSpecjson() != null && !"".equals(pojo.getSpecjson())) {
                    List<SpecjsonPojo> lst = JSONArray.parseArray(pojo.getSpecjson(), SpecjsonPojo.class);
                    int mergeitem = 0;
                    for (SpecjsonPojo spec : lst) {
                        mergeitem = 0;
                        for (SpecjsonPojo org : lstspec) {
                            if (spec.getSpuchang().equals(org.getSpuchang()) && spec.getSpukuan().equals(org.getSpukuan())) {
                                org.setQty(org.getQty() + spec.getQty());
                                mergeitem = 1;
                                break;
                            }
                        }
                        // 如果没有合并，则添加记录
                        if (mergeitem == 0) {
                            lstspec.add(spec);
                        }
                    }
                }
            }
            wkWipnoteitemEntity.setSpecjson(JSONArray.toJSONString(lstspec));
            this.wkWipnoteitemMapper.update(wkWipnoteitemEntity);
        } else if (lstqty.size() == 1) {
            wkWipnoteitemEntity.setSpecjson(lstqty.get(0).getSpecjson());
            this.wkWipnoteitemMapper.update(wkWipnoteitemEntity);
        }
    }

    // 同步出组信息中的Pack规格json Eric 20230321
    private void sumSpecPackjson(String itemid, String tid) {
        List<WkWipqtyPojo> lstqty = this.wkWipqtyMapper.getOutListByWipItemid(itemid, tid);
        WkWipnoteitemEntity wkWipnoteitemEntity = new WkWipnoteitemEntity();
        wkWipnoteitemEntity.setId(itemid);
        wkWipnoteitemEntity.setTenantid(tid);
        if (lstqty.size() > 1) {
            List<SpecPackjsonPojo> lstspec = new ArrayList<>();
            // 循环记录行
            for (WkWipqtyPojo pojo : lstqty) {
                if (pojo.getSpecjson() != null && !"".equals(pojo.getSpecjson())) {
                    List<SpecPackjsonPojo> lst = JSONArray.parseArray(pojo.getSpecjson(), SpecPackjsonPojo.class);
                    int mergeitem = 0;
                    for (SpecPackjsonPojo spec : lst) {
                        mergeitem = 0;
                        for (SpecPackjsonPojo org : lstspec) {
                            // 整件+长+宽+件片数 相同
                            if (spec.getSpare() != null && spec.getSpare() == 0 &&
                                    spec.getSpuchang().equals(org.getSpuchang()) && spec.getSpukuan().equals(org.getSpukuan())
                                    && spec.getInner().equals(org.getInner())) {
                                org.setQty(org.getQty() + spec.getQty());
                                org.setCtn(org.getCtn() + spec.getCtn());
                                mergeitem = 1;
                                break;
                            }
                        }
                        // 如果没有合并，则添加记录
                        if (mergeitem == 0) {
                            lstspec.add(spec);
                        }
                    }
                }
            }
            wkWipnoteitemEntity.setSpecjson(JSONArray.toJSONString(lstspec));
            this.wkWipnoteitemMapper.update(wkWipnoteitemEntity);
        } else if (lstqty.size() == 1) {
            wkWipnoteitemEntity.setSpecjson(lstqty.get(0).getSpecjson());
            this.wkWipnoteitemMapper.update(wkWipnoteitemEntity);
        }
    }


    // 同步出组信息中的Workparam
    private void mergeWorkparam(String itemid, String tid) {
        List<WkWipqtyPojo> lstqty = this.wkWipqtyMapper.getOutListByWipItemid(itemid, tid);
        WkWipnoteitemEntity wkWipnoteitemEntity = new WkWipnoteitemEntity();
        wkWipnoteitemEntity.setId(itemid);
        wkWipnoteitemEntity.setTenantid(tid);
        if (!lstqty.isEmpty()) {
            List<WorkparamPojo> lstParam = new ArrayList<>();
            // 循环记录行
            for (WkWipqtyPojo pojo : lstqty) {
                if (pojo.getWorkparam() != null && !"".equals(pojo.getWorkparam())) {
                    List<WorkparamitemPojo> lst = JSONArray.parseArray(pojo.getWorkparam(), WorkparamitemPojo.class);
                    WorkparamPojo paramPojo = new WorkparamPojo();
                    paramPojo.setDate(pojo.getCreatedate());
                    paramPojo.setQty(pojo.getPcsqty());
                    paramPojo.setItem(lst);
                    lstParam.add(paramPojo);
                }
            }
            wkWipnoteitemEntity.setWorkparam(JSONArray.toJSONString(lstParam));
            this.wkWipnoteitemMapper.update(wkWipnoteitemEntity);
        }
    }

    private MatAccessClonePojo createAccess(QuickWipqtyPojo quickPojo, WkWipnotePojo wkWipnotePojo,
                                            WkWipnoteitemPojo wkWipnoteitemPojo, WkWipqtyPojo wkWipqtyPojo, LoginUser loginUser) {
        // 生产入库单生成
        MatAccessClonePojo matAccessPojo = new MatAccessClonePojo();
        matAccessPojo.setDirection("入库单");
        matAccessPojo.setStoreid(quickPojo.getStoreid());
        matAccessPojo.setStorename(quickPojo.getStorename());
        matAccessPojo.setBilltype("生产入库");
        matAccessPojo.setBilltitle("【" + wkWipnotePojo.getWorkuid() + "】过数入库");
        matAccessPojo.setOperator(quickPojo.getWorker());
        matAccessPojo.setGroupid(wkWipnotePojo.getWorkshopid());

        MatAccessitemClonePojo matAccessitemPojo = new MatAccessitemClonePojo();

        // quickInStore完工入库过数的仍然是当前货品
        // 但是查询一下这个货品是否有主料号Pid，有的话把货品改为主料号的货品再转给store服务去创建出入库
        String goodsid = wkWipnotePojo.getGoodsid();
        String pid_Goodsid = wkWipqtyMapper.getPidBygoodsid(goodsid, loginUser.getTenantid());
        if (isNotBlank(pid_Goodsid)) {
            matAccessitemPojo.setGoodsid(pid_Goodsid);
            matAccessitemPojo.setRemark("由子料号转入：" + wkWipnotePojo.getGoodsuid());
        } else {
            matAccessitemPojo.setGoodsid(goodsid);
        }
        //matAccessitemPojo.setGoodsuid(wkWipnotePojo.getGoodsuid()); 不需要
        matAccessitemPojo.setQuantity(quickPojo.getQty());
        matAccessitemPojo.setAttributejson(wkWipnotePojo.getAttributejson());
        matAccessitemPojo.setMachgroupid(wkWipnotePojo.getMachgroupid());
        matAccessitemPojo.setMachitemid(wkWipnotePojo.getMachitemid());
        matAccessitemPojo.setMachuid(wkWipnotePojo.getMachuid());
        matAccessitemPojo.setCiteuid(wkWipnotePojo.getWorkuid());
        matAccessitemPojo.setCiteitemid(wkWipnotePojo.getWorkitemid());
        matAccessitemPojo.setMainplanuid(wkWipnotePojo.getMainplanuid());
        matAccessitemPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
        matAccessitemPojo.setCustomer(wkWipnotePojo.getCustomer());
        matAccessitemPojo.setCustpo(wkWipnotePojo.getCustpo());
        matAccessitemPojo.setRownum(0);
        matAccessitemPojo.setWkqtyid(wkWipqtyPojo.getId());  //Eric 20230323
        matAccessitemPojo.setBatchno(quickPojo.getBatchno() == null ? "" : quickPojo.getBatchno());
        matAccessitemPojo.setLocation(quickPojo.getLocation() == null ? "" : quickPojo.getLocation());
        List<MatAccessitemClonePojo> lst = new ArrayList<>();
        // 多规格WIP生产单,用于地板P4 Eric 20220912
        //if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())
        //        && wkWipnotePojo.getAttributejson() != null && !"".equals(wkWipnotePojo.getAttributejson())) {
        //    List<Map<String, Object>> listObjectSec = JSONArray.parseObject(wkWipnotePojo.getAttributejson(), List.class);
        //    List<SpecjsonPojo> lstSpec = JSONArray.parseArray(quickPojo.getSpecjson(), SpecjsonPojo.class);
        //    for (SpecjsonPojo specjsonPojo : lstSpec) {
        //        // 用分规格数量入库
        //        matAccessitemPojo.setQuantity(specjsonPojo.getArea());
        //        // 长度参数，重填SPU
        //        for (Map<String, Object> mapList : listObjectSec) {
        //            if (mapList.get("key").equals("spuchang")) {
        //                mapList.put("value", specjsonPojo.getSpuchang());
        //                break;
        //            }
        //        }
        //        matAccessitemPojo.setAttributejson(JSONObject.toJSONString(listObjectSec));
        //        MatAccessitemClonePojo matAccessitemClonePojo = new MatAccessitemClonePojo();
        //        BeanUtils.copyProperties(matAccessitemPojo, matAccessitemClonePojo);
        //        // 分Item入库
        //        lst.add(matAccessitemClonePojo);
        //    }
        //
        //} else {
        lst.add(matAccessitemPojo);
        //}
        matAccessPojo.setItem(lst);
        return matAccessPojo;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<Map<String, Object>> getSumPageListByWp(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<Map<String, Object>> lst = wkWipqtyMapper.getSumPageListByWp(queryParam);
            PageInfo<Map<String, Object>> pageInfo = new PageInfo<Map<String, Object>>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    //@Transactional
    public void quickFinishFB(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        Map<String, String> m = new HashMap<>();
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录" + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());

            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = wkWipnoteitemMapper.getListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), tid);
            WkWipnoteitemPojo wkWipnoteitemPojo;
            if (list.size() > 0) {
                wkWipnoteitemPojo = list.get(0);
            } else {
                wkWipnoteitemPojo = new WkWipnoteitemPojo();
            }
            if (list.size() == 0) {
                throw new BaseBusinessException("未找到WIP工序记录" + quickPojo.getWorkuid());
            }

            // 拿到后一个行号
            quickPojo.setRownum(wkWipnoteitemPojo.getRownum() + 1);
            // 拿到后一条记录
            WkWipnoteitemPojo wkWipnoteitemPojoNext = wkWipnoteitemMapper.getEntityByRownum(quickPojo.getRownum(), quickPojo.getWipid(), tid);
            // 无后一条记录
            if (wkWipnoteitemPojoNext != null) {
                throw new BaseBusinessException(wkWipnoteitemPojo.getWpname() + "非最后工序请用出入组功能操作");
            }
            if (wkWipnoteitemPojo.getInpcsqty() == 0) {
                throw new BaseBusinessException("单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达本工序");
            }
            // 检查余数
            Double RemQty = wkWipnoteitemPojo.getInpcsqty() - wkWipnoteitemPojo.getOutpcsqty() - wkWipnoteitemPojo.getMrbpcsqty();
            if (RemQty <= 0) {
                throw new BaseBusinessException("本工序已入库完成");
            }
            if (quickPojo.getQty() != null && RemQty < quickPojo.getQty()) {
                throw new BaseBusinessException("结余数量" + RemQty + "不足本次过数量" + quickPojo.getQty());
            }
            Double CrtQty = quickPojo.getQty() == null ? RemQty : quickPojo.getQty();

            // 过数记录
            WkWipqtyPojo wkWipqtyPojo = new WkWipqtyPojo();
            wkWipqtyPojo.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyPojo.setWpid(wkWipnoteitemPojo.getWpid());
            wkWipqtyPojo.setWpcode(wkWipnoteitemPojo.getWpcode());
            wkWipqtyPojo.setWpname(wkWipnoteitemPojo.getWpname());
            wkWipqtyPojo.setWkdate(new Date());
            wkWipqtyPojo.setDirection("出组");
            wkWipqtyPojo.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyPojo.setWorker(quickPojo.getWorker());
            wkWipqtyPojo.setPcsqty(CrtQty);
            wkWipqtyPojo.setSecqty(0D);
            wkWipqtyPojo.setRemark(wkWipnoteitemPojo.getRemark());
            wkWipqtyPojo.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyPojo.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyPojo.setWiprownum(wkWipnoteitemPojo.getRownum());
            wkWipqtyPojo.setWipitemid(wkWipnoteitemPojo.getId());
            wkWipqtyPojo.setMrbpcsqty(0D);
            wkWipqtyPojo.setMrbsecqty(0D);
            wkWipqtyPojo.setMrbid("");
            wkWipqtyPojo.setAcceid("");
            wkWipqtyPojo.setCreatebyid(loginUser.getUserid());
            wkWipqtyPojo.setListerid(quickPojo.getListerid());
            wkWipqtyPojo.setCreateby(loginUser.getRealName());
            wkWipqtyPojo.setLister(quickPojo.getLister());
            wkWipqtyPojo.setCreatedate(new Date());
            wkWipqtyPojo.setModifydate(new Date());
            wkWipqtyPojo.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyPojo.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyPojo.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyPojo.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyPojo.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyPojo.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyPojo.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyPojo.setWorkparam(quickPojo.getWorkparam());
            wkWipqtyPojo.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyPojo.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyPojo.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyPojo.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyPojo.setRemark("扫码完工入库");
            // 加入尺寸 Eric20221008
            wkWipqtyPojo.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyPojo.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyPojo.setSizez(wkWipnotePojo.getSizez());
            wkWipqtyPojo = insert(wkWipqtyPojo);
            m.put("billid", wkWipqtyPojo.getId());
            WkWipqtyPojo finalWkWipqtyPojo = wkWipqtyPojo;
            transactionTemplate.execute((status) -> {

                // Wip记录出入组数量
                this.wkWipnoteitemMapper.updateOutQty(wkWipnoteitemPojo.getId(), finalWkWipqtyPojo.getPcsqty(), finalWkWipqtyPojo.getSecqty(), finalWkWipqtyPojo.getWkdate(), tid);

                // 同步 WorkParam eric 20230304
                if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                    this.mergeWorkparam(wkWipnoteitemPojo.getId(), tid);
                }
                // 同步 SPCEPackJSON eric 20230321
                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                    this.sumSpecPackjson(wkWipnoteitemPojo.getId(), tid);
                }
                this.wkWipqtyMapper.updateWipComp(wkWipnoteitemPojo.getId(), loginUser.getTenantid());

                quickPojo.setQty(CrtQty);
                return Boolean.TRUE;
            });
        } catch (BaseBusinessException e) {
            throw new BaseBusinessException(e.getMessage());
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    //快速返回上一道出组
    @Override
    //@Transactional
    public WkWipnotePojo quickReturn(QuickWipqtyPojo quickPojo, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        try {
            // 查询是否有工单
            WkWipnotePojo wkWipnotePojo = wkWipnoteMapper.getEntityByWorkUid(quickPojo.getWorkuid(), tid);
            if (wkWipnotePojo == null) {
                throw new BaseBusinessException("没找到WIP记录" + quickPojo.getWorkuid());
            }
            quickPojo.setWipid(wkWipnotePojo.getId());

            // 查询工单中是否操作工序
            List<WkWipnoteitemPojo> list = wkWipnoteitemMapper.getListByWpid(quickPojo.getWpid(), quickPojo.getWipid(), tid);
            WkWipnoteitemPojo wkWipnoteitemPojo;
            if (!list.isEmpty()) {
                wkWipnoteitemPojo = list.get(0);
            } else {
                wkWipnoteitemPojo = new WkWipnoteitemPojo();
            }
            if (list.isEmpty()) {
                throw new BaseBusinessException("未找到WIP工序记录" + quickPojo.getWorkuid());
            }
            if (wkWipnoteitemPojo.getInpcsqty() == 0) {
                throw new BaseBusinessException("单据在" + wkWipnotePojo.getWkwpname() + ",尚未到达本工序");
            }
            // RemQty在线数量：入组数量-出组数量-报废数量
            Double RemQty = wkWipnoteitemPojo.getInpcsqty() - wkWipnoteitemPojo.getOutpcsqty() - wkWipnoteitemPojo.getMrbpcsqty();
            if (RemQty <= 0) {
                throw new BaseBusinessException("本工序已出组完成");
            }
            // 判断是否超过本工序数量
            if (quickPojo.getQty() != null && RemQty < quickPojo.getQty()) {
                throw new BaseBusinessException("结余数量" + RemQty + "不足本次返回过数量" + quickPojo.getQty());
            }
            //Qty传空,则默认为全数返回在线数RemQty
            Double CrtQty = quickPojo.getQty() == null ? -RemQty : -quickPojo.getQty();
            // 拿到前一个行号
            quickPojo.setRownum(wkWipnoteitemPojo.getRownum() - 1);
            // 拿到前一条记录
            WkWipnoteitemPojo wkWipnoteitemPojoPre = wkWipnoteitemMapper.getPrevPostEntityByRownum(quickPojo.getRownum(), quickPojo.getWipid(), tid);
            if (wkWipnoteitemPojoPre == null) {
                throw new BaseBusinessException("没找到前工序记录" + quickPojo.getWorkuid() + wkWipnoteitemPojo.getWpname());
            }
            logger.info("查询出的前工序POST=1:{}，入组数:{}", wkWipnoteitemPojoPre.getWpname(), wkWipnoteitemPojoPre.getInpcsqty());

            // 过数记录
            WkWipqtyEntity wkWipqtyEntity = new WkWipqtyEntity();
            wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
            wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            wkWipqtyEntity.setWpid(wkWipnoteitemPojoPre.getWpid());
            wkWipqtyEntity.setWpcode(wkWipnoteitemPojoPre.getWpcode());
            wkWipqtyEntity.setWpname(wkWipnoteitemPojoPre.getWpname());
            wkWipqtyEntity.setWkdate(new Date());
            wkWipqtyEntity.setDirection("出组");
            wkWipqtyEntity.setGoodsid(wkWipnotePojo.getGoodsid());
            wkWipqtyEntity.setWorker(quickPojo.getWorker());
            wkWipqtyEntity.setPcsqty(CrtQty);
            wkWipqtyEntity.setSecqty(0D);
            wkWipqtyEntity.setRemark(wkWipnoteitemPojoPre.getRemark());
            wkWipqtyEntity.setWorkuid(wkWipnotePojo.getWorkuid());
            wkWipqtyEntity.setWipuid(wkWipnotePojo.getRefno());
            wkWipqtyEntity.setWiprownum(wkWipnoteitemPojoPre.getRownum());
            wkWipqtyEntity.setWipitemid(wkWipnoteitemPojoPre.getId());
            wkWipqtyEntity.setMrbpcsqty(0D);
            wkWipqtyEntity.setMrbsecqty(0D);
            wkWipqtyEntity.setMrbid("");
            wkWipqtyEntity.setAcceid("");
            wkWipqtyEntity.setCreatebyid(loginUser.getUserid());
            wkWipqtyEntity.setListerid(quickPojo.getListerid());
            wkWipqtyEntity.setCreateby(loginUser.getRealName());
            wkWipqtyEntity.setLister(quickPojo.getLister());
            wkWipqtyEntity.setCreatedate(new Date());
            wkWipqtyEntity.setModifydate(new Date());
            wkWipqtyEntity.setMachuid(wkWipnotePojo.getMachuid());
            wkWipqtyEntity.setMachitemid(wkWipnotePojo.getMachitemid());
            wkWipqtyEntity.setMainplanuid(wkWipnotePojo.getMainplanuid());
            wkWipqtyEntity.setMainplanitemid(wkWipnotePojo.getMainplanitemid());
            wkWipqtyEntity.setWorkitemid(wkWipnotePojo.getWorkitemid());
            wkWipqtyEntity.setMachgroupid(wkWipnotePojo.getMachgroupid());
            wkWipqtyEntity.setTenantid(wkWipnotePojo.getTenantid());
            wkWipqtyEntity.setRemark("扫码过数出组");
            wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
            wkWipqtyEntity.setSpecjson(quickPojo.getSpecjson());
            wkWipqtyEntity.setWorkparam(quickPojo.getWorkparam());  // 完工参数
            wkWipqtyEntity.setStatid(quickPojo.getStatid()); //工位ID
            wkWipqtyEntity.setStatcode(quickPojo.getStatcode()); //工位编码
            wkWipqtyEntity.setStatname(quickPojo.getStatname()); //工位名称
            wkWipqtyEntity.setWorktime(quickPojo.getWorktime()); //工时
            wkWipqtyEntity.setRevision(1);
            // 加入尺寸 Eric20221008
            wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
            wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
            wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
            transactionTemplate.execute((status) -> {

                this.wkWipqtyMapper.insert(wkWipqtyEntity);
                this.wkWipnoteitemMapper.updateOutQty(wkWipnoteitemPojoPre.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), tid);
                //// 加入随过功能 Eric 20220915
                if (wkWipnoteitemPojo.getRownum() - wkWipnoteitemPojoPre.getRownum() > 1) {
                    FollowPost(wkWipnotePojo.getId(), wkWipnoteitemPojoPre.getRownum() + 1, wkWipnoteitemPojo.getRownum() - 1, wkWipqtyEntity);
                }
                // 同步 WorkParam eric 20230304
                if (quickPojo.getWorkparam() != null && !"".equals(quickPojo.getWorkparam())) {
                    this.mergeWorkparam(wkWipnoteitemPojoPre.getId(), tid);
                }
                // 同步 SPCEJSON eric 20220903
                if (quickPojo.getSpecjson() != null && !"".equals(quickPojo.getSpecjson())) {
                    this.sumSpecjson(wkWipnoteitemPojoPre.getId(), tid);
                }
                // 后记录入组
                wkWipqtyEntity.setRefno("wq" + DateUtils.dateTimeNow());
                wkWipqtyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkWipqtyEntity.setWiprownum(wkWipnoteitemPojo.getRownum());
                wkWipqtyEntity.setWipitemid(wkWipnoteitemPojo.getId());
                wkWipqtyEntity.setDirection("入组");
                wkWipqtyEntity.setWpid(wkWipnoteitemPojo.getWpid());
                wkWipqtyEntity.setWpcode(wkWipnoteitemPojo.getWpcode());
                wkWipqtyEntity.setWpname(wkWipnoteitemPojo.getWpname());
                wkWipqtyEntity.setAttributejson(wkWipnotePojo.getAttributejson());
                // 加入尺寸 Eric20221008
                wkWipqtyEntity.setSizex(wkWipnotePojo.getSizex());
                wkWipqtyEntity.setSizey(wkWipnotePojo.getSizey());
                wkWipqtyEntity.setSizez(wkWipnotePojo.getSizez());
                this.wkWipqtyMapper.insert(wkWipqtyEntity);
                this.wkWipnoteitemMapper.updateInQty(wkWipnoteitemPojo.getId(), wkWipqtyEntity.getPcsqty(), wkWipqtyEntity.getSecqty(), wkWipqtyEntity.getWkdate(), null, tid);

                if (RemQty.equals(quickPojo.getQty())) {
                    //更新Wk_WipNote表  (更新到上一道工序）
                    WkWipnotePojo upWipPojo = new WkWipnotePojo();
                    upWipPojo.setId(wkWipnotePojo.getId());
                    upWipPojo.setWkrownum(wkWipnoteitemPojoPre.getRownum());
                    upWipPojo.setWkwpid(wkWipnoteitemPojoPre.getWpid());
                    upWipPojo.setWkwpcode(wkWipnoteitemPojoPre.getWpcode());
                    upWipPojo.setWkwpname(wkWipnoteitemPojoPre.getWpname());
                    upWipPojo.setWkspecjson(quickPojo.getSpecjson());
                    upWipPojo.setMachitemid(wkWipnotePojo.getMachitemid());
                    upWipPojo.setMachuid(wkWipnotePojo.getMachuid());
                    upWipPojo.setWorkitemid(wkWipnotePojo.getWorkitemid());
                    upWipPojo.setTenantid(loginUser.getTenantid());
                    upWipPojo.setWkspecjson(wkWipqtyEntity.getSpecjson());
                    upWipPojo.setMainplanitemid(wkWipnotePojo.getMainplanitemid());

                    this.wkWipqtyMapper.updateWipWkwp(upWipPojo);
                    // 加工单 (更新到上一道工序）
                    if (isNotBlank(wkWipnotePojo.getWorkitemid())) {
                        this.wkWipqtyMapper.updateWorkWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateWorkBillWkwp(upWipPojo);

                    }
                    //更新bus_machiningitem表 (更新到上一道工序）
                    if (isNotBlank(wkWipnotePojo.getMachitemid())) {
                        this.wkWipqtyMapper.updateMachWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateMachBillWkwp(upWipPojo);
                    }
                    // 同步Wk_MainPlan生产主计划主子表工序状态
                    if (isNotBlank(wkWipnotePojo.getMainplanitemid())) {
                        this.wkWipqtyMapper.updateMainPlanWkwp(upWipPojo);
                        this.wkWipqtyMapper.updateMainPlanBillWkwp(upWipPojo);
                    }
                }
                return Boolean.TRUE;
            });
            return this.wkWipnoteMapper.getEntity(quickPojo.getWipid(), tid);

        } catch (BaseBusinessException e) {
            throw new BaseBusinessException(e.getMessage());
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //按条件分页查询工序完工报表
    @Override
    public PageInfo<WkWipqtyPojo> getCostPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipqtyPojo> lst = wkWipqtyMapper.getCostPageList(queryParam);
            PageInfo<WkWipqtyPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> getSumPageListByCost(QueryParam queryParam) {
        try {
            List<ChartPojo> lst = wkWipqtyMapper.getSumPageListByCost(queryParam);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<HashMap<String, Object>> getSumCostPageListByWpMachuid(String machuid, String tid) {
        // 获取工序列表
        List<WkProcessPojo> processList = wkProcessMapper.getAll(tid);
        // 查询出每个员工时间段内每道工序做的金额
        List<HashMap<String, Object>> MapList = wkWipqtyMapper.getSumCostPageListByWpMachuid(processList, machuid, tid);
        return MapList;
    }

    @Override
    public List<HashMap<String, Object>> getSumCostPageListByWpDate(QueryParam queryParam, String tid) {
        // 获取工序列表
        List<WkProcessPojo> processList = wkProcessMapper.getAll(tid);
        // 查询出每个员工时间段内每道工序做的金额
        List<HashMap<String, Object>> MapList = wkWipqtyMapper.getSumCostPageListByWpDate(processList, queryParam, tid);
        return MapList;
    }

    @Override
    public PageInfo<HashMap<String, Object>> getAvgWorkTimeGroupByWp(QueryParam queryParam, String tenantid) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<HashMap<String, Object>> lst = wkWipqtyMapper.getAvgWorkTimeGroupByWp(tenantid);
            PageInfo<HashMap<String, Object>> pageInfo = new PageInfo<HashMap<String, Object>>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<HashMap<String, Object>> getSumOutPcsQtyGroupByWorker(QueryParam queryParam) {
        return wkWipqtyMapper.getSumOutPcsQtyGroupByWorker(queryParam);
    }

    @Override
    public List<Map<String, Object>> getSumQtyAndWorkTimeByWp(QueryParam queryParam, String userid) {
        return wkWipqtyMapper.getSumQtyAndWorkTimeByWp(queryParam, userid);
    }

    @Override
    public List<HashMap<String, Object>> getSumWpByPlanItemid(String mainplanitemid, String tenantid) {
        return wkWipqtyMapper.getSumWpByPlanItemid(mainplanitemid, tenantid);
    }
}
