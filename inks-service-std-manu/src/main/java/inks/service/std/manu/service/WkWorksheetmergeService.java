package inks.service.std.manu.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.manu.domain.pojo.WkWorksheetmergePojo;

/**
 * 加工单合并记录(WkWorksheetmerge)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-15 10:20:45
 */
public interface WkWorksheetmergeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkWorksheetmergePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkWorksheetmergePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param wkWorksheetmergePojo 实例对象
     * @return 实例对象
     */
    WkWorksheetmergePojo insert(WkWorksheetmergePojo wkWorksheetmergePojo);

    /**
     * 修改数据
     *
     * @param wkWorksheetmergepojo 实例对象
     * @return 实例对象
     */
    WkWorksheetmergePojo update(WkWorksheetmergePojo wkWorksheetmergepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    int updateMergeItemid(String mergeItemid, String itemids, String tid);
}
