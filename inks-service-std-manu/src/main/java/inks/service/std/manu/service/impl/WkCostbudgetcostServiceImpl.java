package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkCostbudgetcostEntity;
import inks.service.std.manu.domain.pojo.WkCostbudgetcostPojo;
import inks.service.std.manu.mapper.WkCostbudgetcostMapper;
import inks.service.std.manu.service.WkCostbudgetcostService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生成费用(WkCostbudgetcost)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-24 13:17:01
 */
@Service("wkCostbudgetcostService")
public class WkCostbudgetcostServiceImpl implements WkCostbudgetcostService {
    @Resource
    private WkCostbudgetcostMapper wkCostbudgetcostMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkCostbudgetcostPojo getEntity(String key, String tid) {
        return this.wkCostbudgetcostMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkCostbudgetcostPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkCostbudgetcostPojo> lst = wkCostbudgetcostMapper.getPageList(queryParam);
            PageInfo<WkCostbudgetcostPojo> pageInfo = new PageInfo<WkCostbudgetcostPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<WkCostbudgetcostPojo> getList(String Pid, String tid) {
        try {
            List<WkCostbudgetcostPojo> lst = wkCostbudgetcostMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param wkCostbudgetcostPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCostbudgetcostPojo insert(WkCostbudgetcostPojo wkCostbudgetcostPojo) {
        //初始化item的NULL
        WkCostbudgetcostPojo itempojo = this.clearNull(wkCostbudgetcostPojo);
        WkCostbudgetcostEntity wkCostbudgetcostEntity = new WkCostbudgetcostEntity();
        BeanUtils.copyProperties(itempojo, wkCostbudgetcostEntity);

        wkCostbudgetcostEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkCostbudgetcostEntity.setRevision(1);  //乐观锁
        this.wkCostbudgetcostMapper.insert(wkCostbudgetcostEntity);
        return this.getEntity(wkCostbudgetcostEntity.getId(), wkCostbudgetcostEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkCostbudgetcostPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCostbudgetcostPojo update(WkCostbudgetcostPojo wkCostbudgetcostPojo) {
        WkCostbudgetcostEntity wkCostbudgetcostEntity = new WkCostbudgetcostEntity();
        BeanUtils.copyProperties(wkCostbudgetcostPojo, wkCostbudgetcostEntity);
        this.wkCostbudgetcostMapper.update(wkCostbudgetcostEntity);
        return this.getEntity(wkCostbudgetcostEntity.getId(), wkCostbudgetcostEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkCostbudgetcostMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param wkCostbudgetcostPojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkCostbudgetcostPojo clearNull(WkCostbudgetcostPojo wkCostbudgetcostPojo) {
        //初始化NULL字段
        if (wkCostbudgetcostPojo.getPid() == null) wkCostbudgetcostPojo.setPid("");
        if (wkCostbudgetcostPojo.getGoodsid() == null) wkCostbudgetcostPojo.setGoodsid("");
        if (wkCostbudgetcostPojo.getItemcode() == null) wkCostbudgetcostPojo.setItemcode("");
        if (wkCostbudgetcostPojo.getItemname() == null) wkCostbudgetcostPojo.setItemname("");
        if (wkCostbudgetcostPojo.getItemspec() == null) wkCostbudgetcostPojo.setItemspec("");
        if (wkCostbudgetcostPojo.getItemunit() == null) wkCostbudgetcostPojo.setItemunit("");
        if (wkCostbudgetcostPojo.getCostid() == null) wkCostbudgetcostPojo.setCostid("");
        if (wkCostbudgetcostPojo.getCostdesc() == null) wkCostbudgetcostPojo.setCostdesc("");
        if (wkCostbudgetcostPojo.getQuantity() == null) wkCostbudgetcostPojo.setQuantity(0D);
        if (wkCostbudgetcostPojo.getTaxprice() == null) wkCostbudgetcostPojo.setTaxprice(0D);
        if (wkCostbudgetcostPojo.getTaxamount() == null) wkCostbudgetcostPojo.setTaxamount(0D);
        if (wkCostbudgetcostPojo.getPrice() == null) wkCostbudgetcostPojo.setPrice(0D);
        if (wkCostbudgetcostPojo.getAmount() == null) wkCostbudgetcostPojo.setAmount(0D);
        if (wkCostbudgetcostPojo.getTaxtotal() == null) wkCostbudgetcostPojo.setTaxtotal(0D);
        if (wkCostbudgetcostPojo.getItemtaxrate() == null) wkCostbudgetcostPojo.setItemtaxrate(0);
        if (wkCostbudgetcostPojo.getRownum() == null) wkCostbudgetcostPojo.setRownum(0);
        if (wkCostbudgetcostPojo.getRemark() == null) wkCostbudgetcostPojo.setRemark("");
        if (wkCostbudgetcostPojo.getCustom1() == null) wkCostbudgetcostPojo.setCustom1("");
        if (wkCostbudgetcostPojo.getCustom2() == null) wkCostbudgetcostPojo.setCustom2("");
        if (wkCostbudgetcostPojo.getCustom3() == null) wkCostbudgetcostPojo.setCustom3("");
        if (wkCostbudgetcostPojo.getCustom4() == null) wkCostbudgetcostPojo.setCustom4("");
        if (wkCostbudgetcostPojo.getCustom5() == null) wkCostbudgetcostPojo.setCustom5("");
        if (wkCostbudgetcostPojo.getCustom6() == null) wkCostbudgetcostPojo.setCustom6("");
        if (wkCostbudgetcostPojo.getCustom7() == null) wkCostbudgetcostPojo.setCustom7("");
        if (wkCostbudgetcostPojo.getCustom8() == null) wkCostbudgetcostPojo.setCustom8("");
        if (wkCostbudgetcostPojo.getCustom9() == null) wkCostbudgetcostPojo.setCustom9("");
        if (wkCostbudgetcostPojo.getCustom10() == null) wkCostbudgetcostPojo.setCustom10("");
        if (wkCostbudgetcostPojo.getTenantid() == null) wkCostbudgetcostPojo.setTenantid("");
        if (wkCostbudgetcostPojo.getRevision() == null) wkCostbudgetcostPojo.setRevision(0);
        return wkCostbudgetcostPojo;
    }
}
