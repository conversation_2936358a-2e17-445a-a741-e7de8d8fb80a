package inks.service.std.manu.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.manu.domain.WkWipnotemergeEntity;
import inks.service.std.manu.domain.pojo.WkWipnotemergePojo;
import inks.service.std.manu.mapper.WkWipnotemergeMapper;
import inks.service.std.manu.service.WkWipnotemergeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * WIP合并(WkWipnotemerge)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-13 09:51:14
 */
@Service("wkWipnotemergeService")
public class WkWipnotemergeServiceImpl implements WkWipnotemergeService {
    @Resource
    private WkWipnotemergeMapper wkWipnotemergeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkWipnotemergePojo getEntity(String key, String tid) {
        return this.wkWipnotemergeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkWipnotemergePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkWipnotemergePojo> lst = wkWipnotemergeMapper.getPageList(queryParam);
            PageInfo<WkWipnotemergePojo> pageInfo = new PageInfo<WkWipnotemergePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkWipnotemergePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipnotemergePojo insert(WkWipnotemergePojo wkWipnotemergePojo) {
        //初始化NULL字段
        if (wkWipnotemergePojo.getMergeid() == null) wkWipnotemergePojo.setMergeid("");
        if (wkWipnotemergePojo.getWipid() == null) wkWipnotemergePojo.setWipid("");
        if (wkWipnotemergePojo.getGoodsid() == null) wkWipnotemergePojo.setGoodsid("");
        if (wkWipnotemergePojo.getQuantity() == null) wkWipnotemergePojo.setQuantity(0D);
        if (wkWipnotemergePojo.getWorkuid() == null) wkWipnotemergePojo.setWorkuid("");
        if (wkWipnotemergePojo.getWorkitemid() == null) wkWipnotemergePojo.setWorkitemid("");
        if (wkWipnotemergePojo.getMainmark() == null) wkWipnotemergePojo.setMainmark(0);
        if (wkWipnotemergePojo.getRownum() == null) wkWipnotemergePojo.setRownum(0);
        if (wkWipnotemergePojo.getMachtype() == null) wkWipnotemergePojo.setMachtype("");
        if (wkWipnotemergePojo.getMachuid() == null) wkWipnotemergePojo.setMachuid("");
        if (wkWipnotemergePojo.getMachitemid() == null) wkWipnotemergePojo.setMachitemid("");
        if (wkWipnotemergePojo.getMachgroupid() == null) wkWipnotemergePojo.setMachgroupid("");
        if (wkWipnotemergePojo.getCustomer() == null) wkWipnotemergePojo.setCustomer("");
        if (wkWipnotemergePojo.getCustpo() == null) wkWipnotemergePojo.setCustpo("");
        if (wkWipnotemergePojo.getCiteuid() == null) wkWipnotemergePojo.setCiteuid("");
        if (wkWipnotemergePojo.getCiteitemid() == null) wkWipnotemergePojo.setCiteitemid("");
        if (wkWipnotemergePojo.getMainplanuid() == null) wkWipnotemergePojo.setMainplanuid("");
        if (wkWipnotemergePojo.getMainplanitemid() == null) wkWipnotemergePojo.setMainplanitemid("");
        if (wkWipnotemergePojo.getAttributejson() == null) wkWipnotemergePojo.setAttributejson("");
        if (wkWipnotemergePojo.getWkpcsqty() == null) wkWipnotemergePojo.setWkpcsqty(0D);
        if (wkWipnotemergePojo.getWksecqty() == null) wkWipnotemergePojo.setWksecqty(0D);
        if (wkWipnotemergePojo.getSourcetype() == null) wkWipnotemergePojo.setSourcetype(0);
        if (wkWipnotemergePojo.getRemark() == null) wkWipnotemergePojo.setRemark("");
        if (wkWipnotemergePojo.getCreateby() == null) wkWipnotemergePojo.setCreateby("");
        if (wkWipnotemergePojo.getCreatebyid() == null) wkWipnotemergePojo.setCreatebyid("");
        if (wkWipnotemergePojo.getCreatedate() == null) wkWipnotemergePojo.setCreatedate(new Date());
        if (wkWipnotemergePojo.getLister() == null) wkWipnotemergePojo.setLister("");
        if (wkWipnotemergePojo.getListerid() == null) wkWipnotemergePojo.setListerid("");
        if (wkWipnotemergePojo.getModifydate() == null) wkWipnotemergePojo.setModifydate(new Date());
        if (wkWipnotemergePojo.getCustom1() == null) wkWipnotemergePojo.setCustom1("");
        if (wkWipnotemergePojo.getCustom2() == null) wkWipnotemergePojo.setCustom2("");
        if (wkWipnotemergePojo.getCustom3() == null) wkWipnotemergePojo.setCustom3("");
        if (wkWipnotemergePojo.getCustom4() == null) wkWipnotemergePojo.setCustom4("");
        if (wkWipnotemergePojo.getCustom5() == null) wkWipnotemergePojo.setCustom5("");
        if (wkWipnotemergePojo.getCustom6() == null) wkWipnotemergePojo.setCustom6("");
        if (wkWipnotemergePojo.getCustom7() == null) wkWipnotemergePojo.setCustom7("");
        if (wkWipnotemergePojo.getCustom8() == null) wkWipnotemergePojo.setCustom8("");
        if (wkWipnotemergePojo.getCustom9() == null) wkWipnotemergePojo.setCustom9("");
        if (wkWipnotemergePojo.getCustom10() == null) wkWipnotemergePojo.setCustom10("");
        if (wkWipnotemergePojo.getTenantid() == null) wkWipnotemergePojo.setTenantid("");
        if (wkWipnotemergePojo.getTenantname() == null) wkWipnotemergePojo.setTenantname("");
        if (wkWipnotemergePojo.getRevision() == null) wkWipnotemergePojo.setRevision(0);
        WkWipnotemergeEntity wkWipnotemergeEntity = new WkWipnotemergeEntity();
        BeanUtils.copyProperties(wkWipnotemergePojo, wkWipnotemergeEntity);
        //生成雪花id
        wkWipnotemergeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        wkWipnotemergeEntity.setRevision(1);  //乐观锁
        this.wkWipnotemergeMapper.insert(wkWipnotemergeEntity);
        return this.getEntity(wkWipnotemergeEntity.getId(), wkWipnotemergeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkWipnotemergePojo 实例对象
     * @return 实例对象
     */
    @Override
    public WkWipnotemergePojo update(WkWipnotemergePojo wkWipnotemergePojo) {
        WkWipnotemergeEntity wkWipnotemergeEntity = new WkWipnotemergeEntity();
        BeanUtils.copyProperties(wkWipnotemergePojo, wkWipnotemergeEntity);
        this.wkWipnotemergeMapper.update(wkWipnotemergeEntity);
        return this.getEntity(wkWipnotemergeEntity.getId(), wkWipnotemergeEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.wkWipnotemergeMapper.delete(key, tid);
    }

    @Override
    public int updateMergeid(String mergeIds, String insertId, String tid) {
        return this.wkWipnotemergeMapper.updateMergeid(mergeIds, insertId, tid);
    }
}
