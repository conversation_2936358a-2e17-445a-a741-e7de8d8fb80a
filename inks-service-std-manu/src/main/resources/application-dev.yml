server:
  tomcat:
    uri-encoding: UTF-8
#spring数据源
spring:
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
#        server-addr: **************:8848
        username: nacos
        password: inks0820
        ip: *************
  datasource:
    #MYsql连接字符串 ************ inkssaasjinYi0326
    url: jdbc:mysql://**************:53308/inkssaas?useUnicode=true&characterEncoding=utf-8&allowMutilQueries=true&serverTimezone=Asia/Shanghai&useSSL=false
# 公网   url: *************************************************************************************************************************************************
    username: root
    password: asd@123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    # druid相关配置
    druid:
      # 初始化时建立物理连接的个数
      initial-size: 5
      # 最大连接池数量
      max-active: 20
      # 最小连接池数量
      min-idle: 10
      # 获取连接时最大等待时间，单位毫秒
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 连接保持空闲而不被驱逐的最小时间
      min-evictable-idle-time-millis: 300000
      # 用来检测连接是否有效的sql，要求是一个查询语句
      validation-query: SELECT 1 FROM DUAL
      # 建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-return: false
      # 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭
      pool-prepared-statements: false
      # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
      max-pool-prepared-statement-per-connection-size: 50
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计
      filter:
        stat:
          enabled: true
          # 慢sql记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connect-properties:
        druid.stat.mergeSql: true
        druid.stat.slowSqlMillis: 100

      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true
      web-stat-filter:
        enabled: true # 是否启用 Web 监控过滤器。
        url-pattern: /* # 过滤器的 URL 匹配模式。
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" # 需要排除的 URL 模式，不进行过滤。
        session-stat-enable: true       # 开启session统计功能
        session-stat-max-count: 1000    # session的最大个数,默认100
      stat-view-servlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:   # 允许访问 StatViewServlet 的 IP 地址。127.0.0.1
        deny: # 拒绝访问 StatViewServlet 的 IP 地址。
        url-pattern: /druid/* # StatViewServlet 的 URL 匹配模式
        reset-enable: false # 是否允许重置统计信息。
        # 用户名密码
        login-username: admin
        login-password: 123456
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: **************
    # Redis服务器连接端口
    port: 56379
    # Redis服务器连接密码（默认为空）
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
#  rabbitmq:
#    host: *************
#    port: 5672
#    username: inks
#    password: inks
#    virtual-host: inksoms
#    ##开启发布确认  (三种模式)
#    publisher-confirm-type: correlated
#    # 开启发送端抵达队列确认，消息未被队列接收时触发回调【发送端确认机制+本地事务表】
#    publisher-returns: true
#    #消费者监听器
#    listener:
#      simple:
#        #设置消费端手动 ack
#        acknowledge-mode: manual
#        #消费者 消息预取值 basicQOS
#        prefetch: 1
feign:
  sentinel:
    enabled: true
mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.**.domain
  #配置打印SQL语句到控制台
#雪花算法:  数据中心id,工作机器id
snowflake:
  dataCenterId: 1
  workerId: 1

logging:
  file:
    name: ./slow_sql.log # 相对路径指向resource下的logs文件夹
#    name: \\**************\DbCenter\2021Y\02 产品管理\18 独立工程项目\qqq.log # 绝对路径
  level:
    # 全局的日志级别
    root: INFO
    # 设置Druid连接池的日志级别
    com.alibaba.druid: trace

