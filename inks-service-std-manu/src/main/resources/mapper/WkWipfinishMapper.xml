<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipfinishMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipfinishPojo">
        <include refid="selectbillVo"/>
        where Wk_WipFinish.id = #{key} and Wk_WipFinish.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select
id, RefNo, BillTitle, BillType, BillDate, Wipid, Wpid, WpCode, WpName, Stationid, StatCode, StatName, WorkDate, Quantity, WorkTime, Remark, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Wk_WipFinish
    </sql>
    <sql id="selectdetailVo">
        select
               Wk_WipFinish.RefNo,
               Wk_WipFinish.BillTitle,
               Wk_WipFinish.BillType,
               Wk_WipFinish.BillDate,
               Wk_WipFinish.CreateBy,
               Wk_WipFinish.Lister,
               Wk_WipFinishItem.*
        from Wk_WipFinishItem left join Wk_WipFinish on Wk_WipFinish.id = Wk_WipFinishItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkWipfinishitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_WipFinish.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipFinish.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Wk_WipFinish.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_WipFinish.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_WipFinish.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.wipid != null ">
            and Wk_WipFinish.Wipid like concat('%', #{SearchPojo.wipid}, '%')
        </if>
        <if test="SearchPojo.wpid != null ">
            and Wk_WipFinish.Wpid like concat('%', #{SearchPojo.wpid}, '%')
        </if>
        <if test="SearchPojo.wpcode != null ">
            and Wk_WipFinish.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
        </if>
        <if test="SearchPojo.wpname != null ">
            and Wk_WipFinish.WpName like concat('%', #{SearchPojo.wpname}, '%')
        </if>
        <if test="SearchPojo.stationid != null ">
            and Wk_WipFinish.Stationid like concat('%', #{SearchPojo.stationid}, '%')
        </if>
        <if test="SearchPojo.statcode != null ">
            and Wk_WipFinish.StatCode like concat('%', #{SearchPojo.statcode}, '%')
        </if>
        <if test="SearchPojo.statname != null ">
            and Wk_WipFinish.StatName like concat('%', #{SearchPojo.statname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Wk_WipFinish.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_WipFinish.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_WipFinish.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_WipFinish.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_WipFinish.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_WipFinish.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_WipFinish.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_WipFinish.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_WipFinish.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_WipFinish.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_WipFinish.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_WipFinish.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_WipFinish.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_WipFinish.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_WipFinish.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_WipFinish.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_WipFinish.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_WipFinish.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_WipFinish.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.wipid != null ">
                or Wk_WipFinish.Wipid like concat('%', #{SearchPojo.wipid}, '%')
            </if>
            <if test="SearchPojo.wpid != null ">
                or Wk_WipFinish.Wpid like concat('%', #{SearchPojo.wpid}, '%')
            </if>
            <if test="SearchPojo.wpcode != null ">
                or Wk_WipFinish.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
            </if>
            <if test="SearchPojo.wpname != null ">
                or Wk_WipFinish.WpName like concat('%', #{SearchPojo.wpname}, '%')
            </if>
            <if test="SearchPojo.stationid != null ">
                or Wk_WipFinish.Stationid like concat('%', #{SearchPojo.stationid}, '%')
            </if>
            <if test="SearchPojo.statcode != null ">
                or Wk_WipFinish.StatCode like concat('%', #{SearchPojo.statcode}, '%')
            </if>
            <if test="SearchPojo.statname != null ">
                or Wk_WipFinish.StatName like concat('%', #{SearchPojo.statname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Wk_WipFinish.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_WipFinish.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_WipFinish.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_WipFinish.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_WipFinish.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_WipFinish.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_WipFinish.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_WipFinish.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_WipFinish.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_WipFinish.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_WipFinish.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_WipFinish.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_WipFinish.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_WipFinish.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_WipFinish.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_WipFinish.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkWipfinishPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_WipFinish.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipFinish.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Wk_WipFinish.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_WipFinish.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_WipFinish.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.wipid != null ">
            and Wk_WipFinish.Wipid like concat('%', #{SearchPojo.wipid}, '%')
        </if>
        <if test="SearchPojo.wpid != null ">
            and Wk_WipFinish.Wpid like concat('%', #{SearchPojo.wpid}, '%')
        </if>
        <if test="SearchPojo.wpcode != null ">
            and Wk_WipFinish.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
        </if>
        <if test="SearchPojo.wpname != null ">
            and Wk_WipFinish.WpName like concat('%', #{SearchPojo.wpname}, '%')
        </if>
        <if test="SearchPojo.stationid != null ">
            and Wk_WipFinish.Stationid like concat('%', #{SearchPojo.stationid}, '%')
        </if>
        <if test="SearchPojo.statcode != null ">
            and Wk_WipFinish.StatCode like concat('%', #{SearchPojo.statcode}, '%')
        </if>
        <if test="SearchPojo.statname != null ">
            and Wk_WipFinish.StatName like concat('%', #{SearchPojo.statname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Wk_WipFinish.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_WipFinish.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_WipFinish.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_WipFinish.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_WipFinish.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_WipFinish.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_WipFinish.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_WipFinish.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_WipFinish.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_WipFinish.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_WipFinish.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_WipFinish.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_WipFinish.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_WipFinish.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_WipFinish.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_WipFinish.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_WipFinish.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_WipFinish.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_WipFinish.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.wipid != null ">
                or Wk_WipFinish.Wipid like concat('%', #{SearchPojo.wipid}, '%')
            </if>
            <if test="SearchPojo.wpid != null ">
                or Wk_WipFinish.Wpid like concat('%', #{SearchPojo.wpid}, '%')
            </if>
            <if test="SearchPojo.wpcode != null ">
                or Wk_WipFinish.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
            </if>
            <if test="SearchPojo.wpname != null ">
                or Wk_WipFinish.WpName like concat('%', #{SearchPojo.wpname}, '%')
            </if>
            <if test="SearchPojo.stationid != null ">
                or Wk_WipFinish.Stationid like concat('%', #{SearchPojo.stationid}, '%')
            </if>
            <if test="SearchPojo.statcode != null ">
                or Wk_WipFinish.StatCode like concat('%', #{SearchPojo.statcode}, '%')
            </if>
            <if test="SearchPojo.statname != null ">
                or Wk_WipFinish.StatName like concat('%', #{SearchPojo.statname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Wk_WipFinish.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_WipFinish.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_WipFinish.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_WipFinish.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_WipFinish.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_WipFinish.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_WipFinish.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_WipFinish.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_WipFinish.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_WipFinish.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_WipFinish.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_WipFinish.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_WipFinish.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_WipFinish.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_WipFinish.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_WipFinish.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_WipFinish(id, RefNo, BillTitle, BillType, BillDate, Wipid, Wpid, WpCode, WpName, Stationid, StatCode, StatName, WorkDate, Quantity, WorkTime, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtitle}, #{billtype}, #{billdate}, #{wipid}, #{wpid}, #{wpcode}, #{wpname}, #{stationid}, #{statcode}, #{statname}, #{workdate}, #{quantity}, #{worktime}, #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipFinish
        <set>
            <if test="refno != null ">
            RefNo =#{refno},
        </if>
            <if test="billtitle != null ">
            BillTitle =#{billtitle},
        </if>
            <if test="billtype != null ">
            BillType =#{billtype},
        </if>
            <if test="billdate != null">
            BillDate =#{billdate},
        </if>
            <if test="wipid != null ">
            Wipid =#{wipid},
        </if>
            <if test="wpid != null ">
            Wpid =#{wpid},
        </if>
            <if test="wpcode != null ">
            WpCode =#{wpcode},
        </if>
            <if test="wpname != null ">
            WpName =#{wpname},
        </if>
            <if test="stationid != null ">
            Stationid =#{stationid},
        </if>
            <if test="statcode != null ">
            StatCode =#{statcode},
        </if>
            <if test="statname != null ">
            StatName =#{statname},
        </if>
            <if test="workdate != null">
            WorkDate =#{workdate},
        </if>
            <if test="quantity != null">
            Quantity =#{quantity},
        </if>
            <if test="worktime != null">
            WorkTime =#{worktime},
        </if>
            <if test="remark != null ">
            Remark =#{remark},
        </if>
            <if test="rownum != null">
            RowNum =#{rownum},
        </if>
            <if test="createby != null ">
            CreateBy =#{createby},
        </if>
            <if test="createbyid != null ">
            CreateByid =#{createbyid},
        </if>
            <if test="createdate != null">
            CreateDate =#{createdate},
        </if>
            <if test="lister != null ">
            Lister =#{lister},
        </if>
            <if test="listerid != null ">
            Listerid =#{listerid},
        </if>
            <if test="modifydate != null">
            ModifyDate =#{modifydate},
        </if>
            <if test="custom1 != null ">
            Custom1 =#{custom1},
        </if>
            <if test="custom2 != null ">
            Custom2 =#{custom2},
        </if>
            <if test="custom3 != null ">
            Custom3 =#{custom3},
        </if>
            <if test="custom4 != null ">
            Custom4 =#{custom4},
        </if>
            <if test="custom5 != null ">
            Custom5 =#{custom5},
        </if>
            <if test="custom6 != null ">
            Custom6 =#{custom6},
        </if>
            <if test="custom7 != null ">
            Custom7 =#{custom7},
        </if>
            <if test="custom8 != null ">
            Custom8 =#{custom8},
        </if>
            <if test="custom9 != null ">
            Custom9 =#{custom9},
        </if>
            <if test="custom10 != null ">
            Custom10 =#{custom10},
        </if>
            <if test="tenantname != null ">
            TenantName =#{tenantname},
        </if>
        Revision=Revision+1
    </set>
    where id = #{id} and Tenantid =#{tenantid}
</update>

        <!--通过主键删除-->
<delete id="delete">
delete from Wk_WipFinish where id = #{key} and Tenantid=#{tid}
</delete>
        <!--查询DelListIds-->
<select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.manu.domain.pojo.WkWipfinishPojo">
select
id
from Wk_WipFinishItem
where Pid = #{id}
<if test="item !=null and item.size()>0">
    and id not in
    <foreach collection="item" open="(" close=")" separator="," item="item">
        <if test="item.id != null">
            #{item.id}
        </if>
        <if test="item.id == null">
            ''
        </if>
    </foreach>
</if>
</select>

</mapper>

