<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipepiboleMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipepibolePojo">
        SELECT
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Wk_WipEpibole.id,
            Wk_WipEpibole.RefNo,
            Wk_WipEpibole.BillType,
            Wk_WipEpibole.BillDate,
            Wk_WipEpibole.Groupid,
            Wk_WipEpibole.BillTitle,
            Wk_WipEpibole.Operator,
            Wk_WipEpibole.MultiWp,
            Wk_WipEpibole.Summary,
            Wk_WipEpibole.CreateBy,
            Wk_WipEpibole.CreateByid,
            Wk_WipEpibole.CreateDate,
            Wk_WipEpibole.Lister,
            Wk_WipEpibole.Listerid,
            Wk_WipEpibole.ModifyDate,
            Wk_WipEpibole.Assessor,
            Wk_WipEpibole.Assessorid,
            Wk_WipEpibole.AssessDate,
            Wk_WipEpibole.BillStateText,
            Wk_WipEpibole.BillStateDate,
            Wk_WipEpibole.BillStartDate,
            Wk_WipEpibole.BillPlanDate,
            Wk_WipEpibole.BillTaxAmount,
            Wk_WipEpibole.BillAmount,
            Wk_WipEpibole.ItemCount,
            Wk_WipEpibole.DisannulCount,
            Wk_WipEpibole.FinishCount,
            Wk_WipEpibole.PrintCount,
            Wk_WipEpibole.Custom1,
            Wk_WipEpibole.Custom2,
            Wk_WipEpibole.Custom3,
            Wk_WipEpibole.Custom4,
            Wk_WipEpibole.Custom5,
            Wk_WipEpibole.Custom6,
            Wk_WipEpibole.Custom7,
            Wk_WipEpibole.Custom8,
            Wk_WipEpibole.Custom9,
            Wk_WipEpibole.Custom10,
            Wk_WipEpibole.Tenantid,
            Wk_WipEpibole.TenantName,
            Wk_WipEpibole.Revision
        FROM
            App_Workgroup
                RIGHT JOIN Wk_WipEpibole ON Wk_WipEpibole.Groupid = App_Workgroup.id
        where Wk_WipEpibole.id = #{key}
          and Wk_WipEpibole.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Wk_WipEpibole.id,
            Wk_WipEpibole.RefNo,
            Wk_WipEpibole.BillType,
            Wk_WipEpibole.BillDate,
            Wk_WipEpibole.Groupid,
            Wk_WipEpibole.BillTitle,
            Wk_WipEpibole.Operator,
            Wk_WipEpibole.MultiWp,
            Wk_WipEpibole.Summary,
            Wk_WipEpibole.CreateBy,
            Wk_WipEpibole.CreateByid,
            Wk_WipEpibole.CreateDate,
            Wk_WipEpibole.Lister,
            Wk_WipEpibole.Listerid,
            Wk_WipEpibole.ModifyDate,
            Wk_WipEpibole.Assessor,
            Wk_WipEpibole.Assessorid,
            Wk_WipEpibole.AssessDate,
            Wk_WipEpibole.BillStateText,
            Wk_WipEpibole.BillStateDate,
            Wk_WipEpibole.BillStartDate,
            Wk_WipEpibole.BillPlanDate,
            Wk_WipEpibole.BillTaxAmount,
            Wk_WipEpibole.BillAmount,
            Wk_WipEpibole.ItemCount,
            Wk_WipEpibole.DisannulCount,
            Wk_WipEpibole.FinishCount,
            Wk_WipEpibole.PrintCount,
            Wk_WipEpibole.Custom1,
            Wk_WipEpibole.Custom2,
            Wk_WipEpibole.Custom3,
            Wk_WipEpibole.Custom4,
            Wk_WipEpibole.Custom5,
            Wk_WipEpibole.Custom6,
            Wk_WipEpibole.Custom7,
            Wk_WipEpibole.Custom8,
            Wk_WipEpibole.Custom9,
            Wk_WipEpibole.Custom10,
            Wk_WipEpibole.Tenantid,
            Wk_WipEpibole.TenantName,
            Wk_WipEpibole.Revision
        FROM
            App_Workgroup
                RIGHT JOIN Wk_WipEpibole ON Wk_WipEpibole.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT
            Wk_WipEpiboleItem.id,
            Wk_WipEpiboleItem.Pid,
            Wk_WipEpiboleItem.WipItemid,
            Wk_WipEpiboleItem.Wsid,
            Wk_WipEpiboleItem.WsUid,
            Wk_WipEpiboleItem.Wpid,
            Wk_WipEpiboleItem.EndWpid,
            Wk_WipEpiboleItem.Goodsid,
            Wk_WipEpiboleItem.SubItemid,
            Wk_WipEpiboleItem.SubUse,
            Wk_WipEpiboleItem.SubUnit,
            Wk_WipEpiboleItem.SubQty,
            Wk_WipEpiboleItem.TaxPrice,
            Wk_WipEpiboleItem.TaxAmount,
            Wk_WipEpiboleItem.TaxTotal,
            Wk_WipEpiboleItem.Price,
            Wk_WipEpiboleItem.Amount,
            Wk_WipEpiboleItem.ItemTaxrate,
            Wk_WipEpiboleItem.StartDate,
            Wk_WipEpiboleItem.PlanDate,
            Wk_WipEpiboleItem.Quantity,
            Wk_WipEpiboleItem.RequQty,
            Wk_WipEpiboleItem.FinishQty,
            Wk_WipEpiboleItem.MrbQty,
            Wk_WipEpiboleItem.Closed,
            Wk_WipEpiboleItem.Remark,
            Wk_WipEpiboleItem.VirtualItem,
            Wk_WipEpiboleItem.RowNum,
            Wk_WipEpiboleItem.WkWpName,
            Wk_WipEpiboleItem.Customer,
            Wk_WipEpiboleItem.CustPO,
            Wk_WipEpiboleItem.MachUid,
            Wk_WipEpiboleItem.MachGroupid,
            Wk_WipEpiboleItem.MachItemid,
            Wk_WipEpiboleItem.MainPlanUid,
            Wk_WipEpiboleItem.MainPlanItemid,
            Wk_WipEpiboleItem.StateCode,
            Wk_WipEpiboleItem.StateDate,
            Wk_WipEpiboleItem.DisannulListerid,
            Wk_WipEpiboleItem.DisannulLister,
            Wk_WipEpiboleItem.DisannulDate,
            Wk_WipEpiboleItem.DisannulMark,
            Wk_WipEpiboleItem.AttributeJson,
            Wk_WipEpiboleItem.Custom1,
            Wk_WipEpiboleItem.Custom2,
            Wk_WipEpiboleItem.Custom3,
            Wk_WipEpiboleItem.Custom4,
            Wk_WipEpiboleItem.Custom5,
            Wk_WipEpiboleItem.Custom6,
            Wk_WipEpiboleItem.Custom7,
            Wk_WipEpiboleItem.Custom8,
            Wk_WipEpiboleItem.Custom9,
            Wk_WipEpiboleItem.Custom10,
            Wk_WipEpiboleItem.Tenantid,
            Wk_WipEpiboleItem.Revision,
            Wk_WipEpibole.RefNo,
            Wk_WipEpibole.BillType,
            Wk_WipEpibole.BillDate,
            Wk_WipEpibole.BillTitle,
            Wk_WipEpibole.Operator,
            Wk_WipEpibole.CreateBy,
            Wk_WipEpibole.Lister,
            Wk_WipEpibole.Assessor,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate
        FROM
            Wk_WipEpibole
                RIGHT JOIN Wk_WipEpiboleItem ON Wk_WipEpiboleItem.Pid = Wk_WipEpibole.id
                LEFT JOIN Mat_Goods ON Wk_WipEpiboleItem.Goodsid = Mat_Goods.id
                LEFT JOIN App_Workgroup ON Wk_WipEpibole.Groupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipepiboleitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_WipEpibole.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipEpibole.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Wk_WipEpibole.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_WipEpibole.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Wk_WipEpibole.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_WipEpibole.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_WipEpibole.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_WipEpibole.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_WipEpibole.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_WipEpibole.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_WipEpibole.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_WipEpibole.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_WipEpibole.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_WipEpibole.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatetext != null ">
            and Wk_WipEpibole.billstatetext like concat('%', #{SearchPojo.billstatetext}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_WipEpibole.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_WipEpibole.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_WipEpibole.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_WipEpibole.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_WipEpibole.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_WipEpibole.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_WipEpibole.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_WipEpibole.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_WipEpibole.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_WipEpibole.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_WipEpibole.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_WipEpibole.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_WipEpibole.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Wk_WipEpibole.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_WipEpibole.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_WipEpibole.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_WipEpibole.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_WipEpibole.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_WipEpibole.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_WipEpibole.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_WipEpibole.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_WipEpibole.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_WipEpibole.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatetext != null ">
                or Wk_WipEpibole.BillStateText like concat('%', #{SearchPojo.billstatetext}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_WipEpibole.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_WipEpibole.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_WipEpibole.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_WipEpibole.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_WipEpibole.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_WipEpibole.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_WipEpibole.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_WipEpibole.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_WipEpibole.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_WipEpibole.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_WipEpibole.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipepibolePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_WipEpibole.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipEpibole.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Wk_WipEpibole.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_WipEpibole.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Wk_WipEpibole.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_WipEpibole.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_WipEpibole.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_WipEpibole.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_WipEpibole.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_WipEpibole.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_WipEpibole.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_WipEpibole.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_WipEpibole.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_WipEpibole.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatetext != null ">
            and Wk_WipEpibole.BillStateText like concat('%', #{SearchPojo.billstatetext}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_WipEpibole.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_WipEpibole.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_WipEpibole.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_WipEpibole.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_WipEpibole.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_WipEpibole.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_WipEpibole.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_WipEpibole.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_WipEpibole.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_WipEpibole.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_WipEpibole.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_WipEpibole.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_WipEpibole.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Wk_WipEpibole.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_WipEpibole.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_WipEpibole.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_WipEpibole.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_WipEpibole.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_WipEpibole.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_WipEpibole.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_WipEpibole.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_WipEpibole.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_WipEpibole.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatetext != null ">
                or Wk_WipEpibole.BillStateText like concat('%', #{SearchPojo.billstatetext}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_WipEpibole.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_WipEpibole.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_WipEpibole.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_WipEpibole.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_WipEpibole.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_WipEpibole.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_WipEpibole.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_WipEpibole.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_WipEpibole.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_WipEpibole.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_WipEpibole.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_WipEpibole(id, RefNo, BillType, BillDate, Groupid, BillTitle, Operator, MultiWp, Summary,
                                  CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid,
                                  AssessDate, BillStateText, BillStateDate, BillStartDate, BillPlanDate, BillTaxAmount,
                                  BillAmount, ItemCount, DisannulCount, FinishCount, PrintCount, Custom1, Custom2,
                                  Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                  TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{groupid}, #{billtitle}, #{operator}, #{multiwp},
                #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{assessor}, #{assessorid}, #{assessdate}, #{billstatetext}, #{billstatedate}, #{billstartdate},
                #{billplandate}, #{billtaxamount}, #{billamount}, #{itemcount}, #{disannulcount}, #{finishcount},
                #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipEpibole
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="multiwp != null">
                MultiWp =#{multiwp},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billstatetext != null ">
                BillStateText =#{billstatetext},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billstartdate != null">
                BillStartDate =#{billstartdate},
            </if>
            <if test="billplandate != null">
                BillPlanDate =#{billplandate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WipEpibole
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_WipEpibole
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--刷新打印次数-->
    <update id="updatePrintcount">
        update Wk_WipEpibole
        SET PrintCount = #{printcount}
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>    
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkWipepibolePojo">
        select
        id
        from Wk_WipEpiboleItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>


    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateDisannulCount">
        update Wk_WipEpibole
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Wk_WipEpiboleItem
                                     where Wk_WipEpiboleItem.Pid = #{key}
                                       and Wk_WipEpiboleItem.Tenantid = #{tid}
                                       and Wk_WipEpiboleItem.DisannulMark = 1), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成行数 Eric 20211213-->
    <update id="updateFinishCount">
        update Wk_WipEpibole
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_WipEpiboleItem
                                   where Wk_WipEpiboleItem.Pid = #{key}
                                     and Wk_WipEpiboleItem.Tenantid = #{tid}
                                     and (Wk_WipEpiboleItem.Closed = 1
                                       or Wk_WipEpiboleItem.FinishQty >= Wk_WipEpiboleItem.Quantity)), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
</mapper>

