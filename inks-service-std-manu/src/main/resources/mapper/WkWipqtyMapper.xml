<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipqtyMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipqtyPojo">
        SELECT Wk_WipQty.id,
        Wk_WipQty.RefNo,
        Wk_WipQty.Wpid,
        Wk_WipQty.WpCode,
        Wk_WipQty.WpName,
        Wk_WipQty.WkDate,
        Wk_WipQty.Direction,
        Wk_WipQty.Goodsid,
        Wk_WipQty.Worker,
        Wk_WipQty.PcsQty,
        Wk_WipQty.SecQty,
        Wk_WipQty.Remark,
        Wk_WipQty.WorkUid,
        Wk_WipQty.WipUid,
        Wk_WipQty.WipRowNum,
        Wk_WipQty.WipItemid,
        Wk_WipQty.MrbPcsQty,
        Wk_WipQty.MrbSecQty,
        Wk_WipQty.Mrbid,
        Wk_WipQty.Acceid,
        Wk_WipQty.Inspector,
        Wk_WipQty.CreateBy,
        Wk_WipQty.CreateByid,
        Wk_WipQty.CreateDate,
        Wk_WipQty.Lister,
        Wk_WipQty.Listerid,
        Wk_WipQty.ModifyDate,
        Wk_WipQty.MachUid,
        Wk_WipQty.MachItemid,
        Wk_WipQty.MachGroupid,
        Wk_WipQty.MainPlanUid,
        Wk_WipQty.MainPlanItemid,
        Wk_WipQty.WorkItemid,
        Wk_WipQty.AttributeJson,
        Wk_WipQty.SpecJson,
        Wk_WipQty.SizeX,
        Wk_WipQty.SizeY,
        Wk_WipQty.SizeZ,
        Wk_WipQty.WorkParam,
        Wk_WipQty.Statid,
        Wk_WipQty.StatCode,
        Wk_WipQty.StatName,
        Wk_WipQty.WorkTime,
        Wk_WipQty.StoreMark,
        Wk_WipQty.Custom1,
        Wk_WipQty.Custom2,
        Wk_WipQty.Custom3,
        Wk_WipQty.Custom4,
        Wk_WipQty.Custom5,
        Wk_WipQty.Custom6,
        Wk_WipQty.Custom7,
        Wk_WipQty.Custom8,
        Wk_WipQty.Custom9,
        Wk_WipQty.Custom10,
        Wk_WipQty.Tenantid,
        Wk_WipQty.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM Wk_WipQty
        LEFT JOIN Mat_Goods ON Wk_WipQty.Goodsid = Mat_Goods.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Wk_WipQty.MachGroupid

        where Wk_WipQty.id = #{key}
        and Wk_WipQty.Tenantid = #{tid}
    </select>
    <sql id="selectWkWipqtyVo">
        SELECT Wk_WipQty.id,
               Wk_WipQty.RefNo,
               Wk_WipQty.Wpid,
               Wk_WipQty.WpCode,
               Wk_WipQty.WpName,
               Wk_WipQty.WkDate,
               Wk_WipQty.Direction,
               Wk_WipQty.Goodsid,
               Wk_WipQty.Worker,
               Wk_WipQty.PcsQty,
               Wk_WipQty.SecQty,
               Wk_WipQty.Remark,
               Wk_WipQty.WorkUid,
               Wk_WipQty.WipUid,
               Wk_WipQty.WipRowNum,
               Wk_WipQty.WipItemid,
               Wk_WipQty.MrbPcsQty,
               Wk_WipQty.MrbSecQty,
               Wk_WipQty.Mrbid,
               Wk_WipQty.Acceid,
               Wk_WipQty.Inspector,
               Wk_WipQty.CreateBy,
               Wk_WipQty.CreateByid,
               Wk_WipQty.CreateDate,
               Wk_WipQty.Lister,
               Wk_WipQty.Listerid,
               Wk_WipQty.ModifyDate,
               Wk_WipQty.MachUid,
               Wk_WipQty.MachItemid,
               Wk_WipQty.MachGroupid,
               Wk_WipQty.MainPlanUid,
               Wk_WipQty.MainPlanItemid,
               Wk_WipQty.WorkItemid,
               Wk_WipQty.AttributeJson,
               Wk_WipQty.SpecJson,
               Wk_WipQty.SizeX,
               Wk_WipQty.SizeY,
               Wk_WipQty.SizeZ,
               Wk_WipQty.WorkParam,
               Wk_WipQty.Statid,
               Wk_WipQty.StatCode,
               Wk_WipQty.StatName,
               Wk_WipQty.WorkTime,
               Wk_WipQty.StoreMark,
               Wk_WipQty.Custom1,
               Wk_WipQty.Custom2,
               Wk_WipQty.Custom3,
               Wk_WipQty.Custom4,
               Wk_WipQty.Custom5,
               Wk_WipQty.Custom6,
               Wk_WipQty.Custom7,
               Wk_WipQty.Custom8,
               Wk_WipQty.Custom9,
               Wk_WipQty.Custom10,
               Wk_WipQty.Tenantid,
               Wk_WipQty.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Wk_WipQty
                 LEFT JOIN Mat_Goods ON Wk_WipQty.Goodsid = Mat_Goods.id
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Wk_WipQty.MachGroupid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipqtyPojo">
        <include refid="selectWkWipqtyVo"/>
        where 1 = 1 and Wk_WipQty.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_WipQty.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Wk_WipQty.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.wpid != null">
            and Wk_WipQty.Wpid like concat('%', #{SearchPojo.wpid}, '%')
        </if>
        <if test="SearchPojo.wpcode != null">
            and Wk_WipQty.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
        </if>
        <if test="SearchPojo.wpname != null">
            and Wk_WipQty.WpName like concat('%', #{SearchPojo.wpname}, '%')
        </if>
        <if test="SearchPojo.direction != null">
            and Wk_WipQty.Direction like concat('%', #{SearchPojo.direction}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Wk_WipQty.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.worker != null">
            and Wk_WipQty.Worker like concat('%', #{SearchPojo.worker}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Wk_WipQty.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.workuid != null">
            and Wk_WipQty.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.wipuid != null">
            and Wk_WipQty.WipUid like concat('%', #{SearchPojo.wipuid}, '%')
        </if>
        <if test="SearchPojo.wipitemid != null">
            and Wk_WipQty.WipItemid like concat('%', #{SearchPojo.wipitemid}, '%')
        </if>
        <if test="SearchPojo.mrbid != null">
            and Wk_WipQty.Mrbid like concat('%', #{SearchPojo.mrbid}, '%')
        </if>
        <if test="SearchPojo.acceid != null">
            and Wk_WipQty.Acceid like concat('%', #{SearchPojo.acceid}, '%')
        </if>
        <if test="SearchPojo.inspector != null">
            and Wk_WipQty.Inspector like concat('%', #{SearchPojo.inspector}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_WipQty.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_WipQty.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_WipQty.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_WipQty.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.machuid != null">
            and Wk_WipQty.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null">
            and Wk_WipQty.MachItemid=#{SearchPojo.machitemid}
        </if>
        <if test="SearchPojo.machgroupid != null">
            and Wk_WipQty.MachGroupid=#{SearchPojo.machgroupid}
        </if>
        <if test="SearchPojo.mainplanuid != null">
            and Wk_WipQty.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null">
            and Wk_WipQty.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null">
            and Wk_WipQty.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null">
            and Wk_WipQty.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.statid != null">
            and Wk_WipQty.Statid like concat('%', #{SearchPojo.statid}, '%')
        </if>
        <if test="SearchPojo.statcode != null">
            and Wk_WipQty.StatCode like concat('%', #{SearchPojo.statcode}, '%')
        </if>
        <if test="SearchPojo.statname != null">
            and Wk_WipQty.StatName like concat('%', #{SearchPojo.statname}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_WipQty.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_WipQty.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_WipQty.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_WipQty.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_WipQty.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_WipQty.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_WipQty.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_WipQty.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_WipQty.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_WipQty.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Wk_WipQty.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.wpid != null">
                or Wk_WipQty.Wpid like concat('%', #{SearchPojo.wpid}, '%')
            </if>
            <if test="SearchPojo.wpcode != null">
                or Wk_WipQty.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
            </if>
            <if test="SearchPojo.wpname != null">
                or Wk_WipQty.WpName like concat('%', #{SearchPojo.wpname}, '%')
            </if>
            <if test="SearchPojo.direction != null">
                or Wk_WipQty.Direction like concat('%', #{SearchPojo.direction}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Wk_WipQty.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.worker != null">
                or Wk_WipQty.Worker like concat('%', #{SearchPojo.worker}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Wk_WipQty.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.workuid != null">
                or Wk_WipQty.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.wipuid != null">
                or Wk_WipQty.WipUid like concat('%', #{SearchPojo.wipuid}, '%')
            </if>
            <if test="SearchPojo.wipitemid != null">
                or Wk_WipQty.WipItemid like concat('%', #{SearchPojo.wipitemid}, '%')
            </if>
            <if test="SearchPojo.mrbid != null">
                or Wk_WipQty.Mrbid like concat('%', #{SearchPojo.mrbid}, '%')
            </if>
            <if test="SearchPojo.acceid != null">
                or Wk_WipQty.Acceid like concat('%', #{SearchPojo.acceid}, '%')
            </if>
            <if test="SearchPojo.inspector != null">
                or Wk_WipQty.Inspector like concat('%', #{SearchPojo.inspector}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_WipQty.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_WipQty.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_WipQty.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_WipQty.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.machuid != null">
                or Wk_WipQty.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null">
                or Wk_WipQty.MachItemid=#{SearchPojo.machitemid}
            </if>
            <if test="SearchPojo.machgroupid != null">
                or Wk_WipQty.MachGroupid= #{SearchPojo.machgroupid}
            </if>
            <if test="SearchPojo.mainplanuid != null">
                or Wk_WipQty.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null">
                or Wk_WipQty.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.workitemid != null">
                or Wk_WipQty.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.attributejson != null">
                or Wk_WipQty.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.statid != null">
                or Wk_WipQty.Statid like concat('%', #{SearchPojo.statid}, '%')
            </if>
            <if test="SearchPojo.statcode != null">
                or Wk_WipQty.StatCode like concat('%', #{SearchPojo.statcode}, '%')
            </if>
            <if test="SearchPojo.statname != null">
                or Wk_WipQty.StatName like concat('%', #{SearchPojo.statname}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_WipQty.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_WipQty.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_WipQty.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_WipQty.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_WipQty.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_WipQty.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_WipQty.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_WipQty.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_WipQty.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_WipQty.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_WipQty(id, RefNo, Wpid, WpCode, WpName, WkDate, Direction, Goodsid, Worker, PcsQty, SecQty,
        Remark, WorkUid, WipUid, WipRowNum, WipItemid, MrbPcsQty, MrbSecQty, Mrbid, Acceid,
        Inspector, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, MachUid,
        MachItemid, MachGroupid, MainPlanUid, MainPlanItemid, WorkItemid, AttributeJson, SpecJson,
        SizeX, SizeY, SizeZ,WorkParam,Statid, StatCode, StatName, WorkTime,StoreMark,Custom1,
        Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
        Tenantid, Revision)
        values (#{id}, #{refno}, #{wpid}, #{wpcode}, #{wpname}, #{wkdate}, #{direction}, #{goodsid}, #{worker},
        #{pcsqty}, #{secqty}, #{remark}, #{workuid}, #{wipuid}, #{wiprownum}, #{wipitemid}, #{mrbpcsqty},
        #{mrbsecqty}, #{mrbid}, #{acceid}, #{inspector}, #{createby}, #{createbyid}, #{createdate}, #{lister},
        #{listerid}, #{modifydate}, #{machuid}, #{machitemid}, #{machgroupid}, #{mainplanuid},
        #{mainplanitemid}, #{workitemid}, #{attributejson}, #{specjson}, #{sizex}, #{sizey},
        #{sizez}, #{workparam},#{statid}, #{statcode}, #{statname}, #{worktime},#{storemark},#{custom1}, #{custom2},
        #{custom3}, #{custom4},
        #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipQty
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="wpid != null">
                Wpid =#{wpid},
            </if>
            <if test="wpcode != null">
                WpCode =#{wpcode},
            </if>
            <if test="wpname != null">
                WpName =#{wpname},
            </if>
            <if test="wkdate != null">
                WkDate =#{wkdate},
            </if>
            <if test="direction != null">
                Direction =#{direction},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="worker != null">
                Worker =#{worker},
            </if>
            <if test="pcsqty != null">
                PcsQty =#{pcsqty},
            </if>
            <if test="secqty != null">
                SecQty =#{secqty},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="workuid != null">
                WorkUid =#{workuid},
            </if>
            <if test="wipuid != null">
                WipUid =#{wipuid},
            </if>
            <if test="wiprownum != null">
                WipRowNum =#{wiprownum},
            </if>
            <if test="wipitemid != null">
                WipItemid =#{wipitemid},
            </if>
            <if test="mrbpcsqty != null">
                MrbPcsQty =#{mrbpcsqty},
            </if>
            <if test="mrbsecqty != null">
                MrbSecQty =#{mrbsecqty},
            </if>
            <if test="mrbid != null">
                Mrbid =#{mrbid},
            </if>
            <if test="acceid != null">
                Acceid =#{acceid},
            </if>
            <if test="inspector != null">
                Inspector =#{inspector},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="machuid != null">
                MachUid =#{machuid},
            </if>
            <if test="machitemid != null">
                MachItemid =#{machitemid},
            </if>
            <if test="machgroupid != null">
                MachGroupid =#{machgroupid},
            </if>
            <if test="mainplanuid != null">
                MainPlanUid =#{mainplanuid},
            </if>
            <if test="mainplanitemid != null">
                MainPlanItemid =#{mainplanitemid},
            </if>
            <if test="workitemid != null">
                WorkItemid =#{workitemid},
            </if>
            <if test="attributejson != null">
                AttributeJson =#{attributejson},
            </if>
            <if test="specjson != null">
                SpecJson =#{specjson},
            </if>
            <if test="sizex != null">
                SizeX =#{sizex},
            </if>
            <if test="sizey != null">
                SizeY =#{sizey},
            </if>
            <if test="sizez != null">
                SizeZ =#{sizez},
            </if>
            <if test="workparam != null">
                WorkParam =#{workparam},
            </if>
            <if test="statid != null">
                Statid =#{statid},
            </if>
            <if test="statcode != null">
                StatCode =#{statcode},
            </if>
            <if test="statname != null">
                StatName =#{statname},
            </if>
            <if test="worktime != null">
                WorkTime =#{worktime},
            </if>
            <if test="storemark != null">
                StoreMark =#{storemark},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WipQty
        where id = #{key}
        and Tenantid = #{tid}
    </delete>

    <update id="updateWipWkwp" parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        UPDATE Wk_WipNote
        SET WkWpCode =#{wkwpcode},
        WkWpName =#{wkwpname},
        WkWpid =#{wkwpid},
        WkRowNum =#{wkrownum},
        WkSpecJson=#{wkspecjson}
        WHERE Wk_WipNote.id = #{id}
        and Wk_WipNote.Tenantid = #{tenantid}
    </update>

    <update id="updateWipComp">
        UPDATE Wk_WipNote
        SET CompPcsQty = (SELECT OutPcsQty From Wk_WipNoteItem where id = #{key} and Tenantid = #{tid})
        WHERE Wk_WipNote.id = (SELECT Pid From Wk_WipNoteItem where id = #{key} and Tenantid = #{tid})
        and Wk_WipNote.Tenantid = #{tid}
    </update>

    <update id="updateMachWkwp" parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        UPDATE Bus_MachiningItem
        SET WkWpCode =#{wkwpcode},
        WkWpName =#{wkwpname},
        WkWpid =#{wkwpid},
        WkRowNum =#{wkrownum}
        WHERE Bus_MachiningItem.id = #{machitemid}
        and Bus_MachiningItem.Tenantid = #{tenantid}
    </update>
    <update id="updateMachWkwpByMerge" parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        UPDATE Bus_MachiningItem
        SET WkWpCode =#{wkwpcode},
        WkWpName =#{wkwpname},
        WkWpid =#{wkwpid},
        WkRowNum =#{wkrownum}
        WHERE Bus_MachiningItem.id in (select machitemid from Wk_WorksheetMerge where itemid= #{workitemid} and Tenantid
        = #{tenantid})
        and Bus_MachiningItem.Tenantid = #{tenantid}
    </update>
    <update id="updateMachWkwpByWipNoteMerge">
        UPDATE Bus_MachiningItem
        SET WkWpCode =#{wkwpcode},
        WkWpName =#{wkwpname},
        WkWpid =#{wkwpid},
        WkRowNum =#{wkrownum}
        WHERE Bus_MachiningItem.id in (select machitemid from Wk_WipNoteMerge where Mergeid= #{id} and Tenantid =
        #{tenantid})
        and Bus_MachiningItem.Tenantid = #{tenantid}
    </update>
    <update id="updateMachBillWkwp" parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        UPDATE Bus_Machining
        SET BillWkWpCode =#{wkwpcode},
        BillWkWpName =#{wkwpname},
        BillWkWpid =#{wkwpid}
        WHERE Bus_Machining.Refno = #{machuid}
        and Bus_Machining.Tenantid = #{tenantid}
    </update>
    <update id="updateMachBillWkwpByMerge">
        UPDATE Bus_Machining
        SET BillWkWpCode =#{wkwpcode},
        BillWkWpName =#{wkwpname},
        BillWkWpid =#{wkwpid}
        WHERE Bus_Machining.Refno in (select machuid from Wk_WorksheetMerge where itemid= #{workitemid} and Tenantid =
        #{tenantid})
        and Bus_Machining.Tenantid = #{tenantid}
    </update>
    <update id="updateMachBillWkwpByWipNoteMerge">
        UPDATE Bus_Machining
        SET BillWkWpCode =#{wkwpcode},
        BillWkWpName =#{wkwpname},
        BillWkWpid =#{wkwpid}
        WHERE Bus_Machining.Refno in (select machuid from Wk_WipNoteMerge where Mergeid = #{id} and Tenantid =
        #{tenantid})
        and Bus_Machining.Tenantid = #{tenantid}
    </update>

    <update id="updateWorkWkwp" parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        UPDATE Wk_WorksheetItem
        SET WkWpCode =#{wkwpcode},
        WkWpName =#{wkwpname},
        WkWpid =#{wkwpid},
        WkRowNum =#{wkrownum}
        WHERE Wk_WorksheetItem.id = #{workitemid}
        and Wk_WorksheetItem.Tenantid = #{tenantid}
    </update>
    <update id="updateWorkWkwpByMerge">
        UPDATE Wk_WorksheetItem
        SET WkWpCode =#{wkwpcode},
        WkWpName =#{wkwpname},
        WkWpid =#{wkwpid},
        WkRowNum =#{wkrownum}
        WHERE Wk_WorksheetItem.id in (select workitemid from Wk_WorksheetMerge where itemid= #{workitemid} and Tenantid
        = #{tenantid})
        and Wk_WorksheetItem.Tenantid = #{tenantid}
    </update>

    <update id="updateWorkBillWkwp" parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        UPDATE Wk_Worksheet
        SET BillWkWpCode =#{wkwpcode},
        BillWkWpName =#{wkwpname},
        BillWkWpid =#{wkwpid}
        WHERE Wk_Worksheet.id = (select Pid from Wk_WorksheetItem Where id = #{workitemid})
        and Wk_Worksheet.Tenantid = #{tenantid}
    </update>
    <update id="updateWorkBillWkwpByMerge">
        UPDATE Wk_Worksheet
        SET BillWkWpCode =#{wkwpcode},
        BillWkWpName =#{wkwpname},
        BillWkWpid =#{wkwpid}
        WHERE Wk_Worksheet.refno in (select workuid from Wk_WorksheetMerge where itemid= #{workitemid} and Tenantid =
        #{tenantid})
        and Wk_Worksheet.Tenantid = #{tenantid}
    </update>


    <update id="updateMainPlanWkwp">
        UPDATE Wk_MainPlanItem
        SET WkWpCode =#{wkwpcode},
        WkWpName =#{wkwpname},
        WkWpid =#{wkwpid}
        WHERE Wk_MainPlanItem.id = #{mainplanitemid}
        and Wk_MainPlanItem.Tenantid = #{tenantid}
    </update>

    <update id="updateMainPlanBillWkwp">
        UPDATE Wk_MainPlan
        SET BillWkWpCode =#{wkwpcode},
        BillWkWpName =#{wkwpname},
        BillWkWpid =#{wkwpid}
        WHERE Wk_MainPlan.id = (select Pid from Wk_MainPlanItem Where id = #{mainplanitemid})
        and Wk_MainPlan.Tenantid = #{tenantid}
    </update>

    <update id="updateMainPlanWkwpByMerge">
        UPDATE Wk_MainPlanItem
        SET WkWpCode =#{wkwpcode},
        WkWpName =#{wkwpname},
        WkWpid =#{wkwpid}
        WHERE Wk_MainPlanItem.id in (select mainplanitemid from Wk_WorksheetMerge where itemid= #{workitemid} and
        Tenantid = #{tenantid})
        and Wk_MainPlanItem.Tenantid = #{tenantid}
    </update>

    <update id="updateMainPlanBillWkwpByMerge">
        UPDATE Wk_MainPlan
        SET BillWkWpCode =#{wkwpcode},
        BillWkWpName =#{wkwpname},
        BillWkWpid =#{wkwpid}
        WHERE Wk_MainPlan.refno in (select mainplanuid from Wk_WorksheetMerge where itemid= #{workitemid} and Tenantid =
        #{tenantid})
        and Wk_MainPlan.Tenantid = #{tenantid}
    </update>


    <!--新增所有列-->
    <insert id="insertAccess">
        insert into Mat_Access(id, RefNo, BillDate, TypeCode, BillType, BillTitle, Direction, Groupid, Storeid,
        StoreCode, StoreName, Operator, Summary, ReturnUid, OrgUid, PlusInfo, CreateBy,
        CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4,
        Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{refno}, #{billdate}, #{typecode}, #{billtype}, #{billtitle}, #{direction}, #{groupid},
        #{storeid}, #{storecode}, #{storename}, #{operator}, #{summary}, #{returnuid}, #{orguid}, #{plusinfo},
        #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
        #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
        #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--新增所有列-->
    <insert id="insertAccessitem">
        insert into Mat_AccessItem(id, Pid, Goodsid, Quantity, Price, Amount, TaxPrice, TaxAmount, ItemTaxrate,
        TaxTotal, Remark, CiteUid, CiteItemid, StateCode, StateDate, RowNum, Location,
        BatchNo, PackSn, ExpiDate, Customer, CustPO, MachUid, MachItemid, MachGroupid,
        MainPlanUid, MainPlanItemid, MrpUid, MrpItemid, Inveid, Skuid, AttributeJson,
        Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9,
        Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{quantity}, #{price}, #{amount}, #{taxprice}, #{taxamount}, #{itemtaxrate},
        #{taxtotal}, #{remark}, #{citeuid}, #{citeitemid}, #{statecode}, #{statedate}, #{rownum}, #{location},
        #{batchno}, #{packsn}, #{expidate}, #{customer}, #{custpo}, #{machuid}, #{machitemid}, #{machgroupid},
        #{mainplanuid}, #{mainplanitemid}, #{mrpuid}, #{mrpitemid}, #{inveid}, #{skuid}, #{attributejson},
        #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
        #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <select id="getListByWipUid" resultType="inks.service.std.manu.domain.pojo.WkWipqtyPojo">
        <include refid="selectWkWipqtyVo"/>
        where Wk_WipQty.WipUid=#{key} and Wk_WipQty.Tenantid =#{tid}
    </select>

    <select id="getOutListByWipItemid" resultType="inks.service.std.manu.domain.pojo.WkWipqtyPojo">
        <include refid="selectWkWipqtyVo"/>
        where Wk_WipQty.WipItemid=#{key} and Wk_WipQty.Direction='出组' and Wk_WipQty.Tenantid =#{tid}
    </select>
    <!--通过主键删除-->
    <delete id="deleteByWipItemid">
        delete
        from Wk_WipQty
        where WipItemid = #{key}
        and Tenantid = #{tid}
    </delete>

    <!--    /*客户订单金额排名*/-->
    <select id="getSumPageListByWp" parameterType="inks.common.core.domain.QueryParam"
            resultType="java.util.Map">
        select Wk_WipQty.Wpid as wpid,
        Wk_Process.WpCode as wpcode,
        Wk_Process.WpName as wpname,
        sum(Wk_WipQty.PcsQty) as pcsqty,
        ROUND(sum(Wk_WipQty.PcsQty * Wk_WipQty.SizeX * Wk_WipQty.SizeY / 1000000),2) as area,
        sum(Wk_WipQty.WorkTime) as worktime,
        count(*) as count
        FROM
        Wk_WipQty
        LEFT JOIN Wk_Process ON Wk_WipQty.Wpid = Wk_Process.id
        where Wk_WipQty.Direction = '出组'
        and Wk_WipQty.Tenantid = #{tenantid}
        and (Wk_WipQty.WkDate BETWEEN #{DateRange.StartDate}
        and #{DateRange.EndDate})
        <if test="filterstr != null">
            ${filterstr}
        </if>
        group by Wk_WipQty.Wpid,
        Wk_Process.WpCode,
        Wk_Process.WpName
        order by WpCode
    </select>

    <select id="getSumQtyAndWorkTimeByWp" resultType="java.util.Map">
        select Wk_WipQty.Wpid as wpid,
        Wk_Process.WpCode as wpcode,
        Wk_Process.WpName as wpname,
        sum(Wk_WipQty.PcsQty) as sumpcsqty,
        ROUND(sum(Wk_WipQty.PcsQty * Wk_WipQty.SizeX * Wk_WipQty.SizeY / 1000000),2) as area,
        sum(IFNULL(Wk_WipQty.WorkTime,0)) as sumworktime,
        count(*) as count
        FROM
        Wk_WipQty
        LEFT JOIN Wk_Process ON Wk_WipQty.Wpid = Wk_Process.id
        where Wk_WipQty.Direction = '出组'
        <if test="userid != null and userid != ''">
            and Wk_WipQty.CreateByid = #{userid}
        </if>
        and Wk_WipQty.Tenantid = #{queryParam.tenantid}
        and (Wk_WipQty.WkDate BETWEEN #{queryParam.DateRange.StartDate}
        and #{queryParam.DateRange.EndDate})
        <if test="queryParam.filterstr != null">
            ${queryParam.filterstr}
        </if>
        group by Wk_WipQty.Wpid,
        Wk_Process.WpCode,
        Wk_Process.WpName
        order by WpCode
    </select>

    <!--    汇总基材金额 name:基材名 value:统计次数 valueb:总金额-->
    <select id="getSumPageListByCost原始" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        <![CDATA[
        SELECT temp.name ,
               COUNT(temp.name) AS value,
               SUM(temp.value)  AS valueb
        FROM (SELECT Wk_WipQty.id,
                     jt.name,
                     jt.value
              FROM Wk_WipQty
                       LEFT JOIN Bus_MachiningItem ON Bus_MachiningItem.id = Wk_WipQty.MachItemid
                       JOIN JSON_TABLE(CostGroupJson,
                                       '$[*]' COLUMNS ( name VARCHAR(30) PATH '$.name',
                                           value VARCHAR(30) PATH '$.value')) jt
              WHERE JSON_VALID(CostGroupJson) = 1
                and Wk_WipQty.Tenantid = #{tenantid}
                and  (Wk_WipQty.WkDate BETWEEN #{DateRange.StartDate}  and #{DateRange.EndDate})) temp
        GROUP BY temp.name
        ]]>
    </select>
    <select id="getSumPageListByCost" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT temp.name , COUNT(temp.name) AS value, ROUND(SUM(temp.PcsQty * temp.value)) AS valueb
        FROM (SELECT Wk_WipQty.id,
        JSON_EXTRACT(Bus_MachiningItem.CostGroupJson, CONCAT('$[', numbers.num, '].name')) AS name,
        JSON_EXTRACT(Bus_MachiningItem.CostGroupJson, CONCAT('$[', numbers.num, '].value')) AS value,
        Wk_WipQty.PcsQty
        FROM Wk_WipQty
        LEFT JOIN Bus_MachiningItem ON Bus_MachiningItem.id = Wk_WipQty.MachItemid
        LEFT JOIN Wk_Process ON Wk_Process.id = Wk_WipQty.Wpid
        JOIN (SELECT 0 AS num UNION ALL SELECT 1
        UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT
        7 UNION ALL SELECT 8 UNION ALL SELECT 9) numbers
        WHERE JSON_VALID(Bus_MachiningItem.CostGroupJson) = 1 AND
        numbers.num <![CDATA[<]]> JSON_LENGTH(Bus_MachiningItem.CostGroupJson)
        and Wk_WipQty.Tenantid = #{tenantid}
        and Wk_WipQty.Direction = '出组'
        and Wk_Process.LastMark = 1
        and (Wk_WipQty.WkDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})) temp GROUP BY temp.name
    </select>
    <!--查询指定行数据    <include refid="selectWkWipqtyVo"/>-->
    <select id="getOnlinePageListByLastMark" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipqtyLastPojo">
        SELECT
        DISTINCT Wk_WipQty.id,
        Wk_WipQty.RefNo,
        Wk_WipQty.Wpid,
        Wk_WipQty.WpCode,
        Wk_WipQty.WpName,
        Wk_WipQty.WkDate,
        Wk_WipQty.Direction,
        Wk_WipQty.Goodsid,
        Wk_WipQty.Worker,
        Wk_WipQty.PcsQty,
        Wk_WipQty.SecQty,
        Wk_WipQty.Remark,
        Wk_WipQty.WipUid,
        Wk_WipQty.WorkUid,
        Wk_WipQty.WipRowNum,
        Wk_WipQty.WipItemid,
        Wk_WipQty.MrbPcsQty,
        Wk_WipQty.MrbSecQty,
        Wk_WipQty.Mrbid,
        Wk_WipQty.Acceid,
        Wk_WipQty.Inspector,
        Wk_WipQty.CreateBy,
        Wk_WipQty.CreateByid,
        Wk_WipQty.CreateDate,
        Wk_WipQty.Lister,
        Wk_WipQty.Listerid,
        Wk_WipQty.ModifyDate,
        Wk_WipQty.MachUid,
        Wk_WipQty.MachItemid,
        Wk_WipQty.MachGroupid,
        Wk_WipQty.MainPlanUid,
        Wk_WipQty.MainPlanItemid,
        Wk_WipQty.AttributeJson,
        Wk_WipQty.SpecJson,
        Wk_WipQty.SizeX,
        Wk_WipQty.SizeY,
        Wk_WipQty.SizeZ,
        Wk_WipQty.WorkParam,
        Wk_WipQty.Statid,
        Wk_WipQty.StatCode,
        Wk_WipQty.StatName,
        Wk_WipQty.WorkTime,
        Wk_WipQty.StoreMark,
        Wk_WipQty.Custom1,
        Wk_WipQty.Custom2,
        Wk_WipQty.Custom3,
        Wk_WipQty.Custom4,
        Wk_WipQty.Custom5,
        Wk_WipQty.Custom6,
        Wk_WipQty.Custom7,
        Wk_WipQty.Custom8,
        Wk_WipQty.Custom9,
        Wk_WipQty.Custom10,
        Wk_WipQty.Tenantid,
        Wk_WipQty.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Mat_Goods.Storeid,
        Mat_Storage.StoreName,
        Wk_WipNote.Workshopid,
        Wk_WipNote.Workshop,
        Wk_WipNote.WorkType,
        Wk_WipNote.WorkItemid,
        Wk_WipNote.PlanDate,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM Wk_WipQty
        LEFT JOIN Wk_WipNote ON Wk_WipQty.WipUid = Wk_WipNote.RefNo
        LEFT JOIN Wk_Process ON Wk_Process.id = Wk_WipQty.Wpid
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Wk_WipQty.MachGroupid
        LEFT JOIN Mat_Goods ON Wk_WipQty.Goodsid = Mat_Goods.id
        LEFT JOIN Mat_Storage ON Mat_Goods.Storeid = Mat_Storage.id
        where 1 = 1 and Wk_WipQty.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_WipQty.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>

    <!--UUID转雪花-->
    <update id="copyIdToCustom">
        update ${table} set custom5 = id where id like '%-%' and (Custom5 ='' or Custom5 is null);
    </update>
    <select id="getidAndDateAndCus" resultType="java.util.Map">
        select id,CreateDate,Custom5
        from ${table} where id like '%-%' order by CreateDate ${ordertype};
    </select>
    <update id="updateSnowflakeId">
        update ${table} set id = #{newSnowflakeId} where Custom5= #{custom5};
    </update>
    <update id="copyCiteItemidToCustom">
        update ${table} set custom6 = ${column} where ${column} like '%-%' and (Custom6 ='' or Custom6 is null);
    </update>
    <update id="updateCiteItemid">
        update ${tgtable} set ${tgcolumn} = COALESCE((SELECT id FROM ${orgtable} where
        ${orgtable}.Custom5=${tgtable}.${tgcolumn} ),'') where ${tgtable}.${tgcolumn} like '%-%';
    </update>


    <!--查询指定行数据 按条件分页查询工序完工报表-->
    <select id="getCostPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipqtyPojo">
        SELECT Wk_WipQty.id,
        Wk_WipQty.RefNo,
        Wk_WipQty.Wpid,
        Wk_WipQty.WpCode,
        Wk_WipQty.WpName,
        Wk_WipQty.WkDate,
        Wk_WipQty.Direction,
        Wk_WipQty.Goodsid,
        Wk_WipQty.Worker,
        Wk_WipQty.PcsQty,
        Wk_WipQty.SecQty,
        Wk_WipQty.Remark,
        Wk_WipQty.WorkUid,
        Wk_WipQty.WipUid,
        Wk_WipQty.WipRowNum,
        Wk_WipQty.WipItemid,
        Wk_WipQty.MrbPcsQty,
        Wk_WipQty.MrbSecQty,
        Wk_WipQty.Mrbid,
        Wk_WipQty.Acceid,
        Wk_WipQty.Inspector,
        Wk_WipQty.CreateBy,
        Wk_WipQty.CreateByid,
        Wk_WipQty.CreateDate,
        Wk_WipQty.Lister,
        Wk_WipQty.Listerid,
        Wk_WipQty.ModifyDate,
        Wk_WipQty.MachUid,
        Wk_WipQty.MachItemid,
        Wk_WipQty.MachGroupid,
        Wk_WipQty.MainPlanUid,
        Wk_WipQty.MainPlanItemid,
        Wk_WipQty.WorkItemid,
        Wk_WipQty.AttributeJson,
        Wk_WipQty.SpecJson,
        Wk_WipQty.SizeX,
        Wk_WipQty.SizeY,
        Wk_WipQty.SizeZ,
        Wk_WipQty.WorkParam,
        Wk_WipQty.Statid,
        Wk_WipQty.StatCode,
        Wk_WipQty.StatName,
        Wk_WipQty.WorkTime,
        Wk_WipQty.StoreMark,
        Wk_WipQty.Custom1,
        Wk_WipQty.Custom2,
        Wk_WipQty.Custom3,
        Wk_WipQty.Custom4,
        Wk_WipQty.Custom5,
        Wk_WipQty.Custom6,
        Wk_WipQty.Custom7,
        Wk_WipQty.Custom8,
        Wk_WipQty.Custom9,
        Wk_WipQty.Custom10,
        Wk_WipQty.Tenantid,
        Wk_WipQty.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        Bus_MachiningItem.CostGroupJson
        FROM Wk_WipQty
        LEFT JOIN Mat_Goods ON Wk_WipQty.Goodsid = Mat_Goods.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Wk_WipQty.MachGroupid
        LEFT JOIN Bus_MachiningItem ON Bus_MachiningItem.id = Wk_WipQty.MachItemid
        where 1 = 1 and Wk_WipQty.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_WipQty.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <!--    1.以订单号，分析该工单每个工序的出组工费(工时*单价）-->
    <select id="getSumCostPageListByWpMachuid" resultType="java.util.HashMap">
        SELECT
        Wk_WipQty.MachItemid,
        <foreach item="process" collection="processList" separator=",">
            FORMAT(SUM(CASE WHEN Wk_WipQty.Wpid=#{process.id} THEN Wk_WipQty.WorkTime ELSE 0 END), 1) AS
            ${process.wpcode + 'wt'},
            FORMAT(SUM(CASE WHEN Wk_WipQty.Wpid=#{process.id} THEN Wk_ProcCost.UnitPrice * Wk_WipQty.WorkTime ELSE 0
            END), 2) AS ${process.wpcode + 'amt'}
        </foreach>
        FROM
        Wk_WipQty
        LEFT JOIN Wk_ProcCost ON Wk_WipQty.Wpid = Wk_ProcCost.Wpid
        WHERE
        Wk_WipQty.MachUid = #{machuid}
        AND Wk_WipQty.Direction = '出组'
        AND Wk_WipQty.Tenantid = #{tid}
        GROUP BY
        Wk_WipQty.MachItemid;
    </select>
    <!--    2、以时间为条件，分析每个工序这段时间内工费汇总（工时*单价）-->
    <select id="getSumCostPageListByWpDate" parameterType="inks.common.core.domain.QueryParam"
            resultType="java.util.HashMap">
    </select>

    <select id="getAvgWorkTimeGroupByWp" resultType="java.util.HashMap">
        SELECT WpCode as wpcode,
        WpName as wpname,
        sum(WorkTime) as sumworktime,
        sum(PcsQty) as sumpcsqty,
        SUM(WorkTime) / SUM(PcsQty) as avgworktimes
        FROM Wk_WipQty
        WHERE Tenantid = #{tenantid}
        AND Direction = '出组'
        GROUP BY WpCode, WpName
    </select>

    <update id="updateWipQtyPcsQty">
        update Wk_WipQty
        set PcsQty = #{crtQty},
        ModifyDate = #{date}
        where id = #{wipqtyid}
        and Tenantid = #{tid}
    </update>

    <select id="getEntityByWipQtyid" resultType="inks.service.std.manu.domain.pojo.WkWipqtyPojo">
        select * from Wk_WipQty
        where id = #{wipqtyid} and Tenantid = #{tid} limit 1
    </select>

    <select id="checkWipQtyPcsQty" resultType="inks.service.std.manu.domain.pojo.WkWipqtyPojo">
        select * from Wk_WipQty
        where WipItemid = #{wipitemid} and PcsQty = 0 and Direction = '入组' and Tenantid = #{tenantid} limit 1
    </select>

    <select id="getSumOutPcsQtyGroupByWorker" resultType="java.util.HashMap">
        SELECT IF(Worker = '', Lister, Worker) AS name,
               SUM(PcsQty)                     AS value
        FROM Wk_WipQty
        WHERE Direction = '出组'
          and Tenantid = #{tenantid}
          and (Wk_WipQty.WkDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        GROUP BY IF(Worker = '', Lister, Worker)
    </select>

    <select id="getSumWpByPlanItemid" resultType="java.util.HashMap">
        select Wk_WipNoteItem.Wpid,
               Wk_WipNoteItem.WpName,
               sum(Wk_WipNoteItem.InPcsQty)  as sumpcsqty,
               sum(Wk_WipNoteItem.InSecQty)  as sumsecqty,
               sum(Wk_WipNoteItem.OutPcsQty) as sumoutpcsqty,
               sum(Wk_WipNoteItem.OutSecQty) as sumoutsecqty,
               sum(Wk_WipNoteItem.MrbPcsQty) as summrbpcsqty,
               sum(Wk_WipNoteItem.MrbSecQty) as summrbsecqty
        from Wk_WipNote
                 join Wk_WipNoteItem on Wk_WipNote.id = Wk_WipNoteItem.Pid
        where Wk_WipNoteItem.Tenantid = #{tid}
          and Wk_WipNote.MainPlanItemid = #{mainplanitemid}
        group by Wk_WipNoteItem.Wpid, Wk_WipNoteItem.WpName, Wk_WipNoteItem.RowNum
        order by Wk_WipNoteItem.RowNum
    </select>

    <select id="getPidBygoodsid" resultType="java.lang.String">
        select Pid from Mat_Goods where id= #{goodsid} and Tenantid= #{tid}
    </select>
</mapper>

