<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkProgroupitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkProgroupitemPojo">
        select
          id, Pid, Wpid, WpCode, WpName, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Wk_ProGroupItem
        where Wk_ProGroupItem.id = #{key} and Wk_ProGroupItem.Tenantid=#{tid}
    </select>
    <sql id="selectWkProgroupitemVo">
         select
          id, Pid, Wpid, WpCode, WpN<PERSON>, <PERSON><PERSON><PERSON>, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Wk_ProGroupItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkProgroupitemPojo">
        <include refid="selectWkProgroupitemVo"/>
         where 1 = 1 and Wk_ProGroupItem.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
         <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_ProGroupItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>

             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by #{orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Wk_ProGroupItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
   and Wk_ProGroupItem.wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null and SearchPojo.wpcode != ''">
   and Wk_ProGroupItem.wpcode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null and SearchPojo.wpname != ''">
   and Wk_ProGroupItem.wpname like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Wk_ProGroupItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Wk_ProGroupItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Wk_ProGroupItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Wk_ProGroupItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Wk_ProGroupItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Wk_ProGroupItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Wk_ProGroupItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Wk_ProGroupItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Wk_ProGroupItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Wk_ProGroupItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Wk_ProGroupItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
     and (1=0 
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Wk_ProGroupItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
   or Wk_ProGroupItem.Wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null and SearchPojo.wpcode != ''">
   or Wk_ProGroupItem.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null and SearchPojo.wpname != ''">
   or Wk_ProGroupItem.WpName like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Wk_ProGroupItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Wk_ProGroupItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Wk_ProGroupItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Wk_ProGroupItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Wk_ProGroupItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Wk_ProGroupItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Wk_ProGroupItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Wk_ProGroupItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Wk_ProGroupItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Wk_ProGroupItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Wk_ProGroupItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
)
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkProgroupitemPojo">
        select
          id, Pid, Wpid, WpCode, WpName, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Wk_ProGroupItem
        where Wk_ProGroupItem.Pid = #{Pid} and Wk_ProGroupItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_ProGroupItem(id, Pid, Wpid, WpCode, WpName, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{wpid}, #{wpcode}, #{wpname}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_ProGroupItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="wpid != null ">
                Wpid = #{wpid},
            </if>
            <if test="wpcode != null ">
                WpCode = #{wpcode},
            </if>
            <if test="wpname != null ">
                WpName = #{wpname},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Wk_ProGroupItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

