<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSccarryoveritemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSccarryoveritemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_ScCarryoverItem.id,
            Wk_ScCarryoverItem.Pid,
            Wk_ScCarryoverItem.Goodsid,
            Wk_ScCarryoverItem.ItemCode,
            Wk_ScCarryoverItem.ItemName,
            Wk_ScCarryoverItem.ItemSpec,
            Wk_ScCarryoverItem.ItemUnit,
            Wk_ScCarryoverItem.OpenQty,
            Wk_ScCarryoverItem.OpenAmount,
            Wk_ScCarryoverItem.InQty,
            Wk_ScCarryoverItem.InAmount,
            Wk_ScCarryoverItem.OutQty,
            Wk_ScCarryoverItem.OutAmount,
            Wk_ScCarryoverItem.CloseQty,
            Wk_ScCarryoverItem.CloseAmount,
            Wk_ScCarryoverItem.Skuid,
            Wk_ScCarryoverItem.AttributeJson,
            Wk_ScCarryoverItem.RowNum,
            Wk_ScCarryoverItem.Custom1,
            Wk_ScCarryoverItem.Custom2,
            Wk_ScCarryoverItem.Custom3,
            Wk_ScCarryoverItem.Custom4,
            Wk_ScCarryoverItem.Custom5,
            Wk_ScCarryoverItem.Custom6,
            Wk_ScCarryoverItem.Custom7,
            Wk_ScCarryoverItem.Custom8,
            Wk_ScCarryoverItem.Custom9,
            Wk_ScCarryoverItem.Custom10,
            Wk_ScCarryoverItem.Tenantid,
            Wk_ScCarryoverItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_ScCarryoverItem ON Mat_Goods.id = Wk_ScCarryoverItem.Goodsid
        where Wk_ScCarryoverItem.id = #{key}
          and Wk_ScCarryoverItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkSccarryoveritemVo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_ScCarryoverItem.id,
            Wk_ScCarryoverItem.Pid,
            Wk_ScCarryoverItem.Goodsid,
            Wk_ScCarryoverItem.ItemCode,
            Wk_ScCarryoverItem.ItemName,
            Wk_ScCarryoverItem.ItemSpec,
            Wk_ScCarryoverItem.ItemUnit,
            Wk_ScCarryoverItem.OpenQty,
            Wk_ScCarryoverItem.OpenAmount,
            Wk_ScCarryoverItem.InQty,
            Wk_ScCarryoverItem.InAmount,
            Wk_ScCarryoverItem.OutQty,
            Wk_ScCarryoverItem.OutAmount,
            Wk_ScCarryoverItem.CloseQty,
            Wk_ScCarryoverItem.CloseAmount,
            Wk_ScCarryoverItem.Skuid,
            Wk_ScCarryoverItem.AttributeJson,
            Wk_ScCarryoverItem.RowNum,
            Wk_ScCarryoverItem.Custom1,
            Wk_ScCarryoverItem.Custom2,
            Wk_ScCarryoverItem.Custom3,
            Wk_ScCarryoverItem.Custom4,
            Wk_ScCarryoverItem.Custom5,
            Wk_ScCarryoverItem.Custom6,
            Wk_ScCarryoverItem.Custom7,
            Wk_ScCarryoverItem.Custom8,
            Wk_ScCarryoverItem.Custom9,
            Wk_ScCarryoverItem.Custom10,
            Wk_ScCarryoverItem.Tenantid,
            Wk_ScCarryoverItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_ScCarryoverItem ON Mat_Goods.id = Wk_ScCarryoverItem.Goodsid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSccarryoveritemPojo">
        <include refid="selectWkSccarryoveritemVo"/>
        where 1 = 1 and Wk_ScCarryoverItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_ScCarryoverItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_ScCarryoverItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_ScCarryoverItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_ScCarryoverItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_ScCarryoverItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_ScCarryoverItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_ScCarryoverItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
            and Wk_ScCarryoverItem.skuid like concat('%', #{SearchPojo.skuid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_ScCarryoverItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_ScCarryoverItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_ScCarryoverItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_ScCarryoverItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_ScCarryoverItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_ScCarryoverItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_ScCarryoverItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_ScCarryoverItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_ScCarryoverItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_ScCarryoverItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_ScCarryoverItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_ScCarryoverItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_ScCarryoverItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_ScCarryoverItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_ScCarryoverItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_ScCarryoverItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_ScCarryoverItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
                or Wk_ScCarryoverItem.Skuid like concat('%', #{SearchPojo.skuid}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_ScCarryoverItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_ScCarryoverItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_ScCarryoverItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_ScCarryoverItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_ScCarryoverItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_ScCarryoverItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_ScCarryoverItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_ScCarryoverItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_ScCarryoverItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_ScCarryoverItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_ScCarryoverItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkSccarryoveritemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_ScCarryoverItem.id,
            Wk_ScCarryoverItem.Pid,
            Wk_ScCarryoverItem.Goodsid,
            Wk_ScCarryoverItem.ItemCode,
            Wk_ScCarryoverItem.ItemName,
            Wk_ScCarryoverItem.ItemSpec,
            Wk_ScCarryoverItem.ItemUnit,
            Wk_ScCarryoverItem.OpenQty,
            Wk_ScCarryoverItem.OpenAmount,
            Wk_ScCarryoverItem.InQty,
            Wk_ScCarryoverItem.InAmount,
            Wk_ScCarryoverItem.OutQty,
            Wk_ScCarryoverItem.OutAmount,
            Wk_ScCarryoverItem.CloseQty,
            Wk_ScCarryoverItem.CloseAmount,
            Wk_ScCarryoverItem.Skuid,
            Wk_ScCarryoverItem.AttributeJson,
            Wk_ScCarryoverItem.RowNum,
            Wk_ScCarryoverItem.Custom1,
            Wk_ScCarryoverItem.Custom2,
            Wk_ScCarryoverItem.Custom3,
            Wk_ScCarryoverItem.Custom4,
            Wk_ScCarryoverItem.Custom5,
            Wk_ScCarryoverItem.Custom6,
            Wk_ScCarryoverItem.Custom7,
            Wk_ScCarryoverItem.Custom8,
            Wk_ScCarryoverItem.Custom9,
            Wk_ScCarryoverItem.Custom10,
            Wk_ScCarryoverItem.Tenantid,
            Wk_ScCarryoverItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_ScCarryoverItem ON Mat_Goods.id = Wk_ScCarryoverItem.Goodsid
        where Wk_ScCarryoverItem.Pid = #{Pid}
          and Wk_ScCarryoverItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_ScCarryoverItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, OpenQty, OpenAmount,
                                       InQty, InAmount, OutQty, OutAmount, CloseQty, CloseAmount, Skuid, AttributeJson,
                                       RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                       Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{openqty},
                #{openamount}, #{inqty}, #{inamount}, #{outqty}, #{outamount}, #{closeqty}, #{closeamount}, #{skuid},
                #{attributejson}, #{rownum}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_ScCarryoverItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="openqty != null">
                OpenQty = #{openqty},
            </if>
            <if test="openamount != null">
                OpenAmount = #{openamount},
            </if>
            <if test="inqty != null">
                InQty = #{inqty},
            </if>
            <if test="inamount != null">
                InAmount = #{inamount},
            </if>
            <if test="outqty != null">
                OutQty = #{outqty},
            </if>
            <if test="outamount != null">
                OutAmount = #{outamount},
            </if>
            <if test="closeqty != null">
                CloseQty = #{closeqty},
            </if>
            <if test="closeamount != null">
                CloseAmount = #{closeamount},
            </if>
            <if test="skuid != null ">
                Skuid = #{skuid},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_ScCarryoverItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

