<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWorksheetMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWorksheetPojo">
        <include refid="selectbillVo"/>
        where Wk_Worksheet.id = #{key} and Wk_Worksheet.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Wk_Worksheet.id,
               Wk_Worksheet.RefNo,
               Wk_Worksheet.BillType,
               Wk_Worksheet.BillDate,
               Wk_Worksheet.BillTitle,
               Wk_Worksheet.Operator,
               Wk_Worksheet.Groupid,
               Wk_Worksheet.Summary,
               Wk_Worksheet.CreateBy,
               Wk_Worksheet.CreateByid,
               Wk_Worksheet.CreateDate,
               Wk_Worksheet.Lister,
               Wk_Worksheet.Listerid,
               Wk_Worksheet.ModifyDate,
               Wk_Worksheet.Assessor,
               Wk_Worksheet.Assessorid,
               Wk_Worksheet.AssessDate,
               Wk_Worksheet.BillStateCode,
               Wk_Worksheet.BillStateDate,
               Wk_Worksheet.BillStartDate,
               Wk_Worksheet.BillPlanDate,
               Wk_Worksheet.BillPlanHour,
               Wk_Worksheet.BillFinishHour,
               Wk_Worksheet.ItemCount,
               Wk_Worksheet.WipCount,
               Wk_Worksheet.DisannulCount,
               Wk_Worksheet.FinishCount,
               Wk_Worksheet.PrintCount,
               Wk_Worksheet.MergeCount,
               Wk_Worksheet.BillWkWpid,
               Wk_Worksheet.BillWkWpCode,
               Wk_Worksheet.BillWkWpName,
               Wk_Worksheet.OaFlowMark,
               Wk_Worksheet.BillQuantity,
               Wk_Worksheet.BillWkPcsQty,
               Wk_Worksheet.BillWkSecQty,
               Wk_Worksheet.Custom1,
               Wk_Worksheet.Custom2,
               Wk_Worksheet.Custom3,
               Wk_Worksheet.Custom4,
               Wk_Worksheet.Custom5,
               Wk_Worksheet.Custom6,
               Wk_Worksheet.Custom7,
               Wk_Worksheet.Custom8,
               Wk_Worksheet.Custom9,
               Wk_Worksheet.Custom10,
               Wk_Worksheet.Tenantid,
               Wk_Worksheet.TenantName,
               Wk_Worksheet.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Wk_Worksheet
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Wk_Worksheet.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupName,
               App_Workgroup.GroupUid,
               App_Workgroup.Abbreviate,
               Wk_WorksheetItem.id,
               Wk_WorksheetItem.Pid,
               Wk_WorksheetItem.Goodsid,
               Wk_WorksheetItem.Quantity,
               Wk_WorksheetItem.Price,
               Wk_WorksheetItem.Amount,
               Wk_WorksheetItem.ItemHour,
               Wk_WorksheetItem.PlanHour,
               Wk_WorksheetItem.StartDate,
               Wk_WorksheetItem.PlanDate,
               Wk_WorksheetItem.FinishQty,
               Wk_WorksheetItem.FinishHour,
               Wk_WorksheetItem.MrbQty,
               Wk_WorksheetItem.InStorage,
               Wk_WorksheetItem.EnabledMark,
               Wk_WorksheetItem.Closed,
               Wk_WorksheetItem.StartWpid,
               Wk_WorksheetItem.EndWpid,
               Wk_WorksheetItem.Remark,
               Wk_WorksheetItem.StateCode,
               Wk_WorksheetItem.StateDate,
               Wk_WorksheetItem.RowNum,
               Wk_WorksheetItem.MachType,
               Wk_WorksheetItem.MachUid,
               Wk_WorksheetItem.MachItemid,
               Wk_WorksheetItem.MachBatch,
               Wk_WorksheetItem.MachGroupid,
               Wk_WorksheetItem.MrpUid,
               Wk_WorksheetItem.MrpItemid,
               Wk_WorksheetItem.Customer,
               Wk_WorksheetItem.CustPO,
               Wk_WorksheetItem.CiteUid,
               Wk_WorksheetItem.CiteItemid,
               Wk_WorksheetItem.MainPlanUid,
               Wk_WorksheetItem.MainPlanItemid,
               Wk_WorksheetItem.Location,
               Wk_WorksheetItem.BatchNo,
               Wk_WorksheetItem.WipUsed,
               Wk_WorksheetItem.WkWpid,
               Wk_WorksheetItem.WkWpCode,
               Wk_WorksheetItem.WkWpName,
               Wk_WorksheetItem.WkRowNum,
               Wk_WorksheetItem.DisannulListerid,
               Wk_WorksheetItem.DisannulLister,
               Wk_WorksheetItem.DisannulDate,
               Wk_WorksheetItem.DisannulMark,
               Wk_WorksheetItem.AttributeJson,
               Wk_WorksheetItem.ReportQty,
               Wk_WorksheetItem.CompQty,
               Wk_WorksheetItem.FinishRate,
               Wk_WorksheetItem.SecQty,
               Wk_WorksheetItem.PanelWidth,
               Wk_WorksheetItem.PanelHeight,
               Wk_WorksheetItem.PcsInPanel,
               Wk_WorksheetItem.PanelThick,
               Wk_WorksheetItem.MatCode,
               Wk_WorksheetItem.MatUsed,
               Wk_WorksheetItem.RowCode,
               Wk_WorksheetItem.WkPcsQty,
               Wk_WorksheetItem.WkSecQty,
               Wk_WorksheetItem.MergeMark,
               Wk_WorksheetItem.SourceType,
               Wk_WorksheetItem.CostItemJson,
               Wk_WorksheetItem.CostGroupJson,
               Wk_WorksheetItem.VirtualItem,
               Wk_WorksheetItem.Custom1,
               Wk_WorksheetItem.Custom2,
               Wk_WorksheetItem.Custom3,
               Wk_WorksheetItem.Custom4,
               Wk_WorksheetItem.Custom5,
               Wk_WorksheetItem.Custom6,
               Wk_WorksheetItem.Custom7,
               Wk_WorksheetItem.Custom8,
               Wk_WorksheetItem.Custom9,
               Wk_WorksheetItem.Custom10,
               Wk_WorksheetItem.Tenantid,
               Wk_WorksheetItem.Revision,
               Wk_Worksheet.RefNo,
               Wk_Worksheet.Groupid,
               Wk_Worksheet.BillDate,
               Wk_Worksheet.BillType,
               Wk_Worksheet.BillTitle,
               Wk_Worksheet.Operator,
               Wk_Worksheet.CreateBy,
               Wk_Worksheet.Lister,
               Wk_Worksheet.BillWkWpid,
               Wk_Worksheet.BillWkWpCode,
               Wk_Worksheet.BillWkWpName,
               Wk_Worksheet.MergeCount,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.GoodsState,
               Mat_Goods.WeightQty,
               Mat_Goods.WeightUnit,
               Mat_Goods.AreaQty,
               Mat_Goods.AreaUnit,
               Mat_Goods.VolumeQty,
               Mat_Goods.VolumeUnit,
               Mat_Goods.PackQty,
               Mat_Goods.PackUnit,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        a2.GroupName               as machgroupname,
        Bus_MachiningItem.Quantity as machquantity
        FROM Wk_Worksheet
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Wk_Worksheet.Groupid
                 RIGHT JOIN Wk_WorksheetItem ON Wk_WorksheetItem.Pid = Wk_Worksheet.id
                 LEFT JOIN App_Workgroup a2 on Wk_WorksheetItem.MachGroupid = a2.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Wk_WorksheetItem.Goodsid
                 LEFT JOIN Bus_MachiningItem ON Bus_MachiningItem.id = Wk_WorksheetItem.MachItemid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWorksheetitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_Worksheet.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_Worksheet.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
        ,Wk_WorksheetItem.RowNum
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Wk_Worksheet.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Wk_Worksheet.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Wk_Worksheet.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Wk_Worksheet.operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Wk_Worksheet.groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Wk_Worksheet.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_Worksheet.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_Worksheet.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_Worksheet.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_Worksheet.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Wk_Worksheet.assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Wk_Worksheet.assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null">
            and Wk_Worksheet.billstatecode like concat('%',
                #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_Worksheet.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_Worksheet.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_Worksheet.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_Worksheet.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_Worksheet.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_Worksheet.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_Worksheet.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_Worksheet.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_Worksheet.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_Worksheet.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_Worksheet.tenantname like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_WorksheetItem.remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_WorksheetItem.custpo like concat('%',
                #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Wk_WorksheetItem.mrpuid like concat('%',
                #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Wk_WorksheetItem.mrpitemid like concat('%',
                #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_WorksheetItem.mainplanuid like concat('%',
                #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_WorksheetItem.mainplanitemid like concat('%',
                #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_WorksheetItem.machuid like concat('%',
                #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_WorksheetItem.machitemid like concat('%',
                #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_WorksheetItem.goodsid=
                #{SearchPojo.goodsid}
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Wk_WorksheetItem.citeuid like concat('%',
                #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.matcode != null and SearchPojo.matcode != ''">
            and Wk_WorksheetItem.matcode like concat('%',
                #{SearchPojo.matcode}, '%')
        </if>
        <if test="SearchPojo.matused != null and SearchPojo.matused != ''">
            and Wk_WorksheetItem.matused like concat('%',
                #{SearchPojo.matused}, '%')
        </if>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Wk_Worksheet.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Wk_Worksheet.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Wk_Worksheet.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Wk_Worksheet.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Wk_Worksheet.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Wk_Worksheet.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_Worksheet.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_Worksheet.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_Worksheet.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_Worksheet.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Wk_Worksheet.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Wk_Worksheet.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null">
                or Wk_Worksheet.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_Worksheet.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_Worksheet.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_Worksheet.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_Worksheet.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_Worksheet.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_Worksheet.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_Worksheet.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_Worksheet.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_Worksheet.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_Worksheet.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_Worksheet.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_WorksheetItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_WorksheetItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Wk_WorksheetItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Wk_WorksheetItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_WorksheetItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_WorksheetItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_WorksheetItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_WorksheetItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_WorksheetItem.goodsid= #{SearchPojo.goodsid}
            </if>
            <if test="SearchPojo.matcode != null and SearchPojo.matcode != ''">
                or Wk_WorksheetItem.matcode like concat('%', #{SearchPojo.matcode}, '%')
            </if>
            <if test="SearchPojo.matused != null and SearchPojo.matused != ''">
                or Wk_WorksheetItem.matused like concat('%', #{SearchPojo.matused}, '%')
            </if>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWorksheetPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_Worksheet.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_Worksheet.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Wk_Worksheet.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Wk_Worksheet.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Wk_Worksheet.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Wk_Worksheet.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Wk_Worksheet.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Wk_Worksheet.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_Worksheet.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_Worksheet.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_Worksheet.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_Worksheet.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Wk_Worksheet.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Wk_Worksheet.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null">
            and Wk_Worksheet.BillStateCode like concat('%',
                #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_Worksheet.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_Worksheet.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_Worksheet.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_Worksheet.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_Worksheet.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_Worksheet.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_Worksheet.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_Worksheet.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_Worksheet.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_Worksheet.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_Worksheet.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Wk_Worksheet.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Wk_Worksheet.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Wk_Worksheet.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Wk_Worksheet.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Wk_Worksheet.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Wk_Worksheet.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_Worksheet.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_Worksheet.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_Worksheet.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_Worksheet.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Wk_Worksheet.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Wk_Worksheet.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null">
                or Wk_Worksheet.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_Worksheet.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_Worksheet.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_Worksheet.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_Worksheet.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_Worksheet.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_Worksheet.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_Worksheet.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_Worksheet.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_Worksheet.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_Worksheet.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_Worksheet.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_Worksheet(id, RefNo, BillType, BillDate, BillTitle, Operator, Groupid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, BillStateCode, BillStateDate, BillStartDate, BillPlanDate, BillPlanHour, BillFinishHour, ItemCount, WipCount, DisannulCount, FinishCount, PrintCount, MergeCount, BillWkWpid, BillWkWpCode, BillWkWpName, OaFlowMark, MatMergeCount, BillQuantity, BillWkPcsQty, BillWkSecQty, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{operator}, #{groupid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{billstatecode}, #{billstatedate}, #{billstartdate}, #{billplandate}, #{billplanhour}, #{billfinishhour}, #{itemcount}, #{wipcount}, #{disannulcount}, #{finishcount}, #{printcount}, #{mergecount}, #{billwkwpid}, #{billwkwpcode}, #{billwkwpname}, #{oaflowmark}, #{matmergecount}, #{billquantity}, #{billwkpcsqty}, #{billwksecqty}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_Worksheet
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billstatecode != null">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billstartdate != null">
                BillStartDate =#{billstartdate},
            </if>
            <if test="billplandate != null">
                BillPlanDate =#{billplandate},
            </if>
            <if test="billplanhour != null">
                BillPlanHour =#{billplanhour},
            </if>
            <if test="billfinishhour != null">
                BillFinishHour =#{billfinishhour},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="wipcount != null">
                WipCount =#{wipcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="mergecount != null">
                MergeCount =#{mergecount},
            </if>
            <if test="billwkwpid != null">
                BillWkWpid =#{billwkwpid},
            </if>
            <if test="billwkwpcode != null">
                BillWkWpCode =#{billwkwpcode},
            </if>
            <if test="billwkwpname != null">
                BillWkWpName =#{billwkwpname},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="matmergecount != null">
            MatMergeCount =#{matmergecount},
        </if>
            <if test="billquantity != null">
            BillQuantity =#{billquantity},
        </if>
            <if test="billwkpcsqty != null">
            BillWkPcsQty =#{billwkpcsqty},
        </if>
            <if test="billwksecqty != null">
            BillWkSecQty =#{billwksecqty},
        </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_Worksheet
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_Worksheet
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--刷新打印次数-->
    <update id="updatePrintcount">
        update Wk_Worksheet
        SET PrintCount = #{printcount}
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateDisannulCount">
        update Wk_Worksheet
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Wk_WorksheetItem
                                     where Wk_WorksheetItem.Pid = #{key}
                                       and Wk_WorksheetItem.Tenantid = #{tid}
                                       and Wk_WorksheetItem.DisannulMark = 1), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成行数 Eric 20211213-->
    <update id="updateFinishCount">
        update Wk_Worksheet
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_WorksheetItem
                                   where Wk_WorksheetItem.Pid = #{key}
                                     and Wk_WorksheetItem.Tenantid = #{tid}
                                     and (Wk_WorksheetItem.Closed = 1
                                       or Wk_WorksheetItem.FinishQty >= Wk_WorksheetItem.Quantity)), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkWorksheetPojo">
        select id
        from Wk_WorksheetItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--查询DelListIds-->
    <select id="getDelMatIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkWorksheetPojo">
        select id
        from Wk_WorksheetMat
        where Pid = #{id}
        <if test="mat != null and mat.size() > 0">
            and id not in
            <foreach collection="mat" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateMrpWsFinish">
        update Wk_MrpItem
        SET WkWsQty =COALESCE((SELECT SUM(Wk_WorksheetItem.quantity)
                               FROM Wk_WorksheetItem
                                        LEFT OUTER JOIN Wk_Worksheet
                                                        ON Wk_WorksheetItem.pid = Wk_Worksheet.id
                               where Wk_Worksheet.BillType = 'MRP需求'
                                 and Wk_WorksheetItem.MrpUid = #{refno}
                                 and Wk_WorksheetItem.MrpItemid = #{key}
                                 and Wk_WorksheetItem.DisannulMark = 0
                                 and Wk_WorksheetItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--查询单个-->
    <select id="getEntityByRefNo" resultType="inks.service.std.manu.domain.pojo.WkWorksheetPojo">
        <include refid="selectbillVo"/>
        where Wk_Worksheet.RefNo = #{key}
          and Wk_Worksheet.Tenantid = #{tid}
        LIMIT 1
    </select>


    <select id="getEntityByItemid" resultType="inks.service.std.manu.domain.pojo.WkWorksheetPojo">
        <include refid="selectbillVo"/>
        where Wk_Worksheet.id = (select pid from Wk_WorksheetItem where id = #{itemid})
        and Wk_Worksheet.Tenantid = #{tid}
        LIMIT 1
    </select>

    <select id="getMachitemWKqty" resultType="java.util.Map">
        select WkQty, WkQuantity
        from Bus_MachiningItem
        where id = #{key}
          and Tenantid = #{tid}
    </select>
    <!--   更新销售订单生产数量-->
    <update id="updateMachWKQuantity">
        update Bus_MachiningItem
        SET WkQuantity =COALESCE((SELECT sum(Wk_WorksheetItem.Quantity)
                                  FROM Wk_WorksheetItem
                                           LEFT OUTER JOIN Wk_Worksheet
                                                           ON Wk_WorksheetItem.pid = Wk_Worksheet.id
                                  where Wk_Worksheet.BillType IN ('销售订单')
                                    and Wk_WorksheetItem.MachItemid = #{key}
                                    and Wk_WorksheetItem.DisannulMark = 0
                                    and Wk_WorksheetItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <update id="updateMergeCountTest">
        update Wk_Worksheet
        SET MergeCount =(SELECT count(1)
                         from Wk_Worksheet
                                  Right join Wk_WorksheetItem on Wk_Worksheet.id = Wk_WorksheetItem.Pid
                         where Wk_WorksheetItem.id in (${itemids})
                           and Wk_WorksheetItem.DisannulMark = 1)
        where id in (select Pid from Wk_WorksheetItem where id in (${itemids}))
          and Tenantid = #{tid}
    </update>
    <!--    MySQL不允许同时查询和修改同一张表的数据，而使用临时表a可以绕过这个限制。-->
    <update id="updateMergeCount">
        UPDATE Wk_Worksheet
        SET MergeCount = (SELECT COALESCE(a.count, 0)
                          FROM (SELECT COUNT(Wk_WorksheetItem.MergeMark) AS count
                                FROM Wk_WorksheetItem
                                WHERE Wk_WorksheetItem.pid = #{pid}
                                  AND Wk_WorksheetItem.MergeMark != 0) AS a)
        WHERE Wk_Worksheet.id = #{pid}
          and Wk_Worksheet.Tenantid = #{tid}
    </update>

    <!--  查询生产加工单是否被引用  -->
    <select id="getItemCiteBillName" resultType="java.lang.String">
        (SELECT '生产WIP' as billname From Wk_WipNote where WorkItemid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '领料单' as billname From Mat_RequisitionItem where WorkItemid = #{key} and Tenantid = #{tid} LIMIT 1)
    </select>

    <update id="updateFinishCountByIds">
        update Wk_Worksheet
        SET Lister=#{realname},
            Listerid=#{userid},
            ModifyDate=#{now},
        FinishCount = COALESCE((SELECT COUNT(0)
                                FROM Wk_WorksheetItem
        where Wk_WorksheetItem.Pid IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and Wk_WorksheetItem.Tenantid = #{tid}
        and (Wk_WorksheetItem.Closed = 1
            or Wk_WorksheetItem.FinishQty >= Wk_WorksheetItem.Quantity)), 0)
        where id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and Tenantid = #{tid}
    </update>

    <update id="updateMainPlanStartQty">
        update Wk_MainPlanItem
        SET StartQty = COALESCE((SELECT SUM(Wk_WorksheetItem.Quantity)
                                 FROM Wk_WorksheetItem
                                 where Wk_WorksheetItem.MainPlanItemid = #{mainplanitemid}
                                   and Wk_WorksheetItem.DisannulMark = 0
                                   and Wk_WorksheetItem.Tenantid = #{tid}
                                   and Wk_WorksheetItem.Goodsid=Wk_MainPlanItem.Goodsid), 0)
        where id = #{mainplanitemid}
          and Tenantid = #{tid}
    </update>

    <select id="getItemDetailEntity"  resultType="inks.service.std.manu.domain.pojo.WkWorksheetitemdetailPojo">
    <include refid="selectdetailVo"/>
    where Wk_WorksheetItem.id=#{itemid} and Wk_Worksheet.Tenantid = #{tid}
    </select>

    <select id="getMatSpecpcbitemByGoodsid" resultType="inks.service.std.manu.domain.pojo.MatSpecpcbitemPojo">
        select id,
               Pid,
               Wpid,
               WpUid,
               WpName,
               Description,
               DetailJson,
               RowNum,
               AreaMult,
               FlowCode,
               ToolsCode,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_SpecPcbItem
        where Mat_SpecPcbItem.Pid = (select id from Mat_SpecPcb where Goodsid = #{goodsid})
          and Mat_SpecPcbItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <select id="getMatSpecpcbByGoodsid" resultType="java.util.Map">
        select * from Mat_SpecPcb where Goodsid = #{goodsid} and Tenantid = #{tid}
    </select>

    <update id="updateMatMergeCount">
        update Wk_Worksheet
        set MatMergeCount = #{size}
        where id = #{id}
        and Tenantid = #{tid}
    </update>

    <select id="getQuantityByMachitemid" resultType="double">
        select Quantity from Bus_MachiningItem where id = #{machitemid} and Tenantid = #{tenantid}
    </select>

    <select id="getEntityByGoodsid_Specpcb" resultType="inks.service.std.manu.domain.vo.manu_MatSpecpcbPojo">
        SELECT Mat_SpecPcb.*,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid
        FROM Mat_Goods
                 RIGHT JOIN Mat_SpecPcb ON Mat_SpecPcb.Goodsid = Mat_Goods.id
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Mat_SpecPcb.Groupid
        where Mat_SpecPcb.Goodsid = #{goodsid}
          and Mat_SpecPcb.Tenantid = #{tid}
    </select>

    <select id="getListVO_Specpcbitem" resultType="inks.service.std.manu.domain.vo.manu_MatSpecpcbitemVo">
        select WpCode,
               WpName,
               Description,
               FlowCode,
               ToolsCode,
               Remark
        from Mat_SpecPcbItem
        where Mat_SpecPcbItem.Pid = #{Pid}
          and Mat_SpecPcbItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <select id="getList_Specpcbdrl" resultType="inks.service.std.manu.domain.vo.manu_MatSpecpcbdrlPojo">
        select id,
               Pid,
               Symbol,
               HoleSize,
               Tolerance,
               DrillSize,
               PcsTotal,
               SetTotal,
               PnlTotal,
               PthMark,
               RowNum,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision,
               TenantName
        from Mat_SpecPcbDrl
        where Mat_SpecPcbDrl.Pid = #{Pid}
          and Mat_SpecPcbDrl.Tenantid = #{tid}
        order by RowNum
    </select>

    <select id="getList_Specpcbdraw" resultType="inks.service.std.manu.domain.vo.manu_MatSpecpcbdrawPojo">
        select id,
        Pid,
        DrawType,
        DrawTitle,
        DrawImage,
        DrawJson,
        DrawUrl,
        InsideMark,
        RowNum,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Custom6,
        Custom7,
        Custom8,
        Custom9,
        Custom10,
        Tenantid,
        TenantName,
        Revision
        from Mat_SpecPcbDraw
        where Mat_SpecPcbDraw.Pid = #{Pid}
        and Mat_SpecPcbDraw.Tenantid = #{tid}
        order by RowNum
    </select>

    <select id="getListByGoodsid_Ecn" resultType="inks.service.std.manu.domain.vo.manu_MatEcnPojo">
        SELECT DISTINCT e.*
        FROM Mat_Ecn e
        JOIN Mat_Ecn sub ON e.EclCode = sub.EclCode
        WHERE sub.Goodsid = #{goodsid}
        AND e.Tenantid = #{tid}
    </select>

    <select id="getList_SpecpcbdrlVo" resultType="inks.service.std.manu.domain.vo.manu_MatSpecpcbdrlVo">
        select id,
        Pid,
        Symbol,
        HoleSize,
        Tolerance,
        DrillSize,
        PcsTotal,
        SetTotal,
        PnlTotal,
        PthMark,
        Remark
        from Mat_SpecPcbDrl
        where Mat_SpecPcbDrl.Pid = #{Pid}
        and Mat_SpecPcbDrl.Tenantid = #{tid}
        order by RowNum
    </select>

    <select id="getList_SpecpcbdrawVo" resultType="inks.service.std.manu.domain.vo.manu_MatSpecpcbdrawVo">
        select id,
        Pid,
        DrawType,
        DrawTitle,
        DrawImage,
        DrawJson,
        DrawUrl,
        InsideMark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate
        from Mat_SpecPcbDraw
        where Mat_SpecPcbDraw.Pid = #{Pid}
        and Mat_SpecPcbDraw.Tenantid = #{tid}
        order by RowNum
    </select>

    <select id="getQtyBymachitemid" resultType="java.util.Map">
        select Bus_MachiningItem.Quantity, Bus_Machining.BillType, Bus_Machining.Summary, Bus_MachiningItem.Remark
        from Bus_MachiningItem
        join Bus_Machining on Bus_MachiningItem.Pid = Bus_Machining.id
        where Bus_MachiningItem.id = #{machitemid}
        and Bus_MachiningItem.Tenantid = #{tid}
    </select>

    <select id="getQtyByplanitemid" resultType="java.util.Map">
        select Wk_MainPlanItem.StartQty
        from Wk_MainPlanItem
        where Wk_MainPlanItem.id = #{mainplanitemid}
        and Wk_MainPlanItem.Tenantid = #{tid}
    </select>

    <select id="getIdByRefNo" resultType="java.lang.String">
        select id from Wk_Worksheet where RefNo = #{refno} and Tenantid = #{tid} limit 1
    </select>
</mapper>

