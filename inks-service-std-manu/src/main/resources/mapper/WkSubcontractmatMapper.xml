<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSubcontractmatMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSubcontractmatPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_SubcontractMat.id,
            Wk_SubcontractMat.Pid,
            Wk_SubcontractMat.Itemid,
            Wk_SubcontractMat.Goodsid,
            Wk_SubcontractMat.Quantity,
            Wk_SubcontractMat.FinishQty,
            Wk_SubcontractMat.MrpUid,
            Wk_SubcontractMat.MrpItemid,
            Wk_SubcontractMat.SubQty,
            Wk_SubcontractMat.MainQty,
            Wk_SubcontractMat.LossRate,
            Wk_SubcontractMat.Bomid,
            Wk_SubcontractMat.BomType,
            Wk_SubcontractMat.BomItemid,
            Wk_SubcontractMat.ItemRowCode,
            Wk_SubcontractMat.RowNum,
            Wk_SubcontractMat.Closed,
            Wk_SubcontractMat.BomQty,
            Wk_SubcontractMat.AvaiQty,
            Wk_SubcontractMat.NeedQty,
            Wk_SubcontractMat.StoPlanQty,
            Wk_SubcontractMat.RealQty,
            Wk_SubcontractMat.FlowCode,
            Wk_SubcontractMat.AttributeJson,
            Wk_SubcontractMat.Custom1,
            Wk_SubcontractMat.Custom2,
            Wk_SubcontractMat.Custom3,
            Wk_SubcontractMat.Custom4,
            Wk_SubcontractMat.Custom5,
            Wk_SubcontractMat.Custom6,
            Wk_SubcontractMat.Custom7,
            Wk_SubcontractMat.Custom8,
            Wk_SubcontractMat.Custom9,
            Wk_SubcontractMat.Custom10,
            Wk_SubcontractMat.Tenantid,
            Wk_SubcontractMat.Revision
        FROM
            Wk_SubcontractMat
                LEFT JOIN Mat_Goods ON Wk_SubcontractMat.Goodsid = Mat_Goods.id
        where Wk_SubcontractMat.id = #{key}
          and Wk_SubcontractMat.Tenantid = #{tid}
    </select>
    <sql id="selectWkSubcontractmatVo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_SubcontractMat.id,
            Wk_SubcontractMat.Pid,
            Wk_SubcontractMat.Itemid,
            Wk_SubcontractMat.Goodsid,
            Wk_SubcontractMat.Quantity,
            Wk_SubcontractMat.FinishQty,
            Wk_SubcontractMat.MrpUid,
            Wk_SubcontractMat.MrpItemid,
            Wk_SubcontractMat.SubQty,
            Wk_SubcontractMat.MainQty,
            Wk_SubcontractMat.LossRate,
            Wk_SubcontractMat.Bomid,
            Wk_SubcontractMat.BomType,
            Wk_SubcontractMat.BomItemid,
            Wk_SubcontractMat.ItemRowCode,
            Wk_SubcontractMat.RowNum,
            Wk_SubcontractMat.Closed,
            Wk_SubcontractMat.BomQty,
            Wk_SubcontractMat.AvaiQty,
            Wk_SubcontractMat.NeedQty,
            Wk_SubcontractMat.StoPlanQty,
            Wk_SubcontractMat.RealQty,
            Wk_SubcontractMat.FlowCode,
            Wk_SubcontractMat.AttributeJson,
            Wk_SubcontractMat.Custom1,
            Wk_SubcontractMat.Custom2,
            Wk_SubcontractMat.Custom3,
            Wk_SubcontractMat.Custom4,
            Wk_SubcontractMat.Custom5,
            Wk_SubcontractMat.Custom6,
            Wk_SubcontractMat.Custom7,
            Wk_SubcontractMat.Custom8,
            Wk_SubcontractMat.Custom9,
            Wk_SubcontractMat.Custom10,
            Wk_SubcontractMat.Tenantid,
            Wk_SubcontractMat.Revision
        FROM
            Wk_SubcontractMat
                LEFT JOIN Mat_Goods ON Wk_SubcontractMat.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSubcontractmatPojo">
        <include refid="selectWkSubcontractmatVo"/>
        where 1 = 1 and Wk_SubcontractMat.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_SubcontractMat.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_SubcontractMat.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.itemid != null and SearchPojo.itemid != ''">
            and Wk_SubcontractMat.itemid like concat('%', #{SearchPojo.itemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_SubcontractMat.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Wk_SubcontractMat.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Wk_SubcontractMat.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
            and Wk_SubcontractMat.bomid like concat('%', #{SearchPojo.bomid}, '%')
        </if>
        <if test="SearchPojo.bomitemid != null and SearchPojo.bomitemid != ''">
            and Wk_SubcontractMat.bomitemid like concat('%', #{SearchPojo.bomitemid}, '%')
        </if>
        <if test="SearchPojo.itemrowcode != null and SearchPojo.itemrowcode != ''">
            and Wk_SubcontractMat.itemrowcode like concat('%', #{SearchPojo.itemrowcode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_SubcontractMat.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_SubcontractMat.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_SubcontractMat.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_SubcontractMat.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_SubcontractMat.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_SubcontractMat.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_SubcontractMat.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_SubcontractMat.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_SubcontractMat.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_SubcontractMat.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_SubcontractMat.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.itemid != null and SearchPojo.itemid != ''">
                or Wk_SubcontractMat.Itemid like concat('%', #{SearchPojo.itemid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_SubcontractMat.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Wk_SubcontractMat.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Wk_SubcontractMat.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
                or Wk_SubcontractMat.Bomid like concat('%', #{SearchPojo.bomid}, '%')
            </if>
            <if test="SearchPojo.bomitemid != null and SearchPojo.bomitemid != ''">
                or Wk_SubcontractMat.BomItemid like concat('%', #{SearchPojo.bomitemid}, '%')
            </if>
            <if test="SearchPojo.itemrowcode != null and SearchPojo.itemrowcode != ''">
                or Wk_SubcontractMat.ItemRowCode like concat('%', #{SearchPojo.itemrowcode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_SubcontractMat.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_SubcontractMat.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_SubcontractMat.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_SubcontractMat.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_SubcontractMat.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_SubcontractMat.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_SubcontractMat.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_SubcontractMat.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_SubcontractMat.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_SubcontractMat.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkSubcontractmatPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_SubcontractMat.id,
               Wk_SubcontractMat.Pid,
               Wk_SubcontractMat.Itemid,
               Wk_SubcontractMat.Goodsid,
               Wk_SubcontractMat.Quantity,
               Wk_SubcontractMat.FinishQty,
               Wk_SubcontractMat.MrpUid,
               Wk_SubcontractMat.MrpItemid,
               Wk_SubcontractMat.SubQty,
               Wk_SubcontractMat.MainQty,
               Wk_SubcontractMat.LossRate,
               Wk_SubcontractMat.Bomid,
               Wk_SubcontractMat.BomType,
               Wk_SubcontractMat.BomItemid,
               Wk_SubcontractMat.ItemRowCode,
               Wk_SubcontractMat.RowNum,
               Wk_SubcontractMat.Closed,
               Wk_SubcontractMat.BomQty,
               Wk_SubcontractMat.AvaiQty,
               Wk_SubcontractMat.NeedQty,
               Wk_SubcontractMat.StoPlanQty,
               Wk_SubcontractMat.RealQty,
               Wk_SubcontractMat.FlowCode,
               Wk_SubcontractMat.AttributeJson,
               Wk_SubcontractMat.Custom1,
               Wk_SubcontractMat.Custom2,
               Wk_SubcontractMat.Custom3,
               Wk_SubcontractMat.Custom4,
               Wk_SubcontractMat.Custom5,
               Wk_SubcontractMat.Custom6,
               Wk_SubcontractMat.Custom7,
               Wk_SubcontractMat.Custom8,
               Wk_SubcontractMat.Custom9,
               Wk_SubcontractMat.Custom10,
               Wk_SubcontractMat.Tenantid,
               Wk_SubcontractMat.Revision
        FROM Wk_SubcontractMat
                 LEFT JOIN Mat_Goods ON Wk_SubcontractMat.Goodsid = Mat_Goods.id
        where Wk_SubcontractMat.Pid = #{Pid}
          and Wk_SubcontractMat.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_SubcontractMat(id, Pid, Itemid, Goodsid, Quantity, FinishQty, MrpUid, MrpItemid, SubQty, MainQty,
                                      LossRate, Bomid, BomType, BomItemid, ItemRowCode, RowNum, Closed, BomQty, AvaiQty,
                                      NeedQty, StoPlanQty, RealQty, FlowCode, AttributeJson, Custom1,
                                      Custom2, Custom3,
                                      Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                      Revision)
        values (#{id}, #{pid}, #{itemid}, #{goodsid}, #{quantity}, #{finishqty}, #{mrpuid}, #{mrpitemid}, #{subqty},
                #{mainqty}, #{lossrate}, #{bomid}, #{bomtype}, #{bomitemid}, #{itemrowcode}, #{rownum}, #{closed},
                #{bomqty}, #{avaiqty}, #{needqty}, #{stoplanqty}, #{realqty}, #{flowcode}, #{attributejson},
                #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_SubcontractMat
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemid != null ">
                Itemid = #{itemid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="mainqty != null">
                MainQty = #{mainqty},
            </if>
            <if test="lossrate != null">
                LossRate = #{lossrate},
            </if>
            <if test="bomid != null ">
                Bomid = #{bomid},
            </if>
            <if test="bomtype != null">
                BomType = #{bomtype},
            </if>
            <if test="bomitemid != null ">
                BomItemid = #{bomitemid},
            </if>
            <if test="itemrowcode != null ">
                ItemRowCode = #{itemrowcode},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="bomqty != null">
                BomQty = #{bomqty},
            </if>
            <if test="avaiqty != null">
                AvaiQty = #{avaiqty},
            </if>
            <if test="needqty != null">
                NeedQty = #{needqty},
            </if>
            <if test="stoplanqty != null">
                StoPlanQty = #{stoplanqty},
            </if>
            <if test="realqty != null">
                RealQty = #{realqty},
            </if>
            <if test="flowcode != null ">
                FlowCode = #{flowcode},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_SubcontractMat
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteByItemid">
        delete
        from Wk_SubcontractMat
        where Itemid = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询指定行数据-->
    <select id="getListByItemid"
            resultType="inks.service.std.manu.domain.pojo.WkSubcontractmatPojo">
        <include refid="selectWkSubcontractmatVo"/>
        where Wk_SubcontractMat.Itemid=#{key} and Wk_SubcontractMat.Tenantid =#{tid}
    </select>

    <!--查询指定行数据-->
    <select id="getListByPid"
            resultType="inks.service.std.manu.domain.pojo.WkSubcontractmatPojo">
        <include refid="selectWkSubcontractmatVo"/>
        where Wk_SubcontractMat.Pid=#{key} and Wk_SubcontractMat.Tenantid =#{tid}
    </select>
</mapper>

