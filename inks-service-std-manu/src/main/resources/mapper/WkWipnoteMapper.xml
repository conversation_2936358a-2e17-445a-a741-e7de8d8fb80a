<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipnoteMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        SELECT Wk_WipNote.id,
               Wk_WipNote.RefNo,
               Wk_WipNote.BillDate,
               Wk_WipNote.WorkType,
               Wk_WipNote.Workshopid,
               Wk_WipNote.Workshop,
               Wk_WipNote.Goodsid,
               Wk_WipNote.PlanDate,
               Wk_WipNote.Quantity,
               Wk_WipNote.WkPcsQty,
               Wk_WipNote.WkSecQty,
               Wk_WipNote.Mr<PERSON><PERSON><PERSON><PERSON><PERSON>,
               Wk_WipNote.MrbSecQty,
               Wk_WipNote.Supplement,
               Wk_WipNote.CreateBy,
               Wk_WipNote.CreateByid,
               Wk_WipNote.CreateDate,
               Wk_WipNote.Lister,
               Wk_WipNote.Listerid,
               Wk_WipNote.ModifyDate,
               Wk_WipNote.StateCode,
               Wk_WipNote.StateDate,
               Wk_WipNote.WkWpid,
               Wk_WipNote.WkWpCode,
               Wk_WipNote.WkWpName,
               Wk_WipNote.WkRowNum,
               Wk_WipNote.Customer,
               Wk_WipNote.CustPO,
               Wk_WipNote.MachUid,
               Wk_WipNote.MachItemid,
               Wk_WipNote.MachGroupid,
               Wk_WipNote.MainPlanUid,
               Wk_WipNote.MainPlanItemid,
               Wk_WipNote.WorkUid,
               Wk_WipNote.WorkRefNo,
               Wk_WipNote.WorkRowNum,
               Wk_WipNote.WorkItemid,
               Wk_WipNote.SubStWpid,
               Wk_WipNote.SubStWpCode,
               Wk_WipNote.SubStWpName,
               Wk_WipNote.SubEndWpid,
               Wk_WipNote.SubEndWpCode,
               Wk_WipNote.SubEndWpName,
               Wk_WipNote.SubUid,
               Wk_WipNote.WorkDate,
               Wk_WipNote.CompWpid,
               Wk_WipNote.CompWpCode,
               Wk_WipNote.CompWpName,
               Wk_WipNote.CompPcsQty,
               Wk_WipNote.WipGroupid,
               Wk_WipNote.Summary,
               Wk_WipNote.MatCode,
               Wk_WipNote.MatUsed,
               Wk_WipNote.AttributeJson,
               Wk_WipNote.AttributeStr,
               Wk_WipNote.WkSpecJson,
               Wk_WipNote.ItemCount,
               Wk_WipNote.FinishCount,
               Wk_WipNote.PrintCount,
               Wk_WipNote.ColorLevel,
               Wk_WipNote.SizeX,
               Wk_WipNote.SizeY,
               Wk_WipNote.SizeZ,
               Wk_WipNote.Closed,
               Wk_WipNote.DisannulListerid,
               Wk_WipNote.DisannulLister,
               Wk_WipNote.DisannulDate,
               Wk_WipNote.DisannulMark,
               Wk_WipNote.MergeMark,
               Wk_WipNote.SourceType,
               Wk_WipNote.Exponent,
               Wk_WipNote.JobPcsQty,
               Wk_WipNote.JobSecQty,
               Wk_WipNote.Custom1,
               Wk_WipNote.Custom2,
               Wk_WipNote.Custom3,
               Wk_WipNote.Custom4,
               Wk_WipNote.Custom5,
               Wk_WipNote.Custom6,
               Wk_WipNote.Custom7,
               Wk_WipNote.Custom8,
               Wk_WipNote.Custom9,
               Wk_WipNote.Custom10,
               Wk_WipNote.Tenantid,
               Wk_WipNote.Revision,
               <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Mat_Goods
                 RIGHT JOIN Wk_WipNote ON Wk_WipNote.Goodsid = Mat_Goods.id
                 LEFT JOIN App_Workgroup ON Wk_WipNote.MachGroupid = App_Workgroup.id
        where Wk_WipNote.id = #{key}
          and Wk_WipNote.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Wk_WipNote.id,
               Wk_WipNote.RefNo,
               Wk_WipNote.BillDate,
               Wk_WipNote.WorkType,
               Wk_WipNote.Workshopid,
               Wk_WipNote.Workshop,
               Wk_WipNote.Goodsid,
               Wk_WipNote.PlanDate,
               Wk_WipNote.Quantity,
               Wk_WipNote.WkPcsQty,
               Wk_WipNote.WkSecQty,
               Wk_WipNote.MrbPcsQty,
               Wk_WipNote.MrbSecQty,
               Wk_WipNote.Supplement,
               Wk_WipNote.CreateBy,
               Wk_WipNote.CreateByid,
               Wk_WipNote.CreateDate,
               Wk_WipNote.Lister,
               Wk_WipNote.Listerid,
               Wk_WipNote.ModifyDate,
               Wk_WipNote.StateCode,
               Wk_WipNote.StateDate,
               Wk_WipNote.WkWpid,
               Wk_WipNote.WkWpCode,
               Wk_WipNote.WkWpName,
               Wk_WipNote.WkRowNum,
               Wk_WipNote.Customer,
               Wk_WipNote.CustPO,
               Wk_WipNote.MachUid,
               Wk_WipNote.MachItemid,
               Wk_WipNote.MachGroupid,
               Wk_WipNote.MainPlanUid,
               Wk_WipNote.MainPlanItemid,
               Wk_WipNote.WorkUid,
               Wk_WipNote.WorkRefNo,
               Wk_WipNote.WorkRowNum,
               Wk_WipNote.WorkItemid,
               Wk_WipNote.SubStWpid,
               Wk_WipNote.SubStWpCode,
               Wk_WipNote.SubStWpName,
               Wk_WipNote.SubEndWpid,
               Wk_WipNote.SubEndWpCode,
               Wk_WipNote.SubEndWpName,
               Wk_WipNote.SubUid,
               Wk_WipNote.WorkDate,
               Wk_WipNote.CompWpid,
               Wk_WipNote.CompWpCode,
               Wk_WipNote.CompWpName,
               Wk_WipNote.CompPcsQty,
               Wk_WipNote.WipGroupid,
               Wk_WipNote.Summary,
               Wk_WipNote.MatCode,
               Wk_WipNote.MatUsed,
               Wk_WipNote.AttributeJson,
               Wk_WipNote.AttributeStr,
               Wk_WipNote.WkSpecJson,
               Wk_WipNote.ItemCount,
               Wk_WipNote.FinishCount,
               Wk_WipNote.PrintCount,
               Wk_WipNote.ColorLevel,
               Wk_WipNote.SizeX,
               Wk_WipNote.SizeY,
               Wk_WipNote.SizeZ,
               Wk_WipNote.Closed,
               Wk_WipNote.DisannulListerid,
               Wk_WipNote.DisannulLister,
               Wk_WipNote.DisannulDate,
               Wk_WipNote.DisannulMark,
               Wk_WipNote.MergeMark,
               Wk_WipNote.SourceType,
               Wk_WipNote.Exponent,
               Wk_WipNote.JobPcsQty,
               Wk_WipNote.JobSecQty,
               Wk_WipNote.Custom1,
               Wk_WipNote.Custom2,
               Wk_WipNote.Custom3,
               Wk_WipNote.Custom4,
               Wk_WipNote.Custom5,
               Wk_WipNote.Custom6,
               Wk_WipNote.Custom7,
               Wk_WipNote.Custom8,
               Wk_WipNote.Custom9,
               Wk_WipNote.Custom10,
               Wk_WipNote.Tenantid,
               Wk_WipNote.Revision,
               <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.GroupLevel,
               Wk_GroupLossRate.BetterRate,
               Wk_GroupLossRate.TargetRate,
               Wk_GroupLossRate.AlertsRate,
               Wk_GroupLossRate.MaxRate
        FROM Mat_Goods
                 RIGHT JOIN Wk_WipNote ON Wk_WipNote.Goodsid = Mat_Goods.id
                 LEFT JOIN App_Workgroup ON Wk_WipNote.MachGroupid = App_Workgroup.id
                 LEFT JOIN Wk_GroupLossRate ON Wk_GroupLossRate.Groupid = Wk_WipNote.Workshopid
    </sql>
    <sql id="selectdetailVo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        App_Workgroup.GroupLevel,
        Wk_WipNoteItem.id,
        Wk_WipNoteItem.Pid,
        Wk_WipNoteItem.Wpid,
        Wk_WipNoteItem.WpCode,
        Wk_WipNoteItem.WpName,
        Wk_WipNoteItem.RowNum,
        Wk_WipNoteItem.PlanDate,
        Wk_WipNoteItem.Remark,
        Wk_WipNoteItem.InPcsQty,
        Wk_WipNoteItem.InSecQty,
        Wk_WipNoteItem.OutPcsQty,
        Wk_WipNoteItem.OutSecQty,
        Wk_WipNoteItem.MrbPcsQty,
        Wk_WipNoteItem.MrbSecQty,
        Wk_WipNoteItem.InPcsQty - Wk_WipNoteItem.OutPcsQty - Wk_WipNoteItem.MrbPcsQty as RemPcsQty,
        Wk_WipNoteItem.InSecQty - Wk_WipNoteItem.OutSecQty - Wk_WipNoteItem.MrbSecQty as RemSecQty,
        Wk_WipNoteItem.CompPcsQty,
        Wk_WipNoteItem.CompSecQty,
        Wk_WipNoteItem.SubQty,
        Wk_WipNoteItem.SubUnit,
        Wk_WipNoteItem.StartDate,
        Wk_WipNoteItem.EndDate,
        Wk_WipNoteItem.ItemWorker,
        Wk_WipNoteItem.EpibolePcsQty,
        Wk_WipNoteItem.EpiboleSecQty,
        Wk_WipNoteItem.LastWp,
        Wk_WipNoteItem.SpecJson,
        Wk_WipNoteItem.SpecPackJson,
        Wk_WipNoteItem.Lister,
        Wk_WipNoteItem.CreateDate,
        Wk_WipNoteItem.ModifyDate,
        Wk_WipNoteItem.StartPlan,
        Wk_WipNoteItem.Inspid,
        Wk_WipNoteItem.InspUid,
        Wk_WipNoteItem.InspResult,
        Wk_WipNoteItem.DisableIn,
        Wk_WipNoteItem.DisableOut,
        Wk_WipNoteItem.InWorker,
        Wk_WipNoteItem.OutWorker,
        Wk_WipNoteItem.TasksQty,
        Wk_WipNoteItem.Custom1,
        Wk_WipNoteItem.Custom2,
        Wk_WipNoteItem.Custom3,
        Wk_WipNoteItem.Custom4,
        Wk_WipNoteItem.Custom5,
        Wk_WipNoteItem.Custom6,
        Wk_WipNoteItem.Custom7,
        Wk_WipNoteItem.Custom8,
        Wk_WipNoteItem.Custom9,
        Wk_WipNoteItem.Custom10,
        Wk_WipNoteItem.Tenantid,
        Wk_WipNoteItem.Revision,
        Wk_WipNote.Goodsid,
        Wk_WipNote.RefNo,
        Wk_WipNote.WorkType,
        Wk_WipNote.BillDate,
        Wk_WipNote.Workshopid,
        Wk_WipNote.Workshop,
        Wk_WipNote.Quantity,
        Wk_WipNote.WkPcsQty,
        Wk_WipNote.WkSecQty,
        Wk_WipNote.WkWpCode,
        Wk_WipNote.WkWpName,
        Wk_WipNote.WkRowNum,
        Wk_WipNote.WkWpid,
        Wk_WipNote.CustPO,
        Wk_WipNote.MachUid,
        Wk_WipNote.MachItemid,
        Wk_WipNote.MainPlanUid,
        Wk_WipNote.WorkUid,
        Wk_WipNote.WorkItemid,
        Wk_WipNote.WorkRefNo,
        Wk_WipNote.WorkRowNum,
        Wk_WipNote.AttributeJson,
        Wk_WipNote.AttributeStr,
        Wk_WipNote.MatCode,
        Wk_WipNote.MatUsed,
        Wk_WipNote.ItemCount,
        Wk_WipNote.FinishCount,
        Wk_WipNote.PrintCount,
        Wk_WipNote.SizeX,
        Wk_WipNote.SizeY,
        Wk_WipNote.SizeZ,
        Wk_WipNote.Closed,
        Wk_WipNote.DisannulListerid,
        Wk_WipNote.DisannulLister,
        Wk_WipNote.DisannulDate,
        Wk_WipNote.DisannulMark,
        Wk_WipNote.MergeMark,
        Wk_WipNote.SourceType,
        Wk_WipNote.Exponent,
        Wk_WipNote.JobPcsQty,
        Wk_WipNote.JobSecQty,
        Wk_WipNote.Summary
        FROM Mat_Goods
                 RIGHT JOIN Wk_WipNote ON Wk_WipNote.Goodsid = Mat_Goods.id
                 RIGHT JOIN Wk_WipNoteItem ON Wk_WipNoteItem.Pid = Wk_WipNote.id
                 LEFT JOIN App_Workgroup ON Wk_WipNote.MachGroupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_WipNote.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipNote.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
        , Wk_WipNote.WorkRowNum , Wk_WipNoteItem.RowNum
    </select>


<!--    在getPageList基础上加入查询：销售订单/加工单的摘要列-->
    <select id="getOnlinePageListBySummary" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemdetailPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        App_Workgroup.GroupLevel,
        Wk_WipNoteItem.id,
        Wk_WipNoteItem.Pid,
        Wk_WipNoteItem.Wpid,
        Wk_WipNoteItem.WpCode,
        Wk_WipNoteItem.WpName,
        Wk_WipNoteItem.RowNum,
        Wk_WipNoteItem.PlanDate,
        Wk_WipNoteItem.Remark,
        Wk_WipNoteItem.InPcsQty,
        Wk_WipNoteItem.InSecQty,
        Wk_WipNoteItem.OutPcsQty,
        Wk_WipNoteItem.OutSecQty,
        Wk_WipNoteItem.MrbPcsQty,
        Wk_WipNoteItem.MrbSecQty,
        Wk_WipNoteItem.InPcsQty - Wk_WipNoteItem.OutPcsQty - Wk_WipNoteItem.MrbPcsQty as RemPcsQty,
        Wk_WipNoteItem.InSecQty - Wk_WipNoteItem.OutSecQty - Wk_WipNoteItem.MrbSecQty as RemSecQty,
        Wk_WipNoteItem.CompPcsQty,
        Wk_WipNoteItem.CompSecQty,
        Wk_WipNoteItem.SubQty,
        Wk_WipNoteItem.SubUnit,
        Wk_WipNoteItem.StartDate,
        Wk_WipNoteItem.EndDate,
        Wk_WipNoteItem.ItemWorker,
        Wk_WipNoteItem.EpibolePcsQty,
        Wk_WipNoteItem.EpiboleSecQty,
        Wk_WipNoteItem.LastWp,
        Wk_WipNoteItem.SpecJson,
        Wk_WipNoteItem.SpecPackJson,
        Wk_WipNoteItem.Lister,
        Wk_WipNoteItem.CreateDate,
        Wk_WipNoteItem.ModifyDate,
        Wk_WipNoteItem.StartPlan,
        Wk_WipNoteItem.Inspid,
        Wk_WipNoteItem.InspUid,
        Wk_WipNoteItem.InspResult,
        Wk_WipNoteItem.DisableIn,
        Wk_WipNoteItem.DisableOut,
        Wk_WipNoteItem.InWorker,
        Wk_WipNoteItem.OutWorker,
        Wk_WipNoteItem.TasksQty,
        Wk_WipNoteItem.Custom1,
        Wk_WipNoteItem.Custom2,
        Wk_WipNoteItem.Custom3,
        Wk_WipNoteItem.Custom4,
        Wk_WipNoteItem.Custom5,
        Wk_WipNoteItem.Custom6,
        Wk_WipNoteItem.Custom7,
        Wk_WipNoteItem.Custom8,
        Wk_WipNoteItem.Custom9,
        Wk_WipNoteItem.Custom10,
        Wk_WipNoteItem.Tenantid,
        Wk_WipNoteItem.Revision,
        Wk_WipNote.Goodsid,
        Wk_WipNote.RefNo,
        Wk_WipNote.WorkType,
        Wk_WipNote.BillDate,
        Wk_WipNote.Workshopid,
        Wk_WipNote.Workshop,
        Wk_WipNote.Quantity,
        Wk_WipNote.WkPcsQty,
        Wk_WipNote.WkSecQty,
        Wk_WipNote.WkWpCode,
        Wk_WipNote.WkWpName,
        Wk_WipNote.WkRowNum,
        Wk_WipNote.WkWpid,
        Wk_WipNote.CustPO,
        Wk_WipNote.MachUid,
        Wk_WipNote.MachItemid,
        Wk_WipNote.MainPlanUid,
        Wk_WipNote.WorkUid,
        Wk_WipNote.WorkItemid,
        Wk_WipNote.WorkRefNo,
        Wk_WipNote.WorkRowNum,
        Wk_WipNote.AttributeJson,
        Wk_WipNote.AttributeStr,
        Wk_WipNote.MatCode,
        Wk_WipNote.MatUsed,
        Wk_WipNote.ItemCount,
        Wk_WipNote.FinishCount,
        Wk_WipNote.PrintCount,
        Wk_WipNote.SizeX,
        Wk_WipNote.SizeY,
        Wk_WipNote.SizeZ,
        Wk_WipNote.Closed,
        Wk_WipNote.DisannulListerid,
        Wk_WipNote.DisannulLister,
        Wk_WipNote.DisannulDate,
        Wk_WipNote.DisannulMark,
        Wk_WipNote.MergeMark,
        Wk_WipNote.SourceType,
        Wk_WipNote.Exponent,
        Wk_WipNote.JobPcsQty,
        Wk_WipNote.JobSecQty,
        Wk_WipNote.Summary,
        Bus_Machining.Summary AS machsummary,
        Wk_Worksheet.Summary AS worksummary
        FROM Mat_Goods
        RIGHT JOIN Wk_WipNote ON Wk_WipNote.Goodsid = Mat_Goods.id
        RIGHT JOIN Wk_WipNoteItem ON Wk_WipNoteItem.Pid = Wk_WipNote.id
        LEFT JOIN App_Workgroup ON Wk_WipNote.MachGroupid = App_Workgroup.id
        LEFT JOIN Bus_MachiningItem ON Bus_MachiningItem.id = Wk_WipNote.MachItemid
        LEFT JOIN Bus_Machining ON Bus_Machining.id = Bus_MachiningItem.Pid
        LEFT JOIN Wk_WorksheetItem ON Wk_WorksheetItem.id = Wk_WipNote.WorkItemid
        LEFT JOIN Wk_Worksheet ON Wk_Worksheet.id = Wk_WorksheetItem.Pid
        WHERE 1 = 1
        AND Wk_WipNote.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                AND Wk_WipNote.CreateDate BETWEEN #{dateRange.startDate} AND #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                AND ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and"/>
            </if>
            <if test="SearchType == 1">
                <include refid="or"/>
            </if>
        </if>
        ORDER BY ${orderBy}
        <if test="OrderType == 0"> ASC </if>
        <if test="OrderType == 1"> DESC </if>
        , Wk_WipNote.WorkRowNum, Wk_WipNoteItem.RowNum
    </select>


    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Wk_WipNote.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.worktype != null and SearchPojo.worktype != ''">
            and Wk_WipNote.worktype like concat('%', #{SearchPojo.worktype}, '%')
        </if>
        <if test="SearchPojo.workshop != null and SearchPojo.workshop != ''">
            and Wk_WipNote.workshop like concat('%', #{SearchPojo.workshop}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_WipNote.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Wk_WipNote.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Wk_WipNote.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Wk_WipNote.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Wk_WipNote.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_WipNote.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.wkwpid != null and SearchPojo.wkwpid != ''">
            and Wk_WipNote.wkwpid like concat('%', #{SearchPojo.wkwpid}, '%')
        </if>
        <if test="SearchPojo.wkwpcode != null and SearchPojo.wkwpcode != ''">
            and Wk_WipNote.wkwpcode like concat('%', #{SearchPojo.wkwpcode}, '%')
        </if>
        <if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
            and Wk_WipNote.wkwpname like concat('%', #{SearchPojo.wkwpname}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_WipNote.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_WipNote.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_WipNote.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_WipNote.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_WipNote.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_WipNote.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_WipNote.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Wk_WipNote.workuid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            and Wk_WipNote.workitemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.substwpid != null and SearchPojo.substwpid != ''">
            and Wk_WipNote.substwpid like concat('%', #{SearchPojo.substwpid}, '%')
        </if>
        <if test="SearchPojo.substwpcode != null and SearchPojo.substwpcode != ''">
            and Wk_WipNote.substwpcode like concat('%', #{SearchPojo.substwpcode}, '%')
        </if>
        <if test="SearchPojo.substwpname != null and SearchPojo.substwpname != ''">
            and Wk_WipNote.substwpname like concat('%', #{SearchPojo.substwpname}, '%')
        </if>
        <if test="SearchPojo.subendwpid != null and SearchPojo.subendwpid != ''">
            and Wk_WipNote.subendwpid like concat('%', #{SearchPojo.subendwpid}, '%')
        </if>
        <if test="SearchPojo.subendwpcode != null and SearchPojo.subendwpcode != ''">
            and Wk_WipNote.subendwpcode like concat('%', #{SearchPojo.subendwpcode}, '%')
        </if>
        <if test="SearchPojo.subendwpname != null and SearchPojo.subendwpname != ''">
            and Wk_WipNote.subendwpname like concat('%', #{SearchPojo.subendwpname}, '%')
        </if>
        <if test="SearchPojo.subuid != null and SearchPojo.subuid != ''">
            and Wk_WipNote.subuid like concat('%', #{SearchPojo.subuid}, '%')
        </if>
        <if test="SearchPojo.compwpid != null and SearchPojo.compwpid != ''">
            and Wk_WipNote.compwpid like concat('%', #{SearchPojo.compwpid}, '%')
        </if>
        <if test="SearchPojo.compwpcode != null and SearchPojo.compwpcode != ''">
            and Wk_WipNote.compwpcode like concat('%', #{SearchPojo.compwpcode}, '%')
        </if>
        <if test="SearchPojo.compwpname != null and SearchPojo.compwpname != ''">
            and Wk_WipNote.compwpname like concat('%', #{SearchPojo.compwpname}, '%')
        </if>
        <if test="SearchPojo.wipgroupid != null and SearchPojo.wipgroupid != ''">
            and Wk_WipNote.wipgroupid like concat('%', #{SearchPojo.wipgroupid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_WipNote.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
            and Wk_WipNote.attributestr like concat('%', #{SearchPojo.attributestr}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Wk_WipNote.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.matcode != null and SearchPojo.matcode != ''">
            and Wk_WipNote.matcode like concat('%', #{SearchPojo.matcode}, '%')
        </if>
        <if test="SearchPojo.orgcustom1 != null and SearchPojo.orgcustom1 != ''">
            and Wk_WipNote.orgcustom1 like concat('%', #{SearchPojo.orgcustom1}, '%')
        </if>
        <if test="SearchPojo.orgcustom2 != null and SearchPojo.orgcustom2 != ''">
            and Wk_WipNote.orgcustom2 like concat('%', #{SearchPojo.orgcustom2}, '%')
        </if>
        <if test="SearchPojo.orgcustom3 != null and SearchPojo.orgcustom3 != ''">
            and Wk_WipNote.orgcustom3 like concat('%', #{SearchPojo.orgcustom3}, '%')
        </if>
        <if test="SearchPojo.orgcustom4 != null and SearchPojo.orgcustom4 != ''">
            and Wk_WipNote.orgcustom4 like concat('%', #{SearchPojo.orgcustom4}, '%')
        </if>
        <if test="SearchPojo.orgcustom5 != null and SearchPojo.orgcustom5 != ''">
            and Wk_WipNote.orgcustom5 like concat('%', #{SearchPojo.orgcustom5}, '%')
        </if>
        <if test="SearchPojo.orgcustom6 != null and SearchPojo.orgcustom6 != ''">
            and Wk_WipNote.orgcustom6 like concat('%', #{SearchPojo.orgcustom6}, '%')
        </if>
        <if test="SearchPojo.orgcustom7 != null and SearchPojo.orgcustom7 != ''">
            and Wk_WipNote.orgcustom7 like concat('%', #{SearchPojo.orgcustom7}, '%')
        </if>
        <if test="SearchPojo.orgcustom8 != null and SearchPojo.orgcustom8 != ''">
            and Wk_WipNote.orgcustom8 like concat('%', #{SearchPojo.orgcustom8}, '%')
        </if>
        <if test="SearchPojo.orgcustom9 != null and SearchPojo.orgcustom9 != ''">
            and Wk_WipNote.orgcustom9 like concat('%', #{SearchPojo.orgcustom9}, '%')
        </if>
        <if test="SearchPojo.orgcustom10 != null and SearchPojo.orgcustom10 != ''">
            and Wk_WipNote.orgcustom10 like concat('%', #{SearchPojo.orgcustom10}, '%')
        </if>
        <if test="SearchPojo.orgcustom11 != null and SearchPojo.orgcustom11 != ''">
            and Wk_WipNote.orgcustom11 like concat('%', #{SearchPojo.orgcustom11}, '%')
        </if>
        <if test="SearchPojo.orgcustom12 != null and SearchPojo.orgcustom12 != ''">
            and Wk_WipNote.orgcustom12 like concat('%', #{SearchPojo.orgcustom12}, '%')
        </if>
        <if test="SearchPojo.orgcustom13 != null and SearchPojo.orgcustom13 != ''">
            and Wk_WipNote.orgcustom13 like concat('%', #{SearchPojo.orgcustom13}, '%')
        </if>
        <if test="SearchPojo.orgcustom14 != null and SearchPojo.orgcustom14 != ''">
            and Wk_WipNote.orgcustom14 like concat('%', #{SearchPojo.orgcustom14}, '%')
        </if>
        <if test="SearchPojo.orgcustom15 != null and SearchPojo.orgcustom15 != ''">
            and Wk_WipNote.orgcustom15 like concat('%', #{SearchPojo.orgcustom15}, '%')
        </if>
        <if test="SearchPojo.orgcustom16 != null and SearchPojo.orgcustom16 != ''">
            and Wk_WipNote.orgcustom16 like concat('%', #{SearchPojo.orgcustom16}, '%')
        </if>
        <if test="SearchPojo.colorlevel != null and SearchPojo.colorlevel != ''">
            and Wk_WipNote.colorlevel like concat('%', #{SearchPojo.colorlevel}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_WipNote.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_WipNote.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_WipNote.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_WipNote.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_WipNote.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_WipNote.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_WipNote.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_WipNote.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_WipNote.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_WipNote.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Wk_WipNote.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.worktype != null and SearchPojo.worktype != ''">
                or Wk_WipNote.WorkType like concat('%', #{SearchPojo.worktype}, '%')
            </if>
            <if test="SearchPojo.workshop != null and SearchPojo.workshop != ''">
                or Wk_WipNote.Workshop like concat('%', #{SearchPojo.workshop}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_WipNote.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Wk_WipNote.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Wk_WipNote.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Wk_WipNote.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Wk_WipNote.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Wk_WipNote.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.wkwpid != null and SearchPojo.wkwpid != ''">
                or Wk_WipNote.WkWpid like concat('%', #{SearchPojo.wkwpid}, '%')
            </if>
            <if test="SearchPojo.wkwpcode != null and SearchPojo.wkwpcode != ''">
                or Wk_WipNote.WkWpCode like concat('%', #{SearchPojo.wkwpcode}, '%')
            </if>
            <if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
                or Wk_WipNote.WkWpName like concat('%', #{SearchPojo.wkwpname}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_WipNote.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_WipNote.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_WipNote.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_WipNote.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_WipNote.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_WipNote.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_WipNote.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
                or Wk_WipNote.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
                or Wk_WipNote.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.substwpid != null and SearchPojo.substwpid != ''">
                or Wk_WipNote.SubStWpid like concat('%', #{SearchPojo.substwpid}, '%')
            </if>
            <if test="SearchPojo.substwpcode != null and SearchPojo.substwpcode != ''">
                or Wk_WipNote.SubStWpCode like concat('%', #{SearchPojo.substwpcode}, '%')
            </if>
            <if test="SearchPojo.substwpname != null and SearchPojo.substwpname != ''">
                or Wk_WipNote.SubStWpName like concat('%', #{SearchPojo.substwpname}, '%')
            </if>
            <if test="SearchPojo.subendwpid != null and SearchPojo.subendwpid != ''">
                or Wk_WipNote.SubEndWpid like concat('%', #{SearchPojo.subendwpid}, '%')
            </if>
            <if test="SearchPojo.subendwpcode != null and SearchPojo.subendwpcode != ''">
                or Wk_WipNote.SubEndWpCode like concat('%', #{SearchPojo.subendwpcode}, '%')
            </if>
            <if test="SearchPojo.subendwpname != null and SearchPojo.subendwpname != ''">
                or Wk_WipNote.SubEndWpName like concat('%', #{SearchPojo.subendwpname}, '%')
            </if>
            <if test="SearchPojo.subuid != null and SearchPojo.subuid != ''">
                or Wk_WipNote.SubUid like concat('%', #{SearchPojo.subuid}, '%')
            </if>
            <if test="SearchPojo.compwpid != null and SearchPojo.compwpid != ''">
                or Wk_WipNote.CompWpid like concat('%', #{SearchPojo.compwpid}, '%')
            </if>
            <if test="SearchPojo.compwpcode != null and SearchPojo.compwpcode != ''">
                or Wk_WipNote.CompWpCode like concat('%', #{SearchPojo.compwpcode}, '%')
            </if>
            <if test="SearchPojo.compwpname != null and SearchPojo.compwpname != ''">
                or Wk_WipNote.CompWpName like concat('%', #{SearchPojo.compwpname}, '%')
            </if>
            <if test="SearchPojo.wipgroupid != null and SearchPojo.wipgroupid != ''">
                or Wk_WipNote.WipGroupid like concat('%', #{SearchPojo.wipgroupid}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_WipNote.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
                or Wk_WipNote.AttributeStr like concat('%', #{SearchPojo.attributestr}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Wk_WipNote.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.matcode != null and SearchPojo.matcode != ''">
                or Wk_WipNote.matcode like concat('%', #{SearchPojo.matcode}, '%')
            </if>
            <if test="SearchPojo.orgcustom1 != null and SearchPojo.orgcustom1 != ''">
                or Wk_WipNote.OrgCustom1 like concat('%', #{SearchPojo.orgcustom1}, '%')
            </if>
            <if test="SearchPojo.orgcustom2 != null and SearchPojo.orgcustom2 != ''">
                or Wk_WipNote.OrgCustom2 like concat('%', #{SearchPojo.orgcustom2}, '%')
            </if>
            <if test="SearchPojo.orgcustom3 != null and SearchPojo.orgcustom3 != ''">
                or Wk_WipNote.OrgCustom3 like concat('%', #{SearchPojo.orgcustom3}, '%')
            </if>
            <if test="SearchPojo.orgcustom4 != null and SearchPojo.orgcustom4 != ''">
                or Wk_WipNote.OrgCustom4 like concat('%', #{SearchPojo.orgcustom4}, '%')
            </if>
            <if test="SearchPojo.orgcustom5 != null and SearchPojo.orgcustom5 != ''">
                or Wk_WipNote.OrgCustom5 like concat('%', #{SearchPojo.orgcustom5}, '%')
            </if>
            <if test="SearchPojo.orgcustom6 != null and SearchPojo.orgcustom6 != ''">
                or Wk_WipNote.OrgCustom6 like concat('%', #{SearchPojo.orgcustom6}, '%')
            </if>
            <if test="SearchPojo.orgcustom7 != null and SearchPojo.orgcustom7 != ''">
                or Wk_WipNote.OrgCustom7 like concat('%', #{SearchPojo.orgcustom7}, '%')
            </if>
            <if test="SearchPojo.orgcustom8 != null and SearchPojo.orgcustom8 != ''">
                or Wk_WipNote.OrgCustom8 like concat('%', #{SearchPojo.orgcustom8}, '%')
            </if>
            <if test="SearchPojo.orgcustom9 != null and SearchPojo.orgcustom9 != ''">
                or Wk_WipNote.OrgCustom9 like concat('%', #{SearchPojo.orgcustom9}, '%')
            </if>
            <if test="SearchPojo.orgcustom10 != null and SearchPojo.orgcustom10 != ''">
                or Wk_WipNote.OrgCustom10 like concat('%', #{SearchPojo.orgcustom10}, '%')
            </if>
            <if test="SearchPojo.orgcustom11 != null and SearchPojo.orgcustom11 != ''">
                or Wk_WipNote.OrgCustom11 like concat('%', #{SearchPojo.orgcustom11}, '%')
            </if>
            <if test="SearchPojo.orgcustom12 != null and SearchPojo.orgcustom12 != ''">
                or Wk_WipNote.OrgCustom12 like concat('%', #{SearchPojo.orgcustom12}, '%')
            </if>
            <if test="SearchPojo.orgcustom13 != null and SearchPojo.orgcustom13 != ''">
                or Wk_WipNote.OrgCustom13 like concat('%', #{SearchPojo.orgcustom13}, '%')
            </if>
            <if test="SearchPojo.orgcustom14 != null and SearchPojo.orgcustom14 != ''">
                or Wk_WipNote.OrgCustom14 like concat('%', #{SearchPojo.orgcustom14}, '%')
            </if>
            <if test="SearchPojo.orgcustom15 != null and SearchPojo.orgcustom15 != ''">
                or Wk_WipNote.OrgCustom15 like concat('%', #{SearchPojo.orgcustom15}, '%')
            </if>
            <if test="SearchPojo.orgcustom16 != null and SearchPojo.orgcustom16 != ''">
                or Wk_WipNote.OrgCustom16 like concat('%', #{SearchPojo.orgcustom16}, '%')
            </if>
            <if test="SearchPojo.colorlevel != null and SearchPojo.colorlevel != ''">
                or Wk_WipNote.ColorLevel like concat('%', #{SearchPojo.colorlevel}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_WipNote.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_WipNote.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_WipNote.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_WipNote.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_WipNote.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_WipNote.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_WipNote.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_WipNote.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_WipNote.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_WipNote.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_WipNote.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipNote.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"/>
            </if>
            <if test="SearchType==1">
                <include refid="thor"/>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
        ,Wk_WipNote.WorkRowNum
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Wk_WipNote.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.worktype != null and SearchPojo.worktype != ''">
            and Wk_WipNote.WorkType like concat('%', #{SearchPojo.worktype}, '%')
        </if>
        <if test="SearchPojo.workshop != null and SearchPojo.workshop != ''">
            and Wk_WipNote.Workshop like concat('%', #{SearchPojo.workshop}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_WipNote.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Wk_WipNote.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Wk_WipNote.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Wk_WipNote.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Wk_WipNote.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_WipNote.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.wkwpid != null and SearchPojo.wkwpid != ''">
            and Wk_WipNote.WkWpid like concat('%', #{SearchPojo.wkwpid}, '%')
        </if>
        <if test="SearchPojo.wkwpcode != null and SearchPojo.wkwpcode != ''">
            and Wk_WipNote.WkWpCode like concat('%', #{SearchPojo.wkwpcode}, '%')
        </if>
        <if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
            and Wk_WipNote.WkWpName like concat('%', #{SearchPojo.wkwpname}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_WipNote.Customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_WipNote.CustPO like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_WipNote.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_WipNote.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_WipNote.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_WipNote.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_WipNote.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Wk_WipNote.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            and Wk_WipNote.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.substwpid != null and SearchPojo.substwpid != ''">
            and Wk_WipNote.SubStWpid like concat('%', #{SearchPojo.substwpid}, '%')
        </if>
        <if test="SearchPojo.substwpcode != null and SearchPojo.substwpcode != ''">
            and Wk_WipNote.SubStWpCode like concat('%', #{SearchPojo.substwpcode}, '%')
        </if>
        <if test="SearchPojo.substwpname != null and SearchPojo.substwpname != ''">
            and Wk_WipNote.SubStWpName like concat('%', #{SearchPojo.substwpname}, '%')
        </if>
        <if test="SearchPojo.subendwpid != null and SearchPojo.subendwpid != ''">
            and Wk_WipNote.SubEndWpid like concat('%', #{SearchPojo.subendwpid}, '%')
        </if>
        <if test="SearchPojo.subendwpcode != null and SearchPojo.subendwpcode != ''">
            and Wk_WipNote.SubEndWpCode like concat('%', #{SearchPojo.subendwpcode}, '%')
        </if>
        <if test="SearchPojo.subendwpname != null and SearchPojo.subendwpname != ''">
            and Wk_WipNote.SubEndWpName like concat('%', #{SearchPojo.subendwpname}, '%')
        </if>
        <if test="SearchPojo.subuid != null and SearchPojo.subuid != ''">
            and Wk_WipNote.SubUid like concat('%', #{SearchPojo.subuid}, '%')
        </if>
        <if test="SearchPojo.compwpid != null and SearchPojo.compwpid != ''">
            and Wk_WipNote.CompWpid like concat('%', #{SearchPojo.compwpid}, '%')
        </if>
        <if test="SearchPojo.compwpcode != null and SearchPojo.compwpcode != ''">
            and Wk_WipNote.CompWpCode like concat('%', #{SearchPojo.compwpcode}, '%')
        </if>
        <if test="SearchPojo.compwpname != null and SearchPojo.compwpname != ''">
            and Wk_WipNote.CompWpName like concat('%', #{SearchPojo.compwpname}, '%')
        </if>
        <if test="SearchPojo.wipgroupid != null and SearchPojo.wipgroupid != ''">
            and Wk_WipNote.WipGroupid like concat('%', #{SearchPojo.wipgroupid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_WipNote.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
            and Wk_WipNote.AttributeStr like concat('%', #{SearchPojo.attributestr}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Wk_WipNote.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.matcode != null and SearchPojo.matcode != ''">
            and Wk_WipNote.matcode like concat('%', #{SearchPojo.matcode}, '%')
        </if>
        <if test="SearchPojo.orgcustom1 != null and SearchPojo.orgcustom1 != ''">
            and Wk_WipNote.OrgCustom1 like concat('%', #{SearchPojo.orgcustom1}, '%')
        </if>
        <if test="SearchPojo.orgcustom2 != null and SearchPojo.orgcustom2 != ''">
            and Wk_WipNote.OrgCustom2 like concat('%', #{SearchPojo.orgcustom2}, '%')
        </if>
        <if test="SearchPojo.orgcustom3 != null and SearchPojo.orgcustom3 != ''">
            and Wk_WipNote.OrgCustom3 like concat('%', #{SearchPojo.orgcustom3}, '%')
        </if>
        <if test="SearchPojo.orgcustom4 != null and SearchPojo.orgcustom4 != ''">
            and Wk_WipNote.OrgCustom4 like concat('%', #{SearchPojo.orgcustom4}, '%')
        </if>
        <if test="SearchPojo.orgcustom5 != null and SearchPojo.orgcustom5 != ''">
            and Wk_WipNote.OrgCustom5 like concat('%', #{SearchPojo.orgcustom5}, '%')
        </if>
        <if test="SearchPojo.orgcustom6 != null and SearchPojo.orgcustom6 != ''">
            and Wk_WipNote.OrgCustom6 like concat('%', #{SearchPojo.orgcustom6}, '%')
        </if>
        <if test="SearchPojo.orgcustom7 != null and SearchPojo.orgcustom7 != ''">
            and Wk_WipNote.OrgCustom7 like concat('%', #{SearchPojo.orgcustom7}, '%')
        </if>
        <if test="SearchPojo.orgcustom8 != null and SearchPojo.orgcustom8 != ''">
            and Wk_WipNote.OrgCustom8 like concat('%', #{SearchPojo.orgcustom8}, '%')
        </if>
        <if test="SearchPojo.orgcustom9 != null and SearchPojo.orgcustom9 != ''">
            and Wk_WipNote.OrgCustom9 like concat('%', #{SearchPojo.orgcustom9}, '%')
        </if>
        <if test="SearchPojo.orgcustom10 != null and SearchPojo.orgcustom10 != ''">
            and Wk_WipNote.OrgCustom10 like concat('%', #{SearchPojo.orgcustom10}, '%')
        </if>
        <if test="SearchPojo.orgcustom11 != null and SearchPojo.orgcustom11 != ''">
            and Wk_WipNote.OrgCustom11 like concat('%', #{SearchPojo.orgcustom11}, '%')
        </if>
        <if test="SearchPojo.orgcustom12 != null and SearchPojo.orgcustom12 != ''">
            and Wk_WipNote.OrgCustom12 like concat('%', #{SearchPojo.orgcustom12}, '%')
        </if>
        <if test="SearchPojo.orgcustom13 != null and SearchPojo.orgcustom13 != ''">
            and Wk_WipNote.OrgCustom13 like concat('%', #{SearchPojo.orgcustom13}, '%')
        </if>
        <if test="SearchPojo.orgcustom14 != null and SearchPojo.orgcustom14 != ''">
            and Wk_WipNote.OrgCustom14 like concat('%', #{SearchPojo.orgcustom14}, '%')
        </if>
        <if test="SearchPojo.orgcustom15 != null and SearchPojo.orgcustom15 != ''">
            and Wk_WipNote.OrgCustom15 like concat('%', #{SearchPojo.orgcustom15}, '%')
        </if>
        <if test="SearchPojo.orgcustom16 != null and SearchPojo.orgcustom16 != ''">
            and Wk_WipNote.OrgCustom16 like concat('%', #{SearchPojo.orgcustom16}, '%')
        </if>
        <if test="SearchPojo.colorlevel != null and SearchPojo.colorlevel != ''">
            and Wk_WipNote.ColorLevel like concat('%', #{SearchPojo.colorlevel}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_WipNote.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_WipNote.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_WipNote.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_WipNote.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_WipNote.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_WipNote.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_WipNote.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_WipNote.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_WipNote.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_WipNote.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or Wk_WipNote.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.worktype != null and SearchPojo.worktype != ''">
            or Wk_WipNote.WorkType like concat('%', #{SearchPojo.worktype}, '%')
        </if>
        <if test="SearchPojo.workshop != null and SearchPojo.workshop != ''">
            or Wk_WipNote.Workshop like concat('%', #{SearchPojo.workshop}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            or Wk_WipNote.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Wk_WipNote.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Wk_WipNote.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Wk_WipNote.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Wk_WipNote.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            or Wk_WipNote.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.wkwpid != null and SearchPojo.wkwpid != ''">
            or Wk_WipNote.WkWpid like concat('%', #{SearchPojo.wkwpid}, '%')
        </if>
        <if test="SearchPojo.wkwpcode != null and SearchPojo.wkwpcode != ''">
            or Wk_WipNote.WkWpCode like concat('%', #{SearchPojo.wkwpcode}, '%')
        </if>
        <if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
            or Wk_WipNote.WkWpName like concat('%', #{SearchPojo.wkwpname}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            or Wk_WipNote.Customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            or Wk_WipNote.CustPO like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            or Wk_WipNote.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            or Wk_WipNote.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            or Wk_WipNote.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            or Wk_WipNote.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            or Wk_WipNote.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            or Wk_WipNote.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            or Wk_WipNote.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.substwpid != null and SearchPojo.substwpid != ''">
            or Wk_WipNote.SubStWpid like concat('%', #{SearchPojo.substwpid}, '%')
        </if>
        <if test="SearchPojo.substwpcode != null and SearchPojo.substwpcode != ''">
            or Wk_WipNote.SubStWpCode like concat('%', #{SearchPojo.substwpcode}, '%')
        </if>
        <if test="SearchPojo.substwpname != null and SearchPojo.substwpname != ''">
            or Wk_WipNote.SubStWpName like concat('%', #{SearchPojo.substwpname}, '%')
        </if>
        <if test="SearchPojo.subendwpid != null and SearchPojo.subendwpid != ''">
            or Wk_WipNote.SubEndWpid like concat('%', #{SearchPojo.subendwpid}, '%')
        </if>
        <if test="SearchPojo.subendwpcode != null and SearchPojo.subendwpcode != ''">
            or Wk_WipNote.SubEndWpCode like concat('%', #{SearchPojo.subendwpcode}, '%')
        </if>
        <if test="SearchPojo.subendwpname != null and SearchPojo.subendwpname != ''">
            or Wk_WipNote.SubEndWpName like concat('%', #{SearchPojo.subendwpname}, '%')
        </if>
        <if test="SearchPojo.subuid != null and SearchPojo.subuid != ''">
            or Wk_WipNote.SubUid like concat('%', #{SearchPojo.subuid}, '%')
        </if>
        <if test="SearchPojo.compwpid != null and SearchPojo.compwpid != ''">
            or Wk_WipNote.CompWpid like concat('%', #{SearchPojo.compwpid}, '%')
        </if>
        <if test="SearchPojo.compwpcode != null and SearchPojo.compwpcode != ''">
            or Wk_WipNote.CompWpCode like concat('%', #{SearchPojo.compwpcode}, '%')
        </if>
        <if test="SearchPojo.compwpname != null and SearchPojo.compwpname != ''">
            or Wk_WipNote.CompWpName like concat('%', #{SearchPojo.compwpname}, '%')
        </if>
        <if test="SearchPojo.wipgroupid != null and SearchPojo.wipgroupid != ''">
            or Wk_WipNote.WipGroupid like concat('%', #{SearchPojo.wipgroupid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            or Wk_WipNote.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
            or Wk_WipNote.AttributeStr like concat('%', #{SearchPojo.attributestr}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or Wk_WipNote.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.matcode != null and SearchPojo.matcode != ''">
            or Wk_WipNote.matcode like concat('%', #{SearchPojo.matcode}, '%')
        </if>
        <if test="SearchPojo.orgcustom1 != null and SearchPojo.orgcustom1 != ''">
            or Wk_WipNote.OrgCustom1 like concat('%', #{SearchPojo.orgcustom1}, '%')
        </if>
        <if test="SearchPojo.orgcustom2 != null and SearchPojo.orgcustom2 != ''">
            or Wk_WipNote.OrgCustom2 like concat('%', #{SearchPojo.orgcustom2}, '%')
        </if>
        <if test="SearchPojo.orgcustom3 != null and SearchPojo.orgcustom3 != ''">
            or Wk_WipNote.OrgCustom3 like concat('%', #{SearchPojo.orgcustom3}, '%')
        </if>
        <if test="SearchPojo.orgcustom4 != null and SearchPojo.orgcustom4 != ''">
            or Wk_WipNote.OrgCustom4 like concat('%', #{SearchPojo.orgcustom4}, '%')
        </if>
        <if test="SearchPojo.orgcustom5 != null and SearchPojo.orgcustom5 != ''">
            or Wk_WipNote.OrgCustom5 like concat('%', #{SearchPojo.orgcustom5}, '%')
        </if>
        <if test="SearchPojo.orgcustom6 != null and SearchPojo.orgcustom6 != ''">
            or Wk_WipNote.OrgCustom6 like concat('%', #{SearchPojo.orgcustom6}, '%')
        </if>
        <if test="SearchPojo.orgcustom7 != null and SearchPojo.orgcustom7 != ''">
            or Wk_WipNote.OrgCustom7 like concat('%', #{SearchPojo.orgcustom7}, '%')
        </if>
        <if test="SearchPojo.orgcustom8 != null and SearchPojo.orgcustom8 != ''">
            or Wk_WipNote.OrgCustom8 like concat('%', #{SearchPojo.orgcustom8}, '%')
        </if>
        <if test="SearchPojo.orgcustom9 != null and SearchPojo.orgcustom9 != ''">
            or Wk_WipNote.OrgCustom9 like concat('%', #{SearchPojo.orgcustom9}, '%')
        </if>
        <if test="SearchPojo.orgcustom10 != null and SearchPojo.orgcustom10 != ''">
            or Wk_WipNote.OrgCustom10 like concat('%', #{SearchPojo.orgcustom10}, '%')
        </if>
        <if test="SearchPojo.orgcustom11 != null and SearchPojo.orgcustom11 != ''">
            or Wk_WipNote.OrgCustom11 like concat('%', #{SearchPojo.orgcustom11}, '%')
        </if>
        <if test="SearchPojo.orgcustom12 != null and SearchPojo.orgcustom12 != ''">
            or Wk_WipNote.OrgCustom12 like concat('%', #{SearchPojo.orgcustom12}, '%')
        </if>
        <if test="SearchPojo.orgcustom13 != null and SearchPojo.orgcustom13 != ''">
            or Wk_WipNote.OrgCustom13 like concat('%', #{SearchPojo.orgcustom13}, '%')
        </if>
        <if test="SearchPojo.orgcustom14 != null and SearchPojo.orgcustom14 != ''">
            or Wk_WipNote.OrgCustom14 like concat('%', #{SearchPojo.orgcustom14}, '%')
        </if>
        <if test="SearchPojo.orgcustom15 != null and SearchPojo.orgcustom15 != ''">
            or Wk_WipNote.OrgCustom15 like concat('%', #{SearchPojo.orgcustom15}, '%')
        </if>
        <if test="SearchPojo.orgcustom16 != null and SearchPojo.orgcustom16 != ''">
            or Wk_WipNote.OrgCustom16 like concat('%', #{SearchPojo.orgcustom16}, '%')
        </if>
        <if test="SearchPojo.colorlevel != null and SearchPojo.colorlevel != ''">
            or Wk_WipNote.ColorLevel like concat('%', #{SearchPojo.colorlevel}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Wk_WipNote.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Wk_WipNote.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Wk_WipNote.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Wk_WipNote.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Wk_WipNote.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Wk_WipNote.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Wk_WipNote.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Wk_WipNote.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Wk_WipNote.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Wk_WipNote.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        )
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_WipNote(id, RefNo, BillDate, WorkType, Workshopid, Workshop, Goodsid, PlanDate, Quantity, WkPcsQty, WkSecQty, MrbPcsQty, MrbSecQty, Supplement, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, StateCode, StateDate, WkWpid, WkWpCode, WkWpName, WkRowNum, Customer, CustPO, MachUid, MachItemid, MachGroupid, MainPlanUid, MainPlanItemid, WorkUid, WorkRefNo, WorkRowNum, WorkItemid, SubStWpid, SubStWpCode, SubStWpName, SubEndWpid, SubEndWpCode, SubEndWpName, SubUid, WorkDate, CompWpid, CompWpCode, CompWpName, CompPcsQty, WipGroupid, Summary, AttributeJson, AttributeStr, MatCode, MatUsed, WkSpecJson, ItemCount, FinishCount, PrintCount, ColorLevel, SizeX, SizeY, SizeZ, Closed, DisannulListerid, DisannulLister, DisannulDate, DisannulMark, MergeMark, SourceType, ItemJson, Isolation, Exponent, JobPcsQty, JobSecQty, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billdate}, #{worktype}, #{workshopid}, #{workshop}, #{goodsid}, #{plandate}, #{quantity}, #{wkpcsqty}, #{wksecqty}, #{mrbpcsqty}, #{mrbsecqty}, #{supplement}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{statecode}, #{statedate}, #{wkwpid}, #{wkwpcode}, #{wkwpname}, #{wkrownum}, #{customer}, #{custpo}, #{machuid}, #{machitemid}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid}, #{workuid}, #{workrefno}, #{workrownum}, #{workitemid}, #{substwpid}, #{substwpcode}, #{substwpname}, #{subendwpid}, #{subendwpcode}, #{subendwpname}, #{subuid}, #{workdate}, #{compwpid}, #{compwpcode}, #{compwpname}, #{comppcsqty}, #{wipgroupid}, #{summary}, #{attributejson}, #{attributestr}, #{matcode}, #{matused}, #{wkspecjson}, #{itemcount}, #{finishcount}, #{printcount}, #{colorlevel}, #{sizex}, #{sizey}, #{sizez}, #{closed}, #{disannullisterid}, #{disannullister}, #{disannuldate}, #{disannulmark}, #{mergemark}, #{sourcetype}, #{itemjson}, #{isolation}, #{exponent}, #{jobpcsqty}, #{jobsecqty}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipNote
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="worktype != null ">
                WorkType =#{worktype},
            </if>
            <if test="workshopid != null ">
                Workshopid =#{workshopid},
            </if>
            <if test="workshop != null ">
                Workshop =#{workshop},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="plandate != null">
                PlanDate =#{plandate},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="wkpcsqty != null">
                WkPcsQty =#{wkpcsqty},
            </if>
            <if test="wksecqty != null">
                WkSecQty =#{wksecqty},
            </if>
            <if test="mrbpcsqty != null">
                MrbPcsQty =#{mrbpcsqty},
            </if>
            <if test="mrbsecqty != null">
                MrbSecQty =#{mrbsecqty},
            </if>
            <if test="supplement != null">
                Supplement =#{supplement},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="wkwpid != null ">
                WkWpid =#{wkwpid},
            </if>
            <if test="wkwpcode != null ">
                WkWpCode =#{wkwpcode},
            </if>
            <if test="wkwpname != null ">
                WkWpName =#{wkwpname},
            </if>
            <if test="wkrownum != null">
                WkRowNum =#{wkrownum},
            </if>
            <if test="customer != null ">
                Customer =#{customer},
            </if>
            <if test="custpo != null ">
                CustPO =#{custpo},
            </if>
            <if test="machuid != null ">
                MachUid =#{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid =#{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid =#{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid =#{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid =#{mainplanitemid},
            </if>
            <if test="workuid != null ">
                WorkUid =#{workuid},
            </if>
            <if test="workrefno != null ">
                WorkRefNo =#{workrefno},
            </if>
            <if test="workrownum != null">
                WorkRowNum =#{workrownum},
            </if>
            <if test="workitemid != null ">
                WorkItemid =#{workitemid},
            </if>
            <if test="substwpid != null ">
                SubStWpid =#{substwpid},
            </if>
            <if test="substwpcode != null ">
                SubStWpCode =#{substwpcode},
            </if>
            <if test="substwpname != null ">
                SubStWpName =#{substwpname},
            </if>
            <if test="subendwpid != null ">
                SubEndWpid =#{subendwpid},
            </if>
            <if test="subendwpcode != null ">
                SubEndWpCode =#{subendwpcode},
            </if>
            <if test="subendwpname != null ">
                SubEndWpName =#{subendwpname},
            </if>
            <if test="subuid != null ">
                SubUid =#{subuid},
            </if>
            <if test="workdate != null">
                WorkDate =#{workdate},
            </if>
            <if test="compwpid != null ">
                CompWpid =#{compwpid},
            </if>
            <if test="compwpcode != null ">
                CompWpCode =#{compwpcode},
            </if>
            <if test="compwpname != null ">
                CompWpName =#{compwpname},
            </if>
            <if test="comppcsqty != null">
                CompPcsQty =#{comppcsqty},
            </if>
            <if test="wipgroupid != null ">
                WipGroupid =#{wipgroupid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="attributejson != null ">
                AttributeJson =#{attributejson},
            </if>
            <if test="attributestr != null ">
                AttributeStr =#{attributestr},
            </if>
            <if test="matcode != null ">
                MatCode =#{matcode},
            </if>
            <if test="matused != null">
                MatUsed =#{matused},
            </if>
            <if test="wkspecjson != null ">
                WkSpecJson =#{wkspecjson},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="colorlevel != null ">
                ColorLevel =#{colorlevel},
            </if>
            <if test="sizex != null">
                SizeX =#{sizex},
            </if>
            <if test="sizey != null">
                SizeY =#{sizey},
            </if>
            <if test="sizez != null">
                SizeZ =#{sizez},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid =#{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister =#{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate =#{disannuldate},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="mergemark != null">
                MergeMark =#{mergemark},
            </if>
            <if test="sourcetype != null">
                SourceType =#{sourcetype},
            </if>
            <if test="itemjson != null ">
                ItemJson =#{itemjson},
            </if>
            <if test="isolation != null">
                Isolation =#{isolation},
            </if>
            <if test="exponent != null">
                Exponent =#{exponent},
            </if>
            <if test="jobpcsqty != null">
                JobPcsQty =#{jobpcsqty},
            </if>
            <if test="jobsecqty != null">
                JobSecQty =#{jobsecqty},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WipNote
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
        <!--刷新打印次数-->
        <update id="updatePrintcount">
            update Wk_WipNote
            SET PrintCount = #{printcount}
            where id = #{id}
              and Tenantid = #{tenantid}
        </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        select
        id
        from Wk_WipNoteItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
    <!--查询单个-->
    <select id="getEntityByWorkUid" resultType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_WipNote.id,
               Wk_WipNote.RefNo,
               Wk_WipNote.WorkType,
               Wk_WipNote.Workshopid,
               Wk_WipNote.Workshop,
               Wk_WipNote.Goodsid,
               Wk_WipNote.PlanDate,
               Wk_WipNote.Quantity,
               Wk_WipNote.WkPcsQty,
               Wk_WipNote.WkSecQty,
               Wk_WipNote.MrbPcsQty,
               Wk_WipNote.MrbSecQty,
               Wk_WipNote.Supplement,
               Wk_WipNote.CreateBy,
               Wk_WipNote.CreateByid,
               Wk_WipNote.CreateDate,
               Wk_WipNote.Lister,
               Wk_WipNote.Listerid,
               Wk_WipNote.ModifyDate,
               Wk_WipNote.StateCode,
               Wk_WipNote.StateDate,
               Wk_WipNote.WkWpid,
               Wk_WipNote.WkWpCode,
               Wk_WipNote.WkWpName,
               Wk_WipNote.WkRowNum,
               Wk_WipNote.Customer,
               Wk_WipNote.CustPO,
               Wk_WipNote.MachUid,
               Wk_WipNote.MachItemid,
               Wk_WipNote.MachGroupid,
               Wk_WipNote.MainPlanUid,
               Wk_WipNote.MainPlanItemid,
               Wk_WipNote.WorkUid,
               Wk_WipNote.WorkRefNo,
               Wk_WipNote.WorkRowNum,
               Wk_WipNote.WorkItemid,
               Wk_WipNote.SubStWpid,
               Wk_WipNote.SubStWpCode,
               Wk_WipNote.SubStWpName,
               Wk_WipNote.SubEndWpid,
               Wk_WipNote.SubEndWpCode,
               Wk_WipNote.SubEndWpName,
               Wk_WipNote.SubUid,
               Wk_WipNote.WorkDate,
               Wk_WipNote.CompWpid,
               Wk_WipNote.CompWpCode,
               Wk_WipNote.CompWpName,
               Wk_WipNote.CompPcsQty,
               Wk_WipNote.WipGroupid,
               Wk_WipNote.Summary,
               Wk_WipNote.AttributeJson,
               Wk_WipNote.AttributeStr,
               Wk_WipNote.MatCode,
               Wk_WipNote.MatUsed,
               Wk_WipNote.WkSpecJson,
               Wk_WipNote.ItemCount,
               Wk_WipNote.FinishCount,
               Wk_WipNote.PrintCount,
               Wk_WipNote.ColorLevel,
               Wk_WipNote.SizeX,
               Wk_WipNote.SizeY,
               Wk_WipNote.SizeZ,
               Wk_WipNote.MergeMark,
               Wk_WipNote.SourceType,
               Wk_WipNote.JobPcsQty,
               Wk_WipNote.JobSecQty,
               Wk_WipNote.Custom1,
               Wk_WipNote.Custom2,
               Wk_WipNote.Custom3,
               Wk_WipNote.Custom4,
               Wk_WipNote.Custom5,
               Wk_WipNote.Custom6,
               Wk_WipNote.Custom7,
               Wk_WipNote.Custom8,
               Wk_WipNote.Custom9,
               Wk_WipNote.Custom10,
               Wk_WipNote.Tenantid,
               Wk_WipNote.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_WipNote ON Wk_WipNote.Goodsid = Mat_Goods.id
        where Wk_WipNote.WorkUid = #{key}
          and Wk_WipNote.Tenantid = #{tid}
        Limit 1
    </select>


    <update id="updateMachWipUsed" parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        UPDATE Bus_MachiningItem
        SET WipUsed=1
        WHERE Bus_MachiningItem.id = #{machitemid}
          and Bus_MachiningItem.Tenantid = #{tenantid}
    </update>

    <update id="updateWorkWipUsed" parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        UPDATE Wk_WorksheetItem
        SET WipUsed=1
        WHERE Wk_WorksheetItem.id = #{workitemid}
          and Wk_WorksheetItem.Tenantid = #{tenantid}
    </update>

    <update id="updateUnMachWipUsed" parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        UPDATE Bus_MachiningItem
        SET WipUsed=0
        WHERE Bus_MachiningItem.id = #{machitemid}
          and Bus_MachiningItem.Tenantid = #{tenantid}
    </update>

    <update id="updateUnWorkWipUsed" parameterType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        UPDATE Wk_WorksheetItem
        SET WipUsed=0
        WHERE Wk_WorksheetItem.id = #{workitemid}
          and Wk_WorksheetItem.Tenantid = #{tenantid}
    </update>
    <select id="getByWorkuidLike" resultType="java.lang.String">
        SELECT Wk_WipNote.WorkUid
        FROM Wk_WipNote
        WHERE Wk_WipNote.WorkUid LIKE '${workuid}%'
          and Wk_WipNote.Tenantid = #{tid}
        ORDER BY Wk_WipNote.WorkUid DESC limit 1
    </select>


    <update id="closedids">
        update Wk_WipNote
        set Closed = #{type},
        Lister = #{realname},
        Listerid = #{userid},
        ModifyDate = #{date}
        where Tenantid = #{tenantid}
        <if test="ids != null and ids != ''">
            and id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="updateMachWipCount">
        update Bus_Machining
        SET WkWipCount =COALESCE((SELECT COUNT(0)
                                  FROM Bus_MachiningItem
                                  where Bus_MachiningItem.Pid = (SELECT Pid FROM Bus_MachiningItem where id = #{machitemid})
                                    and Bus_MachiningItem.Tenantid = #{tid}
                                     and Bus_MachiningItem.WipUsed=1), 0)
        where id = (SELECT Pid FROM Bus_MachiningItem where id = #{machitemid})
          and Tenantid = #{tid}
    </update>

    <select id="getAllByTid" resultType="java.util.Map">
        select id,AttributeJson from Wk_WipNote
        where Tenantid = #{tid}
    </select>
    <update id="upateAttrStr">
        update Wk_WipNote
        set AttributeStr = #{attrStr}
        where id = #{id} and Tenantid =#{tid}
    </update>

    <select id="getMaxWipItemRowNumAndId" resultType="java.util.Map">
        select RowNum,id
        from Wk_WipNoteItem
        where Pid = #{wipid}
          and Tenantid = #{tid}
        order by RowNum desc limit 1
    </select>

    <select id="getSpecPcbItemListByGoodsid" resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemPojo">
        select Wk_Process.id as wpid,
               Wk_Process.WpName,
               Wk_Process.WpCode
        from Mat_SpecPcbItem
                 Left Join Mat_SpecPcb on Mat_SpecPcbItem.Pid = Mat_SpecPcb.id
                 Left Join Wk_Process on Mat_SpecPcbItem.Wpid = Wk_Process.id
        where Mat_SpecPcb.Goodsid = #{goodsid}
          and Mat_SpecPcbItem.Tenantid = #{tid}
        order by Mat_SpecPcbItem.RowNum
    </select>

    <update id="wipSyncWkPcsQty">
        update Wk_WipNote
        set WkPcsQty = #{addqty} + (select OutPcsQty from Wk_WipNoteItem where id = #{wipitemid})
        where id = (select Pid from Wk_WipNoteItem where id = #{wipitemid})
          and Tenantid = #{tenantid}
    </update>

    <update id="updateWkpcsqty">
        update Wk_WipNote
        set WkPcsQty = #{wkpcsqtyAdd}
        where id = #{id}
          and Tenantid = #{tid}
    </update>

    <update id="syncSheetItemWkpsqty">
        update Wk_WorksheetItem
        set WkPcsQty = #{wkpcsqtyAdd}
        where id = #{workitemid}
          and Tenantid = #{tid}
    </update>

    <select id="getOnlineCountByWp" resultType="int">
        SELECT count(distinct Wk_WipNote.id)
        FROM Wk_WipNote join Wk_WipNoteItem on Wk_WipNote.id=Wk_WipNoteItem.Pid
        where Wk_WipNote.Tenantid = #{tid}
        and Wk_WipNote.MrbPcsQty+Wk_WipNote.CompPcsQty <![CDATA[<]]> Wk_WipNote.WkPcsQty
        and Wk_WipNote.DisannulMark=0 and Wk_WipNote.Closed=0
        and Wk_WipNote.WkWpid=#{wpid}
        and Wk_WipNoteItem.Wpid=#{wpid}
        and Wk_WipNoteItem.OutPcsQty = 0
    </select>

    <select id="getEntityByitemid" resultType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        <include refid="selectbillVo"/>
        where Wk_WipNote.id = (select Pid from Wk_WipNoteItem where id = #{wipitemid} and Tenantid = #{tid})
        and Wk_WipNote.Tenantid = #{tid}
    </select>

    <select id="getPreWipNoteByGoodsid" resultType="inks.service.std.manu.domain.pojo.WkWipnotePojo">
        select * from Wk_WipNote
        where Wk_WipNote.Goodsid = #{goodsid}
        and Wk_WipNote.WkRowNum <![CDATA[<]]> #{wkrownum}
        <if test="machitemid != null and machitemid != ''">
            and Wk_WipNote.MachItemid = #{machitemid}
        </if>
        Order By Wk_WipNote.WorkRefNo,Wk_WipNote.WorkRowNum limit 1
    </select>

<!--    1、加工单转WIP的接口，加入WorkRefno=加工单的Refno;  WorkRowNum=加工单item.RowNum;-->
    <update id="updateWorkRefNoWorkRowNum">
        with sheet as (select Wk_Worksheet.RefNo, Wk_WorksheetItem.RowNum
                      from Wk_WorksheetItem
                               join Wk_Worksheet on Wk_WorksheetItem.Pid = Wk_Worksheet.id
                      where Wk_WorksheetItem.id = #{worksheetitemid}
                        and Wk_WorksheetItem.Tenantid = #{tid})
        update Wk_WipNote
        set WorkRefNo  = (select RefNo from sheet),
            WorkRowNum = (select RowNum from sheet)
        where id = #{wipid}
          and Tenantid = #{tid}
    </update>

    <select id="getSummaryByMachitemid" resultType="java.lang.String">
        select m.Summary
        from Bus_Machining m
                 join Bus_MachiningItem mi on m.id = mi.Pid
        where mi.id = #{machitemid}
          and mi.Tenantid = #{tid}
    </select>

    <select id="getSummaryByWorkitemid" resultType="java.lang.String">
        select m.Summary
        from Wk_Worksheet m
                 join Wk_WorksheetItem mi on mi.Pid = m.id
        where mi.id = #{workitemid}
          and mi.Tenantid = #{tid}
    </select>
</mapper>

