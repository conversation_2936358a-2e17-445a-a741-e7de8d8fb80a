<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkCompleteMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkCompletePojo">
        select App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Wk_Complete.id,
               Wk_Complete.RefNo,
               Wk_Complete.BillType,
               Wk_Complete.BillDate,
               Wk_Complete.BillTitle,
               Wk_Complete.Operator,
               Wk_Complete.Groupid,
               Wk_Complete.Summary,
               Wk_Complete.CreateBy,
               Wk_Complete.CreateByid,
               Wk_Complete.CreateDate,
               Wk_Complete.Lister,
               Wk_Complete.Listerid,
               Wk_Complete.ModifyDate,
               Wk_Complete.Assessor,
               Wk_Complete.Assessorid,
               Wk_Complete.AssessDate,
               Wk_Complete.BillAmount,
               Wk_Complete.BillTaxAmount,
               Wk_Complete.Custom1,
               Wk_Complete.Custom2,
               Wk_Complete.Custom3,
               Wk_Complete.Custom4,
               Wk_Complete.Custom5,
               Wk_Complete.Custom6,
               Wk_Complete.Custom7,
               Wk_Complete.Custom8,
               Wk_Complete.Custom9,
               Wk_Complete.Custom10,
               Wk_Complete.Tenantid,
               Wk_Complete.Revision
        from App_Workgroup
                 RIGHT JOIN Wk_Complete ON Wk_Complete.Groupid = App_Workgroup.id
        where Wk_Complete.id = #{key}
          and Wk_Complete.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Wk_Complete.id,
               Wk_Complete.RefNo,
               Wk_Complete.BillType,
               Wk_Complete.BillDate,
               Wk_Complete.BillTitle,
               Wk_Complete.Operator,
               Wk_Complete.Groupid,
               Wk_Complete.Summary,
               Wk_Complete.CreateBy,
               Wk_Complete.CreateByid,
               Wk_Complete.CreateDate,
               Wk_Complete.Lister,
               Wk_Complete.Listerid,
               Wk_Complete.ModifyDate,
               Wk_Complete.Assessor,
               Wk_Complete.Assessorid,
               Wk_Complete.AssessDate,
               Wk_Complete.BillAmount,
               Wk_Complete.BillTaxAmount,
               Wk_Complete.Custom1,
               Wk_Complete.Custom2,
               Wk_Complete.Custom3,
               Wk_Complete.Custom4,
               Wk_Complete.Custom5,
               Wk_Complete.Custom6,
               Wk_Complete.Custom7,
               Wk_Complete.Custom8,
               Wk_Complete.Custom9,
               Wk_Complete.Custom10,
               Wk_Complete.Tenantid,
               Wk_Complete.Revision
        from App_Workgroup
                 RIGHT JOIN Wk_Complete ON Wk_Complete.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT Wk_CompleteItem.id,
               Wk_CompleteItem.Pid,
               Wk_CompleteItem.WorkDate,
               Wk_CompleteItem.WorkUid,
               Wk_CompleteItem.WorkItemid,
               Wk_CompleteItem.Goodsid,
               Wk_CompleteItem.ItemCode,
               Wk_CompleteItem.ItemName,
               Wk_CompleteItem.ItemSpec,
               Wk_CompleteItem.ItemUnit,
               Wk_CompleteItem.Quantity,
               Wk_CompleteItem.SubItemid,
               Wk_CompleteItem.SubUse,
               Wk_CompleteItem.SubUnit,
               Wk_CompleteItem.SubQty,
               Wk_CompleteItem.TaxPrice,
               Wk_CompleteItem.TaxAmount,
               Wk_CompleteItem.Price,
               Wk_CompleteItem.Amount,
               Wk_CompleteItem.TaxTotal,
               Wk_CompleteItem.ItemTaxrate,
               Wk_CompleteItem.StartDate,
               Wk_CompleteItem.PlanDate,
               Wk_CompleteItem.FinishQty,
               Wk_CompleteItem.FinishHour,
               Wk_CompleteItem.MrbQty,
               Wk_CompleteItem.EnabledMark,
               Wk_CompleteItem.Closed,
               Wk_CompleteItem.Remark,
               Wk_CompleteItem.StateCode,
               Wk_CompleteItem.StateDate,
               Wk_CompleteItem.RowNum,
               Wk_CompleteItem.MachUid,
               Wk_CompleteItem.MachItemid,
               Wk_CompleteItem.MachGroupid,
               Wk_CompleteItem.MrpUid,
               Wk_CompleteItem.MrpItemid,
               Wk_CompleteItem.Customer,
               Wk_CompleteItem.CustPO,
               Wk_CompleteItem.MainPlanUid,
               Wk_CompleteItem.MainPlanItemid,
               Wk_CompleteItem.CiteUid,
               Wk_CompleteItem.CiteItemid,
               Wk_CompleteItem.Location,
               Wk_CompleteItem.BatchNo,
               Wk_CompleteItem.AttributeJson,
               Wk_CompleteItem.Custom1,
               Wk_CompleteItem.Custom2,
               Wk_CompleteItem.Custom3,
               Wk_CompleteItem.Custom4,
               Wk_CompleteItem.Custom5,
               Wk_CompleteItem.Custom6,
               Wk_CompleteItem.Custom7,
               Wk_CompleteItem.Custom8,
               Wk_CompleteItem.Custom9,
               Wk_CompleteItem.Custom10,
               Wk_CompleteItem.Tenantid,
               Wk_CompleteItem.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_Complete.RefNo,
               Wk_Complete.BillType,
               Wk_Complete.BillDate,
               Wk_Complete.BillTitle,
               Wk_Complete.Operator,
               Wk_Complete.CreateBy,
               Wk_Complete.Lister,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Wk_CompleteItem
                 LEFT JOIN Wk_Complete ON Wk_CompleteItem.Pid = Wk_Complete.id
                 LEFT JOIN App_Workgroup ON Wk_Complete.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Wk_CompleteItem.Goodsid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkCompleteitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_Complete.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_Complete.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Wk_Complete.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Wk_Complete.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Wk_Complete.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Wk_Complete.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Wk_Complete.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Wk_Complete.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Wk_Complete.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Wk_Complete.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Wk_Complete.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Wk_Complete.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Wk_Complete.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Wk_Complete.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_Complete.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_Complete.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_Complete.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_Complete.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_Complete.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_Complete.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_Complete.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_Complete.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_Complete.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_Complete.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Wk_Complete.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Wk_Complete.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Wk_Complete.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
                or Wk_Complete.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Wk_Complete.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Wk_Complete.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Wk_Complete.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Wk_Complete.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Wk_Complete.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Wk_Complete.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Wk_Complete.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Wk_Complete.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_Complete.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_Complete.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_Complete.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_Complete.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_Complete.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_Complete.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_Complete.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_Complete.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_Complete.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_Complete.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkCompletePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_Complete.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_Complete.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Wk_Complete.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Wk_Complete.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Wk_Complete.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Wk_Complete.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Wk_Complete.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Wk_Complete.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Wk_Complete.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Wk_Complete.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Wk_Complete.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Wk_Complete.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Wk_Complete.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Wk_Complete.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_Complete.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_Complete.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_Complete.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_Complete.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_Complete.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_Complete.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_Complete.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_Complete.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_Complete.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_Complete.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Wk_Complete.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Wk_Complete.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Wk_Complete.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
                or Wk_Complete.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Wk_Complete.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Wk_Complete.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Wk_Complete.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Wk_Complete.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Wk_Complete.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Wk_Complete.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Wk_Complete.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Wk_Complete.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_Complete.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_Complete.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_Complete.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_Complete.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_Complete.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_Complete.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_Complete.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_Complete.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_Complete.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_Complete.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_Complete(id, RefNo, BillType, BillDate, BillTitle, Operator, Groupid, Summary, CreateBy,
                                CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate,BillAmount,BillTaxAmount,
                                Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9,
                                Custom10, Tenantid, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{operator}, #{groupid}, #{summary},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor},
                #{assessorid}, #{assessdate},#{billamount},#{billtaxamount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_Complete
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_Complete
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_Complete
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkCompletePojo">
        select
        id
        from Wk_CompleteItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

    <select id="getDelMatIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkCompletePojo">
        select
        id
        from Wk_CompleteMat
        where Pid = #{id} and id not in
        <foreach collection="mat" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
</mapper>

