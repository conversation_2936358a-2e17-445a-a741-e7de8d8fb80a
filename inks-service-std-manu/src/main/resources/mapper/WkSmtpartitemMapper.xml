<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSmtpartitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSmtpartitemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_SmtPartItem.id,
               Wk_SmtPartItem.Pid,
               Wk_SmtPartItem.DevCode,
               Wk_SmtPartItem.TableCode,
               Wk_SmtPartItem.StationNum,
               Wk_SmtPartItem.StationCode,
               Wk_SmtPartItem.PointMark,
               Wk_SmtPartItem.Goodsid,
               Wk_SmtPartItem.ItemType,
               Wk_SmtPartItem.ItemCode,
               Wk_SmtPartItem.ItemName,
               Wk_SmtPartItem.ItemSpec,
               Wk_SmtPartItem.ItemUnit,
               Wk_SmtPartItem.SingleQty,
               Wk_SmtPartItem.Quantity,
               Wk_SmtPartItem.FinishQty,
               Wk_SmtPartItem.AcceptQty,
               Wk_SmtPartItem.RowNum,
               Wk_SmtPartItem.Remark,
               Wk_SmtPartItem.PickCode,
               Wk_SmtPartItem.PickDate,
               Wk_SmtPartItem.PickMark,
               Wk_SmtPartItem.Picker,
               Wk_SmtPartItem.Pickerid,
               Wk_SmtPartItem.Custom1,
               Wk_SmtPartItem.Custom2,
               Wk_SmtPartItem.Custom3,
               Wk_SmtPartItem.Custom4,
               Wk_SmtPartItem.Custom5,
               Wk_SmtPartItem.Custom6,
               Wk_SmtPartItem.Custom7,
               Wk_SmtPartItem.Custom8,
               Wk_SmtPartItem.Custom9,
               Wk_SmtPartItem.Custom10,
               Wk_SmtPartItem.Tenantid,
               Wk_SmtPartItem.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_SmtPartItem ON Wk_SmtPartItem.Goodsid = Mat_Goods.id
        where Wk_SmtPartItem.id = #{key}
          and Wk_SmtPartItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkSmtpartitemVo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_SmtPartItem.id,
               Wk_SmtPartItem.Pid,
               Wk_SmtPartItem.DevCode,
               Wk_SmtPartItem.TableCode,
               Wk_SmtPartItem.StationNum,
               Wk_SmtPartItem.StationCode,
               Wk_SmtPartItem.PointMark,
               Wk_SmtPartItem.Goodsid,
               Wk_SmtPartItem.ItemType,
               Wk_SmtPartItem.ItemCode,
               Wk_SmtPartItem.ItemName,
               Wk_SmtPartItem.ItemSpec,
               Wk_SmtPartItem.ItemUnit,
               Wk_SmtPartItem.SingleQty,
               Wk_SmtPartItem.Quantity,
               Wk_SmtPartItem.FinishQty,
               Wk_SmtPartItem.AcceptQty,
               Wk_SmtPartItem.RowNum,
               Wk_SmtPartItem.Remark,
               Wk_SmtPartItem.PickCode,
               Wk_SmtPartItem.PickDate,
               Wk_SmtPartItem.PickMark,
               Wk_SmtPartItem.Picker,
               Wk_SmtPartItem.Pickerid,
               Wk_SmtPartItem.Custom1,
               Wk_SmtPartItem.Custom2,
               Wk_SmtPartItem.Custom3,
               Wk_SmtPartItem.Custom4,
               Wk_SmtPartItem.Custom5,
               Wk_SmtPartItem.Custom6,
               Wk_SmtPartItem.Custom7,
               Wk_SmtPartItem.Custom8,
               Wk_SmtPartItem.Custom9,
               Wk_SmtPartItem.Custom10,
               Wk_SmtPartItem.Tenantid,
               Wk_SmtPartItem.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_SmtPartItem ON Wk_SmtPartItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSmtpartitemPojo">
        <include refid="selectWkSmtpartitemVo"/>
        where 1 = 1 and Wk_SmtPartItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_SmtPartItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_SmtPartItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.devcode != null and SearchPojo.devcode != ''">
            and Wk_SmtPartItem.devcode like concat('%', #{SearchPojo.devcode}, '%')
        </if>
        <if test="SearchPojo.tablecode != null and SearchPojo.tablecode != ''">
            and Wk_SmtPartItem.tablecode like concat('%', #{SearchPojo.tablecode}, '%')
        </if>
        <if test="SearchPojo.stationcode != null and SearchPojo.stationcode != ''">
            and Wk_SmtPartItem.stationcode like concat('%', #{SearchPojo.stationcode}, '%')
        </if>
        <if test="SearchPojo.pointmark != null and SearchPojo.pointmark != ''">
            and Wk_SmtPartItem.pointmark like concat('%', #{SearchPojo.pointmark}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_SmtPartItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
            and Wk_SmtPartItem.itemtype like concat('%', #{SearchPojo.itemtype}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_SmtPartItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_SmtPartItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_SmtPartItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_SmtPartItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_SmtPartItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.pickcode != null and SearchPojo.pickcode != ''">
            and Wk_SmtPartItem.pickcode like concat('%', #{SearchPojo.pickcode}, '%')
        </if>
        <if test="SearchPojo.picker != null and SearchPojo.picker != ''">
            and Wk_SmtPartItem.picker like concat('%', #{SearchPojo.picker}, '%')
        </if>
        <if test="SearchPojo.pickerid != null and SearchPojo.pickerid != ''">
            and Wk_SmtPartItem.pickerid like concat('%', #{SearchPojo.pickerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_SmtPartItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_SmtPartItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_SmtPartItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_SmtPartItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_SmtPartItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_SmtPartItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_SmtPartItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_SmtPartItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_SmtPartItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_SmtPartItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_SmtPartItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.devcode != null and SearchPojo.devcode != ''">
                or Wk_SmtPartItem.DevCode like concat('%', #{SearchPojo.devcode}, '%')
            </if>
            <if test="SearchPojo.tablecode != null and SearchPojo.tablecode != ''">
                or Wk_SmtPartItem.TableCode like concat('%', #{SearchPojo.tablecode}, '%')
            </if>
            <if test="SearchPojo.stationcode != null and SearchPojo.stationcode != ''">
                or Wk_SmtPartItem.StationCode like concat('%', #{SearchPojo.stationcode}, '%')
            </if>
            <if test="SearchPojo.pointmark != null and SearchPojo.pointmark != ''">
                or Wk_SmtPartItem.PointMark like concat('%', #{SearchPojo.pointmark}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_SmtPartItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
                or Wk_SmtPartItem.ItemType like concat('%', #{SearchPojo.itemtype}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_SmtPartItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_SmtPartItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_SmtPartItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_SmtPartItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_SmtPartItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.pickcode != null and SearchPojo.pickcode != ''">
                or Wk_SmtPartItem.PickCode like concat('%', #{SearchPojo.pickcode}, '%')
            </if>
            <if test="SearchPojo.picker != null and SearchPojo.picker != ''">
                or Wk_SmtPartItem.Picker like concat('%', #{SearchPojo.picker}, '%')
            </if>
            <if test="SearchPojo.pickerid != null and SearchPojo.pickerid != ''">
                or Wk_SmtPartItem.Pickerid like concat('%', #{SearchPojo.pickerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_SmtPartItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_SmtPartItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_SmtPartItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_SmtPartItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_SmtPartItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_SmtPartItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_SmtPartItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_SmtPartItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_SmtPartItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_SmtPartItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkSmtpartitemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_SmtPartItem.id,
               Wk_SmtPartItem.Pid,
               Wk_SmtPartItem.DevCode,
               Wk_SmtPartItem.TableCode,
               Wk_SmtPartItem.StationNum,
               Wk_SmtPartItem.StationCode,
               Wk_SmtPartItem.PointMark,
               Wk_SmtPartItem.Goodsid,
               Wk_SmtPartItem.ItemType,
               Wk_SmtPartItem.ItemCode,
               Wk_SmtPartItem.ItemName,
               Wk_SmtPartItem.ItemSpec,
               Wk_SmtPartItem.ItemUnit,
               Wk_SmtPartItem.SingleQty,
               Wk_SmtPartItem.Quantity,
               Wk_SmtPartItem.FinishQty,
               Wk_SmtPartItem.AcceptQty,
               Wk_SmtPartItem.RowNum,
               Wk_SmtPartItem.Remark,
               Wk_SmtPartItem.PickCode,
               Wk_SmtPartItem.PickDate,
               Wk_SmtPartItem.PickMark,
               Wk_SmtPartItem.Picker,
               Wk_SmtPartItem.Pickerid,
               Wk_SmtPartItem.Custom1,
               Wk_SmtPartItem.Custom2,
               Wk_SmtPartItem.Custom3,
               Wk_SmtPartItem.Custom4,
               Wk_SmtPartItem.Custom5,
               Wk_SmtPartItem.Custom6,
               Wk_SmtPartItem.Custom7,
               Wk_SmtPartItem.Custom8,
               Wk_SmtPartItem.Custom9,
               Wk_SmtPartItem.Custom10,
               Wk_SmtPartItem.Tenantid,
               Wk_SmtPartItem.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_SmtPartItem ON Wk_SmtPartItem.Goodsid = Mat_Goods.id
        where Wk_SmtPartItem.Pid = #{Pid}
          and Wk_SmtPartItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_SmtPartItem(id, Pid, DevCode, TableCode, StationNum, StationCode, PointMark, Goodsid, ItemType,
                                   ItemCode, ItemName, ItemSpec, ItemUnit, SingleQty, Quantity, FinishQty, AcceptQty,
                                   RowNum, Remark, PickCode, PickDate, PickMark, Picker, Pickerid, Custom1, Custom2,
                                   Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                   Revision)
        values (#{id}, #{pid}, #{devcode}, #{tablecode}, #{stationnum}, #{stationcode}, #{pointmark}, #{goodsid},
                #{itemtype}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{singleqty}, #{quantity},
                #{finishqty}, #{acceptqty}, #{rownum}, #{remark}, #{pickcode}, #{pickdate}, #{pickmark}, #{picker},
                #{pickerid}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_SmtPartItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="devcode != null ">
                DevCode = #{devcode},
            </if>
            <if test="tablecode != null ">
                TableCode = #{tablecode},
            </if>
            <if test="stationnum != null">
                StationNum = #{stationnum},
            </if>
            <if test="stationcode != null ">
                StationCode = #{stationcode},
            </if>
            <if test="pointmark != null ">
                PointMark = #{pointmark},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemtype != null ">
                ItemType = #{itemtype},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="singleqty != null">
                SingleQty = #{singleqty},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="acceptqty != null">
                AcceptQty = #{acceptqty},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="pickcode != null ">
                PickCode = #{pickcode},
            </if>
            <if test="pickdate != null">
                PickDate = #{pickdate},
            </if>
            <if test="pickmark != null">
                PickMark = #{pickmark},
            </if>
            <if test="picker != null ">
                Picker = #{picker},
            </if>
            <if test="pickerid != null ">
                Pickerid = #{pickerid},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_SmtPartItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

