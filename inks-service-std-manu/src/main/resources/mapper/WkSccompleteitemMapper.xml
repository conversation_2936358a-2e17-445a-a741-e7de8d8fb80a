<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSccompleteitemMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSccompleteitemPojo">
        <include refid="selectWkSccompleteitemVo"/>
        where Wk_ScCompleteItem.id = #{key}
        and Wk_ScCompleteItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkSccompleteitemVo">
        select id,
               Pid,
               WorkDate,
               WorkUid,
               WorkItemid,
               Goodsid,
               ItemCode,
               ItemName,
               ItemSpec,
               ItemUnit,
               Quantity,
               SubItemid,
               SubUse,
               SubUnit,
               SubQty,
               TaxPrice,
               TaxAmount,
               Price,
               Amount,
               TaxTotal,
               ItemTaxrate,
               StartDate,
               PlanDate,
               FinishQty,
               FinishHour,
               MrbQty,
               EnabledMark,
               Closed,
               Remark,
               StateCode,
               StateDate,
               RowNum,
               MachUid,
               MachItemid,
               MachGroupid,
               MrpUid,
               MrpItemid,
               Customer,
               CustPO,
               MainPlanUid,
               MainPlanItemid,
               CiteUid,
               CiteItemid,
               Location,
               BatchNo,
               AttributeJson,
               AttributeStr,
               InvoQty,
               InvoClosed,
               SourceType,
               VirtualItem,
               SubByPcs,
               DisannulMark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Wk_ScCompleteItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSccompleteitemPojo">
        <include refid="selectWkSccompleteitemVo"/>
        where 1 = 1 and Wk_ScCompleteItem.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_ScCompleteItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_ScCompleteItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.workdate != null and SearchPojo.workdate != ''">
            and Wk_ScCompleteItem.workdate like concat('%',
            #{SearchPojo.workdate}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Wk_ScCompleteItem.workuid like concat('%',
            #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            and Wk_ScCompleteItem.workitemid like concat('%',
            #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_ScCompleteItem.goodsid like concat('%',
            #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_ScCompleteItem.itemcode like concat('%',
            #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_ScCompleteItem.itemname like concat('%',
            #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_ScCompleteItem.itemspec like concat('%',
            #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_ScCompleteItem.itemunit like concat('%',
            #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
            and Wk_ScCompleteItem.subitemid like concat('%',
            #{SearchPojo.subitemid}, '%')
        </if>
        <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
            and Wk_ScCompleteItem.subuse like concat('%',
            #{SearchPojo.subuse}, '%')
        </if>
        <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
            and Wk_ScCompleteItem.subunit like concat('%',
            #{SearchPojo.subunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_ScCompleteItem.remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_ScCompleteItem.statecode like concat('%',
            #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_ScCompleteItem.machuid like concat('%',
            #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_ScCompleteItem.machitemid like concat('%',
            #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_ScCompleteItem.machgroupid like concat('%',
            #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Wk_ScCompleteItem.mrpuid like concat('%',
            #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Wk_ScCompleteItem.mrpitemid like concat('%',
            #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_ScCompleteItem.customer like concat('%',
            #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_ScCompleteItem.custpo like concat('%',
            #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_ScCompleteItem.mainplanuid like concat('%',
            #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_ScCompleteItem.mainplanitemid like concat('%',
            #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Wk_ScCompleteItem.citeuid like concat('%',
            #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Wk_ScCompleteItem.citeitemid like concat('%',
            #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Wk_ScCompleteItem.location like concat('%',
            #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Wk_ScCompleteItem.batchno like concat('%',
            #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_ScCompleteItem.attributejson like concat('%',
            #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
            and Wk_ScCompleteItem.attributestr like concat('%',
            #{SearchPojo.attributestr}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_ScCompleteItem.custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_ScCompleteItem.custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_ScCompleteItem.custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_ScCompleteItem.custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_ScCompleteItem.custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_ScCompleteItem.custom6 like concat('%',
            #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_ScCompleteItem.custom7 like concat('%',
            #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_ScCompleteItem.custom8 like concat('%',
            #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_ScCompleteItem.custom9 like concat('%',
            #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_ScCompleteItem.custom10 like concat('%',
            #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_ScCompleteItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.workdate != null and SearchPojo.workdate != ''">
                or Wk_ScCompleteItem.WorkDate like concat('%', #{SearchPojo.workdate}, '%')
            </if>
            <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
                or Wk_ScCompleteItem.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
                or Wk_ScCompleteItem.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_ScCompleteItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_ScCompleteItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_ScCompleteItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_ScCompleteItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_ScCompleteItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
                or Wk_ScCompleteItem.SubItemid like concat('%', #{SearchPojo.subitemid}, '%')
            </if>
            <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
                or Wk_ScCompleteItem.SubUse like concat('%', #{SearchPojo.subuse}, '%')
            </if>
            <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
                or Wk_ScCompleteItem.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_ScCompleteItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Wk_ScCompleteItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_ScCompleteItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_ScCompleteItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_ScCompleteItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Wk_ScCompleteItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Wk_ScCompleteItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_ScCompleteItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_ScCompleteItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_ScCompleteItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_ScCompleteItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Wk_ScCompleteItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Wk_ScCompleteItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Wk_ScCompleteItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Wk_ScCompleteItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_ScCompleteItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
                or Wk_ScCompleteItem.AttributeStr like concat('%', #{SearchPojo.attributestr}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_ScCompleteItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_ScCompleteItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_ScCompleteItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_ScCompleteItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_ScCompleteItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_ScCompleteItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_ScCompleteItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_ScCompleteItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_ScCompleteItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_ScCompleteItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkSccompleteitemPojo">
        select
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_ScCompleteItem.id,
        Wk_ScCompleteItem.Pid,
        Wk_ScCompleteItem.WorkDate,
        Wk_ScCompleteItem.WorkUid,
        Wk_ScCompleteItem.WorkItemid,
        Wk_ScCompleteItem.Goodsid,
        Wk_ScCompleteItem.ItemCode,
        Wk_ScCompleteItem.ItemName,
        Wk_ScCompleteItem.ItemSpec,
        Wk_ScCompleteItem.ItemUnit,
        Wk_ScCompleteItem.Quantity,
        Wk_ScCompleteItem.SubItemid,
        Wk_ScCompleteItem.SubUse,
        Wk_ScCompleteItem.SubUnit,
        Wk_ScCompleteItem.SubQty,
        Wk_ScCompleteItem.TaxPrice,
        Wk_ScCompleteItem.TaxAmount,
        Wk_ScCompleteItem.Price,
        Wk_ScCompleteItem.Amount,
        Wk_ScCompleteItem.TaxTotal,
        Wk_ScCompleteItem.ItemTaxrate,
        Wk_ScCompleteItem.StartDate,
        Wk_ScCompleteItem.PlanDate,
        Wk_ScCompleteItem.FinishQty,
        Wk_ScCompleteItem.FinishHour,
        Wk_ScCompleteItem.MrbQty,
        Wk_ScCompleteItem.EnabledMark,
        Wk_ScCompleteItem.Closed,
        Wk_ScCompleteItem.Remark,
        Wk_ScCompleteItem.StateCode,
        Wk_ScCompleteItem.StateDate,
        Wk_ScCompleteItem.RowNum,
        Wk_ScCompleteItem.MachUid,
        Wk_ScCompleteItem.MachItemid,
        Wk_ScCompleteItem.MachGroupid,
        Wk_ScCompleteItem.MrpUid,
        Wk_ScCompleteItem.MrpItemid,
        Wk_ScCompleteItem.Customer,
        Wk_ScCompleteItem.CustPO,
        Wk_ScCompleteItem.MainPlanUid,
        Wk_ScCompleteItem.MainPlanItemid,
        Wk_ScCompleteItem.CiteUid,
        Wk_ScCompleteItem.CiteItemid,
        Wk_ScCompleteItem.Location,
        Wk_ScCompleteItem.BatchNo,
        Wk_ScCompleteItem.AttributeJson,
        Wk_ScCompleteItem.AttributeStr,
        Wk_ScCompleteItem.InvoQty,
        Wk_ScCompleteItem.InvoClosed,
        Wk_ScCompleteItem.SourceType,
        Wk_ScCompleteItem.VirtualItem,
        Wk_ScCompleteItem.SubByPcs,
        Wk_ScCompleteItem.Custom1,
        Wk_ScCompleteItem.Custom2,
        Wk_ScCompleteItem.Custom3,
        Wk_ScCompleteItem.Custom4,
        Wk_ScCompleteItem.Custom5,
        Wk_ScCompleteItem.Custom6,
        Wk_ScCompleteItem.Custom7,
        Wk_ScCompleteItem.Custom8,
        Wk_ScCompleteItem.Custom9,
        Wk_ScCompleteItem.Custom10,
        Wk_ScCompleteItem.Tenantid,
        Wk_ScCompleteItem.Revision
        from Wk_ScCompleteItem
        LEFT JOIN Mat_Goods ON Wk_ScCompleteItem.Goodsid = Mat_Goods.id
        where Wk_ScCompleteItem.Pid = #{Pid}
        and Wk_ScCompleteItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_ScCompleteItem(id, Pid, WorkDate, WorkUid, WorkItemid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, SubItemid, SubUse, SubUnit, SubQty, TaxPrice, TaxAmount, Price, Amount, TaxTotal, ItemTaxrate, StartDate, PlanDate, FinishQty, FinishHour, MrbQty, EnabledMark, Closed, Remark, StateCode, StateDate, RowNum, MachUid, MachItemid, MachGroupid, MrpUid, MrpItemid, Customer, CustPO, MainPlanUid, MainPlanItemid, CiteUid, CiteItemid, Location, BatchNo, AttributeJson, AttributeStr, InvoQty, InvoClosed, SourceType, VirtualItem, SubByPcs, DisannulMark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{workdate}, #{workuid}, #{workitemid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{subitemid}, #{subuse}, #{subunit}, #{subqty}, #{taxprice}, #{taxamount}, #{price}, #{amount}, #{taxtotal}, #{itemtaxrate}, #{startdate}, #{plandate}, #{finishqty}, #{finishhour}, #{mrbqty}, #{enabledmark}, #{closed}, #{remark}, #{statecode}, #{statedate}, #{rownum}, #{machuid}, #{machitemid}, #{machgroupid}, #{mrpuid}, #{mrpitemid}, #{customer}, #{custpo}, #{mainplanuid}, #{mainplanitemid}, #{citeuid}, #{citeitemid}, #{location}, #{batchno}, #{attributejson}, #{attributestr}, #{invoqty}, #{invoclosed}, #{sourcetype}, #{virtualitem}, #{subbypcs}, #{disannulmark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_ScCompleteItem
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="workdate != null">
                WorkDate = #{workdate},
            </if>
            <if test="workuid != null">
                WorkUid = #{workuid},
            </if>
            <if test="workitemid != null">
                WorkItemid = #{workitemid},
            </if>
            <if test="goodsid != null">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="subitemid != null">
                SubItemid = #{subitemid},
            </if>
            <if test="subuse != null">
                SubUse = #{subuse},
            </if>
            <if test="subunit != null">
                SubUnit = #{subunit},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="finishhour != null">
                FinishHour = #{finishhour},
            </if>
            <if test="mrbqty != null">
                MrbQty = #{mrbqty},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            <if test="statecode != null">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="machuid != null">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null">
                MachItemid = #{machitemid},
            </if>
            <if test="machgroupid != null">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mrpuid != null">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="customer != null">
                Customer = #{customer},
            </if>
            <if test="custpo != null">
                CustPO = #{custpo},
            </if>
            <if test="mainplanuid != null">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="citeuid != null">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null">
                CiteItemid = #{citeitemid},
            </if>
            <if test="location != null">
                Location = #{location},
            </if>
            <if test="batchno != null">
                BatchNo = #{batchno},
            </if>
            <if test="attributejson != null">
                AttributeJson = #{attributejson},
            </if>
            <if test="attributestr != null">
                AttributeStr = #{attributestr},
            </if>
            <if test="invoqty != null">
                InvoQty = #{invoqty},
            </if>
            <if test="invoclosed != null">
                InvoClosed = #{invoclosed},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="subbypcs != null">
                SubByPcs = #{subbypcs},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="custom1 != null">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 = #{custom10},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_ScCompleteItem
        where id = #{key}
        and Tenantid = #{tid}
    </delete>

    <update id="updateCompQty">
        update Wk_SubcontractItem
        SET CompQty =COALESCE((SELECT sum(IF(Wk_ScComplete.BillType IN ('委制验收'), Wk_ScCompleteItem.quantity,
                                             0 - Wk_ScCompleteItem.quantity))
                               FROM Wk_ScCompleteItem
                                        LEFT OUTER JOIN Wk_ScComplete
                                                        ON Wk_ScCompleteItem.pid = Wk_ScComplete.id
                               where Wk_ScComplete.BillType IN ('委制验收', '委制退货')
                                 and Wk_ScCompleteItem.citeitemid = #{subcontractitemid}
                                 and Wk_ScCompleteItem.Closed = 0
                                 and Wk_ScCompleteItem.Tenantid = #{tid}), 0)
        where id = #{subcontractitemid}
          and Tenantid = #{tid}
    </update>


    <!--  刷新委制单的完成数 记得同步修改SQL: 照抄store服务的matAccessCiteMapper.updateWsAcceFinish方法-->
    <update id="updateWsAcceFinish">
        update Wk_SubcontractItem
        SET FinishQty =
                COALESCE((SELECT SUM(CASE
                                         WHEN ma.BillType = '加工入库' THEN mai.quantity
                                         WHEN ma.BillType = '加工红冲' THEN -mai.quantity
                                         ELSE 0
                    END
                                 )
                          FROM Mat_AccessItem mai
                                   LEFT JOIN Mat_Access ma ON mai.pid = ma.id
                          WHERE mai.citeUid = #{refno}
                            AND mai.citeitemid = #{key}
                            AND mai.Tenantid = #{tid}), 0)
                    +
                COALESCE((SELECT SUM(wsci.quantity)
                          FROM Wk_ScCompleteItem wsci
                                   LEFT JOIN Wk_ScComplete wsc ON wsci.pid = wsc.id
                          WHERE wsci.citeitemid = #{key}
                            AND wsci.Closed = 0
                            AND wsci.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
</mapper>

