<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.D05MBIR1Mapper">
    <!--    获取今日进度(工序进度)返回工序已完成数量,总数量-->
    <!--    CURDATE() 函数获取当前日期，DATE(PlanDate)转换为日期格式以进行比较-->
    <select id="getProcessProgressToday" resultType="java.util.Map">
        select Sum(WkRowNum)  AS sumwkrownum,
               Sum(ItemCount) AS sumitemcount
        from Wk_WipNote
        WHERE DATE(PlanDate) = CURDATE()
          and Tenantid = #{tid}
    </select>

    <!--    获取授权码信息-->
    <select id="getAuthByCode" resultType="java.util.HashMap">
        SELECT UserName,
               UserPassword,
               Tenantid
        FROM PiAuthCode
        WHERE AuthCode = #{auth}
    </select>


    <select id="getFinishCountDeliToday" resultType="int">
        SELECT count(*)
        FROM Bus_Deliery
        where Tenantid = #{tid}
          and BillType IN ('发出商品', '订单退货')
          and FinishCount <![CDATA[>=]]> Bus_Deliery.ItemCount
          and DATE(CreateDate) = CURDATE()
    </select>

    <select id="getOnlineCountMachToday" resultType="int">
        SELECT count(*)
        FROM Bus_Machining
        where Tenantid = #{tid}
          and Bus_Machining.FinishCount + Bus_Machining.DisannulCount <![CDATA[<]]> Bus_Machining.ItemCount
          and DATE(CreateDate) = CURDATE()
    </select>

    <select id="getWipAddAndCompToday" resultType="java.util.Map">
        SELECT sum(WkPcsQty)   AS sumwkpcsqty,
               sum(CompPcsQty) AS sumcomppcsqty
        FROM Wk_WipNote
        WHERE Tenantid = #{tenantid}
          and Wk_WipNote.DisannulMark = 0
          and Wk_WipNote.Closed = 0
          AND DATE(CreateDate) = CURDATE()
        <if test="workshopid != null and workshopid != ''">
            and WorkshopID = #{workshopid}
        </if>
    </select>

    <select id="getWipQtyGroupByGoods" resultType="java.util.Map">
        SELECT Mat_Goods.id                          as goodsid,
               Mat_Goods.GoodsName                   as goodsname,
               Mat_Goods.GoodsUid                    as goodsuid,
               COALESCE(sum(Wk_WipNote.WkPcsQty), 0) AS sumwkpcsqty
        FROM Wk_WipNote
                 Left join Mat_Goods on Wk_WipNote.Goodsid = Mat_Goods.id
        WHERE Wk_WipNote.Tenantid = #{tenantid}
          and Wk_WipNote.DisannulMark = 0
          and Wk_WipNote.Closed = 0
        <if test="workshopid != null and workshopid != ''">
            and Wk_WipNote.Workshopid = #{workshopid}
        </if>
        <if test="queryParam.DateRange != null">
            and Wk_WipNote.CreateDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
        </if>
        GROUP BY Mat_Goods.id, Mat_Goods.GoodsName, Mat_Goods.GoodsUid
        order by sumwkpcsqty desc
        limit 5
    </select>

    <select id="getAllWipQtyByWorkShopid" resultType="double">
        SELECT COALESCE(sum(WkPcsQty), 0) AS sumallwkpcsqty
        FROM Wk_WipNote
        WHERE Tenantid = #{tenantid}
          and Wk_WipNote.DisannulMark = 0
          and Wk_WipNote.Closed = 0
        <if test="workshopid != null and workshopid != ''">
            and Wk_WipNote.Workshopid = #{workshopid}
        </if>
        <if test="queryParam != null and queryParam.DateRange != null">
            and Wk_WipNote.CreateDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
        </if>
    </select>

    <!--    查询生产WIP的[完工记录]:按产品分组统计sum(wip数量),返回值：货品编码，货品名称，数量-->
    <select id="getSumWipCompQtyGroupByGoods" resultType="java.util.Map">
        SELECT Mat_Goods.id                          as goodsid,
               Mat_Goods.GoodsName                   as goodsname,
               Mat_Goods.GoodsUid                    as goodsuid,
               COALESCE(sum(Wk_WipNote.WkPcsQty), 0) AS sumwkpcsqty
        FROM Wk_WipNote
                 Left join Mat_Goods on Wk_WipNote.Goodsid = Mat_Goods.id
        WHERE Wk_WipNote.Tenantid = #{tenantid}
          and Wk_WipNote.DisannulMark = 0
          and Wk_WipNote.Closed = 0
          and Wk_WipNote.MrbPcsQty + Wk_WipNote.CompPcsQty <![CDATA[>=]]> Wk_WipNote.WkPcsQty
        <if test="queryParam != null and queryParam.DateRange != null">
            and Wk_WipNote.CreateDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
        </if>
        GROUP BY Mat_Goods.id, Mat_Goods.GoodsName, Mat_Goods.GoodsUid
        order by sumwkpcsqty desc
    </select>

    <select id="getWipOnlineAndCompQtyEveryDay" resultType="java.util.Map">
        SELECT DATE(CreateDate)                                                  as date,
               sum(IF(MrbPcsQty + CompPcsQty <![CDATA[<]]> WkPcsQty / #{percent}, 1, 0))  as sumonlinepcsqty,
               sum(IF(MrbPcsQty + CompPcsQty <![CDATA[>=]]> WkPcsQty / #{percent}, 1, 0)) as sumcomppcsqty
        FROM Wk_WipNote
        WHERE Tenantid = #{tenantid}
          and Wk_WipNote.DisannulMark = 0
          and Wk_WipNote.Closed = 0
        <if test="queryParam != null and queryParam.DateRange != null">
            and Wk_WipNote.CreateDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
        </if>
        GROUP BY DATE(CreateDate)
        order by date desc
    </select>

    <select id="getWipSumWorkTime" resultType="java.util.Map">
        SELECT Wk_WipNote.RefNo as refno,
               sum(Wk_WipQty.WorkTime) as sumworktime
        FROM Wk_WipNote RIGHT JOIN Wk_WipQty on Wk_WipNote.RefNo = Wk_WipQty.WipUid
        WHERE Wk_WipNote.Tenantid = #{tenantid}
          and Wk_WipNote.DisannulMark = 0
          and Wk_WipNote.Closed = 0
        <if test="queryParam != null and queryParam.DateRange != null">
            and Wk_WipNote.CreateDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
        </if>
        GROUP BY Wk_WipNote.RefNo
        order by sumworktime desc
    </select>

    <!--    SUM(IF(spu.spukey = 'spuzongzhongliang', spu.spuvalue, 0)) AS 总重量,-->
    <!--    SUM(IF(spu.spukey = 'sputuitongxiezongzhong', spu.spuvalue, 0)) AS 退铜屑重量,-->
    <!--    SUM(IF(spu.spukey = 'sputuijiatouzongzhong', spu.spuvalue, 0)) AS 退夹头重量-->
    <select id="getSpuWeightGroupByGroup" resultType="inks.common.core.domain.ChartPojo">
        SELECT w.id,
               w.GroupName                                                     as name,
               w.GroupUid                                                      as code,
               SUM(IF(spu.spukey = 'spuzongzhongliang', spu.spuvalue, 0))      AS value
        FROM Wk_SubcontractItem as mi
                 CROSS JOIN JSON_TABLE(
                mi.AttributeJson,
                "$[*]"
                COLUMNS (
                    spukey VARCHAR(255) PATH "$.key",
                    spuvalue DECIMAL(18, 4) PATH "$.value" )
                            ) AS spu
                 LEFT JOIN Wk_Subcontract m ON mi.Pid = m.id
                 LEFT JOIN App_Workgroup w ON m.Groupid = w.id
                 LEFT JOIN Mat_Goods g ON mi.Goodsid = g.id
        WHERE JSON_VALID(mi.AttributeJson)
          AND mi.Tenantid = #{tenantid}
          AND (m.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        <if test="filterstr != null">
            ${filterstr}
        </if>
        GROUP BY w.id, w.GroupName
        order by value desc
    </select>
</mapper>

