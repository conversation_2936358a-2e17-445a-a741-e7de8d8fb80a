<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkStationstateMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkStationstatePojo">
        select
          id, BillType, BillDate, BillTitle, Operator, Operatorid, Wpid, WpCode, WpName, Statid, StatCode, StatName, StateRefresh, EndDate, StateJson, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision, TenantName        from Wk_StationState
        where Wk_StationState.id = #{key} and Wk_StationState.Tenantid=#{tid}
    </select>
    <sql id="selectWkStationstateVo">
         select
          id, BillType, BillDate, BillTitle, Operator, Operatorid, Wpid, WpCode, WpName, Statid, StatCode, StatName, StateRefresh, EndDate, StateJson, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision, TenantName        from Wk_StationState
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkStationstatePojo">
        <include refid="selectWkStationstateVo"/>
         where 1 = 1 and Wk_StationState.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_StationState.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.billtype != null ">
   and Wk_StationState.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Wk_StationState.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Wk_StationState.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Wk_StationState.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.wpid != null ">
   and Wk_StationState.Wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null ">
   and Wk_StationState.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null ">
   and Wk_StationState.WpName like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.statid != null ">
   and Wk_StationState.Statid like concat('%', #{SearchPojo.statid}, '%')
</if>
<if test="SearchPojo.statcode != null ">
   and Wk_StationState.StatCode like concat('%', #{SearchPojo.statcode}, '%')
</if>
<if test="SearchPojo.statname != null ">
   and Wk_StationState.StatName like concat('%', #{SearchPojo.statname}, '%')
</if>
<if test="SearchPojo.statejson != null ">
   and Wk_StationState.StateJson like concat('%', #{SearchPojo.statejson}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Wk_StationState.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Wk_StationState.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Wk_StationState.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Wk_StationState.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Wk_StationState.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Wk_StationState.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Wk_StationState.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Wk_StationState.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Wk_StationState.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Wk_StationState.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Wk_StationState.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Wk_StationState.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Wk_StationState.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Wk_StationState.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Wk_StationState.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.billtype != null ">
   or Wk_StationState.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Wk_StationState.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Wk_StationState.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Wk_StationState.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.wpid != null ">
   or Wk_StationState.Wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null ">
   or Wk_StationState.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null ">
   or Wk_StationState.WpName like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.statid != null ">
   or Wk_StationState.Statid like concat('%', #{SearchPojo.statid}, '%')
</if>
<if test="SearchPojo.statcode != null ">
   or Wk_StationState.StatCode like concat('%', #{SearchPojo.statcode}, '%')
</if>
<if test="SearchPojo.statname != null ">
   or Wk_StationState.StatName like concat('%', #{SearchPojo.statname}, '%')
</if>
<if test="SearchPojo.statejson != null ">
   or Wk_StationState.StateJson like concat('%', #{SearchPojo.statejson}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Wk_StationState.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Wk_StationState.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Wk_StationState.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Wk_StationState.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Wk_StationState.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Wk_StationState.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Wk_StationState.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Wk_StationState.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Wk_StationState.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Wk_StationState.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Wk_StationState.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Wk_StationState.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Wk_StationState.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Wk_StationState.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Wk_StationState.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_StationState(id, BillType, BillDate, BillTitle, Operator, Operatorid, Wpid, WpCode, WpName, Statid, StatCode, StatName, StateRefresh, EndDate, StateJson, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision, TenantName)
        values (#{id}, #{billtype}, #{billdate}, #{billtitle}, #{operator}, #{operatorid}, #{wpid}, #{wpcode}, #{wpname}, #{statid}, #{statcode}, #{statname}, #{staterefresh}, #{enddate}, #{statejson}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision}, #{tenantname})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_StationState
        <set>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="wpid != null ">
                Wpid =#{wpid},
            </if>
            <if test="wpcode != null ">
                WpCode =#{wpcode},
            </if>
            <if test="wpname != null ">
                WpName =#{wpname},
            </if>
            <if test="statid != null ">
                Statid =#{statid},
            </if>
            <if test="statcode != null ">
                StatCode =#{statcode},
            </if>
            <if test="statname != null ">
                StatName =#{statname},
            </if>
            <if test="staterefresh != null">
                StateRefresh =#{staterefresh},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="statejson != null ">
                StateJson =#{statejson},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Wk_StationState where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                                                        </mapper>

