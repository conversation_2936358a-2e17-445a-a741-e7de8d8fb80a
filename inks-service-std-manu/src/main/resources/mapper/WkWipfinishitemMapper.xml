<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipfinishitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipfinishitemPojo">
        <include refid="selectWkWipfinishitemVo"/>
        where Wk_WipFinishItem.id = #{key} and Wk_WipFinishItem.Tenantid=#{tid}
    </select>
    <sql id="selectWkWipfinishitemVo">
         select
id, Pid, Wipitemid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, ItemLabel, Moldid, MoldCode, MoldName, WorkType, Workshopid, Workshop, Quantity, Wk<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mrb<PERSON><PERSON><PERSON><PERSON>, Mr<PERSON><PERSON>ec<PERSON><PERSON>, Finish<PERSON>ty, Customer, CustPO, MachUid, MachItemid, MachGroupid, MainPlanUid, MainPlanItemid, WorkUid, WorkItemid, TasksUid, TasksItemid, WorkDate, Remark, AttributeJson, AttributeStr, Closed, SourceType, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Wk_WipFinishItem
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkWipfinishitemPojo">
        <include refid="selectWkWipfinishitemVo"/>
        where Wk_WipFinishItem.Pid = #{Pid} and Wk_WipFinishItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkWipfinishitemPojo">
        <include refid="selectWkWipfinishitemVo"/>
         where 1 = 1 and Wk_WipFinishItem.Tenantid =#{tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_WipFinishItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Wk_WipFinishItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.wipitemid != null and SearchPojo.wipitemid != ''">
   and Wk_WipFinishItem.wipitemid like concat('%', #{SearchPojo.wipitemid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   and Wk_WipFinishItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   and Wk_WipFinishItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   and Wk_WipFinishItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   and Wk_WipFinishItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   and Wk_WipFinishItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.itemlabel != null and SearchPojo.itemlabel != ''">
   and Wk_WipFinishItem.itemlabel like concat('%', #{SearchPojo.itemlabel}, '%')
</if>
<if test="SearchPojo.moldid != null and SearchPojo.moldid != ''">
   and Wk_WipFinishItem.moldid like concat('%', #{SearchPojo.moldid}, '%')
</if>
<if test="SearchPojo.moldcode != null and SearchPojo.moldcode != ''">
   and Wk_WipFinishItem.moldcode like concat('%', #{SearchPojo.moldcode}, '%')
</if>
<if test="SearchPojo.moldname != null and SearchPojo.moldname != ''">
   and Wk_WipFinishItem.moldname like concat('%', #{SearchPojo.moldname}, '%')
</if>
<if test="SearchPojo.worktype != null and SearchPojo.worktype != ''">
   and Wk_WipFinishItem.worktype like concat('%', #{SearchPojo.worktype}, '%')
</if>
<if test="SearchPojo.workshopid != null and SearchPojo.workshopid != ''">
   and Wk_WipFinishItem.workshopid like concat('%', #{SearchPojo.workshopid}, '%')
</if>
<if test="SearchPojo.workshop != null and SearchPojo.workshop != ''">
   and Wk_WipFinishItem.workshop like concat('%', #{SearchPojo.workshop}, '%')
</if>
<if test="SearchPojo.customer != null and SearchPojo.customer != ''">
   and Wk_WipFinishItem.customer like concat('%', #{SearchPojo.customer}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   and Wk_WipFinishItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
   and Wk_WipFinishItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
   and Wk_WipFinishItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
</if>
<if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
   and Wk_WipFinishItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
</if>
<if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
   and Wk_WipFinishItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
</if>
<if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
   and Wk_WipFinishItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
</if>
<if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
   and Wk_WipFinishItem.workuid like concat('%', #{SearchPojo.workuid}, '%')
</if>
<if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
   and Wk_WipFinishItem.workitemid like concat('%', #{SearchPojo.workitemid}, '%')
</if>
<if test="SearchPojo.tasksuid != null and SearchPojo.tasksuid != ''">
   and Wk_WipFinishItem.tasksuid like concat('%', #{SearchPojo.tasksuid}, '%')
</if>
<if test="SearchPojo.tasksitemid != null and SearchPojo.tasksitemid != ''">
   and Wk_WipFinishItem.tasksitemid like concat('%', #{SearchPojo.tasksitemid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Wk_WipFinishItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   and Wk_WipFinishItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
   and Wk_WipFinishItem.attributestr like concat('%', #{SearchPojo.attributestr}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Wk_WipFinishItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Wk_WipFinishItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Wk_WipFinishItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Wk_WipFinishItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Wk_WipFinishItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Wk_WipFinishItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Wk_WipFinishItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Wk_WipFinishItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Wk_WipFinishItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Wk_WipFinishItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Wk_WipFinishItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.wipitemid != null and SearchPojo.wipitemid != ''">
   or Wk_WipFinishItem.Wipitemid like concat('%', #{SearchPojo.wipitemid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   or Wk_WipFinishItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   or Wk_WipFinishItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   or Wk_WipFinishItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   or Wk_WipFinishItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   or Wk_WipFinishItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.itemlabel != null and SearchPojo.itemlabel != ''">
   or Wk_WipFinishItem.ItemLabel like concat('%', #{SearchPojo.itemlabel}, '%')
</if>
<if test="SearchPojo.moldid != null and SearchPojo.moldid != ''">
   or Wk_WipFinishItem.Moldid like concat('%', #{SearchPojo.moldid}, '%')
</if>
<if test="SearchPojo.moldcode != null and SearchPojo.moldcode != ''">
   or Wk_WipFinishItem.MoldCode like concat('%', #{SearchPojo.moldcode}, '%')
</if>
<if test="SearchPojo.moldname != null and SearchPojo.moldname != ''">
   or Wk_WipFinishItem.MoldName like concat('%', #{SearchPojo.moldname}, '%')
</if>
<if test="SearchPojo.worktype != null and SearchPojo.worktype != ''">
   or Wk_WipFinishItem.WorkType like concat('%', #{SearchPojo.worktype}, '%')
</if>
<if test="SearchPojo.workshopid != null and SearchPojo.workshopid != ''">
   or Wk_WipFinishItem.Workshopid like concat('%', #{SearchPojo.workshopid}, '%')
</if>
<if test="SearchPojo.workshop != null and SearchPojo.workshop != ''">
   or Wk_WipFinishItem.Workshop like concat('%', #{SearchPojo.workshop}, '%')
</if>
<if test="SearchPojo.customer != null and SearchPojo.customer != ''">
   or Wk_WipFinishItem.Customer like concat('%', #{SearchPojo.customer}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   or Wk_WipFinishItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
   or Wk_WipFinishItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
   or Wk_WipFinishItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
</if>
<if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
   or Wk_WipFinishItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
</if>
<if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
   or Wk_WipFinishItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
</if>
<if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
   or Wk_WipFinishItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
</if>
<if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
   or Wk_WipFinishItem.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
</if>
<if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
   or Wk_WipFinishItem.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
</if>
<if test="SearchPojo.tasksuid != null and SearchPojo.tasksuid != ''">
   or Wk_WipFinishItem.TasksUid like concat('%', #{SearchPojo.tasksuid}, '%')
</if>
<if test="SearchPojo.tasksitemid != null and SearchPojo.tasksitemid != ''">
   or Wk_WipFinishItem.TasksItemid like concat('%', #{SearchPojo.tasksitemid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Wk_WipFinishItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   or Wk_WipFinishItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
   or Wk_WipFinishItem.AttributeStr like concat('%', #{SearchPojo.attributestr}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Wk_WipFinishItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Wk_WipFinishItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Wk_WipFinishItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Wk_WipFinishItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Wk_WipFinishItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Wk_WipFinishItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Wk_WipFinishItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Wk_WipFinishItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Wk_WipFinishItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Wk_WipFinishItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_WipFinishItem(id, Pid, Wipitemid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, ItemLabel, Moldid, MoldCode, MoldName, WorkType, Workshopid, Workshop, Quantity, WkPcsQty, WkSecQty, MrbPcsQty, MrbSecQty, FinishQty, Customer, CustPO, MachUid, MachItemid, MachGroupid, MainPlanUid, MainPlanItemid, WorkUid, WorkItemid, TasksUid, TasksItemid, WorkDate, Remark, AttributeJson, AttributeStr, Closed, SourceType, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{wipitemid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{itemlabel}, #{moldid}, #{moldcode}, #{moldname}, #{worktype}, #{workshopid}, #{workshop}, #{quantity}, #{wkpcsqty}, #{wksecqty}, #{mrbpcsqty}, #{mrbsecqty}, #{finishqty}, #{customer}, #{custpo}, #{machuid}, #{machitemid}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid}, #{workuid}, #{workitemid}, #{tasksuid}, #{tasksitemid}, #{workdate}, #{remark}, #{attributejson}, #{attributestr}, #{closed}, #{sourcetype}, #{rownum}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipFinishItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="wipitemid != null ">
                Wipitemid = #{wipitemid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="itemlabel != null ">
                ItemLabel = #{itemlabel},
            </if>
            <if test="moldid != null ">
                Moldid = #{moldid},
            </if>
            <if test="moldcode != null ">
                MoldCode = #{moldcode},
            </if>
            <if test="moldname != null ">
                MoldName = #{moldname},
            </if>
            <if test="worktype != null ">
                WorkType = #{worktype},
            </if>
            <if test="workshopid != null ">
                Workshopid = #{workshopid},
            </if>
            <if test="workshop != null ">
                Workshop = #{workshop},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="wkpcsqty != null">
                WkPcsQty = #{wkpcsqty},
            </if>
            <if test="wksecqty != null">
                WkSecQty = #{wksecqty},
            </if>
            <if test="mrbpcsqty != null">
                MrbPcsQty = #{mrbpcsqty},
            </if>
            <if test="mrbsecqty != null">
                MrbSecQty = #{mrbsecqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="workuid != null ">
                WorkUid = #{workuid},
            </if>
            <if test="workitemid != null ">
                WorkItemid = #{workitemid},
            </if>
            <if test="tasksuid != null ">
                TasksUid = #{tasksuid},
            </if>
            <if test="tasksitemid != null ">
                TasksItemid = #{tasksitemid},
            </if>
            <if test="workdate != null">
                WorkDate = #{workdate},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="attributestr != null ">
                AttributeStr = #{attributestr},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Wk_WipFinishItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

