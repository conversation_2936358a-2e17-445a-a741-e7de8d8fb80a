<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkStepprogroupMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkStepprogroupPojo">
        <include refid="selectbillVo"/>
        where Wk_StepProGroup.id = #{key}
          and Wk_StepProGroup.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_StepProGroup.id,
               Wk_StepProGroup.RefNo,
               Wk_StepProGroup.BillType,
               Wk_StepProGroup.BillTitle,
               Wk_StepProGroup.BillDate,
               Wk_StepProGroup.Goodsid,
               Wk_StepProGroup.Spus,
               Wk_StepProGroup.SpuJson,
               Wk_StepProGroup.SpuValue,
               Wk_StepProGroup.CreateBy,
               Wk_StepProGroup.CreateByid,
               Wk_StepProGroup.CreateDate,
               Wk_StepProGroup.Lister,
               Wk_StepProGroup.Listerid,
               Wk_StepProGroup.ModifyDate,
               Wk_StepProGroup.Assessor,
               Wk_StepProGroup.Assessorid,
               Wk_StepProGroup.AssessDate,
               Wk_StepProGroup.Summary,
               Wk_StepProGroup.Custom1,
               Wk_StepProGroup.Custom2,
               Wk_StepProGroup.Custom3,
               Wk_StepProGroup.Custom4,
               Wk_StepProGroup.Custom5,
               Wk_StepProGroup.Custom6,
               Wk_StepProGroup.Custom7,
               Wk_StepProGroup.Custom8,
               Wk_StepProGroup.Custom9,
               Wk_StepProGroup.Custom10,
               Wk_StepProGroup.Tenantid,
               Wk_StepProGroup.TenantName,
               Wk_StepProGroup.Revision
        from Wk_StepProGroup
                 LEFT JOIN Mat_Goods ON Wk_StepProGroup.Goodsid = Mat_Goods.id
    </sql>
    <sql id="selectdetailVo">
        select Wk_StepProGroupItem.id,
               Wk_StepProGroupItem.Pid,
               Wk_StepProGroupItem.StartQty,
               Wk_StepProGroupItem.EndQty,
               Wk_StepProGroupItem.GroupCode,
               Wk_StepProGroupItem.GroupName,
               Wk_StepProGroupItem.FlowDesc,
               Wk_StepProGroupItem.FlowCount,
               Wk_StepProGroupItem.FlowJson,
               Wk_StepProGroupItem.RowNum,
               Wk_StepProGroupItem.Remark,
               Wk_StepProGroupItem.Custom1,
               Wk_StepProGroupItem.Custom2,
               Wk_StepProGroupItem.Custom3,
               Wk_StepProGroupItem.Custom4,
               Wk_StepProGroupItem.Custom5,
               Wk_StepProGroupItem.Custom6,
               Wk_StepProGroupItem.Custom7,
               Wk_StepProGroupItem.Custom8,
               Wk_StepProGroupItem.Custom9,
               Wk_StepProGroupItem.Custom10,
               Wk_StepProGroupItem.Tenantid,
               Wk_StepProGroupItem.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_StepProGroup.RefNo,
               Wk_StepProGroup.BillType,
               Wk_StepProGroup.BillTitle,
               Wk_StepProGroup.BillDate,
               Wk_StepProGroup.Goodsid,
               Wk_StepProGroup.Spus,
               Wk_StepProGroup.SpuJson,
               Wk_StepProGroup.SpuValue,
               Wk_StepProGroup.CreateBy,
               Wk_StepProGroup.CreateByid,
               Wk_StepProGroup.CreateDate,
               Wk_StepProGroup.Lister,
               Wk_StepProGroup.Listerid,
               Wk_StepProGroup.ModifyDate,
               Wk_StepProGroup.Assessor,
               Wk_StepProGroup.Assessorid,
               Wk_StepProGroup.AssessDate,
               Wk_StepProGroup.Summary
        from Wk_StepProGroupItem
                 left JOIN Wk_StepProGroup ON Wk_StepProGroup.id = Wk_StepProGroupItem.Pid
                 LEFT JOIN Mat_Goods ON Wk_StepProGroup.Goodsid = Mat_Goods.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkStepprogroupitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_StepProGroup.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_StepProGroup.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Wk_StepProGroup.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Wk_StepProGroup.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Wk_StepProGroup.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Wk_StepProGroup.goodsid like concat('%',
                #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.spus != null">
            and Wk_StepProGroup.spus like concat('%',
                #{SearchPojo.spus}, '%')
        </if>
        <if test="SearchPojo.spujson != null">
            and Wk_StepProGroup.spujson like concat('%',
                #{SearchPojo.spujson}, '%')
        </if>
        <if test="SearchPojo.spuvalue != null">
            and Wk_StepProGroup.spuvalue like concat('%',
                #{SearchPojo.spuvalue}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_StepProGroup.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_StepProGroup.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_StepProGroup.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_StepProGroup.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Wk_StepProGroup.assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Wk_StepProGroup.assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Wk_StepProGroup.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_StepProGroup.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_StepProGroup.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_StepProGroup.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_StepProGroup.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_StepProGroup.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_StepProGroup.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_StepProGroup.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_StepProGroup.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_StepProGroup.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_StepProGroup.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_StepProGroup.tenantname like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Wk_StepProGroup.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Wk_StepProGroup.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Wk_StepProGroup.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Wk_StepProGroup.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.spus != null">
                or Wk_StepProGroup.Spus like concat('%', #{SearchPojo.spus}, '%')
            </if>
            <if test="SearchPojo.spujson != null">
                or Wk_StepProGroup.SpuJson like concat('%', #{SearchPojo.spujson}, '%')
            </if>
            <if test="SearchPojo.spuvalue != null">
                or Wk_StepProGroup.SpuValue like concat('%', #{SearchPojo.spuvalue}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_StepProGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_StepProGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_StepProGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_StepProGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Wk_StepProGroup.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Wk_StepProGroup.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Wk_StepProGroup.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_StepProGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_StepProGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_StepProGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_StepProGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_StepProGroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_StepProGroup.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_StepProGroup.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_StepProGroup.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_StepProGroup.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_StepProGroup.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_StepProGroup.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkStepprogroupPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_StepProGroup.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_StepProGroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Wk_StepProGroup.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Wk_StepProGroup.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Wk_StepProGroup.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Wk_StepProGroup.Goodsid like concat('%',
                #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.spus != null">
            and Wk_StepProGroup.Spus like concat('%',
                #{SearchPojo.spus}, '%')
        </if>
        <if test="SearchPojo.spujson != null">
            and Wk_StepProGroup.SpuJson like concat('%',
                #{SearchPojo.spujson}, '%')
        </if>
        <if test="SearchPojo.spuvalue != null">
            and Wk_StepProGroup.SpuValue like concat('%',
                #{SearchPojo.spuvalue}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_StepProGroup.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_StepProGroup.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_StepProGroup.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_StepProGroup.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Wk_StepProGroup.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Wk_StepProGroup.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Wk_StepProGroup.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_StepProGroup.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_StepProGroup.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_StepProGroup.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_StepProGroup.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_StepProGroup.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_StepProGroup.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_StepProGroup.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_StepProGroup.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_StepProGroup.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_StepProGroup.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_StepProGroup.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Wk_StepProGroup.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Wk_StepProGroup.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Wk_StepProGroup.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Wk_StepProGroup.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.spus != null">
                or Wk_StepProGroup.Spus like concat('%', #{SearchPojo.spus}, '%')
            </if>
            <if test="SearchPojo.spujson != null">
                or Wk_StepProGroup.SpuJson like concat('%', #{SearchPojo.spujson}, '%')
            </if>
            <if test="SearchPojo.spuvalue != null">
                or Wk_StepProGroup.SpuValue like concat('%', #{SearchPojo.spuvalue}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_StepProGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_StepProGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_StepProGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_StepProGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Wk_StepProGroup.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Wk_StepProGroup.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Wk_StepProGroup.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_StepProGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_StepProGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_StepProGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_StepProGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_StepProGroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_StepProGroup.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_StepProGroup.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_StepProGroup.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_StepProGroup.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_StepProGroup.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_StepProGroup.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_StepProGroup(id, RefNo, BillType, BillTitle, BillDate, Goodsid, Spus, SpuJson, SpuValue,
                                    CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor,
                                    Assessorid, AssessDate, Summary, Custom1, Custom2, Custom3, Custom4, Custom5,
                                    Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{goodsid}, #{spus}, #{spujson}, #{spuvalue},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor},
                #{assessorid}, #{assessdate}, #{summary}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_StepProGroup
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="spus != null">
                Spus =#{spus},
            </if>
            <if test="spujson != null">
                SpuJson =#{spujson},
            </if>
            <if test="spuvalue != null">
                SpuValue =#{spuvalue},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_StepProGroup
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_StepProGroup
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkStepprogroupPojo">
        select id
        from Wk_StepProGroupItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getidBySpuJson" resultType="java.lang.String">
        select id
        from (SELECT id,
                     goodsid,
                     jt.spukey,
                     jt.startvalue,
                     jt.endvalue
              FROM Wk_StepProGroup
                       CROSS JOIN JSON_TABLE(
                      Wk_StepProGroup.SpuJson,
                      "$[*]"
                      COLUMNS (
                          spukey VARCHAR(255) PATH "$.key",
                          startvalue VARCHAR(255) PATH "$.startval",
                          endvalue VARCHAR(255) PATH "$.endval"
                          )
                                  ) AS jt
              where Wk_StepProGroup.Goodsid = #{goodsid}
                and Wk_StepProGroup.Tenantid = #{tid}) as t
        where ${sql}
        group by id
        having Count(0) = #{size}
        order by Count(0)
                desc
        limit 1
    </select>

    <select id="getFlowJson" resultType="java.lang.String">
        select FlowJson
        from Wk_StepProGroupItem
        where Pid = #{wkStepprogroupId}
        and #{quantity} between StartQty and EndQty
        and Tenantid = #{tid} limit 1
    </select>
</mapper>

