<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSccompleteMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSccompletePojo">
        select App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Wk_ScComplete.id,
               Wk_ScComplete.RefNo,
               Wk_ScComplete.BillType,
               Wk_ScComplete.BillDate,
               Wk_ScComplete.BillTitle,
               Wk_ScComplete.Operator,
               Wk_ScComplete.Groupid,
               Wk_ScComplete.Summary,
               Wk_ScComplete.CreateBy,
               Wk_ScComplete.CreateByid,
               Wk_ScComplete.CreateDate,
               Wk_ScComplete.Lister,
               Wk_ScComplete.Listerid,
               Wk_ScComplete.ModifyDate,
               Wk_ScComplete.Assessor,
               Wk_ScComplete.Assessorid,
               Wk_ScComplete.AssessDate,
               Wk_ScComplete.ItemCount,
               Wk_ScComplete.DisannulCount,
               Wk_ScComplete.FinishCount,
               Wk_ScComplete.PrintCount,
               Wk_ScComplete.Custom1,
               Wk_ScComplete.BillStateCode,
               Wk_ScComplete.BillStateDate,
               Wk_ScComplete.BillTaxAmount,
               Wk_ScComplete.BillAmount,
               Wk_ScComplete.BillTaxTotal,
               Wk_ScComplete.BillPaid,
               Wk_ScComplete.Custom2,
               Wk_ScComplete.Custom3,
               Wk_ScComplete.Custom4,
               Wk_ScComplete.Custom5,
               Wk_ScComplete.Custom6,
               Wk_ScComplete.Custom7,
               Wk_ScComplete.Custom8,
               Wk_ScComplete.Custom9,
               Wk_ScComplete.Custom10,
               Wk_ScComplete.Tenantid,
               Wk_ScComplete.TenantName,
               Wk_ScComplete.Revision
        from Wk_ScComplete
                 left join App_Workgroup on Wk_ScComplete.Groupid = App_Workgroup.id
        where Wk_ScComplete.id = #{key}
          and Wk_ScComplete.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Wk_ScComplete.id,
               Wk_ScComplete.RefNo,
               Wk_ScComplete.BillType,
               Wk_ScComplete.BillDate,
               Wk_ScComplete.BillTitle,
               Wk_ScComplete.Operator,
               Wk_ScComplete.Groupid,
               Wk_ScComplete.Summary,
               Wk_ScComplete.CreateBy,
               Wk_ScComplete.CreateByid,
               Wk_ScComplete.CreateDate,
               Wk_ScComplete.Lister,
               Wk_ScComplete.Listerid,
               Wk_ScComplete.ModifyDate,
               Wk_ScComplete.Assessor,
               Wk_ScComplete.Assessorid,
               Wk_ScComplete.AssessDate,
               Wk_ScComplete.ItemCount,
               Wk_ScComplete.DisannulCount,
               Wk_ScComplete.FinishCount,
               Wk_ScComplete.PrintCount,
               Wk_ScComplete.Custom1,
               Wk_ScComplete.BillStateCode,
               Wk_ScComplete.BillStateDate,
               Wk_ScComplete.BillTaxAmount,
               Wk_ScComplete.BillAmount,
               Wk_ScComplete.BillTaxTotal,
               Wk_ScComplete.BillPaid,
               Wk_ScComplete.Custom2,
               Wk_ScComplete.Custom3,
               Wk_ScComplete.Custom4,
               Wk_ScComplete.Custom5,
               Wk_ScComplete.Custom6,
               Wk_ScComplete.Custom7,
               Wk_ScComplete.Custom8,
               Wk_ScComplete.Custom9,
               Wk_ScComplete.Custom10,
               Wk_ScComplete.Tenantid,
               Wk_ScComplete.TenantName,
               Wk_ScComplete.Revision
        from Wk_ScComplete
                 left join App_Workgroup on Wk_ScComplete.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        select Wk_ScCompleteItem.id,
               Wk_ScCompleteItem.Pid,
               Wk_ScCompleteItem.WorkDate,
               Wk_ScCompleteItem.WorkUid,
               Wk_ScCompleteItem.WorkItemid,
               Wk_ScCompleteItem.Goodsid,
               Wk_ScCompleteItem.ItemCode,
               Wk_ScCompleteItem.ItemName,
               Wk_ScCompleteItem.ItemSpec,
               Wk_ScCompleteItem.ItemUnit,
               Wk_ScCompleteItem.SubItemid,
               Wk_ScCompleteItem.SubUse,
               Wk_ScCompleteItem.SubUnit,
               Wk_ScCompleteItem.SubQty,
               Wk_ScCompleteItem.TaxPrice,
               Wk_ScCompleteItem.Price,
               Wk_ScCompleteItem.Quantity,
               Wk_ScCompleteItem.TaxAmount,
               Wk_ScCompleteItem.Amount,
               Wk_ScCompleteItem.TaxTotal,
               Wk_ScCompleteItem.ItemTaxrate,
               Wk_ScCompleteItem.StartDate,
               Wk_ScCompleteItem.PlanDate,
               Wk_ScCompleteItem.FinishQty,
               Wk_ScCompleteItem.FinishHour,
               Wk_ScCompleteItem.MrbQty,
               Wk_ScCompleteItem.EnabledMark,
               Wk_ScCompleteItem.Closed,
               Wk_ScCompleteItem.Remark,
               Wk_ScCompleteItem.StateCode,
               Wk_ScCompleteItem.StateDate,
               Wk_ScCompleteItem.RowNum,
               Wk_ScCompleteItem.MachUid,
               Wk_ScCompleteItem.MachItemid,
               Wk_ScCompleteItem.MachGroupid,
               Wk_ScCompleteItem.MrpUid,
               Wk_ScCompleteItem.MrpItemid,
               Wk_ScCompleteItem.Customer,
               Wk_ScCompleteItem.CustPO,
               Wk_ScCompleteItem.MainPlanUid,
               Wk_ScCompleteItem.MainPlanItemid,
               Wk_ScCompleteItem.CiteUid,
               Wk_ScCompleteItem.CiteItemid,
               Wk_ScCompleteItem.Location,
               Wk_ScCompleteItem.BatchNo,
               Wk_ScCompleteItem.AttributeJson,
               Wk_ScCompleteItem.AttributeStr,
               Wk_ScCompleteItem.InvoQty,
               Wk_ScCompleteItem.InvoClosed,
               Wk_ScCompleteItem.SourceType,
               Wk_ScCompleteItem.SubByPcs,
               Wk_ScCompleteItem.Custom1,
               Wk_ScCompleteItem.Custom2,
               Wk_ScCompleteItem.Custom3,
               Wk_ScCompleteItem.Custom4,
               Wk_ScCompleteItem.Custom5,
               Wk_ScCompleteItem.Custom6,
               Wk_ScCompleteItem.Custom7,
               Wk_ScCompleteItem.Custom8,
               Wk_ScCompleteItem.Custom9,
               Wk_ScCompleteItem.Custom10,
               Wk_ScCompleteItem.Tenantid,
               Wk_ScCompleteItem.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_ScComplete.RefNo,
               Wk_ScComplete.BillType,
               Wk_ScComplete.BillDate,
               Wk_ScComplete.BillTitle,
               Wk_ScComplete.Groupid,
               Wk_ScComplete.Operator,
               Wk_ScComplete.CreateBy,
               Wk_ScComplete.Lister,
               Wk_ScComplete.Assessor,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        from Wk_ScCompleteItem
                 Left Join Wk_ScComplete on Wk_ScCompleteItem.Pid = Wk_ScComplete.id
                 LEFT JOIN Mat_Goods ON Wk_ScCompleteItem.Goodsid = Mat_Goods.id
                 left join App_Workgroup on Wk_ScComplete.Groupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSccompleteitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_ScComplete.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_ScComplete.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.citeuid != null">
            and Wk_ScCompleteItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null">
            and Wk_ScCompleteItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.refno != null">
            and Wk_ScComplete.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Wk_ScComplete.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Wk_ScComplete.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Wk_ScComplete.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Wk_ScComplete.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Wk_ScComplete.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_ScComplete.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_ScComplete.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_ScComplete.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_ScComplete.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Wk_ScComplete.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Wk_ScComplete.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_ScComplete.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_ScComplete.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_ScComplete.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_ScComplete.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_ScComplete.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_ScComplete.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_ScComplete.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_ScComplete.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_ScComplete.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_ScComplete.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_ScComplete.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Wk_ScComplete.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.citeuid != null">
                or Wk_ScCompleteItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null">
                or Wk_ScCompleteItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Wk_ScComplete.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Wk_ScComplete.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Wk_ScComplete.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Wk_ScComplete.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Wk_ScComplete.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_ScComplete.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_ScComplete.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_ScComplete.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_ScComplete.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Wk_ScComplete.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Wk_ScComplete.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_ScComplete.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_ScComplete.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_ScComplete.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_ScComplete.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_ScComplete.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_ScComplete.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_ScComplete.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_ScComplete.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_ScComplete.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_ScComplete.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_ScComplete.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsorfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSccompletePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_ScComplete.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_ScComplete.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Wk_ScComplete.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Wk_ScComplete.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Wk_ScComplete.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Wk_ScComplete.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Wk_ScComplete.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Wk_ScComplete.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_ScComplete.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_ScComplete.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_ScComplete.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_ScComplete.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Wk_ScComplete.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Wk_ScComplete.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_ScComplete.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_ScComplete.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_ScComplete.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_ScComplete.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_ScComplete.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_ScComplete.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_ScComplete.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_ScComplete.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_ScComplete.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_ScComplete.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_ScComplete.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Wk_ScComplete.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Wk_ScComplete.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Wk_ScComplete.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Wk_ScComplete.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Wk_ScComplete.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Wk_ScComplete.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_ScComplete.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_ScComplete.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_ScComplete.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_ScComplete.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Wk_ScComplete.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Wk_ScComplete.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_ScComplete.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_ScComplete.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_ScComplete.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_ScComplete.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_ScComplete.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_ScComplete.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_ScComplete.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_ScComplete.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_ScComplete.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_ScComplete.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_ScComplete.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsorfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_ScComplete(id, RefNo, BillType, BillDate, BillTitle, Operator, Groupid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, ItemCount, DisannulCount, FinishCount, PrintCount, Custom1, BillStateCode, BillStateDate, BillTaxAmount, BillAmount, BillTaxTotal, BillPaid, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{operator}, #{groupid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{itemcount}, #{disannulcount}, #{finishcount}, #{printcount}, #{custom1}, #{billstatecode}, #{billstatedate}, #{billtaxamount}, #{billamount}, #{billtaxtotal}, #{billpaid}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_ScComplete
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="billstatecode != null ">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billpaid != null">
                BillPaid =#{billpaid},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Wk_ScComplete where id = #{key} and Tenantid=#{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_ScComplete SET
        Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision+1
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkSccompletePojo">
        select
        id
        from Wk_ScCompleteItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getDelMatIds" resultType="java.lang.String">
        select
        id
        from Wk_ScCompleteMat
        where Pid = #{id}
        <if test="mat != null and mat.size() > 0">
            and id not in
            <foreach collection="mat" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <update id="updateFinishCount">
        update Wk_ScComplete
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_ScCompleteItem
                                   where Wk_ScCompleteItem.Pid = #{key}
                                     and Wk_ScCompleteItem.Tenantid = #{tid}
                                     and (Wk_ScCompleteItem.FinishQty >= Wk_ScCompleteItem.Quantity or
                                          Wk_ScCompleteItem.Closed = 1)), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--刷新打印次数-->
    <update id="updatePrintcount">
        update Wk_ScComplete
        SET PrintCount = #{printcount}
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>

    <update id="updateDisannulCount">
        update Wk_ScComplete
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Wk_ScCompleteItem
                                     where Wk_ScCompleteItem.Pid = #{key}
                                       and Wk_ScCompleteItem.Tenantid = #{tid}
                                       and Wk_ScCompleteItem.DisannulMark = 1), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
</mapper>

