<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkCompleteitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkCompleteitemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_CompleteItem.id,
            Wk_CompleteItem.Pid,
            Wk_CompleteItem.WorkDate,
            Wk_CompleteItem.WorkUid,
            Wk_CompleteItem.WorkItemid,
            Wk_CompleteItem.Goodsid,
            Wk_CompleteItem.ItemCode,
            Wk_CompleteItem.ItemName,
            Wk_CompleteItem.ItemSpec,
            Wk_CompleteItem.ItemUnit,
            Wk_CompleteItem.Quantity,
            Wk_CompleteItem.SubItemid,
            Wk_CompleteItem.SubUse,
            Wk_CompleteItem.SubUnit,
            Wk_CompleteItem.SubQty,
            Wk_CompleteItem.TaxPrice,
            Wk_CompleteItem.TaxAmount,
            Wk_CompleteItem.Price,
            Wk_CompleteItem.Amount,
            Wk_CompleteItem.TaxTotal,
            Wk_CompleteItem.ItemTaxrate,
            Wk_CompleteItem.StartDate,
            Wk_CompleteItem.PlanDate,
            Wk_CompleteItem.FinishQty,
            Wk_CompleteItem.FinishHour,
            Wk_CompleteItem.MrbQty,
            Wk_CompleteItem.EnabledMark,
            Wk_CompleteItem.Closed,
            Wk_CompleteItem.Remark,
            Wk_CompleteItem.StateCode,
            Wk_CompleteItem.StateDate,
            Wk_CompleteItem.RowNum,
            Wk_CompleteItem.MachUid,
            Wk_CompleteItem.MachItemid,
            Wk_CompleteItem.MachGroupid,
            Wk_CompleteItem.MrpUid,
            Wk_CompleteItem.MrpItemid,
            Wk_CompleteItem.Customer,
            Wk_CompleteItem.CustPO,
            Wk_CompleteItem.MainPlanUid,
            Wk_CompleteItem.MainPlanItemid,
            Wk_CompleteItem.CiteUid,
            Wk_CompleteItem.CiteItemid,
            Wk_CompleteItem.Location,
            Wk_CompleteItem.BatchNo,
            Wk_CompleteItem.AttributeJson,
            Wk_CompleteItem.Custom1,
            Wk_CompleteItem.Custom2,
            Wk_CompleteItem.Custom3,
            Wk_CompleteItem.Custom4,
            Wk_CompleteItem.Custom5,
            Wk_CompleteItem.Custom6,
            Wk_CompleteItem.Custom7,
            Wk_CompleteItem.Custom8,
            Wk_CompleteItem.Custom9,
            Wk_CompleteItem.Custom10,
            Wk_CompleteItem.Tenantid,
            Wk_CompleteItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_CompleteItem ON Mat_Goods.id = Wk_CompleteItem.Goodsid
        where Wk_CompleteItem.id = #{key}
          and Wk_CompleteItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkCompleteitemVo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_CompleteItem.id,
            Wk_CompleteItem.Pid,
            Wk_CompleteItem.WorkDate,
            Wk_CompleteItem.WorkUid,
            Wk_CompleteItem.WorkItemid,
            Wk_CompleteItem.Goodsid,
            Wk_CompleteItem.ItemCode,
            Wk_CompleteItem.ItemName,
            Wk_CompleteItem.ItemSpec,
            Wk_CompleteItem.ItemUnit,
            Wk_CompleteItem.Quantity,
            Wk_CompleteItem.SubItemid,
            Wk_CompleteItem.SubUse,
            Wk_CompleteItem.SubUnit,
            Wk_CompleteItem.SubQty,
            Wk_CompleteItem.TaxPrice,
            Wk_CompleteItem.TaxAmount,
            Wk_CompleteItem.Price,
            Wk_CompleteItem.Amount,
            Wk_CompleteItem.TaxTotal,
            Wk_CompleteItem.ItemTaxrate,
            Wk_CompleteItem.StartDate,
            Wk_CompleteItem.PlanDate,
            Wk_CompleteItem.FinishQty,
            Wk_CompleteItem.FinishHour,
            Wk_CompleteItem.MrbQty,
            Wk_CompleteItem.EnabledMark,
            Wk_CompleteItem.Closed,
            Wk_CompleteItem.Remark,
            Wk_CompleteItem.StateCode,
            Wk_CompleteItem.StateDate,
            Wk_CompleteItem.RowNum,
            Wk_CompleteItem.MachUid,
            Wk_CompleteItem.MachItemid,
            Wk_CompleteItem.MachGroupid,
            Wk_CompleteItem.MrpUid,
            Wk_CompleteItem.MrpItemid,
            Wk_CompleteItem.Customer,
            Wk_CompleteItem.CustPO,
            Wk_CompleteItem.MainPlanUid,
            Wk_CompleteItem.MainPlanItemid,
            Wk_CompleteItem.CiteUid,
            Wk_CompleteItem.CiteItemid,
            Wk_CompleteItem.Location,
            Wk_CompleteItem.BatchNo,
            Wk_CompleteItem.AttributeJson,
            Wk_CompleteItem.Custom1,
            Wk_CompleteItem.Custom2,
            Wk_CompleteItem.Custom3,
            Wk_CompleteItem.Custom4,
            Wk_CompleteItem.Custom5,
            Wk_CompleteItem.Custom6,
            Wk_CompleteItem.Custom7,
            Wk_CompleteItem.Custom8,
            Wk_CompleteItem.Custom9,
            Wk_CompleteItem.Custom10,
            Wk_CompleteItem.Tenantid,
            Wk_CompleteItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_CompleteItem ON Mat_Goods.id = Wk_CompleteItem.Goodsid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkCompleteitemPojo">
        <include refid="selectWkCompleteitemVo"/>
        where 1 = 1 and Wk_CompleteItem.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_CompleteItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_CompleteItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.workdate != null and SearchPojo.workdate != ''">
            and Wk_CompleteItem.workdate like concat('%', #{SearchPojo.workdate}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Wk_CompleteItem.workuid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            and Wk_CompleteItem.workitemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_CompleteItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_CompleteItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_CompleteItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_CompleteItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_CompleteItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
            and Wk_CompleteItem.subitemid like concat('%', #{SearchPojo.subitemid}, '%')
        </if>
        <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
            and Wk_CompleteItem.subuse like concat('%', #{SearchPojo.subuse}, '%')
        </if>
        <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
            and Wk_CompleteItem.subunit like concat('%', #{SearchPojo.subunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_CompleteItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_CompleteItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_CompleteItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_CompleteItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_CompleteItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Wk_CompleteItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Wk_CompleteItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_CompleteItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_CompleteItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_CompleteItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_CompleteItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Wk_CompleteItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Wk_CompleteItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Wk_CompleteItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Wk_CompleteItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_CompleteItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_CompleteItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_CompleteItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_CompleteItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_CompleteItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_CompleteItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_CompleteItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_CompleteItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_CompleteItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_CompleteItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_CompleteItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_CompleteItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.workdate != null and SearchPojo.workdate != ''">
                or Wk_CompleteItem.WorkDate like concat('%', #{SearchPojo.workdate}, '%')
            </if>
            <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
                or Wk_CompleteItem.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
                or Wk_CompleteItem.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_CompleteItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_CompleteItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_CompleteItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_CompleteItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_CompleteItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
                or Wk_CompleteItem.SubItemid like concat('%', #{SearchPojo.subitemid}, '%')
            </if>
            <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
                or Wk_CompleteItem.SubUse like concat('%', #{SearchPojo.subuse}, '%')
            </if>
            <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
                or Wk_CompleteItem.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_CompleteItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Wk_CompleteItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_CompleteItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_CompleteItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_CompleteItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Wk_CompleteItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Wk_CompleteItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_CompleteItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_CompleteItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_CompleteItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_CompleteItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Wk_CompleteItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Wk_CompleteItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Wk_CompleteItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Wk_CompleteItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_CompleteItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_CompleteItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_CompleteItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_CompleteItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_CompleteItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_CompleteItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_CompleteItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_CompleteItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_CompleteItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_CompleteItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_CompleteItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkCompleteitemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_CompleteItem.id,
            Wk_CompleteItem.Pid,
            Wk_CompleteItem.WorkDate,
            Wk_CompleteItem.WorkUid,
            Wk_CompleteItem.WorkItemid,
            Wk_CompleteItem.Goodsid,
            Wk_CompleteItem.ItemCode,
            Wk_CompleteItem.ItemName,
            Wk_CompleteItem.ItemSpec,
            Wk_CompleteItem.ItemUnit,
            Wk_CompleteItem.Quantity,
            Wk_CompleteItem.SubItemid,
            Wk_CompleteItem.SubUse,
            Wk_CompleteItem.SubUnit,
            Wk_CompleteItem.SubQty,
            Wk_CompleteItem.TaxPrice,
            Wk_CompleteItem.TaxAmount,
            Wk_CompleteItem.Price,
            Wk_CompleteItem.Amount,
            Wk_CompleteItem.TaxTotal,
            Wk_CompleteItem.ItemTaxrate,
            Wk_CompleteItem.StartDate,
            Wk_CompleteItem.PlanDate,
            Wk_CompleteItem.FinishQty,
            Wk_CompleteItem.FinishHour,
            Wk_CompleteItem.MrbQty,
            Wk_CompleteItem.EnabledMark,
            Wk_CompleteItem.Closed,
            Wk_CompleteItem.Remark,
            Wk_CompleteItem.StateCode,
            Wk_CompleteItem.StateDate,
            Wk_CompleteItem.RowNum,
            Wk_CompleteItem.MachUid,
            Wk_CompleteItem.MachItemid,
            Wk_CompleteItem.MachGroupid,
            Wk_CompleteItem.MrpUid,
            Wk_CompleteItem.MrpItemid,
            Wk_CompleteItem.Customer,
            Wk_CompleteItem.CustPO,
            Wk_CompleteItem.MainPlanUid,
            Wk_CompleteItem.MainPlanItemid,
            Wk_CompleteItem.CiteUid,
            Wk_CompleteItem.CiteItemid,
            Wk_CompleteItem.Location,
            Wk_CompleteItem.BatchNo,
            Wk_CompleteItem.AttributeJson,
            Wk_CompleteItem.Custom1,
            Wk_CompleteItem.Custom2,
            Wk_CompleteItem.Custom3,
            Wk_CompleteItem.Custom4,
            Wk_CompleteItem.Custom5,
            Wk_CompleteItem.Custom6,
            Wk_CompleteItem.Custom7,
            Wk_CompleteItem.Custom8,
            Wk_CompleteItem.Custom9,
            Wk_CompleteItem.Custom10,
            Wk_CompleteItem.Tenantid,
            Wk_CompleteItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_CompleteItem ON Mat_Goods.id = Wk_CompleteItem.Goodsid
        where Wk_CompleteItem.Pid = #{Pid}
          and Wk_CompleteItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_CompleteItem(id, Pid, WorkDate, WorkUid, WorkItemid, Goodsid, ItemCode, ItemName, ItemSpec,
                                    ItemUnit, Quantity, SubItemid, SubUse, SubUnit, SubQty, TaxPrice, TaxAmount, Price,
                                    Amount, TaxTotal, ItemTaxrate, StartDate, PlanDate, FinishQty, FinishHour, MrbQty,
                                    EnabledMark, Closed, Remark, StateCode, StateDate, RowNum, MachUid, MachItemid,
                                    MachGroupid, MrpUid, MrpItemid, Customer, CustPO, MainPlanUid, MainPlanItemid,
                                    CiteUid, CiteItemid, Location, BatchNo, AttributeJson, Custom1, Custom2, Custom3,
                                    Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{workdate}, #{workuid}, #{workitemid}, #{goodsid}, #{itemcode}, #{itemname},
                #{itemspec}, #{itemunit}, #{quantity}, #{subitemid}, #{subuse}, #{subunit}, #{subqty}, #{taxprice},
                #{taxamount}, #{price}, #{amount}, #{taxtotal}, #{itemtaxrate}, #{startdate}, #{plandate}, #{finishqty},
                #{finishhour}, #{mrbqty}, #{enabledmark}, #{closed}, #{remark}, #{statecode}, #{statedate}, #{rownum},
                #{machuid}, #{machitemid}, #{machgroupid}, #{mrpuid}, #{mrpitemid}, #{customer}, #{custpo},
                #{mainplanuid}, #{mainplanitemid}, #{citeuid}, #{citeitemid}, #{location}, #{batchno}, #{attributejson},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_CompleteItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="workdate != null ">
                WorkDate = #{workdate},
            </if>
            <if test="workuid != null ">
                WorkUid = #{workuid},
            </if>
            <if test="workitemid != null ">
                WorkItemid = #{workitemid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="subitemid != null ">
                SubItemid = #{subitemid},
            </if>
            <if test="subuse != null ">
                SubUse = #{subuse},
            </if>
            <if test="subunit != null ">
                SubUnit = #{subunit},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="finishhour != null">
                FinishHour = #{finishhour},
            </if>
            <if test="mrbqty != null">
                MrbQty = #{mrbqty},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_CompleteItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

