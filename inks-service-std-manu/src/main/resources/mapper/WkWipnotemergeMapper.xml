<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipnotemergeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipnotemergePojo">
        select id,
               Mergeid,
               Wipid,
               Goodsid,
               Quantity,
               WorkUid,
               WorkItemid,
               MainMark,
               RowNum,
               MachType,
               MachUid,
               MachItemid,
               MachGroupid,
               Customer,
               CustPO,
               CiteUid,
               CiteItemid,
               MainPlanUid,
               MainPlanItemid,
               AttributeJson,
               WkPcsQty,
               WkSecQty,
               SourceType,
               Remark,
               CreateBy,
               CreateByid,
               <PERSON>reateDate,
               Lister,
               <PERSON>erid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Wk_WipNoteMerge
        where Wk_WipNoteMerge.id = #{key}
          and Wk_WipNoteMerge.Tenantid = #{tid}
    </select>
    <sql id="selectWkWipnotemergeVo">
        select id,
               Mergeid,
               Wipid,
               Goodsid,
               Quantity,
               WorkUid,
               WorkItemid,
               MainMark,
               RowNum,
               MachType,
               MachUid,
               MachItemid,
               MachGroupid,
               Customer,
               CustPO,
               CiteUid,
               CiteItemid,
               MainPlanUid,
               MainPlanItemid,
               AttributeJson,
               WkPcsQty,
               WkSecQty,
               SourceType,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Wk_WipNoteMerge
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipnotemergePojo">
        <include refid="selectWkWipnotemergeVo"/>
        where 1 = 1 and Wk_WipNoteMerge.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipNoteMerge.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.mergeid != null ">
            and Wk_WipNoteMerge.Mergeid like concat('%', #{SearchPojo.mergeid}, '%')
        </if>
        <if test="SearchPojo.wipid != null ">
            and Wk_WipNoteMerge.Wipid like concat('%', #{SearchPojo.wipid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null ">
            and Wk_WipNoteMerge.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.workuid != null ">
            and Wk_WipNoteMerge.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null ">
            and Wk_WipNoteMerge.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.machtype != null ">
            and Wk_WipNoteMerge.MachType like concat('%', #{SearchPojo.machtype}, '%')
        </if>
        <if test="SearchPojo.machuid != null ">
            and Wk_WipNoteMerge.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null ">
            and Wk_WipNoteMerge.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null ">
            and Wk_WipNoteMerge.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.customer != null ">
            and Wk_WipNoteMerge.Customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null ">
            and Wk_WipNoteMerge.CustPO like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.citeuid != null ">
            and Wk_WipNoteMerge.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null ">
            and Wk_WipNoteMerge.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null ">
            and Wk_WipNoteMerge.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null ">
            and Wk_WipNoteMerge.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null ">
            and Wk_WipNoteMerge.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Wk_WipNoteMerge.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_WipNoteMerge.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_WipNoteMerge.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_WipNoteMerge.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_WipNoteMerge.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_WipNoteMerge.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_WipNoteMerge.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_WipNoteMerge.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_WipNoteMerge.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_WipNoteMerge.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_WipNoteMerge.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_WipNoteMerge.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_WipNoteMerge.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_WipNoteMerge.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_WipNoteMerge.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_WipNoteMerge.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.mergeid != null ">
                or Wk_WipNoteMerge.Mergeid like concat('%', #{SearchPojo.mergeid}, '%')
            </if>
            <if test="SearchPojo.wipid != null ">
                or Wk_WipNoteMerge.Wipid like concat('%', #{SearchPojo.wipid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null ">
                or Wk_WipNoteMerge.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.workuid != null ">
                or Wk_WipNoteMerge.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workitemid != null ">
                or Wk_WipNoteMerge.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.machtype != null ">
                or Wk_WipNoteMerge.MachType like concat('%', #{SearchPojo.machtype}, '%')
            </if>
            <if test="SearchPojo.machuid != null ">
                or Wk_WipNoteMerge.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null ">
                or Wk_WipNoteMerge.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null ">
                or Wk_WipNoteMerge.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.customer != null ">
                or Wk_WipNoteMerge.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null ">
                or Wk_WipNoteMerge.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.citeuid != null ">
                or Wk_WipNoteMerge.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null ">
                or Wk_WipNoteMerge.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null ">
                or Wk_WipNoteMerge.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null ">
                or Wk_WipNoteMerge.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.attributejson != null ">
                or Wk_WipNoteMerge.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Wk_WipNoteMerge.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_WipNoteMerge.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_WipNoteMerge.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_WipNoteMerge.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_WipNoteMerge.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_WipNoteMerge.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_WipNoteMerge.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_WipNoteMerge.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_WipNoteMerge.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_WipNoteMerge.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_WipNoteMerge.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_WipNoteMerge.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_WipNoteMerge.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_WipNoteMerge.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_WipNoteMerge.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_WipNoteMerge.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_WipNoteMerge(id, Mergeid, Wipid, Goodsid, Quantity, WorkUid, WorkItemid, MainMark, RowNum,
                                    MachType, MachUid, MachItemid, MachGroupid, Customer, CustPO, CiteUid, CiteItemid,
                                    MainPlanUid, MainPlanItemid, AttributeJson, WkPcsQty, WkSecQty, SourceType, Remark,
                                    CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2,
                                    Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                    TenantName, Revision)
        values (#{id}, #{mergeid}, #{wipid}, #{goodsid}, #{quantity}, #{workuid}, #{workitemid}, #{mainmark}, #{rownum},
                #{machtype}, #{machuid}, #{machitemid}, #{machgroupid}, #{customer}, #{custpo}, #{citeuid},
                #{citeitemid}, #{mainplanuid}, #{mainplanitemid}, #{attributejson}, #{wkpcsqty}, #{wksecqty},
                #{sourcetype}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipNoteMerge
        <set>
            <if test="mergeid != null ">
                Mergeid =#{mergeid},
            </if>
            <if test="wipid != null ">
                Wipid =#{wipid},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="workuid != null ">
                WorkUid =#{workuid},
            </if>
            <if test="workitemid != null ">
                WorkItemid =#{workitemid},
            </if>
            <if test="mainmark != null">
                MainMark =#{mainmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="machtype != null ">
                MachType =#{machtype},
            </if>
            <if test="machuid != null ">
                MachUid =#{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid =#{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid =#{machgroupid},
            </if>
            <if test="customer != null ">
                Customer =#{customer},
            </if>
            <if test="custpo != null ">
                CustPO =#{custpo},
            </if>
            <if test="citeuid != null ">
                CiteUid =#{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid =#{citeitemid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid =#{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid =#{mainplanitemid},
            </if>
            <if test="attributejson != null ">
                AttributeJson =#{attributejson},
            </if>
            <if test="wkpcsqty != null">
                WkPcsQty =#{wkpcsqty},
            </if>
            <if test="wksecqty != null">
                WkSecQty =#{wksecqty},
            </if>
            <if test="sourcetype != null">
                SourceType =#{sourcetype},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>


    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WipNoteMerge
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <update id="updateMergeid">
        update Wk_WipNoteMerge
        set Mergeid = #{insertId}
        where id in (${mergeIds})
          and Tenantid = #{tid}
    </update>


    <select id="getEntityByMachItemid" resultType="inks.service.std.manu.domain.pojo.WkWipnotemergePojo">
        select id
        from Wk_WipNoteMerge
        where MachItemid = #{machitemid} and Tenantid = #{tid} limit 1
    </select>

</mapper>

