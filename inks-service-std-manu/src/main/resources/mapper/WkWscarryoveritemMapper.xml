<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWscarryoveritemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWscarryoveritemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_WsCarryoverItem.id,
               Wk_WsCarryoverItem.Pid,
               Wk_WsCarryoverItem.Goodsid,
               Wk_WsCarryoverItem.ItemCode,
               Wk_WsCarryoverItem.ItemName,
               Wk_WsCarryoverItem.ItemSpec,
               Wk_WsCarryoverItem.ItemUnit,
               Wk_WsCarryoverItem.OpenQty,
               Wk_WsCarryoverItem.OpenAmount,
               Wk_WsCarryoverItem.InQty,
               Wk_WsCarryoverItem.InAmount,
               Wk_WsCarryoverItem.OutQty,
               Wk_WsCarryoverItem.OutAmount,
               Wk_WsCarryoverItem.CloseQty,
               Wk_WsCarryoverItem.CloseAmount,
               Wk_WsCarryoverItem.Skuid,
               Wk_WsCarryoverItem.AttributeJson,
               Wk_WsCarryoverItem.RowNum,
               Wk_WsCarryoverItem.Custom1,
               Wk_WsCarryoverItem.Custom2,
               Wk_WsCarryoverItem.Custom3,
               Wk_WsCarryoverItem.Custom4,
               Wk_WsCarryoverItem.Custom5,
               Wk_WsCarryoverItem.Custom6,
               Wk_WsCarryoverItem.Custom7,
               Wk_WsCarryoverItem.Custom8,
               Wk_WsCarryoverItem.Custom9,
               Wk_WsCarryoverItem.Custom10,
               Wk_WsCarryoverItem.Tenantid,
               Wk_WsCarryoverItem.Revision
        FROM Wk_WsCarryoverItem
                 LEFT JOIN Mat_Goods ON Wk_WsCarryoverItem.Goodsid = Mat_Goods.id
        where Wk_WsCarryoverItem.id = #{key}
          and Wk_WsCarryoverItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkWscarryoveritemVo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_WsCarryoverItem.id,
               Wk_WsCarryoverItem.Pid,
               Wk_WsCarryoverItem.Goodsid,
               Wk_WsCarryoverItem.ItemCode,
               Wk_WsCarryoverItem.ItemName,
               Wk_WsCarryoverItem.ItemSpec,
               Wk_WsCarryoverItem.ItemUnit,
               Wk_WsCarryoverItem.OpenQty,
               Wk_WsCarryoverItem.OpenAmount,
               Wk_WsCarryoverItem.InQty,
               Wk_WsCarryoverItem.InAmount,
               Wk_WsCarryoverItem.OutQty,
               Wk_WsCarryoverItem.OutAmount,
               Wk_WsCarryoverItem.CloseQty,
               Wk_WsCarryoverItem.CloseAmount,
               Wk_WsCarryoverItem.Skuid,
               Wk_WsCarryoverItem.AttributeJson,
               Wk_WsCarryoverItem.RowNum,
               Wk_WsCarryoverItem.Custom1,
               Wk_WsCarryoverItem.Custom2,
               Wk_WsCarryoverItem.Custom3,
               Wk_WsCarryoverItem.Custom4,
               Wk_WsCarryoverItem.Custom5,
               Wk_WsCarryoverItem.Custom6,
               Wk_WsCarryoverItem.Custom7,
               Wk_WsCarryoverItem.Custom8,
               Wk_WsCarryoverItem.Custom9,
               Wk_WsCarryoverItem.Custom10,
               Wk_WsCarryoverItem.Tenantid,
               Wk_WsCarryoverItem.Revision
        FROM Wk_WsCarryoverItem
                 LEFT JOIN Mat_Goods ON Wk_WsCarryoverItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWscarryoveritemPojo">
        <include refid="selectWkWscarryoveritemVo"/>
        where 1 = 1 and Wk_WsCarryoverItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WsCarryoverItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_WsCarryoverItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_WsCarryoverItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_WsCarryoverItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_WsCarryoverItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_WsCarryoverItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_WsCarryoverItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
            and Wk_WsCarryoverItem.skuid like concat('%', #{SearchPojo.skuid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_WsCarryoverItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_WsCarryoverItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_WsCarryoverItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_WsCarryoverItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_WsCarryoverItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_WsCarryoverItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_WsCarryoverItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_WsCarryoverItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_WsCarryoverItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_WsCarryoverItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_WsCarryoverItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_WsCarryoverItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_WsCarryoverItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_WsCarryoverItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_WsCarryoverItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_WsCarryoverItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_WsCarryoverItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
                or Wk_WsCarryoverItem.Skuid like concat('%', #{SearchPojo.skuid}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_WsCarryoverItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_WsCarryoverItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_WsCarryoverItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_WsCarryoverItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_WsCarryoverItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_WsCarryoverItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_WsCarryoverItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_WsCarryoverItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_WsCarryoverItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_WsCarryoverItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_WsCarryoverItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkWscarryoveritemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_WsCarryoverItem.id,
               Wk_WsCarryoverItem.Pid,
               Wk_WsCarryoverItem.Goodsid,
               Wk_WsCarryoverItem.ItemCode,
               Wk_WsCarryoverItem.ItemName,
               Wk_WsCarryoverItem.ItemSpec,
               Wk_WsCarryoverItem.ItemUnit,
               Wk_WsCarryoverItem.OpenQty,
               Wk_WsCarryoverItem.OpenAmount,
               Wk_WsCarryoverItem.InQty,
               Wk_WsCarryoverItem.InAmount,
               Wk_WsCarryoverItem.OutQty,
               Wk_WsCarryoverItem.OutAmount,
               Wk_WsCarryoverItem.CloseQty,
               Wk_WsCarryoverItem.CloseAmount,
               Wk_WsCarryoverItem.Skuid,
               Wk_WsCarryoverItem.AttributeJson,
               Wk_WsCarryoverItem.RowNum,
               Wk_WsCarryoverItem.Custom1,
               Wk_WsCarryoverItem.Custom2,
               Wk_WsCarryoverItem.Custom3,
               Wk_WsCarryoverItem.Custom4,
               Wk_WsCarryoverItem.Custom5,
               Wk_WsCarryoverItem.Custom6,
               Wk_WsCarryoverItem.Custom7,
               Wk_WsCarryoverItem.Custom8,
               Wk_WsCarryoverItem.Custom9,
               Wk_WsCarryoverItem.Custom10,
               Wk_WsCarryoverItem.Tenantid,
               Wk_WsCarryoverItem.Revision
        FROM Wk_WsCarryoverItem
                 LEFT JOIN Mat_Goods ON Wk_WsCarryoverItem.Goodsid = Mat_Goods.id
        where Wk_WsCarryoverItem.Pid = #{Pid}
          and Wk_WsCarryoverItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_WsCarryoverItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, OpenQty, OpenAmount,
                                       InQty, InAmount, OutQty, OutAmount, CloseQty, CloseAmount, Skuid, AttributeJson,
                                       RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                       Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{openqty},
                #{openamount}, #{inqty}, #{inamount}, #{outqty}, #{outamount}, #{closeqty}, #{closeamount}, #{skuid},
                #{attributejson}, #{rownum}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WsCarryoverItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="openqty != null">
                OpenQty = #{openqty},
            </if>
            <if test="openamount != null">
                OpenAmount = #{openamount},
            </if>
            <if test="inqty != null">
                InQty = #{inqty},
            </if>
            <if test="inamount != null">
                InAmount = #{inamount},
            </if>
            <if test="outqty != null">
                OutQty = #{outqty},
            </if>
            <if test="outamount != null">
                OutAmount = #{outamount},
            </if>
            <if test="closeqty != null">
                CloseQty = #{closeqty},
            </if>
            <if test="closeamount != null">
                CloseAmount = #{closeamount},
            </if>
            <if test="skuid != null ">
                Skuid = #{skuid},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WsCarryoverItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

