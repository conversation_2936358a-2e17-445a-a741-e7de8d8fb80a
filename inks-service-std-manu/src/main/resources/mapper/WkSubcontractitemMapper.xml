<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSubcontractitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSubcontractitemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_SubcontractItem.id,
               Wk_SubcontractItem.Pid,
               Wk_SubcontractItem.Goodsid,
               Wk_SubcontractItem.SubItemid,
               Wk_SubcontractItem.SubUse,
               Wk_SubcontractItem.SubUnit,
               Wk_SubcontractItem.SubQty,
               Wk_SubcontractItem.TaxPrice,
               Wk_SubcontractItem.TaxAmount,
               Wk_SubcontractItem.Price,
               Wk_SubcontractItem.Amount,
               Wk_SubcontractItem.TaxTotal,
               Wk_SubcontractItem.ItemTaxrate,
               Wk_SubcontractItem.StartDate,
               Wk_SubcontractItem.PlanDate,
               Wk_SubcontractItem.Quantity,
               Wk_SubcontractItem.FinishQty,
               Wk_SubcontractItem.MrbQty,
               Wk_SubcontractItem.InStorage,
               Wk_SubcontractItem.EnabledMark,
               Wk_SubcontractItem.Closed,
               Wk_SubcontractItem.Remark,
               Wk_SubcontractItem.StateCode,
               Wk_SubcontractItem.StateDate,
               Wk_SubcontractItem.RowNum,
               Wk_SubcontractItem.MachUid,
               Wk_SubcontractItem.MachItemid,
               Wk_SubcontractItem.MachGroupid,
               Wk_SubcontractItem.Customer,
               Wk_SubcontractItem.CustPO,
               Wk_SubcontractItem.MainPlanUid,
               Wk_SubcontractItem.MainPlanItemid,
               Wk_SubcontractItem.MrpUid,
               Wk_SubcontractItem.MrpItemid,
               Wk_SubcontractItem.CiteUid,
               Wk_SubcontractItem.CiteItemid,
               Wk_SubcontractItem.DisannulListerid,
               Wk_SubcontractItem.DisannulLister,
               Wk_SubcontractItem.DisannulDate,
               Wk_SubcontractItem.DisannulMark,
               Wk_SubcontractItem.AttributeJson,
               Wk_SubcontractItem.ReportQty,
               Wk_SubcontractItem.CompQty,
               Wk_SubcontractItem.FinishRate,
               Wk_SubcontractItem.SecQty,
               Wk_SubcontractItem.PanelWidth,
               Wk_SubcontractItem.PanelHeight,
               Wk_SubcontractItem.PcsInPanel,
               Wk_SubcontractItem.PanelThick,
               Wk_SubcontractItem.RowCode,
               Wk_SubcontractItem.WkPcsQty,
               Wk_SubcontractItem.WkSecQty,
               Wk_SubcontractItem.SourceType,
               Wk_SubcontractItem.VirtualItem,
               Wk_SubcontractItem.SubByPcs,
               Wk_SubcontractItem.Custom1,
               Wk_SubcontractItem.Custom2,
               Wk_SubcontractItem.Custom3,
               Wk_SubcontractItem.Custom4,
               Wk_SubcontractItem.Custom5,
               Wk_SubcontractItem.Custom6,
               Wk_SubcontractItem.Custom7,
               Wk_SubcontractItem.Custom8,
               Wk_SubcontractItem.Custom9,
               Wk_SubcontractItem.Custom10,
               Wk_SubcontractItem.Tenantid,
               Wk_SubcontractItem.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_SubcontractItem ON Wk_SubcontractItem.Goodsid = Mat_Goods.id
        where Wk_SubcontractItem.id = #{key}
          and Wk_SubcontractItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkSubcontractitemVo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_SubcontractItem.id,
               Wk_SubcontractItem.Pid,
               Wk_SubcontractItem.Goodsid,
               Wk_SubcontractItem.SubItemid,
               Wk_SubcontractItem.SubUse,
               Wk_SubcontractItem.SubUnit,
               Wk_SubcontractItem.SubQty,
               Wk_SubcontractItem.TaxPrice,
               Wk_SubcontractItem.TaxAmount,
               Wk_SubcontractItem.Price,
               Wk_SubcontractItem.Amount,
               Wk_SubcontractItem.TaxTotal,
               Wk_SubcontractItem.ItemTaxrate,
               Wk_SubcontractItem.StartDate,
               Wk_SubcontractItem.PlanDate,
               Wk_SubcontractItem.Quantity,
               Wk_SubcontractItem.FinishQty,
               Wk_SubcontractItem.MrbQty,
               Wk_SubcontractItem.InStorage,
               Wk_SubcontractItem.EnabledMark,
               Wk_SubcontractItem.Closed,
               Wk_SubcontractItem.Remark,
               Wk_SubcontractItem.StateCode,
               Wk_SubcontractItem.StateDate,
               Wk_SubcontractItem.RowNum,
               Wk_SubcontractItem.MachUid,
               Wk_SubcontractItem.MachItemid,
               Wk_SubcontractItem.MachGroupid,
               Wk_SubcontractItem.Customer,
               Wk_SubcontractItem.CustPO,
               Wk_SubcontractItem.MainPlanUid,
               Wk_SubcontractItem.MainPlanItemid,
               Wk_SubcontractItem.MrpUid,
               Wk_SubcontractItem.MrpItemid,
               Wk_SubcontractItem.CiteUid,
               Wk_SubcontractItem.CiteItemid,
               Wk_SubcontractItem.DisannulListerid,
               Wk_SubcontractItem.DisannulLister,
               Wk_SubcontractItem.DisannulDate,
               Wk_SubcontractItem.DisannulMark,
               Wk_SubcontractItem.AttributeJson,
               Wk_SubcontractItem.ReportQty,
               Wk_SubcontractItem.CompQty,
               Wk_SubcontractItem.FinishRate,
               Wk_SubcontractItem.SecQty,
               Wk_SubcontractItem.PanelWidth,
               Wk_SubcontractItem.PanelHeight,
               Wk_SubcontractItem.PcsInPanel,
               Wk_SubcontractItem.PanelThick,
               Wk_SubcontractItem.RowCode,
               Wk_SubcontractItem.WkPcsQty,
               Wk_SubcontractItem.WkSecQty,
               Wk_SubcontractItem.SourceType,
               Wk_SubcontractItem.VirtualItem,
               Wk_SubcontractItem.SubByPcs,
               Wk_SubcontractItem.Custom1,
               Wk_SubcontractItem.Custom2,
               Wk_SubcontractItem.Custom3,
               Wk_SubcontractItem.Custom4,
               Wk_SubcontractItem.Custom5,
               Wk_SubcontractItem.Custom6,
               Wk_SubcontractItem.Custom7,
               Wk_SubcontractItem.Custom8,
               Wk_SubcontractItem.Custom9,
               Wk_SubcontractItem.Custom10,
               Wk_SubcontractItem.Tenantid,
               Wk_SubcontractItem.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_SubcontractItem ON Wk_SubcontractItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSubcontractitemPojo">
        <include refid="selectWkSubcontractitemVo"/>
        where 1 = 1 and Wk_SubcontractItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_SubcontractItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_SubcontractItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_SubcontractItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
            and Wk_SubcontractItem.subitemid like concat('%', #{SearchPojo.subitemid}, '%')
        </if>
        <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
            and Wk_SubcontractItem.subuse like concat('%', #{SearchPojo.subuse}, '%')
        </if>
        <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
            and Wk_SubcontractItem.subunit like concat('%', #{SearchPojo.subunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_SubcontractItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_SubcontractItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_SubcontractItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_SubcontractItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_SubcontractItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_SubcontractItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_SubcontractItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_SubcontractItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_SubcontractItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Wk_SubcontractItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Wk_SubcontractItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Wk_SubcontractItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Wk_SubcontractItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Wk_SubcontractItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Wk_SubcontractItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_SubcontractItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_SubcontractItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_SubcontractItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_SubcontractItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_SubcontractItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_SubcontractItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_SubcontractItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_SubcontractItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_SubcontractItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_SubcontractItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_SubcontractItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_SubcontractItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_SubcontractItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
                or Wk_SubcontractItem.SubItemid like concat('%', #{SearchPojo.subitemid}, '%')
            </if>
            <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
                or Wk_SubcontractItem.SubUse like concat('%', #{SearchPojo.subuse}, '%')
            </if>
            <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
                or Wk_SubcontractItem.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_SubcontractItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Wk_SubcontractItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_SubcontractItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_SubcontractItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_SubcontractItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_SubcontractItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_SubcontractItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_SubcontractItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_SubcontractItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Wk_SubcontractItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Wk_SubcontractItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Wk_SubcontractItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Wk_SubcontractItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Wk_SubcontractItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Wk_SubcontractItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_SubcontractItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_SubcontractItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_SubcontractItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_SubcontractItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_SubcontractItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_SubcontractItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_SubcontractItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_SubcontractItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_SubcontractItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_SubcontractItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_SubcontractItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkSubcontractitemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_SubcontractItem.id,
               Wk_SubcontractItem.Pid,
               Wk_SubcontractItem.Goodsid,
               Wk_SubcontractItem.SubItemid,
               Wk_SubcontractItem.SubUse,
               Wk_SubcontractItem.SubUnit,
               Wk_SubcontractItem.SubQty,
               Wk_SubcontractItem.TaxPrice,
               Wk_SubcontractItem.TaxAmount,
               Wk_SubcontractItem.Price,
               Wk_SubcontractItem.Amount,
               Wk_SubcontractItem.TaxTotal,
               Wk_SubcontractItem.ItemTaxrate,
               Wk_SubcontractItem.StartDate,
               Wk_SubcontractItem.PlanDate,
               Wk_SubcontractItem.Quantity,
               Wk_SubcontractItem.FinishQty,
               Wk_SubcontractItem.MrbQty,
               Wk_SubcontractItem.InStorage,
               Wk_SubcontractItem.EnabledMark,
               Wk_SubcontractItem.Closed,
               Wk_SubcontractItem.Remark,
               Wk_SubcontractItem.StateCode,
               Wk_SubcontractItem.StateDate,
               Wk_SubcontractItem.RowNum,
               Wk_SubcontractItem.MachUid,
               Wk_SubcontractItem.MachItemid,
               Wk_SubcontractItem.MachGroupid,
               Wk_SubcontractItem.Customer,
               Wk_SubcontractItem.CustPO,
               Wk_SubcontractItem.MainPlanUid,
               Wk_SubcontractItem.MainPlanItemid,
               Wk_SubcontractItem.MrpUid,
               Wk_SubcontractItem.MrpItemid,
               Wk_SubcontractItem.CiteUid,
               Wk_SubcontractItem.CiteItemid,
               Wk_SubcontractItem.DisannulListerid,
               Wk_SubcontractItem.DisannulLister,
               Wk_SubcontractItem.DisannulDate,
               Wk_SubcontractItem.DisannulMark,
               Wk_SubcontractItem.AttributeJson,
               Wk_SubcontractItem.ReportQty,
               Wk_SubcontractItem.CompQty,
               Wk_SubcontractItem.FinishRate,
               Wk_SubcontractItem.SecQty,
               Wk_SubcontractItem.PanelWidth,
               Wk_SubcontractItem.PanelHeight,
               Wk_SubcontractItem.PcsInPanel,
               Wk_SubcontractItem.PanelThick,
               Wk_SubcontractItem.RowCode,
               Wk_SubcontractItem.WkPcsQty,
               Wk_SubcontractItem.WkSecQty,
               Wk_SubcontractItem.SourceType,
               Wk_SubcontractItem.VirtualItem,
               Wk_SubcontractItem.SubByPcs,
               Wk_SubcontractItem.Custom1,
               Wk_SubcontractItem.Custom2,
               Wk_SubcontractItem.Custom3,
               Wk_SubcontractItem.Custom4,
               Wk_SubcontractItem.Custom5,
               Wk_SubcontractItem.Custom6,
               Wk_SubcontractItem.Custom7,
               Wk_SubcontractItem.Custom8,
               Wk_SubcontractItem.Custom9,
               Wk_SubcontractItem.Custom10,
               Wk_SubcontractItem.Tenantid,
               Wk_SubcontractItem.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_SubcontractItem ON Wk_SubcontractItem.Goodsid = Mat_Goods.id
        where Wk_SubcontractItem.Pid = #{Pid}
          and Wk_SubcontractItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_SubcontractItem(id, Pid, Goodsid, SubItemid, SubUse, SubUnit, SubQty, TaxPrice, TaxAmount, Price,
                                       Amount, TaxTotal, ItemTaxrate, StartDate, PlanDate, Quantity, FinishQty, MrbQty,
                                       InStorage, EnabledMark, Closed, Remark, StateCode, StateDate, RowNum, MachUid,
                                       MachItemid, MachGroupid, Customer, CustPO, MainPlanUid, MainPlanItemid, MrpUid,
                                       MrpItemid, CiteUid, CiteItemid, DisannulListerid, DisannulLister, DisannulDate,
                                       DisannulMark, AttributeJson, ReportQty, CompQty, FinishRate, SecQty, PanelWidth,
                                       PanelHeight, PcsInPanel, PanelThick, RowCode, WkPcsQty, WkSecQty, SourceType,
                                       VirtualItem, SubByPcs,
                                       Custom1,
                                       Custom2, Custom3,
                                       Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                       Revision)
        values (#{id}, #{pid}, #{goodsid}, #{subitemid}, #{subuse}, #{subunit}, #{subqty}, #{taxprice}, #{taxamount},
                #{price}, #{amount}, #{taxtotal}, #{itemtaxrate}, #{startdate}, #{plandate}, #{quantity}, #{finishqty},
                #{mrbqty}, #{instorage}, #{enabledmark}, #{closed}, #{remark}, #{statecode}, #{statedate}, #{rownum},
                #{machuid}, #{machitemid}, #{machgroupid}, #{customer}, #{custpo}, #{mainplanuid}, #{mainplanitemid},
                #{mrpuid}, #{mrpitemid}, #{citeuid}, #{citeitemid}, #{disannullisterid}, #{disannullister},
                #{disannuldate}, #{disannulmark}, #{attributejson}, #{reportqty}, #{compqty},
                #{finishrate}, #{secqty}, #{panelwidth}, #{panelheight}, #{pcsinpanel}, #{panelthick}, #{rowcode},
                #{wkpcsqty}, #{wksecqty}, #{sourcetype}, #{virtualitem}, #{subbypcs}, #{custom1}, #{custom2},
                #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10},
                #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_SubcontractItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="subitemid != null ">
                SubItemid = #{subitemid},
            </if>
            <if test="subuse != null ">
                SubUse = #{subuse},
            </if>
            <if test="subunit != null ">
                SubUnit = #{subunit},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="mrbqty != null">
                MrbQty = #{mrbqty},
            </if>
            <if test="instorage != null">
                InStorage = #{instorage},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="reportqty != null">
                ReportQty = #{reportqty},
            </if>
            <if test="compqty != null">
                CompQty = #{compqty},
            </if>
            <if test="finishrate != null">
                FinishRate = #{finishrate},
            </if>
            <if test="secqty != null">
                SecQty = #{secqty},
            </if>
            <if test="panelwidth != null">
                PanelWidth = #{panelwidth},
            </if>
            <if test="panelheight != null">
                PanelHeight = #{panelheight},
            </if>
            <if test="pcsinpanel != null">
                PcsInPanel = #{pcsinpanel},
            </if>
            <if test="panelthick != null">
                PanelThick = #{panelthick},
            </if>
            <if test="rowcode != null ">
                RowCode = #{rowcode},
            </if>
            <if test="wkpcsqty != null">
                WkPcsQty = #{wkpcsqty},
            </if>
            <if test="wksecqty != null">
                WkSecQty = #{wksecqty},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="subbypcs != null">
                SubByPcs = #{subbypcs},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_SubcontractItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <update id="updateWkSubcontractFinishCount">
        update Wk_Subcontract
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_SubcontractItem
                                   where Wk_SubcontractItem.Pid = (SELECT Pid FROM Wk_SubcontractItem where id = #{key})
                                     and Wk_SubcontractItem.Tenantid = #{tid}
                                     and (Wk_SubcontractItem.Closed = 1
                                       or Wk_SubcontractItem.FinishQty >= Wk_SubcontractItem.Quantity)), 0)
        where id = (SELECT Pid FROM Wk_SubcontractItem where id = #{key})
          and Tenantid = #{tid}
    </update>
</mapper>

