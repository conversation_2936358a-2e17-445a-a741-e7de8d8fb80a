<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkMpcarryoveritemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkMpcarryoveritemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_MpCarryoverItem.id,
            Wk_MpCarryoverItem.Pid,
            Wk_MpCarryoverItem.Goodsid,
            Wk_MpCarryoverItem.ItemCode,
            Wk_MpCarryoverItem.ItemName,
            Wk_MpCarryoverItem.ItemSpec,
            Wk_MpCarryoverItem.ItemUnit,
            Wk_MpCarryoverItem.OpenQty,
            Wk_MpCarryoverItem.OpenAmount,
            Wk_MpCarryoverItem.InQty,
            Wk_MpCarryoverItem.InAmount,
            Wk_MpCarryoverItem.OutQty,
            Wk_MpCarryoverItem.OutAmount,
            Wk_MpCarryoverItem.CloseQty,
            Wk_MpCarryoverItem.CloseAmount,
            Wk_MpCarryoverItem.Skuid,
            Wk_MpCarryoverItem.AttributeJson,
            Wk_MpCarryoverItem.RowNum,
            Wk_MpCarryoverItem.Custom1,
            Wk_MpCarryoverItem.Custom2,
            Wk_MpCarryoverItem.Custom3,
            Wk_MpCarryoverItem.Custom4,
            Wk_MpCarryoverItem.Custom5,
            Wk_MpCarryoverItem.Custom6,
            Wk_MpCarryoverItem.Custom7,
            Wk_MpCarryoverItem.Custom8,
            Wk_MpCarryoverItem.Custom9,
            Wk_MpCarryoverItem.Custom10,
            Wk_MpCarryoverItem.Tenantid,
            Wk_MpCarryoverItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_MpCarryoverItem ON Mat_Goods.id = Wk_MpCarryoverItem.Goodsid
        where Wk_MpCarryoverItem.id = #{key}
          and Wk_MpCarryoverItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkMpcarryoveritemVo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_MpCarryoverItem.id,
            Wk_MpCarryoverItem.Pid,
            Wk_MpCarryoverItem.Goodsid,
            Wk_MpCarryoverItem.ItemCode,
            Wk_MpCarryoverItem.ItemName,
            Wk_MpCarryoverItem.ItemSpec,
            Wk_MpCarryoverItem.ItemUnit,
            Wk_MpCarryoverItem.OpenQty,
            Wk_MpCarryoverItem.OpenAmount,
            Wk_MpCarryoverItem.InQty,
            Wk_MpCarryoverItem.InAmount,
            Wk_MpCarryoverItem.OutQty,
            Wk_MpCarryoverItem.OutAmount,
            Wk_MpCarryoverItem.CloseQty,
            Wk_MpCarryoverItem.CloseAmount,
            Wk_MpCarryoverItem.Skuid,
            Wk_MpCarryoverItem.AttributeJson,
            Wk_MpCarryoverItem.RowNum,
            Wk_MpCarryoverItem.Custom1,
            Wk_MpCarryoverItem.Custom2,
            Wk_MpCarryoverItem.Custom3,
            Wk_MpCarryoverItem.Custom4,
            Wk_MpCarryoverItem.Custom5,
            Wk_MpCarryoverItem.Custom6,
            Wk_MpCarryoverItem.Custom7,
            Wk_MpCarryoverItem.Custom8,
            Wk_MpCarryoverItem.Custom9,
            Wk_MpCarryoverItem.Custom10,
            Wk_MpCarryoverItem.Tenantid,
            Wk_MpCarryoverItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_MpCarryoverItem ON Mat_Goods.id = Wk_MpCarryoverItem.Goodsid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkMpcarryoveritemPojo">
        <include refid="selectWkMpcarryoveritemVo"/>
        where 1 = 1 and Wk_MpCarryoverItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_MpCarryoverItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_MpCarryoverItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_MpCarryoverItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_MpCarryoverItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_MpCarryoverItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_MpCarryoverItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_MpCarryoverItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
            and Wk_MpCarryoverItem.skuid like concat('%', #{SearchPojo.skuid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_MpCarryoverItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_MpCarryoverItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_MpCarryoverItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_MpCarryoverItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_MpCarryoverItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_MpCarryoverItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_MpCarryoverItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_MpCarryoverItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_MpCarryoverItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_MpCarryoverItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_MpCarryoverItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_MpCarryoverItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_MpCarryoverItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_MpCarryoverItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_MpCarryoverItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_MpCarryoverItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_MpCarryoverItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
                or Wk_MpCarryoverItem.Skuid like concat('%', #{SearchPojo.skuid}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_MpCarryoverItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_MpCarryoverItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_MpCarryoverItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_MpCarryoverItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_MpCarryoverItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_MpCarryoverItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_MpCarryoverItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_MpCarryoverItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_MpCarryoverItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_MpCarryoverItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_MpCarryoverItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkMpcarryoveritemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_MpCarryoverItem.id,
            Wk_MpCarryoverItem.Pid,
            Wk_MpCarryoverItem.Goodsid,
            Wk_MpCarryoverItem.ItemCode,
            Wk_MpCarryoverItem.ItemName,
            Wk_MpCarryoverItem.ItemSpec,
            Wk_MpCarryoverItem.ItemUnit,
            Wk_MpCarryoverItem.OpenQty,
            Wk_MpCarryoverItem.OpenAmount,
            Wk_MpCarryoverItem.InQty,
            Wk_MpCarryoverItem.InAmount,
            Wk_MpCarryoverItem.OutQty,
            Wk_MpCarryoverItem.OutAmount,
            Wk_MpCarryoverItem.CloseQty,
            Wk_MpCarryoverItem.CloseAmount,
            Wk_MpCarryoverItem.Skuid,
            Wk_MpCarryoverItem.AttributeJson,
            Wk_MpCarryoverItem.RowNum,
            Wk_MpCarryoverItem.Custom1,
            Wk_MpCarryoverItem.Custom2,
            Wk_MpCarryoverItem.Custom3,
            Wk_MpCarryoverItem.Custom4,
            Wk_MpCarryoverItem.Custom5,
            Wk_MpCarryoverItem.Custom6,
            Wk_MpCarryoverItem.Custom7,
            Wk_MpCarryoverItem.Custom8,
            Wk_MpCarryoverItem.Custom9,
            Wk_MpCarryoverItem.Custom10,
            Wk_MpCarryoverItem.Tenantid,
            Wk_MpCarryoverItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_MpCarryoverItem ON Mat_Goods.id = Wk_MpCarryoverItem.Goodsid
        where Wk_MpCarryoverItem.Pid = #{Pid}
          and Wk_MpCarryoverItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_MpCarryoverItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, OpenQty, OpenAmount,
                                       InQty, InAmount, OutQty, OutAmount, CloseQty, CloseAmount, Skuid, AttributeJson,
                                       RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                       Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{openqty},
                #{openamount}, #{inqty}, #{inamount}, #{outqty}, #{outamount}, #{closeqty}, #{closeamount}, #{skuid},
                #{attributejson}, #{rownum}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_MpCarryoverItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="openqty != null">
                OpenQty = #{openqty},
            </if>
            <if test="openamount != null">
                OpenAmount = #{openamount},
            </if>
            <if test="inqty != null">
                InQty = #{inqty},
            </if>
            <if test="inamount != null">
                InAmount = #{inamount},
            </if>
            <if test="outqty != null">
                OutQty = #{outqty},
            </if>
            <if test="outamount != null">
                OutAmount = #{outamount},
            </if>
            <if test="closeqty != null">
                CloseQty = #{closeqty},
            </if>
            <if test="closeamount != null">
                CloseAmount = #{closeamount},
            </if>
            <if test="skuid != null ">
                Skuid = #{skuid},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_MpCarryoverItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

