<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkMainplanitemMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkMainplanitemPojo">
        <include refid="selectWkMainplanitemVo"/>
        where Wk_MainPlanItem.id = #{key}
          and Wk_MainPlanItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkMainplanitemVo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_MainPlanItem.id,
               Wk_MainPlanItem.Pid,
               Wk_MainPlanItem.Goodsid,
               Wk_MainPlanItem.Quantity,
               Wk_MainPlanItem.Price,
               Wk_MainPlanItem.Amount,
               Wk_MainPlanItem.StartDate,
               Wk_MainPlanItem.PlanDate,
               Wk_MainPlanItem.StartQty,
               Wk_MainPlanItem.FinishQty,
               Wk_MainPlanItem.MrbQty,
               Wk_MainPlanItem.StartSecQty,
               Wk_MainPlanItem.EnabledMark,
               Wk_MainPlanItem.Closed,
               Wk_MainPlanItem.Remark,
               Wk_MainPlanItem.StateCode,
               Wk_MainPlanItem.StateDate,
               Wk_MainPlanItem.RowNum,
               Wk_MainPlanItem.MachType,
               Wk_MainPlanItem.MachUid,
               Wk_MainPlanItem.MachItemid,
               Wk_MainPlanItem.MachBatch,
               Wk_MainPlanItem.MachGroupid,
               Wk_MainPlanItem.Customer,
               Wk_MainPlanItem.CustPO,
               Wk_MainPlanItem.MrpUid,
               Wk_MainPlanItem.Mrpid,
               Wk_MainPlanItem.DisannulMark,
               Wk_MainPlanItem.DisannulLister,
               Wk_MainPlanItem.DisannulListerid,
               Wk_MainPlanItem.DisannulDate,
               Wk_MainPlanItem.AttributeJson,
               Wk_MainPlanItem.StartRate,
               Wk_MainPlanItem.FinishRate,
               Wk_MainPlanItem.SourceType,
               Wk_MainPlanItem.WkWpid,
               Wk_MainPlanItem.WkWpCode,
               Wk_MainPlanItem.WkWpName,
               Wk_MainPlanItem.MergeMark,
               Wk_MainPlanItem.MergeItems,
               Wk_MainPlanItem.Custom1,
               Wk_MainPlanItem.Custom2,
               Wk_MainPlanItem.Custom3,
               Wk_MainPlanItem.Custom4,
               Wk_MainPlanItem.Custom5,
               Wk_MainPlanItem.Custom6,
               Wk_MainPlanItem.Custom7,
               Wk_MainPlanItem.Custom8,
               Wk_MainPlanItem.Custom9,
               Wk_MainPlanItem.Custom10,
               Wk_MainPlanItem.Tenantid,
               Wk_MainPlanItem.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_MainPlanItem ON Mat_Goods.id = Wk_MainPlanItem.Goodsid
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkMainplanitemPojo">
        <include refid="selectWkMainplanitemVo"/>
        where Wk_MainPlanItem.Pid = #{Pid}
          and Wk_MainPlanItem.Tenantid = #{tid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkMainplanitemPojo">
        <include refid="selectWkMainplanitemVo"/>
        where 1 = 1 and Wk_MainPlanItem.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_MainPlanItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_MainPlanItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_MainPlanItem.goodsid like concat('%',
                #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_MainPlanItem.remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_MainPlanItem.statecode like concat('%',
                #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
            and Wk_MainPlanItem.machtype like concat('%',
                #{SearchPojo.machtype}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_MainPlanItem.machuid like concat('%',
                #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_MainPlanItem.machitemid like concat('%',
                #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_MainPlanItem.machgroupid like concat('%',
                #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_MainPlanItem.customer like concat('%',
                #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_MainPlanItem.custpo like concat('%',
                #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Wk_MainPlanItem.mrpuid like concat('%',
                #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpid != null and SearchPojo.mrpid != ''">
            and Wk_MainPlanItem.mrpid like concat('%',
                #{SearchPojo.mrpid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Wk_MainPlanItem.disannullister like concat('%',
                #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Wk_MainPlanItem.disannullisterid like concat('%',
                #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_MainPlanItem.attributejson like concat('%',
                #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_MainPlanItem.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_MainPlanItem.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_MainPlanItem.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_MainPlanItem.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_MainPlanItem.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_MainPlanItem.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_MainPlanItem.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_MainPlanItem.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_MainPlanItem.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_MainPlanItem.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_MainPlanItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_MainPlanItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_MainPlanItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Wk_MainPlanItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
                or Wk_MainPlanItem.MachType like concat('%', #{SearchPojo.machtype}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_MainPlanItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_MainPlanItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_MainPlanItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_MainPlanItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_MainPlanItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Wk_MainPlanItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpid != null and SearchPojo.mrpid != ''">
                or Wk_MainPlanItem.Mrpid like concat('%', #{SearchPojo.mrpid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Wk_MainPlanItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Wk_MainPlanItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_MainPlanItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_MainPlanItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_MainPlanItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_MainPlanItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_MainPlanItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_MainPlanItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_MainPlanItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_MainPlanItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_MainPlanItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_MainPlanItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_MainPlanItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>


    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_MainPlanItem(id, Pid, Goodsid, Quantity, Price, Amount, StartDate, PlanDate, StartQty, FinishQty,
                                    MrbQty, StartSecQty, EnabledMark, Closed, Remark, StateCode, StateDate, RowNum,
                                    MachType, MachUid, MachItemid, MachBatch, MachGroupid, Customer, CustPO, MrpUid,
                                    Mrpid, DisannulMark, DisannulLister, DisannulListerid, DisannulDate, AttributeJson,
                                    StartRate, FinishRate, SourceType, WkWpid, WkWpCode, WkWpName, MergeMark,
                                    MergeItems, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                    Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{quantity}, #{price}, #{amount}, #{startdate}, #{plandate}, #{startqty},
                #{finishqty}, #{mrbqty}, #{startsecqty}, #{enabledmark}, #{closed}, #{remark}, #{statecode},
                #{statedate}, #{rownum}, #{machtype}, #{machuid}, #{machitemid}, #{machbatch}, #{machgroupid},
                #{customer}, #{custpo}, #{mrpuid}, #{mrpid}, #{disannulmark}, #{disannullister}, #{disannullisterid},
                #{disannuldate}, #{attributejson}, #{startrate}, #{finishrate}, #{sourcetype}, #{wkwpid}, #{wkwpcode},
                #{wkwpname}, #{mergemark}, #{mergeitems}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_MainPlanItem
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="goodsid != null">
                Goodsid = #{goodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="startqty != null">
                StartQty = #{startqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="mrbqty != null">
                MrbQty = #{mrbqty},
            </if>
            <if test="startsecqty != null">
                StartSecQty = #{startsecqty},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            <if test="statecode != null">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="machtype != null">
                MachType = #{machtype},
            </if>
            <if test="machuid != null">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null">
                MachItemid = #{machitemid},
            </if>
            <if test="machbatch != null">
                MachBatch = #{machbatch},
            </if>
            <if test="machgroupid != null">
                MachGroupid = #{machgroupid},
            </if>
            <if test="customer != null">
                Customer = #{customer},
            </if>
            <if test="custpo != null">
                CustPO = #{custpo},
            </if>
            <if test="mrpuid != null">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpid != null">
                Mrpid = #{mrpid},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullister != null">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannullisterid != null">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="attributejson != null">
                AttributeJson = #{attributejson},
            </if>
            <if test="startrate != null">
                StartRate = #{startrate},
            </if>
            <if test="finishrate != null">
                FinishRate = #{finishrate},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="wkwpid != null">
                WkWpid = #{wkwpid},
            </if>
            <if test="wkwpcode != null">
                WkWpCode = #{wkwpcode},
            </if>
            <if test="wkwpname != null">
                WkWpName = #{wkwpname},
            </if>
            <if test="mergemark != null">
                MergeMark = #{mergemark},
            </if>
            <if test="mergeitems != null">
                MergeItems = #{mergeitems},
            </if>
            <if test="custom1 != null">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 = #{custom10},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_MainPlanItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <update id="syncBusMachItemMainPlanQty">
        update Bus_MachiningItem
        SET MainPlanQty =COALESCE((SELECT SUM(Wk_MainPlanItem.quantity)
                                   FROM Wk_MainPlanItem
                                   where Wk_MainPlanItem.MachItemid = #{machitemid}
                                     and Wk_MainPlanItem.Tenantid = #{tid}), 0)
        where id = #{machitemid}
          and Tenantid = #{tid}
    </update>

    <update id="syncBusMachMainPlanCount">
        update Bus_Machining
        SET MainPlanCount =COALESCE((SELECT COUNT(0)
                                     FROM Bus_MachiningItem
                                     where Bus_MachiningItem.Pid =
                                           (SELECT Pid FROM Bus_MachiningItem where id = #{machitemid})
                                       and Bus_MachiningItem.Tenantid = #{tid}
                                       and (Bus_MachiningItem.MainPlanClosed = 1
                                         or Bus_MachiningItem.MainPlanQty >= Bus_MachiningItem.Quantity)), 0)
        where id = (SELECT Pid FROM Bus_MachiningItem where id = #{machitemid})
          and Tenantid = #{tid}
    </update>

    <update id="syncMergeMarkInIds">
        update Wk_MainPlanItem
        set MergeMark = #{mergeMark}
        where id in
        <foreach collection="mainPlanItemIds" item="mainPlanItemId" open="(" separator="," close=")">
            #{mainPlanItemId}
        </foreach>
    </update>

    <select id="getMachItemidsInPlanItemids" resultType="java.lang.String">
        select MachItemid
        from Wk_MainPlanItem
        where id in
        <foreach collection="mergeItemIds" item="planItemId" open="(" separator="," close=")">
            #{planItemId}
        </foreach>
        and Tenantid = #{tid}
    </select>
</mapper>

