<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkProcessMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkProcessPojo">
        <include refid="selectbillVo"/>
        where Wk_Process.id = #{key}
          and Wk_Process.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               WpCode,
               WpName,
               Post,
               Outsourcing,
               BalanceUnit,
               Capacity,
               MinTime,
               SubQty,
               SubUnit,
               SubUnitUse,
               AimMrb,
               AimCost,
               SplitQty,
               RowNum,
               Summary,
               EnabledMark,
               Create<PERSON>y,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON>reate<PERSON><PERSON>,
               Lister,
               Listerid,
               ModifyDate,
               DeleteMark,
               DeleteListerid,
               DeleteLister,
               DeleteDate,
               BackColorArgb,
               ForeColorArgb,
               TargetQty,
               TargetHours,
               TargetAmt,
               FrontPhoto,
               WorkContent,
               LeaderNameA,
               LeaderTitleA,
               LeaderAvatarA,
               LeaderNameB,
               LeaderTitleB,
               LeaderAvatarB,
               WorkParam,
               LastMark,
               OnlineBatch,
               KeyMark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Wk_Process
    </sql>
    <sql id="selectdetailVo">
        select id,
               WpCode,
               WpName,
               Post,
               Outsourcing,
               BalanceUnit,
               Capacity,
               MinTime,
               SubQty,
               SubUnit,
               SubUnitUse,
               AimMrb,
               AimCost,
               SplitQty,
               RowNum,
               Summary,
               EnabledMark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               DeleteMark,
               DeleteListerid,
               DeleteLister,
               DeleteDate,
               BackColorArgb,
               ForeColorArgb,
               TargetQty,
               TargetHours,
               TargetAmt,
               FrontPhoto,
               WorkContent,
               LeaderNameA,
               LeaderTitleA,
               LeaderAvatarA,
               LeaderNameB,
               LeaderTitleB,
               LeaderAvatarB,
               WorkParam,
               LastMark,
               OnlineBatch,
               KeyMark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Wk_Process
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkProcessitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_Process.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_Process.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.wpcode != null ">
            and Wk_Process.wpcode like concat('%', #{SearchPojo.wpcode}, '%')
        </if>
        <if test="SearchPojo.wpname != null ">
            and Wk_Process.wpname like concat('%', #{SearchPojo.wpname}, '%')
        </if>
        <if test="SearchPojo.balanceunit != null ">
            and Wk_Process.balanceunit like concat('%', #{SearchPojo.balanceunit}, '%')
        </if>
        <if test="SearchPojo.subunit != null ">
            and Wk_Process.subunit like concat('%', #{SearchPojo.subunit}, '%')
        </if>
        <if test="SearchPojo.subunituse != null ">
            and Wk_Process.subunituse like concat('%', #{SearchPojo.subunituse}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_Process.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_Process.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_Process.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_Process.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_Process.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null ">
            and Wk_Process.deletelisterid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null ">
            and Wk_Process.deletelister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.backcolorargb != null ">
            and Wk_Process.backcolorargb like concat('%', #{SearchPojo.backcolorargb}, '%')
        </if>
        <if test="SearchPojo.forecolorargb != null ">
            and Wk_Process.forecolorargb like concat('%', #{SearchPojo.forecolorargb}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null ">
            and Wk_Process.frontphoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.workcontent != null ">
            and Wk_Process.workcontent like concat('%', #{SearchPojo.workcontent}, '%')
        </if>
        <if test="SearchPojo.leadernamea != null ">
            and Wk_Process.leadernamea like concat('%', #{SearchPojo.leadernamea}, '%')
        </if>
        <if test="SearchPojo.leadertitlea != null ">
            and Wk_Process.leadertitlea like concat('%', #{SearchPojo.leadertitlea}, '%')
        </if>
        <if test="SearchPojo.leaderavatara != null ">
            and Wk_Process.leaderavatara like concat('%', #{SearchPojo.leaderavatara}, '%')
        </if>
        <if test="SearchPojo.leadernameb != null ">
            and Wk_Process.leadernameb like concat('%', #{SearchPojo.leadernameb}, '%')
        </if>
        <if test="SearchPojo.leadertitleb != null ">
            and Wk_Process.leadertitleb like concat('%', #{SearchPojo.leadertitleb}, '%')
        </if>
        <if test="SearchPojo.leaderavatarb != null ">
            and Wk_Process.leaderavatarb like concat('%', #{SearchPojo.leaderavatarb}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_Process.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_Process.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_Process.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_Process.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_Process.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_Process.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_Process.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_Process.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_Process.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_Process.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.wpcode != null ">
                or Wk_Process.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
            </if>
            <if test="SearchPojo.wpname != null ">
                or Wk_Process.WpName like concat('%', #{SearchPojo.wpname}, '%')
            </if>
            <if test="SearchPojo.balanceunit != null ">
                or Wk_Process.BalanceUnit like concat('%', #{SearchPojo.balanceunit}, '%')
            </if>
            <if test="SearchPojo.subunit != null ">
                or Wk_Process.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
            </if>
            <if test="SearchPojo.subunituse != null ">
                or Wk_Process.SubUnitUse like concat('%', #{SearchPojo.subunituse}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_Process.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_Process.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_Process.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_Process.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_Process.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null ">
                or Wk_Process.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.deletelister != null ">
                or Wk_Process.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.backcolorargb != null ">
                or Wk_Process.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
            </if>
            <if test="SearchPojo.forecolorargb != null ">
                or Wk_Process.ForeColorArgb like concat('%', #{SearchPojo.forecolorargb}, '%')
            </if>
            <if test="SearchPojo.frontphoto != null ">
                or Wk_Process.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
            </if>
            <if test="SearchPojo.workcontent != null ">
                or Wk_Process.WorkContent like concat('%', #{SearchPojo.workcontent}, '%')
            </if>
            <if test="SearchPojo.leadernamea != null ">
                or Wk_Process.LeaderNameA like concat('%', #{SearchPojo.leadernamea}, '%')
            </if>
            <if test="SearchPojo.leadertitlea != null ">
                or Wk_Process.LeaderTitleA like concat('%', #{SearchPojo.leadertitlea}, '%')
            </if>
            <if test="SearchPojo.leaderavatara != null ">
                or Wk_Process.LeaderAvatarA like concat('%', #{SearchPojo.leaderavatara}, '%')
            </if>
            <if test="SearchPojo.leadernameb != null ">
                or Wk_Process.LeaderNameB like concat('%', #{SearchPojo.leadernameb}, '%')
            </if>
            <if test="SearchPojo.leadertitleb != null ">
                or Wk_Process.LeaderTitleB like concat('%', #{SearchPojo.leadertitleb}, '%')
            </if>
            <if test="SearchPojo.leaderavatarb != null ">
                or Wk_Process.LeaderAvatarB like concat('%', #{SearchPojo.leaderavatarb}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_Process.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_Process.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_Process.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_Process.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_Process.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_Process.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_Process.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_Process.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_Process.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_Process.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkProcessPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_Process.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_Process.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.wpcode != null ">
            and Wk_Process.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
        </if>
        <if test="SearchPojo.wpname != null ">
            and Wk_Process.WpName like concat('%', #{SearchPojo.wpname}, '%')
        </if>
        <if test="SearchPojo.balanceunit != null ">
            and Wk_Process.BalanceUnit like concat('%', #{SearchPojo.balanceunit}, '%')
        </if>
        <if test="SearchPojo.subunit != null ">
            and Wk_Process.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
        </if>
        <if test="SearchPojo.subunituse != null ">
            and Wk_Process.SubUnitUse like concat('%', #{SearchPojo.subunituse}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_Process.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_Process.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_Process.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_Process.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_Process.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null ">
            and Wk_Process.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null ">
            and Wk_Process.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.backcolorargb != null ">
            and Wk_Process.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
        </if>
        <if test="SearchPojo.forecolorargb != null ">
            and Wk_Process.ForeColorArgb like concat('%', #{SearchPojo.forecolorargb}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null ">
            and Wk_Process.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.workcontent != null ">
            and Wk_Process.WorkContent like concat('%', #{SearchPojo.workcontent}, '%')
        </if>
        <if test="SearchPojo.leadernamea != null ">
            and Wk_Process.LeaderNameA like concat('%', #{SearchPojo.leadernamea}, '%')
        </if>
        <if test="SearchPojo.leadertitlea != null ">
            and Wk_Process.LeaderTitleA like concat('%', #{SearchPojo.leadertitlea}, '%')
        </if>
        <if test="SearchPojo.leaderavatara != null ">
            and Wk_Process.LeaderAvatarA like concat('%', #{SearchPojo.leaderavatara}, '%')
        </if>
        <if test="SearchPojo.leadernameb != null ">
            and Wk_Process.LeaderNameB like concat('%', #{SearchPojo.leadernameb}, '%')
        </if>
        <if test="SearchPojo.leadertitleb != null ">
            and Wk_Process.LeaderTitleB like concat('%', #{SearchPojo.leadertitleb}, '%')
        </if>
        <if test="SearchPojo.leaderavatarb != null ">
            and Wk_Process.LeaderAvatarB like concat('%', #{SearchPojo.leaderavatarb}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_Process.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_Process.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_Process.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_Process.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_Process.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_Process.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_Process.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_Process.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_Process.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_Process.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.wpcode != null ">
                or Wk_Process.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
            </if>
            <if test="SearchPojo.wpname != null ">
                or Wk_Process.WpName like concat('%', #{SearchPojo.wpname}, '%')
            </if>
            <if test="SearchPojo.balanceunit != null ">
                or Wk_Process.BalanceUnit like concat('%', #{SearchPojo.balanceunit}, '%')
            </if>
            <if test="SearchPojo.subunit != null ">
                or Wk_Process.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
            </if>
            <if test="SearchPojo.subunituse != null ">
                or Wk_Process.SubUnitUse like concat('%', #{SearchPojo.subunituse}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_Process.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_Process.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_Process.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_Process.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_Process.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null ">
                or Wk_Process.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.deletelister != null ">
                or Wk_Process.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.backcolorargb != null ">
                or Wk_Process.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
            </if>
            <if test="SearchPojo.forecolorargb != null ">
                or Wk_Process.ForeColorArgb like concat('%', #{SearchPojo.forecolorargb}, '%')
            </if>
            <if test="SearchPojo.frontphoto != null ">
                or Wk_Process.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
            </if>
            <if test="SearchPojo.workcontent != null ">
                or Wk_Process.WorkContent like concat('%', #{SearchPojo.workcontent}, '%')
            </if>
            <if test="SearchPojo.leadernamea != null ">
                or Wk_Process.LeaderNameA like concat('%', #{SearchPojo.leadernamea}, '%')
            </if>
            <if test="SearchPojo.leadertitlea != null ">
                or Wk_Process.LeaderTitleA like concat('%', #{SearchPojo.leadertitlea}, '%')
            </if>
            <if test="SearchPojo.leaderavatara != null ">
                or Wk_Process.LeaderAvatarA like concat('%', #{SearchPojo.leaderavatara}, '%')
            </if>
            <if test="SearchPojo.leadernameb != null ">
                or Wk_Process.LeaderNameB like concat('%', #{SearchPojo.leadernameb}, '%')
            </if>
            <if test="SearchPojo.leadertitleb != null ">
                or Wk_Process.LeaderTitleB like concat('%', #{SearchPojo.leadertitleb}, '%')
            </if>
            <if test="SearchPojo.leaderavatarb != null ">
                or Wk_Process.LeaderAvatarB like concat('%', #{SearchPojo.leaderavatarb}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_Process.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_Process.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_Process.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_Process.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_Process.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_Process.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_Process.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_Process.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_Process.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_Process.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_Process(id, WpCode, WpName, Post, Outsourcing, BalanceUnit, Capacity, MinTime, SubQty, SubUnit,
                               SubUnitUse, AimMrb, AimCost, SplitQty, RowNum, Summary, EnabledMark, CreateBy,
                               CreateByid, CreateDate, Lister, Listerid, ModifyDate, DeleteMark, DeleteListerid,
                               DeleteLister, DeleteDate, BackColorArgb, ForeColorArgb, TargetQty, TargetHours,
                               TargetAmt, FrontPhoto, WorkContent, LeaderNameA, LeaderTitleA, LeaderAvatarA,
                               LeaderNameB, LeaderTitleB, LeaderAvatarB, WorkParam, LastMark, OnlineBatch, KeyMark,
                               Custom1, Custom2, Custom3, Custom4, Custom5,
                               Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{wpcode}, #{wpname}, #{post}, #{outsourcing}, #{balanceunit}, #{capacity}, #{mintime},
                #{subqty}, #{subunit}, #{subunituse}, #{aimmrb}, #{aimcost}, #{splitqty}, #{rownum}, #{summary},
                #{enabledmark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{deletemark}, #{deletelisterid}, #{deletelister}, #{deletedate}, #{backcolorargb}, #{forecolorargb},
                #{targetqty}, #{targethours}, #{targetamt}, #{frontphoto}, #{workcontent}, #{leadernamea},
                #{leadertitlea}, #{leaderavatara}, #{leadernameb}, #{leadertitleb}, #{leaderavatarb}, #{workparam},
                #{lastmark}, #{onlinebatch}, #{keymark},
                #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_Process
        <set>
            <if test="wpcode != null ">
                WpCode =#{wpcode},
            </if>
            <if test="wpname != null ">
                WpName =#{wpname},
            </if>
            <if test="post != null">
                Post =#{post},
            </if>
            <if test="outsourcing != null">
                Outsourcing =#{outsourcing},
            </if>
            <if test="balanceunit != null ">
                BalanceUnit =#{balanceunit},
            </if>
            <if test="capacity != null">
                Capacity =#{capacity},
            </if>
            <if test="mintime != null">
                MinTime =#{mintime},
            </if>
            <if test="subqty != null">
                SubQty =#{subqty},
            </if>
            <if test="subunit != null ">
                SubUnit =#{subunit},
            </if>
            <if test="subunituse != null ">
                SubUnitUse =#{subunituse},
            </if>
            <if test="aimmrb != null">
                AimMrb =#{aimmrb},
            </if>
            <if test="aimcost != null">
                AimCost =#{aimcost},
            </if>
            <if test="splitqty != null">
                SplitQty =#{splitqty},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelisterid != null ">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="backcolorargb != null ">
                BackColorArgb =#{backcolorargb},
            </if>
            <if test="forecolorargb != null ">
                ForeColorArgb =#{forecolorargb},
            </if>
            <if test="targetqty != null">
                TargetQty =#{targetqty},
            </if>
            <if test="targethours != null">
                TargetHours =#{targethours},
            </if>
            <if test="targetamt != null">
                TargetAmt =#{targetamt},
            </if>
            <if test="frontphoto != null ">
                FrontPhoto =#{frontphoto},
            </if>
            <if test="workcontent != null ">
                WorkContent =#{workcontent},
            </if>
            <if test="leadernamea != null ">
                LeaderNameA =#{leadernamea},
            </if>
            <if test="leadertitlea != null ">
                LeaderTitleA =#{leadertitlea},
            </if>
            <if test="leaderavatara != null ">
                LeaderAvatarA =#{leaderavatara},
            </if>
            <if test="leadernameb != null ">
                LeaderNameB =#{leadernameb},
            </if>
            <if test="leadertitleb != null ">
                LeaderTitleB =#{leadertitleb},
            </if>
            <if test="leaderavatarb != null ">
                LeaderAvatarB =#{leaderavatarb},
            </if>
            <if test="workparam != null ">
                WorkParam =#{workparam},
            </if>
            <if test="lastmark != null">
                LastMark =#{lastmark},
            </if>
            <if test="onlinebatch != null">
                OnlineBatch =#{onlinebatch},
            </if>
            <if test="keymark != null">
                KeyMark =#{keymark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_Process
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkProcessPojo">
        select
        id
        from Wk_ProcessItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <select id="getAll" resultType="inks.service.std.manu.domain.pojo.WkProcessPojo">
        select * from Wk_Process where Tenantid = #{tid}
    </select>
    <select id="getDelStatIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkProcessPojo">
        select
        id
        from Wk_ProcessStat
        where Pid = #{id}
        <if test="stat !=null and stat.size()>0">
            and id not in
            <foreach collection="stat" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getDefNameCode" resultType="java.util.Map">
        select DefCode,DefName from Qms_Defect where Tenantid = #{tid} and id= #{defectid}
    </select>

    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkProcessitemdetailPojo">
        <include refid="selectdetailVo"/>
        order by RowNum
    </select>

    <select id="getListByWpid" resultType="inks.service.std.manu.domain.pojo.WkProcessitemdetailPojo">
        <include refid="selectdetailVo"/>
        where Wk_Process.id = #{wpid} and Tenantid = #{tenantid}
    </select>

    <select id="getPostList" resultType="inks.service.std.manu.domain.pojo.WkProcessPojo">
        <include refid="selectbillVo"/>
        where Post = 1 and Tenantid = #{tenantid}
    </select>
    <select id="getEntityByWpName" resultType="inks.service.std.manu.domain.pojo.WkProcessPojo">
        <include refid="selectbillVo"/>
        where Wk_Process.WpName = #{wpname}
        and Wk_Process.Tenantid = #{tenantid} limit 1
    </select>

    <select id="getOnlineBatchCount" resultType="int">
        SELECT COALESCE(OnlineBatch, 0) AS OnlineBatch FROM Wk_Process WHERE Tenantid = #{tid} AND id = #{wpid}
    </select>

    <select id="checkSectByWkid" resultType="java.lang.String">
        select Wk_Section.SectName
        from Wk_SectionItem
                 left join Wk_Section on Wk_SectionItem.Pid = Wk_Section.id
        where Wk_SectionItem.Wpid = #{wpid}
          and Wk_SectionItem.Tenantid = #{tid} limit 1
    </select>

</mapper>

