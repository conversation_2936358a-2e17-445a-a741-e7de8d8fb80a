<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWorksheetmatMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWorksheetmatPojo">
        <include refid="selectWkWorksheetmatVo"/>
        where Wk_WorksheetMat.id = #{key}
          and Wk_WorksheetMat.Tenantid = #{tid}
    </select>
    <sql id="selectWkWorksheetmatVo">
        SELECT Wk_WorksheetMat.id,
               Wk_WorksheetMat.Pid,
               Wk_WorksheetMat.Itemid,
               Wk_WorksheetMat.Goodsid,
               Wk_WorksheetMat.Quantity,
               Wk_WorksheetMat.FinishQty,
               Wk_WorksheetMat.MrpUid,
               Wk_WorksheetMat.MrpItemid,
               Wk_WorksheetMat.SubQty,
               Wk_WorksheetMat.MainQty,
               Wk_WorksheetMat.LossRate,
               Wk_WorksheetMat.Bomid,
               Wk_WorksheetMat.BomType,
               Wk_WorksheetMat.BomItemid,
               Wk_WorksheetMat.ItemRowCode,
               Wk_WorksheetMat.RowNum,
               Wk_WorksheetMat.Closed,
               Wk_WorksheetMat.BomQty,
               Wk_WorksheetMat.AvaiQty,
               Wk_WorksheetMat.NeedQty,
               Wk_WorksheetMat.StoPlanQty,
               Wk_WorksheetMat.RealQty,
               Wk_WorksheetMat.FlowCode,
               Wk_WorksheetMat.AttributeJson,
               Wk_WorksheetMat.Mergeid,
               Wk_WorksheetMat.MergeFinishQty,
               Wk_WorksheetMat.Custom1,
               Wk_WorksheetMat.Custom2,
               Wk_WorksheetMat.Custom3,
               Wk_WorksheetMat.Custom4,
               Wk_WorksheetMat.Custom5,
               Wk_WorksheetMat.Custom6,
               Wk_WorksheetMat.Custom7,
               Wk_WorksheetMat.Custom8,
               Wk_WorksheetMat.Custom9,
               Wk_WorksheetMat.Custom10,
               Wk_WorksheetMat.Tenantid,
               Wk_WorksheetMat.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_WorksheetItem.MachUid
        FROM Mat_Goods
                 RIGHT JOIN Wk_WorksheetMat ON Mat_Goods.id = Wk_WorksheetMat.Goodsid
                 Left join Wk_WorksheetItem on Wk_WorksheetMat.Itemid = Wk_WorksheetItem.id
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkWorksheetmatPojo">
        <include refid="selectWkWorksheetmatVo"/>
        where Wk_WorksheetMat.Pid = #{Pid} and Wk_WorksheetMat.Tenantid=#{tid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWorksheetmatPojo">
        <include refid="selectWkWorksheetmatVo"/>
        where 1 = 1 and Wk_WorksheetMat.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WorksheetMat.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_WorksheetMat.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.itemid != null and SearchPojo.itemid != ''">
            and Wk_WorksheetMat.itemid like concat('%', #{SearchPojo.itemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_WorksheetMat.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Wk_WorksheetMat.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Wk_WorksheetMat.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
            and Wk_WorksheetMat.bomid like concat('%', #{SearchPojo.bomid}, '%')
        </if>
        <if test="SearchPojo.bomitemid != null and SearchPojo.bomitemid != ''">
            and Wk_WorksheetMat.bomitemid like concat('%', #{SearchPojo.bomitemid}, '%')
        </if>
        <if test="SearchPojo.itemrowcode != null and SearchPojo.itemrowcode != ''">
            and Wk_WorksheetMat.itemrowcode like concat('%', #{SearchPojo.itemrowcode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_WorksheetMat.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_WorksheetMat.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_WorksheetMat.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_WorksheetMat.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_WorksheetMat.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_WorksheetMat.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_WorksheetMat.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_WorksheetMat.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_WorksheetMat.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_WorksheetMat.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_WorksheetMat.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.itemid != null and SearchPojo.itemid != ''">
                or Wk_WorksheetMat.Itemid like concat('%', #{SearchPojo.itemid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_WorksheetMat.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Wk_WorksheetMat.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Wk_WorksheetMat.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
                or Wk_WorksheetMat.Bomid like concat('%', #{SearchPojo.bomid}, '%')
            </if>
            <if test="SearchPojo.bomitemid != null and SearchPojo.bomitemid != ''">
                or Wk_WorksheetMat.BomItemid like concat('%', #{SearchPojo.bomitemid}, '%')
            </if>
            <if test="SearchPojo.itemrowcode != null and SearchPojo.itemrowcode != ''">
                or Wk_WorksheetMat.ItemRowCode like concat('%', #{SearchPojo.itemrowcode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_WorksheetMat.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_WorksheetMat.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_WorksheetMat.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_WorksheetMat.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_WorksheetMat.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_WorksheetMat.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_WorksheetMat.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_WorksheetMat.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_WorksheetMat.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_WorksheetMat.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>



    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_WorksheetMat(id, Pid, Itemid, Goodsid, Quantity, FinishQty, MrpUid, MrpItemid, SubQty, MainQty, LossRate, Bomid, BomType, BomItemid, ItemRowCode, RowNum, Closed, BomQty, AvaiQty, NeedQty, StoPlanQty, RealQty, FlowCode, AttributeJson, Mergeid, MergeFinishQty, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{itemid}, #{goodsid}, #{quantity}, #{finishqty}, #{mrpuid}, #{mrpitemid}, #{subqty}, #{mainqty}, #{lossrate}, #{bomid}, #{bomtype}, #{bomitemid}, #{itemrowcode}, #{rownum}, #{closed}, #{bomqty}, #{avaiqty}, #{needqty}, #{stoplanqty}, #{realqty}, #{flowcode}, #{attributejson}, #{mergeid}, #{mergefinishqty}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WorksheetMat
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemid != null ">
                Itemid = #{itemid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="mainqty != null">
                MainQty = #{mainqty},
            </if>
            <if test="lossrate != null">
                LossRate = #{lossrate},
            </if>
            <if test="bomid != null ">
                Bomid = #{bomid},
            </if>
            <if test="bomtype != null">
                BomType = #{bomtype},
            </if>
            <if test="bomitemid != null ">
                BomItemid = #{bomitemid},
            </if>
            <if test="itemrowcode != null ">
                ItemRowCode = #{itemrowcode},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="bomqty != null">
                BomQty = #{bomqty},
            </if>
            <if test="avaiqty != null">
                AvaiQty = #{avaiqty},
            </if>
            <if test="needqty != null">
                NeedQty = #{needqty},
            </if>
            <if test="stoplanqty != null">
                StoPlanQty = #{stoplanqty},
            </if>
            <if test="realqty != null">
                RealQty = #{realqty},
            </if>
            <if test="flowcode != null ">
                FlowCode = #{flowcode},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="mergeid != null ">
                Mergeid = #{mergeid},
            </if>
            <if test="mergefinishqty != null">
                MergeFinishQty = #{mergefinishqty},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WorksheetMat
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteByItemid">
        delete
        from Wk_WorksheetMat
        where Itemid = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询指定行数据-->
    <select id="getListByItemid"
            resultType="inks.service.std.manu.domain.pojo.WkWorksheetmatPojo">
        <include refid="selectWkWorksheetmatVo"/>
        where Wk_WorksheetMat.Itemid=#{key} and Wk_WorksheetMat.Tenantid =#{tid}
    </select>
    <!--查询指定行数据-->
    <select id="getListByPid"
            resultType="inks.service.std.manu.domain.pojo.WkWorksheetmatPojo">
        <include refid="selectWkWorksheetmatVo"/>
        where Wk_WorksheetMat.Pid=#{key} and Wk_WorksheetMat.Tenantid =#{tid}
    </select>

    <select id="getMatListByItemIds" resultType="inks.service.std.manu.domain.pojo.WkWorksheetmatPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_WorksheetMat.id,
        Wk_WorksheetMat.Pid,
        Wk_WorksheetMat.Itemid,
        Wk_WorksheetMat.Goodsid,
        Wk_WorksheetMat.Quantity,
        Wk_WorksheetMat.FinishQty,
        Wk_WorksheetMat.MrpUid,
        Wk_WorksheetMat.MrpItemid,
        Wk_WorksheetMat.SubQty,
        Wk_WorksheetMat.MainQty,
        Wk_WorksheetMat.LossRate,
        Wk_WorksheetMat.Bomid,
        Wk_WorksheetMat.BomType,
        Wk_WorksheetMat.BomItemid,
        Wk_WorksheetMat.ItemRowCode,
        Wk_WorksheetMat.RowNum,
        Wk_WorksheetMat.Closed,
        Wk_WorksheetMat.BomQty,
        Wk_WorksheetMat.AvaiQty,
        Wk_WorksheetMat.NeedQty,
        Wk_WorksheetMat.StoPlanQty,
        Wk_WorksheetMat.RealQty,
        Wk_WorksheetMat.FlowCode,
        Wk_WorksheetMat.AttributeJson,
        Wk_WorksheetMat.Custom1,
        Wk_WorksheetMat.Custom2,
        Wk_WorksheetMat.Custom3,
        Wk_WorksheetMat.Custom4,
        Wk_WorksheetMat.Custom5,
        Wk_WorksheetMat.Custom6,
        Wk_WorksheetMat.Custom7,
        Wk_WorksheetMat.Custom8,
        Wk_WorksheetMat.Custom9,
        Wk_WorksheetMat.Custom10,
        Wk_WorksheetMat.Tenantid,
        Wk_WorksheetMat.Revision
        FROM Mat_Goods
        RIGHT JOIN Wk_WorksheetMat ON Mat_Goods.id = Wk_WorksheetMat.Goodsid
        where Wk_WorksheetMat.Tenantid = #{tid}
        <foreach collection="itemIdList" item="itemId" open="and Wk_WorksheetMat.Itemid in (" close=")" separator=",">
            #{itemId}
        </foreach>
        order by Wk_WorksheetMat.RowNum
    </select>

    <select id="getMatIdByGoodsIdAndItemId" resultType="java.lang.String">
        SELECT Wk_WorksheetMat.id
        FROM Wk_WorksheetMat
        where Wk_WorksheetMat.Tenantid = #{tid}
        and Wk_WorksheetMat.Itemid = #{itemid}
        and Wk_WorksheetMat.Goodsid = #{goodsid}
    </select>

    <update id="updateMergeid">
        update Wk_WorksheetMat
        set Mergeid = #{mergeidInsert}
        where id = #{id} and Tenantid =#{tid}
    </update>
</mapper>

