<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSteppriceMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSteppricePojo">
        select
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_StepPrice.id,
        Wk_StepPrice.RefNo,
        Wk_StepPrice.BillType,
        Wk_StepPrice.BillTitle,
        Wk_StepPrice.BillDate,
        Wk_StepPrice.Goodsid,
        Wk_StepPrice.Wpid,
        Wk_StepPrice.WpCode,
        Wk_StepPrice.WpName,
        Wk_StepPrice.Spus,
        Wk_StepPrice.SpuJson,
        Wk_StepPrice.SpuValue,
        Wk_StepPrice.CreateBy,
        Wk_StepPrice.CreateByid,
        Wk_StepPrice.CreateDate,
        Wk_StepPrice.Lister,
        Wk_StepPrice.Listerid,
        Wk_StepPrice.ModifyDate,
        Wk_StepPrice.Assessor,
        Wk_StepPrice.Assessorid,
        Wk_StepPrice.AssessDate,
        Wk_StepPrice.Summary,
        Wk_StepPrice.Custom1,
        Wk_StepPrice.Custom2,
        Wk_StepPrice.Custom3,
        Wk_StepPrice.Custom4,
        Wk_StepPrice.Custom5,
        Wk_StepPrice.Custom6,
        Wk_StepPrice.Custom7,
        Wk_StepPrice.Custom8,
        Wk_StepPrice.Custom9,
        Wk_StepPrice.Custom10,
        Wk_StepPrice.Tenantid,
        Wk_StepPrice.TenantName,
        Wk_StepPrice.Revision
        from Wk_StepPrice
        LEFT JOIN Mat_Goods ON Wk_StepPrice.Goodsid = Mat_Goods.id
        where Wk_StepPrice.id = #{key}
        and Wk_StepPrice.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_StepPrice.id,
        Wk_StepPrice.RefNo,
        Wk_StepPrice.BillType,
        Wk_StepPrice.BillTitle,
        Wk_StepPrice.BillDate,
        Wk_StepPrice.Goodsid,
        Wk_StepPrice.Wpid,
        Wk_StepPrice.WpCode,
        Wk_StepPrice.WpName,
        Wk_StepPrice.Spus,
        Wk_StepPrice.SpuJson,
        Wk_StepPrice.SpuValue,
        Wk_StepPrice.CreateBy,
        Wk_StepPrice.CreateByid,
        Wk_StepPrice.CreateDate,
        Wk_StepPrice.Lister,
        Wk_StepPrice.Listerid,
        Wk_StepPrice.ModifyDate,
        Wk_StepPrice.Assessor,
        Wk_StepPrice.Assessorid,
        Wk_StepPrice.AssessDate,
        Wk_StepPrice.Summary,
        Wk_StepPrice.Custom1,
        Wk_StepPrice.Custom2,
        Wk_StepPrice.Custom3,
        Wk_StepPrice.Custom4,
        Wk_StepPrice.Custom5,
        Wk_StepPrice.Custom6,
        Wk_StepPrice.Custom7,
        Wk_StepPrice.Custom8,
        Wk_StepPrice.Custom9,
        Wk_StepPrice.Custom10,
        Wk_StepPrice.Tenantid,
        Wk_StepPrice.TenantName,
        Wk_StepPrice.Revision
        from Wk_StepPrice LEFT JOIN Mat_Goods ON Wk_StepPrice.Goodsid = Mat_Goods.id
    </sql>
    <sql id="selectdetailVo">
        select Wk_StepPriceItem.id,
               Wk_StepPriceItem.Pid,
               Wk_StepPriceItem.StartQty,
               Wk_StepPriceItem.EndQty,
               Wk_StepPriceItem.Price,
               Wk_StepPriceItem.WorkHour,
               Wk_StepPriceItem.RowNum,
               Wk_StepPriceItem.Remark,
               Wk_StepPriceItem.Custom1,
               Wk_StepPriceItem.Custom2,
               Wk_StepPriceItem.Custom3,
               Wk_StepPriceItem.Custom4,
               Wk_StepPriceItem.Custom5,
               Wk_StepPriceItem.Custom6,
               Wk_StepPriceItem.Custom7,
               Wk_StepPriceItem.Custom8,
               Wk_StepPriceItem.Custom9,
               Wk_StepPriceItem.Custom10,
               Wk_StepPriceItem.Tenantid,
               Wk_StepPriceItem.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_StepPrice.RefNo,
               Wk_StepPrice.BillType,
               Wk_StepPrice.BillTitle,
               Wk_StepPrice.BillDate,
               Wk_StepPrice.Goodsid,
               Wk_StepPrice.Wpid,
               Wk_StepPrice.WpCode,
               Wk_StepPrice.WpName,
               Wk_StepPrice.Spus,
               Wk_StepPrice.SpuJson,
               Wk_StepPrice.SpuValue,
               Wk_StepPrice.CreateBy,
               Wk_StepPrice.CreateByid,
               Wk_StepPrice.CreateDate,
               Wk_StepPrice.Lister,
               Wk_StepPrice.Listerid,
               Wk_StepPrice.ModifyDate,
               Wk_StepPrice.Assessor,
               Wk_StepPrice.Assessorid,
               Wk_StepPrice.AssessDate,
               Wk_StepPrice.Summary
        from Wk_StepPrice
                 RIGHT JOIN Wk_StepPriceItem ON Wk_StepPrice.id = Wk_StepPriceItem.Pid
                 LEFT JOIN Mat_Goods ON Wk_StepPrice.Goodsid = Mat_Goods.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSteppriceitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_StepPrice.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_StepPrice.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Wk_StepPrice.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Wk_StepPrice.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Wk_StepPrice.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Wk_StepPrice.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.wpid != null">
            and Wk_StepPrice.wpid like concat('%', #{SearchPojo.wpid}, '%')
        </if>
        <if test="SearchPojo.wpcode != null">
            and Wk_StepPrice.wpcode like concat('%', #{SearchPojo.wpcode}, '%')
        </if>
        <if test="SearchPojo.wpname != null">
            and Wk_StepPrice.wpname like concat('%', #{SearchPojo.wpname}, '%')
        </if>
        <if test="SearchPojo.spujson != null">
            and Wk_StepPrice.spujson like concat('%', #{SearchPojo.spujson}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_StepPrice.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_StepPrice.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_StepPrice.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_StepPrice.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Wk_StepPrice.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Wk_StepPrice.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Wk_StepPrice.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_StepPrice.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_StepPrice.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_StepPrice.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_StepPrice.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_StepPrice.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_StepPrice.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_StepPrice.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_StepPrice.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_StepPrice.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_StepPrice.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_StepPrice.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Wk_StepPrice.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Wk_StepPrice.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Wk_StepPrice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Wk_StepPrice.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.wpid != null">
                or Wk_StepPrice.Wpid like concat('%', #{SearchPojo.wpid}, '%')
            </if>
            <if test="SearchPojo.wpcode != null">
                or Wk_StepPrice.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
            </if>
            <if test="SearchPojo.wpname != null">
                or Wk_StepPrice.WpName like concat('%', #{SearchPojo.wpname}, '%')
            </if>
            <if test="SearchPojo.spujson != null">
                or Wk_StepPrice.SpuJson like concat('%', #{SearchPojo.spujson}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_StepPrice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_StepPrice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_StepPrice.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_StepPrice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Wk_StepPrice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Wk_StepPrice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Wk_StepPrice.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_StepPrice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_StepPrice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_StepPrice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_StepPrice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_StepPrice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_StepPrice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_StepPrice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_StepPrice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_StepPrice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_StepPrice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_StepPrice.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSteppricePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_StepPrice.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_StepPrice.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Wk_StepPrice.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Wk_StepPrice.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Wk_StepPrice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Wk_StepPrice.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.wpid != null">
            and Wk_StepPrice.Wpid like concat('%', #{SearchPojo.wpid}, '%')
        </if>
        <if test="SearchPojo.wpcode != null">
            and Wk_StepPrice.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
        </if>
        <if test="SearchPojo.wpname != null">
            and Wk_StepPrice.WpName like concat('%', #{SearchPojo.wpname}, '%')
        </if>
        <if test="SearchPojo.spujson != null">
            and Wk_StepPrice.SpuJson like concat('%', #{SearchPojo.spujson}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_StepPrice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_StepPrice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_StepPrice.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_StepPrice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Wk_StepPrice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Wk_StepPrice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Wk_StepPrice.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_StepPrice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_StepPrice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_StepPrice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_StepPrice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_StepPrice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_StepPrice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_StepPrice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_StepPrice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_StepPrice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_StepPrice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_StepPrice.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Wk_StepPrice.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Wk_StepPrice.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Wk_StepPrice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Wk_StepPrice.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.wpid != null">
                or Wk_StepPrice.Wpid like concat('%', #{SearchPojo.wpid}, '%')
            </if>
            <if test="SearchPojo.wpcode != null">
                or Wk_StepPrice.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
            </if>
            <if test="SearchPojo.wpname != null">
                or Wk_StepPrice.WpName like concat('%', #{SearchPojo.wpname}, '%')
            </if>
            <if test="SearchPojo.spujson != null">
                or Wk_StepPrice.SpuJson like concat('%', #{SearchPojo.spujson}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_StepPrice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_StepPrice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_StepPrice.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_StepPrice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Wk_StepPrice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Wk_StepPrice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Wk_StepPrice.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_StepPrice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_StepPrice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_StepPrice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_StepPrice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_StepPrice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_StepPrice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_StepPrice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_StepPrice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_StepPrice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_StepPrice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_StepPrice.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_StepPrice(id, RefNo, BillType, BillTitle, BillDate, Goodsid, Wpid, WpCode, WpName, Spus, SpuJson,
        SpuValue,
        CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid,
        AssessDate, Summary, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7,
        Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{goodsid}, #{wpid}, #{wpcode}, #{wpname},
        #{spus}, #{spujson}, #{spuvalue},
        #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
        #{assessor}, #{assessorid}, #{assessdate}, #{summary}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
        #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname},
        #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_StepPrice
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="wpid != null">
                Wpid =#{wpid},
            </if>
            <if test="wpcode != null">
                WpCode =#{wpcode},
            </if>
            <if test="wpname != null">
                WpName =#{wpname},
            </if>
            <if test="spus != null">
                Spus =#{spus},
            </if>
            <if test="spujson != null">
                SpuJson =#{spujson},
            </if>
            <if test="spuvalue != null">
                SpuValue =#{spuvalue},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_StepPrice
        where id = #{key}
        and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_StepPrice
        SET Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision + 1
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkSteppricePojo">
        select
        id
        from Wk_StepPriceItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!-- 根据goodsid+wpid+spuvalue+quantity,查询单价出来，查不到为0；-->
    <select id="getidBySpuJson" resultType="java.lang.String">
        select id
        from (SELECT id,
        goodsid,
        wpid,
        jt.spukey,
        jt.startvalue,
        jt.endvalue
        FROM Wk_StepPrice
        CROSS JOIN JSON_TABLE(
        Wk_StepPrice.SpuJson,
        "$[*]"
        COLUMNS (
        spukey VARCHAR(255) PATH "$.key",
        startvalue VARCHAR(255) PATH "$.startval",
        endvalue VARCHAR(255) PATH "$.endval"
        )
        ) AS jt
        where Wk_StepPrice.Goodsid = #{goodsid}
        and Wk_StepPrice.Wpid = #{wpid}
        and Wk_StepPrice.Tenantid = #{tid}) as t
        where ${sql}
        group by id
        having Count(0) =#{size}
        order by Count(0)
        desc limit 1
    </select>
    <select id="getPrice" resultType="java.lang.Double">
        select Price
        from Wk_StepPriceItem
        where Wk_StepPriceItem.Pid = #{id}
        and #{quantity} between Wk_StepPriceItem.StartQty and Wk_StepPriceItem.EndQty
        and Wk_StepPriceItem.Tenantid = #{tid} limit 1
    </select>
    <select id="getMachItemList" resultType="java.util.Map">
        select id,
        Goodsid,
        Quantity,
        AttributeJson
        from Bus_MachiningItem
        where pid = #{id}
        and tenantid = #{tid}
    </select>
</mapper>

