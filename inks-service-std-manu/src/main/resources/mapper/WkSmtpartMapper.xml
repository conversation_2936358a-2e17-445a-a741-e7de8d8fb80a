<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSmtpartMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSmtpartPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_SmtPart.id,
               Wk_SmtPart.RefNo,
               Wk_SmtPart.BillType,
               Wk_SmtPart.BillTitle,
               Wk_SmtPart.BillDate,
               Wk_SmtPart.WorkUid,
               Wk_SmtPart.WorkItemid,
               Wk_SmtPart.MachUid,
               Wk_SmtPart.MachItemid,
               Wk_SmtPart.MachGroupid,
               Wk_SmtPart.Operator,
               Wk_SmtPart.WorkLine,
               Wk_SmtPart.LayerType,
               Wk_SmtPart.StationTotal,
               Wk_SmtPart.PointTotal,
               Wk_SmtPart.Summary,
               Wk_SmtPart.CreateBy,
               Wk_SmtPart.CreateByid,
               Wk_SmtPart.CreateDate,
               Wk_SmtPart.Lister,
               Wk_SmtPart.Listerid,
               Wk_SmtPart.ModifyDate,
               Wk_SmtPart.Assessor,
               Wk_SmtPart.Assessorid,
               Wk_SmtPart.AssessDate,
               Wk_SmtPart.Custom1,
               Wk_SmtPart.Custom2,
               Wk_SmtPart.Custom3,
               Wk_SmtPart.Custom4,
               Wk_SmtPart.Custom5,
               Wk_SmtPart.Custom6,
               Wk_SmtPart.Custom7,
               Wk_SmtPart.Custom8,
               Wk_SmtPart.Custom9,
               Wk_SmtPart.Custom10,
               Wk_SmtPart.Tenantid,
               Wk_SmtPart.Revision,
               Wk_WorksheetItem.Quantity,
               Wk_WorksheetItem.StartDate,
               Wk_WorksheetItem.PlanDate,
               Wk_WorksheetItem.Goodsid
        FROM Wk_SmtPart
                 LEFT JOIN Wk_WorksheetItem ON Wk_SmtPart.WorkItemid = Wk_WorksheetItem.id
                 LEFT JOIN Mat_Goods ON Wk_WorksheetItem.Goodsid = Mat_Goods.id
        where Wk_SmtPart.id = #{key}
          and Wk_SmtPart.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_SmtPart.id,
               Wk_SmtPart.RefNo,
               Wk_SmtPart.BillType,
               Wk_SmtPart.BillTitle,
               Wk_SmtPart.BillDate,
               Wk_SmtPart.WorkUid,
               Wk_SmtPart.WorkItemid,
               Wk_SmtPart.MachUid,
               Wk_SmtPart.MachItemid,
               Wk_SmtPart.MachGroupid,
               Wk_SmtPart.Operator,
               Wk_SmtPart.Operator,
               Wk_SmtPart.WorkLine,
               Wk_SmtPart.LayerType,
               Wk_SmtPart.StationTotal,
               Wk_SmtPart.PointTotal,
               Wk_SmtPart.Summary,
               Wk_SmtPart.CreateBy,
               Wk_SmtPart.CreateByid,
               Wk_SmtPart.CreateDate,
               Wk_SmtPart.Lister,
               Wk_SmtPart.Listerid,
               Wk_SmtPart.ModifyDate,
               Wk_SmtPart.Assessor,
               Wk_SmtPart.Assessorid,
               Wk_SmtPart.AssessDate,
               Wk_SmtPart.Custom1,
               Wk_SmtPart.Custom2,
               Wk_SmtPart.Custom3,
               Wk_SmtPart.Custom4,
               Wk_SmtPart.Custom5,
               Wk_SmtPart.Custom6,
               Wk_SmtPart.Custom7,
               Wk_SmtPart.Custom8,
               Wk_SmtPart.Custom9,
               Wk_SmtPart.Custom10,
               Wk_SmtPart.Tenantid,
               Wk_SmtPart.Revision,
               Wk_WorksheetItem.Quantity,
               Wk_WorksheetItem.StartDate,
               Wk_WorksheetItem.PlanDate,
               Wk_WorksheetItem.Goodsid
        FROM Wk_SmtPart
                 LEFT JOIN Wk_WorksheetItem ON Wk_SmtPart.WorkItemid = Wk_WorksheetItem.id
                 LEFT JOIN Mat_Goods ON Wk_WorksheetItem.Goodsid = Mat_Goods.id
    </sql>
    <sql id="selectdetailVo">
        select id,
               RefNo,
               BillType,
               BillTitle,
               BillDate,
               WorkUid,
               WorkItemid,
               MachUid,
               MachItemid,
               MachGroupid,
               Operator,
               Operator,
               WorkLine,
               LayerType,
               StationTotal,
               PointTotal,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Assessor,
               Assessorid,
               AssessDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Wk_SmtPart
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSmtpartitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_SmtPart.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_SmtPart.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Wk_SmtPart.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Wk_SmtPart.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Wk_SmtPart.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Wk_SmtPart.workuid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            and Wk_SmtPart.workitemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_SmtPart.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_SmtPart.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_SmtPart.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Wk_SmtPart.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.workline != null and SearchPojo.workline != ''">
            and Wk_SmtPart.WorkLine like concat('%', #{SearchPojo.workline}, '%')
        </if>
        <if test="SearchPojo.layertype != null and SearchPojo.layertype != ''">
            and Wk_SmtPart.LayerType like concat('%', #{SearchPojo.layertype}, '%')
        </if>

        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Wk_SmtPart.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Wk_SmtPart.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Wk_SmtPart.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Wk_SmtPart.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Wk_SmtPart.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Wk_SmtPart.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Wk_SmtPart.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_SmtPart.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_SmtPart.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_SmtPart.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_SmtPart.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_SmtPart.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_SmtPart.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_SmtPart.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_SmtPart.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_SmtPart.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_SmtPart.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <!--货品查询 -->
        <if test="SearchPojo.goodsuid != null and SearchPojo.goodsuid  != ''">
            and Mat_Goods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
        </if>
        <if test="SearchPojo.goodsname != null and SearchPojo.goodsname  != ''">
            and Mat_Goods.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null and SearchPojo.goodsspec  != ''">
            and Mat_Goods.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
        </if>
        <if test="SearchPojo.partid != null and SearchPojo.partid  != ''">
            and Mat_Goods.Partid like concat('%', #{SearchPojo.partid}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or Wk_SmtPart.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or Wk_SmtPart.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or Wk_SmtPart.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            or Wk_SmtPart.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            or Wk_SmtPart.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            or Wk_SmtPart.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            or Wk_SmtPart.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            or Wk_SmtPart.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            or Wk_SmtPart.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.workline != null and SearchPojo.workline != ''">
            or Wk_SmtPart.WorkLine like concat('%', #{SearchPojo.workline}, '%')
        </if>
        <if test="SearchPojo.layertype != null and SearchPojo.layertype != ''">
            or Wk_SmtPart.LayerType like concat('%', #{SearchPojo.layertype}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or Wk_SmtPart.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Wk_SmtPart.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Wk_SmtPart.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Wk_SmtPart.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Wk_SmtPart.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            or Wk_SmtPart.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            or Wk_SmtPart.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Wk_SmtPart.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Wk_SmtPart.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Wk_SmtPart.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Wk_SmtPart.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Wk_SmtPart.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Wk_SmtPart.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Wk_SmtPart.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Wk_SmtPart.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Wk_SmtPart.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Wk_SmtPart.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <!--货品查询 -->
        <if test="SearchPojo.goodsuid != null and SearchPojo.goodsuid != ''">
            or Mat_Goods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
        </if>
        <if test="SearchPojo.goodsname != null and SearchPojo.goodsname != ''">
            or Mat_Goods.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null and SearchPojo.goodsspec != ''">
            or Mat_Goods.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
        </if>
        <if test="SearchPojo.partid != null and SearchPojo.partid != ''">
            or Mat_Goods.Partid like concat('%', #{SearchPojo.partid}, '%')
        </if>
        )
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSmtpartPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_SmtPart.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_SmtPart.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Wk_SmtPart.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Wk_SmtPart.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Wk_SmtPart.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Wk_SmtPart.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            and Wk_SmtPart.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_SmtPart.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_SmtPart.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_SmtPart.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Wk_SmtPart.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.workline != null and SearchPojo.workline != ''">
            and Wk_SmtPart.WorkLine like concat('%', #{SearchPojo.workline}, '%')
        </if>
        <if test="SearchPojo.layertype != null and SearchPojo.layertype != ''">
            and Wk_SmtPart.LayerType like concat('%', #{SearchPojo.layertype}, '%')
        </if>

        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Wk_SmtPart.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Wk_SmtPart.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Wk_SmtPart.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Wk_SmtPart.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Wk_SmtPart.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Wk_SmtPart.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Wk_SmtPart.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_SmtPart.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_SmtPart.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_SmtPart.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_SmtPart.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_SmtPart.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_SmtPart.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_SmtPart.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_SmtPart.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_SmtPart.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_SmtPart.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or Wk_SmtPart.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or Wk_SmtPart.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or Wk_SmtPart.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            or Wk_SmtPart.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            or Wk_SmtPart.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            or Wk_SmtPart.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            or Wk_SmtPart.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            or Wk_SmtPart.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            or Wk_SmtPart.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.workline != null and SearchPojo.workline != ''">
            or Wk_SmtPart.WorkLine like concat('%', #{SearchPojo.workline}, '%')
        </if>
        <if test="SearchPojo.layertype != null and SearchPojo.layertype != ''">
            or Wk_SmtPart.LayerType like concat('%', #{SearchPojo.layertype}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or Wk_SmtPart.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Wk_SmtPart.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Wk_SmtPart.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Wk_SmtPart.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Wk_SmtPart.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            or Wk_SmtPart.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            or Wk_SmtPart.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Wk_SmtPart.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Wk_SmtPart.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Wk_SmtPart.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Wk_SmtPart.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Wk_SmtPart.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Wk_SmtPart.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Wk_SmtPart.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Wk_SmtPart.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Wk_SmtPart.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Wk_SmtPart.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        )
    </sql>


    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_SmtPart(id, RefNo, BillType, BillTitle, BillDate, WorkUid, WorkItemid, MachUid, MachItemid,
                               MachGroupid, Operator, WorkLine, LayerType, StationTotal, PointTotal, Summary, CreateBy,
                               CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate,
                               Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9,
                               Custom10, Tenantid, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{workuid}, #{workitemid}, #{machuid},
                #{machitemid}, #{machgroupid}, #{operator}, #{workline}, #{layertype}, #{stationtotal}, #{pointtotal},
                #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>
    <!--通过主键修改数据-->
    <update id="update">
        update Wk_SmtPart
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="workuid != null ">
                WorkUid =#{workuid},
            </if>
            <if test="workitemid != null ">
                WorkItemid =#{workitemid},
            </if>
            <if test="machuid != null ">
                MachUid =#{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid =#{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid =#{machgroupid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="workline != null ">
                WorkLine =#{workline},
            </if>
            <if test="layertype != null ">
                LayerType =#{layertype},
            </if>
            <if test="stationtotal != null">
                StationTotal =#{stationtotal},
            </if>
            <if test="pointtotal != null">
                PointTotal =#{pointtotal},
            </if>

            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_SmtPart
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_SmtPart
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkSmtpartPojo">
        select
        id
        from Wk_SmtPartItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>


</mapper>

