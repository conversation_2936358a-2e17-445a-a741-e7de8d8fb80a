<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkManureportitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkManureportitemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_ManuReportItem.id,
            Wk_ManuReportItem.Pid,
            Wk_ManuReportItem.WorkDate,
            Wk_ManuReportItem.WorkUid,
            Wk_ManuReportItem.WorkItemid,
            Wk_ManuReportItem.Goodsid,
            Wk_ManuReportItem.ItemCode,
            Wk_ManuReportItem.ItemName,
            Wk_ManuReportItem.ItemSpec,
            Wk_ManuReportItem.ItemUnit,
            Wk_ManuReportItem.WorkQty,
            Wk_ManuReportItem.Quantity,
            Wk_ManuReportItem.MrbQty,
            Wk_ManuReportItem.SubItemid,
            Wk_ManuReportItem.SubUse,
            Wk_ManuReportItem.SubUnit,
            Wk_ManuReportItem.SubQty,
            Wk_ManuReportItem.TaxPrice,
            Wk_ManuReportItem.TaxAmount,
            Wk_ManuReportItem.Price,
            Wk_ManuReportItem.Amount,
            Wk_ManuReportItem.TaxTotal,
            Wk_ManuReportItem.ItemTaxrate,
            Wk_ManuReportItem.StartDate,
            Wk_ManuReportItem.PlanDate,
            Wk_ManuReportItem.FinishQty,
            Wk_ManuReportItem.FinishHour,
            Wk_ManuReportItem.Closed,
            Wk_ManuReportItem.Remark,
            Wk_ManuReportItem.StateCode,
            Wk_ManuReportItem.StateDate,
            Wk_ManuReportItem.RowNum,
            Wk_ManuReportItem.MachUid,
            Wk_ManuReportItem.MachItemid,
            Wk_ManuReportItem.MachGroupid,
            Wk_ManuReportItem.MrpUid,
            Wk_ManuReportItem.MrpItemid,
            Wk_ManuReportItem.Customer,
            Wk_ManuReportItem.CustPO,
            Wk_ManuReportItem.MainPlanUid,
            Wk_ManuReportItem.MainPlanItemid,
            Wk_ManuReportItem.Location,
            Wk_ManuReportItem.BatchNo,
            Wk_ManuReportItem.DisannulMark,
            Wk_ManuReportItem.DisannulLister,
            Wk_ManuReportItem.DisannulListerid,
            Wk_ManuReportItem.DisannulDate,
            Wk_ManuReportItem.AttributeJson,
            Wk_ManuReportItem.Custom1,
            Wk_ManuReportItem.Custom2,
            Wk_ManuReportItem.Custom3,
            Wk_ManuReportItem.Custom4,
            Wk_ManuReportItem.Custom5,
            Wk_ManuReportItem.Custom6,
            Wk_ManuReportItem.Custom7,
            Wk_ManuReportItem.Custom8,
            Wk_ManuReportItem.Custom9,
            Wk_ManuReportItem.Custom10,
            Wk_ManuReportItem.Tenantid,
            Wk_ManuReportItem.Revision
        FROM
            Wk_ManuReportItem
                LEFT JOIN Mat_Goods ON Mat_Goods.id = Wk_ManuReportItem.Goodsid
        where Wk_ManuReportItem.id = #{key}
          and Wk_ManuReportItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkManureportitemVo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_ManuReportItem.id,
            Wk_ManuReportItem.Pid,
            Wk_ManuReportItem.WorkDate,
            Wk_ManuReportItem.WorkUid,
            Wk_ManuReportItem.WorkItemid,
            Wk_ManuReportItem.Goodsid,
            Wk_ManuReportItem.ItemCode,
            Wk_ManuReportItem.ItemName,
            Wk_ManuReportItem.ItemSpec,
            Wk_ManuReportItem.ItemUnit,
            Wk_ManuReportItem.WorkQty,
            Wk_ManuReportItem.Quantity,
            Wk_ManuReportItem.MrbQty,
            Wk_ManuReportItem.SubItemid,
            Wk_ManuReportItem.SubUse,
            Wk_ManuReportItem.SubUnit,
            Wk_ManuReportItem.SubQty,
            Wk_ManuReportItem.TaxPrice,
            Wk_ManuReportItem.TaxAmount,
            Wk_ManuReportItem.Price,
            Wk_ManuReportItem.Amount,
            Wk_ManuReportItem.TaxTotal,
            Wk_ManuReportItem.ItemTaxrate,
            Wk_ManuReportItem.StartDate,
            Wk_ManuReportItem.PlanDate,
            Wk_ManuReportItem.FinishQty,
            Wk_ManuReportItem.FinishHour,
            Wk_ManuReportItem.Closed,
            Wk_ManuReportItem.Remark,
            Wk_ManuReportItem.StateCode,
            Wk_ManuReportItem.StateDate,
            Wk_ManuReportItem.RowNum,
            Wk_ManuReportItem.MachUid,
            Wk_ManuReportItem.MachItemid,
            Wk_ManuReportItem.MachGroupid,
            Wk_ManuReportItem.MrpUid,
            Wk_ManuReportItem.MrpItemid,
            Wk_ManuReportItem.Customer,
            Wk_ManuReportItem.CustPO,
            Wk_ManuReportItem.MainPlanUid,
            Wk_ManuReportItem.MainPlanItemid,
            Wk_ManuReportItem.Location,
            Wk_ManuReportItem.BatchNo,
            Wk_ManuReportItem.DisannulMark,
            Wk_ManuReportItem.DisannulLister,
            Wk_ManuReportItem.DisannulListerid,
            Wk_ManuReportItem.DisannulDate,
            Wk_ManuReportItem.AttributeJson,
            Wk_ManuReportItem.Custom1,
            Wk_ManuReportItem.Custom2,
            Wk_ManuReportItem.Custom3,
            Wk_ManuReportItem.Custom4,
            Wk_ManuReportItem.Custom5,
            Wk_ManuReportItem.Custom6,
            Wk_ManuReportItem.Custom7,
            Wk_ManuReportItem.Custom8,
            Wk_ManuReportItem.Custom9,
            Wk_ManuReportItem.Custom10,
            Wk_ManuReportItem.Tenantid,
            Wk_ManuReportItem.Revision
        FROM
            Wk_ManuReportItem
                LEFT JOIN Mat_Goods ON Mat_Goods.id = Wk_ManuReportItem.Goodsid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkManureportitemPojo">
        <include refid="selectWkManureportitemVo"/>
        where 1 = 1 and Wk_ManuReportItem.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_ManuReportItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_ManuReportItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.workdate != null and SearchPojo.workdate != ''">
            and Wk_ManuReportItem.workdate like concat('%', #{SearchPojo.workdate}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Wk_ManuReportItem.workuid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            and Wk_ManuReportItem.workitemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_ManuReportItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_ManuReportItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_ManuReportItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_ManuReportItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_ManuReportItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
            and Wk_ManuReportItem.subitemid like concat('%', #{SearchPojo.subitemid}, '%')
        </if>
        <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
            and Wk_ManuReportItem.subuse like concat('%', #{SearchPojo.subuse}, '%')
        </if>
        <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
            and Wk_ManuReportItem.subunit like concat('%', #{SearchPojo.subunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_ManuReportItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_ManuReportItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_ManuReportItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_ManuReportItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_ManuReportItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Wk_ManuReportItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Wk_ManuReportItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_ManuReportItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_ManuReportItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_ManuReportItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_ManuReportItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Wk_ManuReportItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Wk_ManuReportItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Wk_ManuReportItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Wk_ManuReportItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_ManuReportItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_ManuReportItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_ManuReportItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_ManuReportItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_ManuReportItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_ManuReportItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_ManuReportItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_ManuReportItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_ManuReportItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_ManuReportItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_ManuReportItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_ManuReportItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.workdate != null and SearchPojo.workdate != ''">
                or Wk_ManuReportItem.WorkDate like concat('%', #{SearchPojo.workdate}, '%')
            </if>
            <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
                or Wk_ManuReportItem.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
                or Wk_ManuReportItem.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_ManuReportItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_ManuReportItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_ManuReportItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_ManuReportItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_ManuReportItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
                or Wk_ManuReportItem.SubItemid like concat('%', #{SearchPojo.subitemid}, '%')
            </if>
            <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
                or Wk_ManuReportItem.SubUse like concat('%', #{SearchPojo.subuse}, '%')
            </if>
            <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
                or Wk_ManuReportItem.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_ManuReportItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Wk_ManuReportItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_ManuReportItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_ManuReportItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_ManuReportItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Wk_ManuReportItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Wk_ManuReportItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_ManuReportItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_ManuReportItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_ManuReportItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_ManuReportItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Wk_ManuReportItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Wk_ManuReportItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Wk_ManuReportItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Wk_ManuReportItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_ManuReportItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_ManuReportItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_ManuReportItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_ManuReportItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_ManuReportItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_ManuReportItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_ManuReportItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_ManuReportItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_ManuReportItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_ManuReportItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_ManuReportItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkManureportitemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_ManuReportItem.id,
            Wk_ManuReportItem.Pid,
            Wk_ManuReportItem.WorkDate,
            Wk_ManuReportItem.WorkUid,
            Wk_ManuReportItem.WorkItemid,
            Wk_ManuReportItem.Goodsid,
            Wk_ManuReportItem.ItemCode,
            Wk_ManuReportItem.ItemName,
            Wk_ManuReportItem.ItemSpec,
            Wk_ManuReportItem.ItemUnit,
            Wk_ManuReportItem.WorkQty,
            Wk_ManuReportItem.Quantity,
            Wk_ManuReportItem.MrbQty,
            Wk_ManuReportItem.SubItemid,
            Wk_ManuReportItem.SubUse,
            Wk_ManuReportItem.SubUnit,
            Wk_ManuReportItem.SubQty,
            Wk_ManuReportItem.TaxPrice,
            Wk_ManuReportItem.TaxAmount,
            Wk_ManuReportItem.Price,
            Wk_ManuReportItem.Amount,
            Wk_ManuReportItem.TaxTotal,
            Wk_ManuReportItem.ItemTaxrate,
            Wk_ManuReportItem.StartDate,
            Wk_ManuReportItem.PlanDate,
            Wk_ManuReportItem.FinishQty,
            Wk_ManuReportItem.FinishHour,
            Wk_ManuReportItem.Closed,
            Wk_ManuReportItem.Remark,
            Wk_ManuReportItem.StateCode,
            Wk_ManuReportItem.StateDate,
            Wk_ManuReportItem.RowNum,
            Wk_ManuReportItem.MachUid,
            Wk_ManuReportItem.MachItemid,
            Wk_ManuReportItem.MachGroupid,
            Wk_ManuReportItem.MrpUid,
            Wk_ManuReportItem.MrpItemid,
            Wk_ManuReportItem.Customer,
            Wk_ManuReportItem.CustPO,
            Wk_ManuReportItem.MainPlanUid,
            Wk_ManuReportItem.MainPlanItemid,
            Wk_ManuReportItem.Location,
            Wk_ManuReportItem.BatchNo,
            Wk_ManuReportItem.DisannulMark,
            Wk_ManuReportItem.DisannulLister,
            Wk_ManuReportItem.DisannulListerid,
            Wk_ManuReportItem.DisannulDate,
            Wk_ManuReportItem.AttributeJson,
            Wk_ManuReportItem.Custom1,
            Wk_ManuReportItem.Custom2,
            Wk_ManuReportItem.Custom3,
            Wk_ManuReportItem.Custom4,
            Wk_ManuReportItem.Custom5,
            Wk_ManuReportItem.Custom6,
            Wk_ManuReportItem.Custom7,
            Wk_ManuReportItem.Custom8,
            Wk_ManuReportItem.Custom9,
            Wk_ManuReportItem.Custom10,
            Wk_ManuReportItem.Tenantid,
            Wk_ManuReportItem.Revision
        FROM
            Wk_ManuReportItem
                LEFT JOIN Mat_Goods ON Mat_Goods.id = Wk_ManuReportItem.Goodsid
        where Wk_ManuReportItem.Pid = #{Pid}
          and Wk_ManuReportItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_ManuReportItem(id, Pid, WorkDate, WorkUid, WorkItemid, Goodsid, ItemCode, ItemName, ItemSpec,
                                      ItemUnit, WorkQty, Quantity, MrbQty, SubItemid, SubUse, SubUnit, SubQty, TaxPrice,
                                      TaxAmount, Price, Amount, TaxTotal, ItemTaxrate, StartDate, PlanDate, FinishQty,
                                      FinishHour,  Closed, Remark, StateCode, StateDate, RowNum, MachUid,
                                      MachItemid, MachGroupid, MrpUid, MrpItemid, Customer, CustPO, MainPlanUid,
                                      MainPlanItemid, Location, BatchNo, DisannulMark, DisannulLister, DisannulListerid,
                                      DisannulDate, AttributeJson, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6,
                                      Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{workdate}, #{workuid}, #{workitemid}, #{goodsid}, #{itemcode}, #{itemname},
                #{itemspec}, #{itemunit}, #{workqty}, #{quantity}, #{mrbqty}, #{subitemid}, #{subuse}, #{subunit},
                #{subqty}, #{taxprice}, #{taxamount}, #{price}, #{amount}, #{taxtotal}, #{itemtaxrate}, #{startdate},
                #{plandate}, #{finishqty}, #{finishhour},  #{closed}, #{remark}, #{statecode},
                #{statedate}, #{rownum}, #{machuid}, #{machitemid}, #{machgroupid}, #{mrpuid}, #{mrpitemid},
                #{customer}, #{custpo}, #{mainplanuid}, #{mainplanitemid}, #{location}, #{batchno}, #{disannulmark},
                #{disannullister}, #{disannullisterid}, #{disannuldate}, #{attributejson}, #{custom1}, #{custom2},
                #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10},
                #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_ManuReportItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="workdate != null ">
                WorkDate = #{workdate},
            </if>
            <if test="workuid != null ">
                WorkUid = #{workuid},
            </if>
            <if test="workitemid != null ">
                WorkItemid = #{workitemid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="workqty != null">
                WorkQty = #{workqty},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="mrbqty != null">
                MrbQty = #{mrbqty},
            </if>
            <if test="subitemid != null ">
                SubItemid = #{subitemid},
            </if>
            <if test="subuse != null ">
                SubUse = #{subuse},
            </if>
            <if test="subunit != null ">
                SubUnit = #{subunit},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="finishhour != null">
                FinishHour = #{finishhour},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_ManuReportItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

