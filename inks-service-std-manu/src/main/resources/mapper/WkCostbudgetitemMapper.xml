<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkCostbudgetitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkCostbudgetitemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_CostBudgetItem.id,
            Wk_CostBudgetItem.Pid,
            Wk_CostBudgetItem.Goodsid,
            Wk_CostBudgetItem.ItemCode,
            Wk_CostBudgetItem.ItemName,
            Wk_CostBudgetItem.ItemSpec,
            Wk_CostBudgetItem.ItemUnit,
            Wk_CostBudgetItem.Quantity,
            Wk_CostBudgetItem.TaxPrice,
            Wk_CostBudgetItem.TaxAmount,
            Wk_CostBudgetItem.Price,
            Wk_CostBudgetItem.Amount,
            Wk_CostBudgetItem.TaxTotal,
            Wk_CostBudgetItem.ItemTaxrate,
            Wk_CostBudgetItem.StartDate,
            Wk_CostBudgetItem.PlanDate,
            Wk_CostBudgetItem.EnabledMark,
            Wk_CostBudgetItem.Closed,
            Wk_CostBudgetItem.Remark,
            Wk_CostBudgetItem.StateCode,
            Wk_CostBudgetItem.StateDate,
            Wk_CostBudgetItem.RowNum,
            Wk_CostBudgetItem.MachUid,
            Wk_CostBudgetItem.MachItemid,
            Wk_CostBudgetItem.Customer,
            Wk_CostBudgetItem.CustPO,
            Wk_CostBudgetItem.Custom1,
            Wk_CostBudgetItem.Custom2,
            Wk_CostBudgetItem.Custom3,
            Wk_CostBudgetItem.Custom4,
            Wk_CostBudgetItem.Custom5,
            Wk_CostBudgetItem.Custom6,
            Wk_CostBudgetItem.Custom7,
            Wk_CostBudgetItem.Custom8,
            Wk_CostBudgetItem.Custom9,
            Wk_CostBudgetItem.Custom10,
            Wk_CostBudgetItem.Tenantid,
            Wk_CostBudgetItem.Revision
        FROM
            Wk_CostBudgetItem
                LEFT JOIN Mat_Goods ON Wk_CostBudgetItem.Goodsid = Mat_Goods.id
        where Wk_CostBudgetItem.id = #{key}
          and Wk_CostBudgetItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkCostbudgetitemVo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_CostBudgetItem.id,
            Wk_CostBudgetItem.Pid,
            Wk_CostBudgetItem.Goodsid,
            Wk_CostBudgetItem.ItemCode,
            Wk_CostBudgetItem.ItemName,
            Wk_CostBudgetItem.ItemSpec,
            Wk_CostBudgetItem.ItemUnit,
            Wk_CostBudgetItem.Quantity,
            Wk_CostBudgetItem.TaxPrice,
            Wk_CostBudgetItem.TaxAmount,
            Wk_CostBudgetItem.Price,
            Wk_CostBudgetItem.Amount,
            Wk_CostBudgetItem.TaxTotal,
            Wk_CostBudgetItem.ItemTaxrate,
            Wk_CostBudgetItem.StartDate,
            Wk_CostBudgetItem.PlanDate,
            Wk_CostBudgetItem.EnabledMark,
            Wk_CostBudgetItem.Closed,
            Wk_CostBudgetItem.Remark,
            Wk_CostBudgetItem.StateCode,
            Wk_CostBudgetItem.StateDate,
            Wk_CostBudgetItem.RowNum,
            Wk_CostBudgetItem.MachUid,
            Wk_CostBudgetItem.MachItemid,
            Wk_CostBudgetItem.Customer,
            Wk_CostBudgetItem.CustPO,
            Wk_CostBudgetItem.Custom1,
            Wk_CostBudgetItem.Custom2,
            Wk_CostBudgetItem.Custom3,
            Wk_CostBudgetItem.Custom4,
            Wk_CostBudgetItem.Custom5,
            Wk_CostBudgetItem.Custom6,
            Wk_CostBudgetItem.Custom7,
            Wk_CostBudgetItem.Custom8,
            Wk_CostBudgetItem.Custom9,
            Wk_CostBudgetItem.Custom10,
            Wk_CostBudgetItem.Tenantid,
            Wk_CostBudgetItem.Revision
        FROM
            Wk_CostBudgetItem
                LEFT JOIN Mat_Goods ON Wk_CostBudgetItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkCostbudgetitemPojo">
        <include refid="selectWkCostbudgetitemVo"/>
        where 1 = 1 and Wk_CostBudgetItem.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_CostBudgetItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_CostBudgetItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_CostBudgetItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_CostBudgetItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_CostBudgetItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_CostBudgetItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_CostBudgetItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_CostBudgetItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_CostBudgetItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_CostBudgetItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_CostBudgetItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_CostBudgetItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_CostBudgetItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_CostBudgetItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_CostBudgetItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_CostBudgetItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_CostBudgetItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_CostBudgetItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_CostBudgetItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_CostBudgetItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_CostBudgetItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_CostBudgetItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_CostBudgetItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_CostBudgetItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_CostBudgetItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_CostBudgetItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_CostBudgetItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_CostBudgetItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_CostBudgetItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_CostBudgetItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Wk_CostBudgetItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_CostBudgetItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_CostBudgetItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_CostBudgetItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_CostBudgetItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_CostBudgetItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_CostBudgetItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_CostBudgetItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_CostBudgetItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_CostBudgetItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_CostBudgetItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_CostBudgetItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_CostBudgetItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_CostBudgetItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_CostBudgetItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkCostbudgetitemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_CostBudgetItem.id,
            Wk_CostBudgetItem.Pid,
            Wk_CostBudgetItem.Goodsid,
            Wk_CostBudgetItem.ItemCode,
            Wk_CostBudgetItem.ItemName,
            Wk_CostBudgetItem.ItemSpec,
            Wk_CostBudgetItem.ItemUnit,
            Wk_CostBudgetItem.Quantity,
            Wk_CostBudgetItem.TaxPrice,
            Wk_CostBudgetItem.TaxAmount,
            Wk_CostBudgetItem.Price,
            Wk_CostBudgetItem.Amount,
            Wk_CostBudgetItem.TaxTotal,
            Wk_CostBudgetItem.ItemTaxrate,
            Wk_CostBudgetItem.StartDate,
            Wk_CostBudgetItem.PlanDate,
            Wk_CostBudgetItem.EnabledMark,
            Wk_CostBudgetItem.Closed,
            Wk_CostBudgetItem.Remark,
            Wk_CostBudgetItem.StateCode,
            Wk_CostBudgetItem.StateDate,
            Wk_CostBudgetItem.RowNum,
            Wk_CostBudgetItem.MachUid,
            Wk_CostBudgetItem.MachItemid,
            Wk_CostBudgetItem.Customer,
            Wk_CostBudgetItem.CustPO,
            Wk_CostBudgetItem.Custom1,
            Wk_CostBudgetItem.Custom2,
            Wk_CostBudgetItem.Custom3,
            Wk_CostBudgetItem.Custom4,
            Wk_CostBudgetItem.Custom5,
            Wk_CostBudgetItem.Custom6,
            Wk_CostBudgetItem.Custom7,
            Wk_CostBudgetItem.Custom8,
            Wk_CostBudgetItem.Custom9,
            Wk_CostBudgetItem.Custom10,
            Wk_CostBudgetItem.Tenantid,
            Wk_CostBudgetItem.Revision
        FROM
            Wk_CostBudgetItem
                LEFT JOIN Mat_Goods ON Wk_CostBudgetItem.Goodsid = Mat_Goods.id
        where Wk_CostBudgetItem.Pid = #{Pid}
          and Wk_CostBudgetItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_CostBudgetItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice,
                                      TaxAmount, Price, Amount, TaxTotal, ItemTaxrate, StartDate, PlanDate, EnabledMark,
                                      Closed, Remark, StateCode, StateDate, RowNum, MachUid, MachItemid, Customer,
                                      CustPO, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                      Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{taxprice},
                #{taxamount}, #{price}, #{amount}, #{taxtotal}, #{itemtaxrate}, #{startdate}, #{plandate},
                #{enabledmark}, #{closed}, #{remark}, #{statecode}, #{statedate}, #{rownum}, #{machuid}, #{machitemid},
                #{customer}, #{custpo}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_CostBudgetItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_CostBudgetItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

