<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWiptasksMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWiptasksPojo">
        <include refid="selectbillVo"/>
        where Wk_WipTasks.id = #{key} and Wk_WipTasks.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select
id, RefNo, BillType, BillTitle, BillDate, Wpid, WpCode, WpName, Stationid, StatCode, StatName, PlanDate, Su<PERSON>ry, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt, <PERSON>ish<PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, <PERSON><PERSON>d, <PERSON>di<PERSON><PERSON>ate, <PERSON><PERSON>sor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Wk_WipTasks
    </sql>
    <sql id="selectdetailVo">
        select
               Wk_WipTasks.RefNo,
               Wk_WipTasks.BillType,
               Wk_WipTasks.BillTitle,
               Wk_WipTasks.BillDate,
               Wk_WipTasks.CreateBy,
               Wk_WipTasks.Lister,
               Wk_WipTasks.Assessor,
               Wk_WipTasks.Summary,
               Wk_WipTasksItem.*
        from Wk_WipTasksItem left join Wk_WipTasks on Wk_WipTasks.id = Wk_WipTasksItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkWiptasksitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_WipTasks.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipTasks.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by Wk_WipTasksItem.Weight desc, Wk_WipTasksItem.ItemPlanDate asc
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Wk_WipTasks.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_WipTasks.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_WipTasks.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.wpid != null ">
            and Wk_WipTasks.Wpid like concat('%', #{SearchPojo.wpid}, '%')
        </if>
        <if test="SearchPojo.wpcode != null ">
            and Wk_WipTasks.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
        </if>
        <if test="SearchPojo.wpname != null ">
            and Wk_WipTasks.WpName like concat('%', #{SearchPojo.wpname}, '%')
        </if>
        <if test="SearchPojo.stationid != null ">
            and Wk_WipTasks.Stationid like concat('%', #{SearchPojo.stationid}, '%')
        </if>
        <if test="SearchPojo.statcode != null ">
            and Wk_WipTasks.StatCode like concat('%', #{SearchPojo.statcode}, '%')
        </if>
        <if test="SearchPojo.statname != null ">
            and Wk_WipTasks.StatName like concat('%', #{SearchPojo.statname}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_WipTasks.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_WipTasks.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_WipTasks.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_WipTasks.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_WipTasks.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_WipTasks.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_WipTasks.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_WipTasks.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_WipTasks.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_WipTasks.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_WipTasks.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_WipTasks.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_WipTasks.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_WipTasks.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_WipTasks.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_WipTasks.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_WipTasks.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_WipTasks.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_WipTasksItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_WipTasksItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_WipTasksItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_WipTasksItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_WipTasksItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Wk_WipTasksItem.workuid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            and Wk_WipTasksItem.workitemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_WipTasks.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_WipTasks.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_WipTasks.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.wpid != null ">
                or Wk_WipTasks.Wpid like concat('%', #{SearchPojo.wpid}, '%')
            </if>
            <if test="SearchPojo.wpcode != null ">
                or Wk_WipTasks.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
            </if>
            <if test="SearchPojo.wpname != null ">
                or Wk_WipTasks.WpName like concat('%', #{SearchPojo.wpname}, '%')
            </if>
            <if test="SearchPojo.stationid != null ">
                or Wk_WipTasks.Stationid like concat('%', #{SearchPojo.stationid}, '%')
            </if>
            <if test="SearchPojo.statcode != null ">
                or Wk_WipTasks.StatCode like concat('%', #{SearchPojo.statcode}, '%')
            </if>
            <if test="SearchPojo.statname != null ">
                or Wk_WipTasks.StatName like concat('%', #{SearchPojo.statname}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_WipTasks.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_WipTasks.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_WipTasks.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_WipTasks.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_WipTasks.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_WipTasks.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_WipTasks.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_WipTasks.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_WipTasks.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_WipTasks.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_WipTasks.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_WipTasks.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_WipTasks.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_WipTasks.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_WipTasks.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_WipTasks.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_WipTasks.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_WipTasks.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_WipTasksItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_WipTasksItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_WipTasksItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_WipTasksItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_WipTasksItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
                or Wk_WipTasksItem.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
                or Wk_WipTasksItem.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkWiptasksPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_WipTasks.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipTasks.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Wk_WipTasks.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_WipTasks.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_WipTasks.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.wpid != null ">
            and Wk_WipTasks.Wpid like concat('%', #{SearchPojo.wpid}, '%')
        </if>
        <if test="SearchPojo.wpcode != null ">
            and Wk_WipTasks.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
        </if>
        <if test="SearchPojo.wpname != null ">
            and Wk_WipTasks.WpName like concat('%', #{SearchPojo.wpname}, '%')
        </if>
        <if test="SearchPojo.stationid != null ">
            and Wk_WipTasks.Stationid like concat('%', #{SearchPojo.stationid}, '%')
        </if>
        <if test="SearchPojo.statcode != null ">
            and Wk_WipTasks.StatCode like concat('%', #{SearchPojo.statcode}, '%')
        </if>
        <if test="SearchPojo.statname != null ">
            and Wk_WipTasks.StatName like concat('%', #{SearchPojo.statname}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_WipTasks.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_WipTasks.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_WipTasks.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_WipTasks.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_WipTasks.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_WipTasks.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_WipTasks.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_WipTasks.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_WipTasks.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_WipTasks.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_WipTasks.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_WipTasks.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_WipTasks.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_WipTasks.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_WipTasks.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_WipTasks.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_WipTasks.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_WipTasks.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_WipTasks.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_WipTasks.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_WipTasks.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.wpid != null ">
                or Wk_WipTasks.Wpid like concat('%', #{SearchPojo.wpid}, '%')
            </if>
            <if test="SearchPojo.wpcode != null ">
                or Wk_WipTasks.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
            </if>
            <if test="SearchPojo.wpname != null ">
                or Wk_WipTasks.WpName like concat('%', #{SearchPojo.wpname}, '%')
            </if>
            <if test="SearchPojo.stationid != null ">
                or Wk_WipTasks.Stationid like concat('%', #{SearchPojo.stationid}, '%')
            </if>
            <if test="SearchPojo.statcode != null ">
                or Wk_WipTasks.StatCode like concat('%', #{SearchPojo.statcode}, '%')
            </if>
            <if test="SearchPojo.statname != null ">
                or Wk_WipTasks.StatName like concat('%', #{SearchPojo.statname}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_WipTasks.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_WipTasks.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_WipTasks.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_WipTasks.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_WipTasks.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_WipTasks.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_WipTasks.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_WipTasks.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_WipTasks.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_WipTasks.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_WipTasks.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_WipTasks.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_WipTasks.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_WipTasks.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_WipTasks.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_WipTasks.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_WipTasks.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_WipTasks.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_WipTasks(id, RefNo, BillType, BillTitle, BillDate, Wpid, WpCode, WpName, Stationid, StatCode, StatName, PlanDate, Summary, DisannulCount, FinishCount, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{wpid}, #{wpcode}, #{wpname}, #{stationid}, #{statcode}, #{statname}, #{plandate}, #{summary}, #{disannulcount}, #{finishcount}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipTasks
        <set>
            <if test="refno != null ">
            RefNo =#{refno},
        </if>
            <if test="billtype != null ">
            BillType =#{billtype},
        </if>
            <if test="billtitle != null ">
            BillTitle =#{billtitle},
        </if>
            <if test="billdate != null">
            BillDate =#{billdate},
        </if>
            <if test="wpid != null ">
            Wpid =#{wpid},
        </if>
            <if test="wpcode != null ">
            WpCode =#{wpcode},
        </if>
            <if test="wpname != null ">
            WpName =#{wpname},
        </if>
            <if test="stationid != null ">
            Stationid =#{stationid},
        </if>
            <if test="statcode != null ">
            StatCode =#{statcode},
        </if>
            <if test="statname != null ">
            StatName =#{statname},
        </if>
            <if test="plandate != null">
            PlanDate =#{plandate},
        </if>
            <if test="summary != null ">
            Summary =#{summary},
        </if>
            <if test="disannulcount != null">
            DisannulCount =#{disannulcount},
        </if>
            <if test="finishcount != null">
            FinishCount =#{finishcount},
        </if>
            <if test="createby != null ">
            CreateBy =#{createby},
        </if>
            <if test="createbyid != null ">
            CreateByid =#{createbyid},
        </if>
            <if test="createdate != null">
            CreateDate =#{createdate},
        </if>
            <if test="lister != null ">
            Lister =#{lister},
        </if>
            <if test="listerid != null ">
            Listerid =#{listerid},
        </if>
            <if test="modifydate != null">
            ModifyDate =#{modifydate},
        </if>
            <if test="assessor != null ">
            Assessor =#{assessor},
        </if>
            <if test="assessorid != null ">
            Assessorid =#{assessorid},
        </if>
            <if test="assessdate != null">
            AssessDate =#{assessdate},
        </if>
            <if test="custom1 != null ">
            Custom1 =#{custom1},
        </if>
            <if test="custom2 != null ">
            Custom2 =#{custom2},
        </if>
            <if test="custom3 != null ">
            Custom3 =#{custom3},
        </if>
            <if test="custom4 != null ">
            Custom4 =#{custom4},
        </if>
            <if test="custom5 != null ">
            Custom5 =#{custom5},
        </if>
            <if test="custom6 != null ">
            Custom6 =#{custom6},
        </if>
            <if test="custom7 != null ">
            Custom7 =#{custom7},
        </if>
            <if test="custom8 != null ">
            Custom8 =#{custom8},
        </if>
            <if test="custom9 != null ">
            Custom9 =#{custom9},
        </if>
            <if test="custom10 != null ">
            Custom10 =#{custom10},
        </if>
            <if test="tenantname != null ">
            TenantName =#{tenantname},
        </if>
        Revision=Revision+1
    </set>
    where id = #{id} and Tenantid =#{tenantid}
</update>

        <!--通过主键删除-->
<delete id="delete">
delete from Wk_WipTasks where id = #{key} and Tenantid=#{tid}
</delete>
        <!--通过主键审核数据-->
<update id="approval">
update Wk_WipTasks SET
Assessor = #{assessor},
Assessorid = #{assessorid},
AssessDate = #{assessdate},
Revision=Revision+1
where id = #{id}
and Tenantid = #{tenantid}
</update>
        <!--查询DelListIds-->
<select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.manu.domain.pojo.WkWiptasksPojo">
select
id
from Wk_WipTasksItem
where Pid = #{id}
<if test="item !=null and item.size()>0">
    and id not in
    <foreach collection="item" open="(" close=")" separator="," item="item">
        <if test="item.id != null">
            #{item.id}
        </if>
        <if test="item.id == null">
            ''
        </if>
    </foreach>
</if>
</select>

    <select id="getWkWpNameByMachitemid" resultType="java.lang.String">
        select WkWpName
        from Bus_MachiningItem
        where id = #{machitemid}
        and Tenantid = #{tid}
    </select>

    <select id="getWkWpNameByWorkitemid" resultType="java.lang.String">
        select WkWpName
        from Wk_WorksheetItem
        where id = #{workitemid}
        and Tenantid = #{tid}
    </select>

    <update id="updateDisannulCount">
        update Wk_WipTasks
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Wk_WipTasksItem
                                     where Wk_WipTasksItem.Pid = #{pid}
                                       and Wk_WipTasksItem.Tenantid = #{tid}
                                       and Wk_WipTasksItem.DisannulMark = 1), 0)
        where id = #{pid}
          and Tenantid = #{tid}
    </update>
    <update id="updateFinishCount">
        update Wk_WipTasks
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_WipTasksItem
                                   where Wk_WipTasksItem.Pid = #{pid}
                                     and Wk_WipTasksItem.Tenantid = #{tid}
                                     and (Wk_WipTasksItem.Closed = 1
                                       or Wk_WipTasksItem.FinishQty >= Wk_WipTasksItem.Quantity)), 0)
        where id = #{pid}
          and Tenantid = #{tid}
    </update>

<!--    反查内容：根据wipitemid查D05M05B1生产WIP的wkwpname，Wk_WipTasks主表的WpName也返回。-->
    <select id="getOnlinePageListByWip" resultType="inks.service.std.manu.domain.pojo.WkWiptasksitemdetailPojo">
        select Wk_WipTasks.RefNo,
               Wk_WipTasks.BillType,
               Wk_WipTasks.BillTitle,
               Wk_WipTasks.BillDate,
               Wk_WipTasks.Summary,
               Wk_WipTasks.WpName,
               Wk_WipNote.WkWpName,
               Wk_WipTasksItem.*
        from Wk_WipTasksItem
                 left join Wk_WipTasks on Wk_WipTasks.id = Wk_WipTasksItem.Pid
                 left join Wk_WipNoteItem on Wk_WipNoteItem.id = Wk_WipTasksItem.Wipitemid
                 left join Wk_WipNote on Wk_WipNote.id = Wk_WipNoteItem.Pid
        where Wk_WipTasksItem.Closed = 0
          and Wk_WipTasksItem.DisannulMark = 0
          and (Wk_WipNoteItem.OutPcsQty <![CDATA[<]]> Wk_WipNoteItem.InPcsQty OR
               (Wk_WipNoteItem.OutPcsQty = 0 AND Wk_WipNoteItem.InPcsQty = 0))
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipTasks.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by Wk_WipTasksItem.Weight desc, Wk_WipTasksItem.ItemPlanDate
    </select>
</mapper>

