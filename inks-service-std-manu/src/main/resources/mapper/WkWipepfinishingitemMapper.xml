<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipepfinishingitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipepfinishingitemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_WipEpFinishingItem.id,
            Wk_WipEpFinishingItem.Pid,
            Wk_WipEpFinishingItem.Goodsid,
            Wk_WipEpFinishingItem.Quantity,
            Wk_WipEpFinishingItem.MrbQty,
            Wk_WipEpFinishingItem.SubItemid,
            Wk_WipEpFinishingItem.SubUse,
            Wk_WipEpFinishingItem.SubUnit,
            Wk_WipEpFinishingItem.SubQty,
            Wk_WipEpFinishingItem.TaxPrice,
            Wk_WipEpFinishingItem.TaxAmount,
            Wk_WipEpFinishingItem.TaxTotal,
            Wk_WipEpFinishingItem.Price,
            Wk_WipEpFinishingItem.Amount,
            Wk_WipEpFinishingItem.ItemTaxrate,
            Wk_WipEpFinishingItem.Remark,
            Wk_WipEpFinishingItem.CiteUid,
            Wk_WipEpFinishingItem.CiteItemid,
            Wk_WipEpFinishingItem.StateCode,
            Wk_WipEpFinishingItem.StateDate,
            Wk_WipEpFinishingItem.Inspected,
            Wk_WipEpFinishingItem.Closed,
            Wk_WipEpFinishingItem.RowNum,
            Wk_WipEpFinishingItem.InvoQty,
            Wk_WipEpFinishingItem.InvoClosed,
            Wk_WipEpFinishingItem.VirtualItem,
            Wk_WipEpFinishingItem.WipItemid,
            Wk_WipEpFinishingItem.Wsid,
            Wk_WipEpFinishingItem.WsUid,
            Wk_WipEpFinishingItem.Wpid,
            Wk_WipEpFinishingItem.EndWpid,
            Wk_WipEpFinishingItem.Customer,
            Wk_WipEpFinishingItem.CustPO,
            Wk_WipEpFinishingItem.MachUid,
            Wk_WipEpFinishingItem.MachItemid,
            Wk_WipEpFinishingItem.MainPlanUid,
            Wk_WipEpFinishingItem.MainPlanItemid,
            Wk_WipEpFinishingItem.MachGroupid,
            Wk_WipEpFinishingItem.DisannulMark,
            Wk_WipEpFinishingItem.DisannulListerid,
            Wk_WipEpFinishingItem.DisannulLister,
            Wk_WipEpFinishingItem.DisannulDate,
            Wk_WipEpFinishingItem.AttributeJson,
            Wk_WipEpFinishingItem.Custom1,
            Wk_WipEpFinishingItem.Custom2,
            Wk_WipEpFinishingItem.Custom3,
            Wk_WipEpFinishingItem.Custom4,
            Wk_WipEpFinishingItem.Custom5,
            Wk_WipEpFinishingItem.Custom6,
            Wk_WipEpFinishingItem.Custom7,
            Wk_WipEpFinishingItem.Custom8,
            Wk_WipEpFinishingItem.Custom9,
            Wk_WipEpFinishingItem.Custom10,
            Wk_WipEpFinishingItem.Tenantid,
            Wk_WipEpFinishingItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_WipEpFinishingItem ON Mat_Goods.id = Wk_WipEpFinishingItem.Goodsid
        where Wk_WipEpFinishingItem.id = #{key}
          and Wk_WipEpFinishingItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkWipepfinishingitemVo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_WipEpFinishingItem.id,
            Wk_WipEpFinishingItem.Pid,
            Wk_WipEpFinishingItem.Goodsid,
            Wk_WipEpFinishingItem.Quantity,
            Wk_WipEpFinishingItem.MrbQty,
            Wk_WipEpFinishingItem.SubItemid,
            Wk_WipEpFinishingItem.SubUse,
            Wk_WipEpFinishingItem.SubUnit,
            Wk_WipEpFinishingItem.SubQty,
            Wk_WipEpFinishingItem.TaxPrice,
            Wk_WipEpFinishingItem.TaxAmount,
            Wk_WipEpFinishingItem.TaxTotal,
            Wk_WipEpFinishingItem.Price,
            Wk_WipEpFinishingItem.Amount,
            Wk_WipEpFinishingItem.ItemTaxrate,
            Wk_WipEpFinishingItem.Remark,
            Wk_WipEpFinishingItem.CiteUid,
            Wk_WipEpFinishingItem.CiteItemid,
            Wk_WipEpFinishingItem.StateCode,
            Wk_WipEpFinishingItem.StateDate,
            Wk_WipEpFinishingItem.Inspected,
            Wk_WipEpFinishingItem.Closed,
            Wk_WipEpFinishingItem.RowNum,
            Wk_WipEpFinishingItem.InvoQty,
            Wk_WipEpFinishingItem.InvoClosed,
            Wk_WipEpFinishingItem.VirtualItem,
            Wk_WipEpFinishingItem.WipItemid,
            Wk_WipEpFinishingItem.Wsid,
            Wk_WipEpFinishingItem.WsUid,
            Wk_WipEpFinishingItem.Wpid,
            Wk_WipEpFinishingItem.EndWpid,
            Wk_WipEpFinishingItem.Customer,
            Wk_WipEpFinishingItem.CustPO,
            Wk_WipEpFinishingItem.MachUid,
            Wk_WipEpFinishingItem.MachItemid,
            Wk_WipEpFinishingItem.MainPlanUid,
            Wk_WipEpFinishingItem.MainPlanItemid,
            Wk_WipEpFinishingItem.MachGroupid,
            Wk_WipEpFinishingItem.DisannulMark,
            Wk_WipEpFinishingItem.DisannulListerid,
            Wk_WipEpFinishingItem.DisannulLister,
            Wk_WipEpFinishingItem.DisannulDate,
            Wk_WipEpFinishingItem.AttributeJson,
            Wk_WipEpFinishingItem.Custom1,
            Wk_WipEpFinishingItem.Custom2,
            Wk_WipEpFinishingItem.Custom3,
            Wk_WipEpFinishingItem.Custom4,
            Wk_WipEpFinishingItem.Custom5,
            Wk_WipEpFinishingItem.Custom6,
            Wk_WipEpFinishingItem.Custom7,
            Wk_WipEpFinishingItem.Custom8,
            Wk_WipEpFinishingItem.Custom9,
            Wk_WipEpFinishingItem.Custom10,
            Wk_WipEpFinishingItem.Tenantid,
            Wk_WipEpFinishingItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_WipEpFinishingItem ON Mat_Goods.id = Wk_WipEpFinishingItem.Goodsid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipepfinishingitemPojo">
        <include refid="selectWkWipepfinishingitemVo"/>
        where 1 = 1 and Wk_WipEpFinishingItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipEpFinishingItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_WipEpFinishingItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_WipEpFinishingItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
            and Wk_WipEpFinishingItem.subitemid like concat('%', #{SearchPojo.subitemid}, '%')
        </if>
        <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
            and Wk_WipEpFinishingItem.subuse like concat('%', #{SearchPojo.subuse}, '%')
        </if>
        <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
            and Wk_WipEpFinishingItem.subunit like concat('%', #{SearchPojo.subunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_WipEpFinishingItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Wk_WipEpFinishingItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Wk_WipEpFinishingItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_WipEpFinishingItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.wipitemid != null and SearchPojo.wipitemid != ''">
            and Wk_WipEpFinishingItem.wipitemid like concat('%', #{SearchPojo.wipitemid}, '%')
        </if>
        <if test="SearchPojo.wsid != null and SearchPojo.wsid != ''">
            and Wk_WipEpFinishingItem.wsid like concat('%', #{SearchPojo.wsid}, '%')
        </if>
        <if test="SearchPojo.wsuid != null and SearchPojo.wsuid != ''">
            and Wk_WipEpFinishingItem.wsuid like concat('%', #{SearchPojo.wsuid}, '%')
        </if>
        <if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
            and Wk_WipEpFinishingItem.wpid like concat('%', #{SearchPojo.wpid}, '%')
        </if>
        <if test="SearchPojo.endwpid != null and SearchPojo.endwpid != ''">
            and Wk_WipEpFinishingItem.endwpid like concat('%', #{SearchPojo.endwpid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_WipEpFinishingItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_WipEpFinishingItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_WipEpFinishingItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_WipEpFinishingItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_WipEpFinishingItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_WipEpFinishingItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_WipEpFinishingItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Wk_WipEpFinishingItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Wk_WipEpFinishingItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_WipEpFinishingItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_WipEpFinishingItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_WipEpFinishingItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_WipEpFinishingItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_WipEpFinishingItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_WipEpFinishingItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_WipEpFinishingItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_WipEpFinishingItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_WipEpFinishingItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_WipEpFinishingItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_WipEpFinishingItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_WipEpFinishingItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_WipEpFinishingItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
                or Wk_WipEpFinishingItem.SubItemid like concat('%', #{SearchPojo.subitemid}, '%')
            </if>
            <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
                or Wk_WipEpFinishingItem.SubUse like concat('%', #{SearchPojo.subuse}, '%')
            </if>
            <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
                or Wk_WipEpFinishingItem.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_WipEpFinishingItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Wk_WipEpFinishingItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Wk_WipEpFinishingItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Wk_WipEpFinishingItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.wipitemid != null and SearchPojo.wipitemid != ''">
                or Wk_WipEpFinishingItem.WipItemid like concat('%', #{SearchPojo.wipitemid}, '%')
            </if>
            <if test="SearchPojo.wsid != null and SearchPojo.wsid != ''">
                or Wk_WipEpFinishingItem.Wsid like concat('%', #{SearchPojo.wsid}, '%')
            </if>
            <if test="SearchPojo.wsuid != null and SearchPojo.wsuid != ''">
                or Wk_WipEpFinishingItem.WsUid like concat('%', #{SearchPojo.wsuid}, '%')
            </if>
            <if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
                or Wk_WipEpFinishingItem.Wpid like concat('%', #{SearchPojo.wpid}, '%')
            </if>
            <if test="SearchPojo.endwpid != null and SearchPojo.endwpid != ''">
                or Wk_WipEpFinishingItem.EndWpid like concat('%', #{SearchPojo.endwpid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_WipEpFinishingItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_WipEpFinishingItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_WipEpFinishingItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_WipEpFinishingItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_WipEpFinishingItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_WipEpFinishingItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_WipEpFinishingItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Wk_WipEpFinishingItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Wk_WipEpFinishingItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_WipEpFinishingItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_WipEpFinishingItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_WipEpFinishingItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_WipEpFinishingItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_WipEpFinishingItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_WipEpFinishingItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_WipEpFinishingItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_WipEpFinishingItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_WipEpFinishingItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_WipEpFinishingItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_WipEpFinishingItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkWipepfinishingitemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_WipEpFinishingItem.id,
            Wk_WipEpFinishingItem.Pid,
            Wk_WipEpFinishingItem.Goodsid,
            Wk_WipEpFinishingItem.Quantity,
            Wk_WipEpFinishingItem.MrbQty,
            Wk_WipEpFinishingItem.SubItemid,
            Wk_WipEpFinishingItem.SubUse,
            Wk_WipEpFinishingItem.SubUnit,
            Wk_WipEpFinishingItem.SubQty,
            Wk_WipEpFinishingItem.TaxPrice,
            Wk_WipEpFinishingItem.TaxAmount,
            Wk_WipEpFinishingItem.TaxTotal,
            Wk_WipEpFinishingItem.Price,
            Wk_WipEpFinishingItem.Amount,
            Wk_WipEpFinishingItem.ItemTaxrate,
            Wk_WipEpFinishingItem.Remark,
            Wk_WipEpFinishingItem.CiteUid,
            Wk_WipEpFinishingItem.CiteItemid,
            Wk_WipEpFinishingItem.StateCode,
            Wk_WipEpFinishingItem.StateDate,
            Wk_WipEpFinishingItem.Inspected,
            Wk_WipEpFinishingItem.Closed,
            Wk_WipEpFinishingItem.RowNum,
            Wk_WipEpFinishingItem.InvoQty,
            Wk_WipEpFinishingItem.InvoClosed,
            Wk_WipEpFinishingItem.VirtualItem,
            Wk_WipEpFinishingItem.WipItemid,
            Wk_WipEpFinishingItem.Wsid,
            Wk_WipEpFinishingItem.WsUid,
            Wk_WipEpFinishingItem.Wpid,
            Wk_WipEpFinishingItem.EndWpid,
            Wk_WipEpFinishingItem.Customer,
            Wk_WipEpFinishingItem.CustPO,
            Wk_WipEpFinishingItem.MachUid,
            Wk_WipEpFinishingItem.MachItemid,
            Wk_WipEpFinishingItem.MainPlanUid,
            Wk_WipEpFinishingItem.MainPlanItemid,
            Wk_WipEpFinishingItem.MachGroupid,
            Wk_WipEpFinishingItem.DisannulMark,
            Wk_WipEpFinishingItem.DisannulListerid,
            Wk_WipEpFinishingItem.DisannulLister,
            Wk_WipEpFinishingItem.DisannulDate,
            Wk_WipEpFinishingItem.AttributeJson,
            Wk_WipEpFinishingItem.Custom1,
            Wk_WipEpFinishingItem.Custom2,
            Wk_WipEpFinishingItem.Custom3,
            Wk_WipEpFinishingItem.Custom4,
            Wk_WipEpFinishingItem.Custom5,
            Wk_WipEpFinishingItem.Custom6,
            Wk_WipEpFinishingItem.Custom7,
            Wk_WipEpFinishingItem.Custom8,
            Wk_WipEpFinishingItem.Custom9,
            Wk_WipEpFinishingItem.Custom10,
            Wk_WipEpFinishingItem.Tenantid,
            Wk_WipEpFinishingItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_WipEpFinishingItem ON Mat_Goods.id = Wk_WipEpFinishingItem.Goodsid
        where Wk_WipEpFinishingItem.Pid = #{Pid}
          and Wk_WipEpFinishingItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_WipEpFinishingItem(id, Pid, Goodsid, Quantity, MrbQty, SubItemid, SubUse, SubUnit, SubQty,
                                          TaxPrice, TaxAmount, TaxTotal, Price, Amount, ItemTaxrate, Remark, CiteUid,
                                          CiteItemid, StateCode, StateDate, Inspected, Closed, RowNum, InvoQty,
                                          InvoClosed, VirtualItem, WipItemid, Wsid, WsUid, Wpid, EndWpid, Customer,
                                          CustPO, MachUid, MachItemid, MainPlanUid, MainPlanItemid, MachGroupid,
                                          DisannulMark, DisannulListerid, DisannulLister, DisannulDate, AttributeJson,
                                          Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                          Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{quantity}, #{mrbqty}, #{subitemid}, #{subuse}, #{subunit}, #{subqty},
                #{taxprice}, #{taxamount}, #{taxtotal}, #{price}, #{amount}, #{itemtaxrate}, #{remark}, #{citeuid},
                #{citeitemid}, #{statecode}, #{statedate}, #{inspected}, #{closed}, #{rownum}, #{invoqty},
                #{invoclosed}, #{virtualitem}, #{wipitemid}, #{wsid}, #{wsuid}, #{wpid}, #{endwpid}, #{customer},
                #{custpo}, #{machuid}, #{machitemid}, #{mainplanuid}, #{mainplanitemid}, #{machgroupid},
                #{disannulmark}, #{disannullisterid}, #{disannullister}, #{disannuldate}, #{attributejson}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipEpFinishingItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="mrbqty != null">
                MrbQty = #{mrbqty},
            </if>
            <if test="subitemid != null ">
                SubItemid = #{subitemid},
            </if>
            <if test="subuse != null ">
                SubUse = #{subuse},
            </if>
            <if test="subunit != null ">
                SubUnit = #{subunit},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="inspected != null">
                Inspected = #{inspected},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="invoqty != null">
                InvoQty = #{invoqty},
            </if>
            <if test="invoclosed != null">
                InvoClosed = #{invoclosed},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="wipitemid != null ">
                WipItemid = #{wipitemid},
            </if>
            <if test="wsid != null ">
                Wsid = #{wsid},
            </if>
            <if test="wsuid != null ">
                WsUid = #{wsuid},
            </if>
            <if test="wpid != null ">
                Wpid = #{wpid},
            </if>
            <if test="endwpid != null ">
                EndWpid = #{endwpid},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WipEpFinishingItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

