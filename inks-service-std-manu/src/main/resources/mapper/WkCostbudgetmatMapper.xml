<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkCostbudgetmatMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkCostbudgetmatPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_CostBudgetMat.id,
            Wk_CostBudgetMat.Pid,
            Wk_CostBudgetMat.Itemid,
            Wk_CostBudgetMat.Goodsid,
            Wk_CostBudgetMat.ItemCode,
            Wk_CostBudgetMat.ItemName,
            Wk_CostBudgetMat.ItemSpec,
            Wk_CostBudgetMat.ItemUnit,
            Wk_CostBudgetMat.Quantity,
            Wk_CostBudgetMat.TaxPrice,
            Wk_CostBudgetMat.TaxAmount,
            Wk_CostBudgetMat.Price,
            Wk_CostBudgetMat.Amount,
            Wk_CostBudgetMat.TaxTotal,
            Wk_CostBudgetMat.ItemTaxrate,
            Wk_CostBudgetMat.RowNum,
            Wk_CostBudgetMat.Remark,
            Wk_CostBudgetMat.Custom1,
            Wk_CostBudgetMat.Custom2,
            Wk_CostBudgetMat.Custom3,
            Wk_CostBudgetMat.Custom4,
            Wk_CostBudgetMat.Custom5,
            Wk_CostBudgetMat.Custom6,
            Wk_CostBudgetMat.Custom7,
            Wk_CostBudgetMat.Custom8,
            Wk_CostBudgetMat.Custom9,
            Wk_CostBudgetMat.Custom10,
            Wk_CostBudgetMat.Tenantid,
            Wk_CostBudgetMat.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_CostBudgetMat ON Mat_Goods.id = Wk_CostBudgetMat.Goodsid
        where Wk_CostBudgetMat.id = #{key}
          and Wk_CostBudgetMat.Tenantid = #{tid}
    </select>
    <sql id="selectWkCostbudgetmatVo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_CostBudgetMat.id,
            Wk_CostBudgetMat.Pid,
            Wk_CostBudgetMat.Itemid,
            Wk_CostBudgetMat.Goodsid,
            Wk_CostBudgetMat.ItemCode,
            Wk_CostBudgetMat.ItemName,
            Wk_CostBudgetMat.ItemSpec,
            Wk_CostBudgetMat.ItemUnit,
            Wk_CostBudgetMat.Quantity,
            Wk_CostBudgetMat.TaxPrice,
            Wk_CostBudgetMat.TaxAmount,
            Wk_CostBudgetMat.Price,
            Wk_CostBudgetMat.Amount,
            Wk_CostBudgetMat.TaxTotal,
            Wk_CostBudgetMat.ItemTaxrate,
            Wk_CostBudgetMat.RowNum,
            Wk_CostBudgetMat.Remark,
            Wk_CostBudgetMat.Custom1,
            Wk_CostBudgetMat.Custom2,
            Wk_CostBudgetMat.Custom3,
            Wk_CostBudgetMat.Custom4,
            Wk_CostBudgetMat.Custom5,
            Wk_CostBudgetMat.Custom6,
            Wk_CostBudgetMat.Custom7,
            Wk_CostBudgetMat.Custom8,
            Wk_CostBudgetMat.Custom9,
            Wk_CostBudgetMat.Custom10,
            Wk_CostBudgetMat.Tenantid,
            Wk_CostBudgetMat.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_CostBudgetMat ON Mat_Goods.id = Wk_CostBudgetMat.Goodsid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkCostbudgetmatPojo">
        <include refid="selectWkCostbudgetmatVo"/>
        where 1 = 1 and Wk_CostBudgetMat.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_CostBudgetMat.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_CostBudgetMat.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.itemid != null and SearchPojo.itemid != ''">
            and Wk_CostBudgetMat.itemid like concat('%', #{SearchPojo.itemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_CostBudgetMat.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_CostBudgetMat.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_CostBudgetMat.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_CostBudgetMat.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_CostBudgetMat.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_CostBudgetMat.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_CostBudgetMat.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_CostBudgetMat.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_CostBudgetMat.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_CostBudgetMat.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_CostBudgetMat.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_CostBudgetMat.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_CostBudgetMat.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_CostBudgetMat.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_CostBudgetMat.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_CostBudgetMat.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_CostBudgetMat.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.itemid != null and SearchPojo.itemid != ''">
                or Wk_CostBudgetMat.Itemid like concat('%', #{SearchPojo.itemid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_CostBudgetMat.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_CostBudgetMat.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_CostBudgetMat.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_CostBudgetMat.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_CostBudgetMat.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_CostBudgetMat.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_CostBudgetMat.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_CostBudgetMat.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_CostBudgetMat.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_CostBudgetMat.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_CostBudgetMat.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_CostBudgetMat.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_CostBudgetMat.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_CostBudgetMat.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_CostBudgetMat.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_CostBudgetMat.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkCostbudgetmatPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_CostBudgetMat.id,
            Wk_CostBudgetMat.Pid,
            Wk_CostBudgetMat.Itemid,
            Wk_CostBudgetMat.Goodsid,
            Wk_CostBudgetMat.ItemCode,
            Wk_CostBudgetMat.ItemName,
            Wk_CostBudgetMat.ItemSpec,
            Wk_CostBudgetMat.ItemUnit,
            Wk_CostBudgetMat.Quantity,
            Wk_CostBudgetMat.TaxPrice,
            Wk_CostBudgetMat.TaxAmount,
            Wk_CostBudgetMat.Price,
            Wk_CostBudgetMat.Amount,
            Wk_CostBudgetMat.TaxTotal,
            Wk_CostBudgetMat.ItemTaxrate,
            Wk_CostBudgetMat.RowNum,
            Wk_CostBudgetMat.Remark,
            Wk_CostBudgetMat.Custom1,
            Wk_CostBudgetMat.Custom2,
            Wk_CostBudgetMat.Custom3,
            Wk_CostBudgetMat.Custom4,
            Wk_CostBudgetMat.Custom5,
            Wk_CostBudgetMat.Custom6,
            Wk_CostBudgetMat.Custom7,
            Wk_CostBudgetMat.Custom8,
            Wk_CostBudgetMat.Custom9,
            Wk_CostBudgetMat.Custom10,
            Wk_CostBudgetMat.Tenantid,
            Wk_CostBudgetMat.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Wk_CostBudgetMat ON Mat_Goods.id = Wk_CostBudgetMat.Goodsid
        where Wk_CostBudgetMat.Pid = #{Pid}
          and Wk_CostBudgetMat.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_CostBudgetMat(id, Pid, Itemid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity,
                                     TaxPrice, TaxAmount, Price, Amount, TaxTotal, ItemTaxrate, RowNum, Remark, Custom1,
                                     Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                                     Tenantid, Revision)
        values (#{id}, #{pid}, #{itemid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity},
                #{taxprice}, #{taxamount}, #{price}, #{amount}, #{taxtotal}, #{itemtaxrate}, #{rownum}, #{remark},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_CostBudgetMat
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemid != null ">
                Itemid = #{itemid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_CostBudgetMat
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

