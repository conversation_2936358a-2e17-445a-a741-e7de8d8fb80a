<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWorksheetitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWorksheetitemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_WorksheetItem.id,
               Wk_WorksheetItem.Pid,
               Wk_WorksheetItem.Goodsid,
               Wk_WorksheetItem.Quantity,
               Wk_WorksheetItem.Price,
               Wk_WorksheetItem.Amount,
               Wk_WorksheetItem.ItemHour,
               Wk_WorksheetItem.PlanHour,
               Wk_WorksheetItem.StartDate,
               Wk_WorksheetItem.PlanDate,
               Wk_WorksheetItem.FinishQty,
               Wk_WorksheetItem.FinishHour,
               Wk_WorksheetItem.MrbQty,
               Wk_WorksheetItem.InStorage,
               Wk_WorksheetItem.EnabledMark,
               Wk_WorksheetItem.Closed,
               Wk_WorksheetItem.StartWpid,
               Wk_WorksheetItem.EndWpid,
               Wk_WorksheetItem.Remark,
               Wk_WorksheetItem.StateCode,
               Wk_WorksheetItem.StateDate,
               Wk_WorksheetItem.RowNum,
               Wk_WorksheetItem.MachType,
               Wk_WorksheetItem.MachUid,
               Wk_WorksheetItem.MachItemid,
               Wk_WorksheetItem.MachBatch,
               Wk_WorksheetItem.MachGroupid,
               Wk_WorksheetItem.MrpUid,
               Wk_WorksheetItem.MrpItemid,
               Wk_WorksheetItem.Customer,
               Wk_WorksheetItem.CustPO,
               Wk_WorksheetItem.CiteUid,
               Wk_WorksheetItem.CiteItemid,
               Wk_WorksheetItem.MainPlanUid,
               Wk_WorksheetItem.MainPlanItemid,
               Wk_WorksheetItem.Location,
               Wk_WorksheetItem.BatchNo,
               Wk_WorksheetItem.WipUsed,
               Wk_WorksheetItem.WkWpid,
               Wk_WorksheetItem.WkWpCode,
               Wk_WorksheetItem.WkWpName,
               Wk_WorksheetItem.WkRowNum,
               Wk_WorksheetItem.DisannulListerid,
               Wk_WorksheetItem.DisannulLister,
               Wk_WorksheetItem.DisannulDate,
               Wk_WorksheetItem.DisannulMark,
               Wk_WorksheetItem.AttributeJson,
               Wk_WorksheetItem.ReportQty,
               Wk_WorksheetItem.CompQty,
               Wk_WorksheetItem.FinishRate,
               Wk_WorksheetItem.SecQty,
               Wk_WorksheetItem.PanelWidth,
               Wk_WorksheetItem.PanelHeight,
               Wk_WorksheetItem.PcsInPanel,
               Wk_WorksheetItem.PanelThick,
               Wk_WorksheetItem.MatCode,
               Wk_WorksheetItem.MatUsed,
               Wk_WorksheetItem.RowCode,
               Wk_WorksheetItem.WkPcsQty,
               Wk_WorksheetItem.WkSecQty,
               Wk_WorksheetItem.MergeMark,
               Wk_WorksheetItem.SourceType,
               Wk_WorksheetItem.CostItemJson,
               Wk_WorksheetItem.CostGroupJson,
               Wk_WorksheetItem.VirtualItem,
               Wk_WorksheetItem.Custom1,
               Wk_WorksheetItem.Custom2,
               Wk_WorksheetItem.Custom3,
               Wk_WorksheetItem.Custom4,
               Wk_WorksheetItem.Custom5,
               Wk_WorksheetItem.Custom6,
               Wk_WorksheetItem.Custom7,
               Wk_WorksheetItem.Custom8,
               Wk_WorksheetItem.Custom9,
               Wk_WorksheetItem.Custom10,
               Wk_WorksheetItem.Tenantid,
               Wk_WorksheetItem.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_WorksheetItem ON Mat_Goods.id = Wk_WorksheetItem.Goodsid
        where Wk_WorksheetItem.id = #{key}
          and Wk_WorksheetItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkWorksheetitemVo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               App_Workgroup.GroupName as machgroupname,
               Wk_WorksheetItem.id,
               Wk_WorksheetItem.Pid,
               Wk_WorksheetItem.Goodsid,
               Wk_WorksheetItem.Quantity,
               Wk_WorksheetItem.Price,
               Wk_WorksheetItem.Amount,
               Wk_WorksheetItem.ItemHour,
               Wk_WorksheetItem.PlanHour,
               Wk_WorksheetItem.StartDate,
               Wk_WorksheetItem.PlanDate,
               Wk_WorksheetItem.FinishQty,
               Wk_WorksheetItem.FinishHour,
               Wk_WorksheetItem.MrbQty,
               Wk_WorksheetItem.InStorage,
               Wk_WorksheetItem.EnabledMark,
               Wk_WorksheetItem.Closed,
               Wk_WorksheetItem.StartWpid,
               Wk_WorksheetItem.EndWpid,
               Wk_WorksheetItem.Remark,
               Wk_WorksheetItem.StateCode,
               Wk_WorksheetItem.StateDate,
               Wk_WorksheetItem.RowNum,
               Wk_WorksheetItem.MachType,
               Wk_WorksheetItem.MachUid,
               Wk_WorksheetItem.MachItemid,
               Wk_WorksheetItem.MachBatch,
               Wk_WorksheetItem.MachGroupid,
               Wk_WorksheetItem.MrpUid,
               Wk_WorksheetItem.MrpItemid,
               Wk_WorksheetItem.Customer,
               Wk_WorksheetItem.CustPO,
               Wk_WorksheetItem.CiteUid,
               Wk_WorksheetItem.CiteItemid,
               Wk_WorksheetItem.MainPlanUid,
               Wk_WorksheetItem.MainPlanItemid,
               Wk_WorksheetItem.Location,
               Wk_WorksheetItem.BatchNo,
               Wk_WorksheetItem.WipUsed,
               Wk_WorksheetItem.WkWpid,
               Wk_WorksheetItem.WkWpCode,
               Wk_WorksheetItem.WkWpName,
               Wk_WorksheetItem.WkRowNum,
               Wk_WorksheetItem.DisannulListerid,
               Wk_WorksheetItem.DisannulLister,
               Wk_WorksheetItem.DisannulDate,
               Wk_WorksheetItem.DisannulMark,
               Wk_WorksheetItem.AttributeJson,
               Wk_WorksheetItem.ReportQty,
               Wk_WorksheetItem.CompQty,
               Wk_WorksheetItem.FinishRate,
               Wk_WorksheetItem.SecQty,
               Wk_WorksheetItem.PanelWidth,
               Wk_WorksheetItem.PanelHeight,
               Wk_WorksheetItem.PcsInPanel,
               Wk_WorksheetItem.PanelThick,
               Wk_WorksheetItem.MatCode,
               Wk_WorksheetItem.MatUsed,
               Wk_WorksheetItem.RowCode,
               Wk_WorksheetItem.WkPcsQty,
               Wk_WorksheetItem.WkSecQty,
               Wk_WorksheetItem.MergeMark,
               Wk_WorksheetItem.SourceType,
               Wk_WorksheetItem.CostItemJson,
               Wk_WorksheetItem.CostGroupJson,
               Wk_WorksheetItem.VirtualItem,
               Wk_WorksheetItem.Custom1,
               Wk_WorksheetItem.Custom2,
               Wk_WorksheetItem.Custom3,
               Wk_WorksheetItem.Custom4,
               Wk_WorksheetItem.Custom5,
               Wk_WorksheetItem.Custom6,
               Wk_WorksheetItem.Custom7,
               Wk_WorksheetItem.Custom8,
               Wk_WorksheetItem.Custom9,
               Wk_WorksheetItem.Custom10,
               Wk_WorksheetItem.Tenantid,
               Wk_WorksheetItem.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_WorksheetItem ON Mat_Goods.id = Wk_WorksheetItem.Goodsid
                 LEFT JOIN App_Workgroup on Wk_WorksheetItem.MachGroupid = App_Workgroup.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWorksheetitemPojo">
        <include refid="selectWkWorksheetitemVo"/>
        where 1 = 1 and Wk_WorksheetItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WorksheetItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_WorksheetItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_WorksheetItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.startwpid != null and SearchPojo.startwpid != ''">
            and Wk_WorksheetItem.startwpid like concat('%', #{SearchPojo.startwpid}, '%')
        </if>
        <if test="SearchPojo.endwpid != null and SearchPojo.endwpid != ''">
            and Wk_WorksheetItem.endwpid like concat('%', #{SearchPojo.endwpid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_WorksheetItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_WorksheetItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_WorksheetItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_WorksheetItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_WorksheetItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Wk_WorksheetItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Wk_WorksheetItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_WorksheetItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_WorksheetItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Wk_WorksheetItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Wk_WorksheetItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_WorksheetItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_WorksheetItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Wk_WorksheetItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Wk_WorksheetItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.wkwpid != null and SearchPojo.wkwpid != ''">
            and Wk_WorksheetItem.wkwpid like concat('%', #{SearchPojo.wkwpid}, '%')
        </if>
        <if test="SearchPojo.wkwpcode != null and SearchPojo.wkwpcode != ''">
            and Wk_WorksheetItem.wkwpcode like concat('%', #{SearchPojo.wkwpcode}, '%')
        </if>
        <if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
            and Wk_WorksheetItem.wkwpname like concat('%', #{SearchPojo.wkwpname}, '%')
        </if>
        <if test="SearchPojo.wkrownum != null and SearchPojo.wkrownum != ''">
            and Wk_WorksheetItem.wkrownum like concat('%', #{SearchPojo.wkrownum}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Wk_WorksheetItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Wk_WorksheetItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_WorksheetItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_WorksheetItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_WorksheetItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_WorksheetItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_WorksheetItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_WorksheetItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_WorksheetItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_WorksheetItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_WorksheetItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_WorksheetItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_WorksheetItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_WorksheetItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_WorksheetItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.startwpid != null and SearchPojo.startwpid != ''">
                or Wk_WorksheetItem.StartWpid like concat('%', #{SearchPojo.startwpid}, '%')
            </if>
            <if test="SearchPojo.endwpid != null and SearchPojo.endwpid != ''">
                or Wk_WorksheetItem.EndWpid like concat('%', #{SearchPojo.endwpid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_WorksheetItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Wk_WorksheetItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_WorksheetItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_WorksheetItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_WorksheetItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Wk_WorksheetItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Wk_WorksheetItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_WorksheetItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_WorksheetItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Wk_WorksheetItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Wk_WorksheetItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_WorksheetItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_WorksheetItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Wk_WorksheetItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Wk_WorksheetItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.wkwpid != null and SearchPojo.wkwpid != ''">
                or Wk_WorksheetItem.WkWpid like concat('%', #{SearchPojo.wkwpid}, '%')
            </if>
            <if test="SearchPojo.wkwpcode != null and SearchPojo.wkwpcode != ''">
                or Wk_WorksheetItem.WkWpCode like concat('%', #{SearchPojo.wkwpcode}, '%')
            </if>
            <if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
                or Wk_WorksheetItem.WkWpName like concat('%', #{SearchPojo.wkwpname}, '%')
            </if>
            <if test="SearchPojo.wkrownum != null and SearchPojo.wkrownum != ''">
                or Wk_WorksheetItem.WkRowNum like concat('%', #{SearchPojo.wkrownum}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Wk_WorksheetItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Wk_WorksheetItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_WorksheetItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_WorksheetItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_WorksheetItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_WorksheetItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_WorksheetItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_WorksheetItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_WorksheetItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_WorksheetItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_WorksheetItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_WorksheetItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_WorksheetItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkWorksheetitemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               App_Workgroup.GroupName    as machgroupname,
               Wk_WorksheetItem.id,
               Wk_WorksheetItem.Pid,
               Wk_WorksheetItem.Goodsid,
               Wk_WorksheetItem.Quantity,
               Wk_WorksheetItem.Price,
               Wk_WorksheetItem.Amount,
               Wk_WorksheetItem.ItemHour,
               Wk_WorksheetItem.PlanHour,
               Wk_WorksheetItem.StartDate,
               Wk_WorksheetItem.PlanDate,
               Wk_WorksheetItem.FinishQty,
               Wk_WorksheetItem.FinishHour,
               Wk_WorksheetItem.MrbQty,
               Wk_WorksheetItem.InStorage,
               Wk_WorksheetItem.EnabledMark,
               Wk_WorksheetItem.Closed,
               Wk_WorksheetItem.StartWpid,
               Wk_WorksheetItem.EndWpid,
               Wk_WorksheetItem.Remark,
               Wk_WorksheetItem.StateCode,
               Wk_WorksheetItem.StateDate,
               Wk_WorksheetItem.RowNum,
               Wk_WorksheetItem.MachType,
               Wk_WorksheetItem.MachUid,
               Wk_WorksheetItem.MachItemid,
               Wk_WorksheetItem.MachBatch,
               Wk_WorksheetItem.MachGroupid,
               Wk_WorksheetItem.MrpUid,
               Wk_WorksheetItem.MrpItemid,
               Wk_WorksheetItem.Customer,
               Wk_WorksheetItem.CustPO,
               Wk_WorksheetItem.CiteUid,
               Wk_WorksheetItem.CiteItemid,
               Wk_WorksheetItem.MainPlanUid,
               Wk_WorksheetItem.MainPlanItemid,
               Wk_WorksheetItem.Location,
               Wk_WorksheetItem.BatchNo,
               Wk_WorksheetItem.WipUsed,
               Wk_WorksheetItem.WkWpid,
               Wk_WorksheetItem.WkWpCode,
               Wk_WorksheetItem.WkWpName,
               Wk_WorksheetItem.WkRowNum,
               Wk_WorksheetItem.DisannulListerid,
               Wk_WorksheetItem.DisannulLister,
               Wk_WorksheetItem.DisannulDate,
               Wk_WorksheetItem.DisannulMark,
               Wk_WorksheetItem.AttributeJson,
               Wk_WorksheetItem.ReportQty,
               Wk_WorksheetItem.CompQty,
               Wk_WorksheetItem.FinishRate,
               Wk_WorksheetItem.SecQty,
               Wk_WorksheetItem.PanelWidth,
               Wk_WorksheetItem.PanelHeight,
               Wk_WorksheetItem.PcsInPanel,
               Wk_WorksheetItem.PanelThick,
               Wk_WorksheetItem.MatCode,
               Wk_WorksheetItem.MatUsed,
               Wk_WorksheetItem.RowCode,
               Wk_WorksheetItem.WkPcsQty,
               Wk_WorksheetItem.WkSecQty,
               Wk_WorksheetItem.MergeMark,
               Wk_WorksheetItem.SourceType,
               Wk_WorksheetItem.CostItemJson,
               Wk_WorksheetItem.CostGroupJson,
               Wk_WorksheetItem.VirtualItem,
               Wk_WorksheetItem.Custom1,
               Wk_WorksheetItem.Custom2,
               Wk_WorksheetItem.Custom3,
               Wk_WorksheetItem.Custom4,
               Wk_WorksheetItem.Custom5,
               Wk_WorksheetItem.Custom6,
               Wk_WorksheetItem.Custom7,
               Wk_WorksheetItem.Custom8,
               Wk_WorksheetItem.Custom9,
               Wk_WorksheetItem.Custom10,
               Wk_WorksheetItem.Tenantid,
               Wk_WorksheetItem.Revision,
               Bus_MachiningItem.Quantity as machquantity
        FROM Mat_Goods
                 RIGHT JOIN Wk_WorksheetItem ON Mat_Goods.id = Wk_WorksheetItem.Goodsid
                 LEFT JOIN App_Workgroup on Wk_WorksheetItem.MachGroupid = App_Workgroup.id
                 LEFT JOIN Bus_MachiningItem on Wk_WorksheetItem.MachItemid = Bus_MachiningItem.id
        where Wk_WorksheetItem.Pid = #{Pid}
          and Wk_WorksheetItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_WorksheetItem(id, Pid, Goodsid, Quantity, Price, Amount, ItemHour, PlanHour, StartDate, PlanDate,
                                     FinishQty, FinishHour, MrbQty, InStorage, EnabledMark, Closed, StartWpid, EndWpid,
                                     Remark, StateCode, StateDate, RowNum, MachType, MachUid, MachItemid, MachBatch,
                                     MachGroupid,
                                     MrpUid, MrpItemid, Customer, CustPO, CiteUid, CiteItemid, MainPlanUid,
                                     MainPlanItemid, Location, BatchNo, WipUsed, WkWpid, WkWpCode, WkWpName, WkRowNum,
                                     DisannulListerid, DisannulLister, DisannulDate, DisannulMark, AttributeJson,
                                     ReportQty, CompQty, FinishRate, SecQty, PanelWidth, PanelHeight, PcsInPanel,
                                     PanelThick, MatCode, MatUsed, RowCode, WkPcsQty, WkSecQty, MergeMark, SourceType,
                                     CostItemJson, CostGroupJson, VirtualItem,
                                     Custom1, Custom2,
                                     Custom3, Custom4, Custom5, Custom6,
                                     Custom7, Custom8,
                                     Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{quantity}, #{price}, #{amount}, #{itemhour}, #{planhour}, #{startdate},
                #{plandate}, #{finishqty}, #{finishhour}, #{mrbqty}, #{instorage}, #{enabledmark}, #{closed},
                #{startwpid}, #{endwpid}, #{remark}, #{statecode}, #{statedate}, #{rownum}, #{machtype}, #{machuid},
                #{machitemid}, #{machbatch}, #{machgroupid}, #{mrpuid}, #{mrpitemid}, #{customer}, #{custpo},
                #{citeuid},
                #{citeitemid}, #{mainplanuid}, #{mainplanitemid}, #{location}, #{batchno}, #{wipused}, #{wkwpid},
                #{wkwpcode}, #{wkwpname}, #{wkrownum}, #{disannullisterid}, #{disannullister}, #{disannuldate},
                #{disannulmark}, #{attributejson}, #{reportqty}, #{compqty}, #{finishrate}, #{secqty}, #{panelwidth},
                #{panelheight}, #{pcsinpanel}, #{panelthick}, #{matcode}, #{matused}, #{rowcode}, #{wkpcsqty},
                #{wksecqty}, #{mergemark}, #{sourcetype}, #{costitemjson}, #{costgroupjson}, #{virtualitem}, #{custom1},
                #{custom2},
                #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WorksheetItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="itemhour != null">
                ItemHour = #{itemhour},
            </if>
            <if test="planhour != null">
                PlanHour = #{planhour},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="finishhour != null">
                FinishHour = #{finishhour},
            </if>
            <if test="mrbqty != null">
                MrbQty = #{mrbqty},
            </if>
            <if test="instorage != null">
                InStorage = #{instorage},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="startwpid != null ">
                StartWpid = #{startwpid},
            </if>
            <if test="endwpid != null ">
                EndWpid = #{endwpid},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="machtype != null ">
                MachType = #{machtype},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machbatch != null ">
                MachBatch = #{machbatch},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="wipused != null">
                WipUsed = #{wipused},
            </if>
            <if test="wkwpid != null ">
                WkWpid = #{wkwpid},
            </if>
            <if test="wkwpcode != null ">
                WkWpCode = #{wkwpcode},
            </if>
            <if test="wkwpname != null ">
                WkWpName = #{wkwpname},
            </if>
            <if test="wkrownum != null ">
                WkRowNum = #{wkrownum},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="reportqty != null">
                ReportQty = #{reportqty},
            </if>
            <if test="compqty != null">
                CompQty = #{compqty},
            </if>
            <if test="finishrate != null">
                FinishRate = #{finishrate},
            </if>
            <if test="secqty != null">
                SecQty = #{secqty},
            </if>
            <if test="panelwidth != null">
                PanelWidth = #{panelwidth},
            </if>
            <if test="panelheight != null">
                PanelHeight = #{panelheight},
            </if>
            <if test="pcsinpanel != null">
                PcsInPanel = #{pcsinpanel},
            </if>
            <if test="panelthick != null">
                PanelThick = #{panelthick},
            </if>
            <if test="matcode != null ">
                MatCode = #{matcode},
            </if>
            <if test="matused != null">
                MatUsed = #{matused},
            </if>
            <if test="wkpcsqty != null">
                WkPcsQty = #{wkpcsqty},
            </if>
            <if test="wksecqty != null">
                WkSecQty = #{wksecqty},
            </if>
            <if test="rowcode != null ">
                RowCode = #{rowcode},
            </if>
            <if test="mergemark != null">
                MergeMark = #{mergemark},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="costitemjson != null">
                CostItemJson = #{costitemjson},
            </if>
            <if test="costgroupjson != null">
                CostGroupJson = #{costgroupjson},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WorksheetItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

<!--    更新合并标记为1：被合并项-->
    <update id="updateMergemark1">
        update Wk_WorksheetItem
        set MergeMark = 1
        where id in(${itemids}) and Tenantid = #{tid}
    </update>
    <!--    删除合并单：更新被合并项合并标记为0：-->
    <update id="updateMergemark0">
        update Wk_WorksheetItem
        set MergeMark = 0
        where id =#{key}
        and Tenantid = #{tid}
    </update>

    <select id="getUid" resultType="java.lang.String">
        select RefNo from Wk_Worksheet where id = (select Pid from Wk_WorksheetItem where id = #{id} and Tenantid = #{tid})
        and Tenantid = #{tid}
    </select>
    <!--查询单个-->
    <select id="getEntityDetail" resultType="inks.service.std.manu.domain.pojo.WkWorksheetitemdetailPojo">
        SELECT App_Workgroup.GroupName,
               App_Workgroup.GroupUid,
               App_Workgroup.Abbreviate,
               Wk_WorksheetItem.*,
               Wk_Worksheet.RefNo,
               Wk_Worksheet.BillDate,
               Wk_Worksheet.BillType,
               Wk_Worksheet.BillTitle,
               Wk_Worksheet.Lister,
               Wk_Worksheet.Summary,
               Mat_Goods.Material as GoodsMaterial,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               a2.GroupName       as machgroupname
        FROM Wk_Worksheet
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Wk_Worksheet.Groupid
                 RIGHT JOIN Wk_WorksheetItem ON Wk_WorksheetItem.Pid = Wk_Worksheet.id
                 LEFT JOIN App_Workgroup a2 on Wk_WorksheetItem.MachGroupid = a2.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Wk_WorksheetItem.Goodsid
        where Wk_WorksheetItem.id = #{key}
          and Wk_WorksheetItem.Tenantid = #{tid}
    </select>

    <update id="closedids">
        update Wk_WorksheetItem
        set Closed = #{type}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and Tenantid = #{tenantid}
    </update>

    <select id="getPid" resultType="java.lang.String">
        select Pid from Wk_WorksheetItem where id = #{workitemid} and Tenantid = #{tenantid}
    </select>

    <select id="getIdByMrpitemid" resultType="java.lang.String">
        select id from Wk_WorksheetItem where MrpItemid = #{itemparentid} and Tenantid = #{tenantid} limit 1
    </select>

    <select id="getItemListByIds" resultType="inks.service.std.manu.domain.pojo.WkWorksheetitemPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        App_Workgroup.GroupName    as machgroupname,
        Wk_WorksheetItem.id,
        Wk_WorksheetItem.Pid,
        Wk_WorksheetItem.Goodsid,
        Wk_WorksheetItem.Quantity,
        Wk_WorksheetItem.Price,
        Wk_WorksheetItem.Amount,
        Wk_WorksheetItem.ItemHour,
        Wk_WorksheetItem.PlanHour,
        Wk_WorksheetItem.StartDate,
        Wk_WorksheetItem.PlanDate,
        Wk_WorksheetItem.FinishQty,
        Wk_WorksheetItem.FinishHour,
        Wk_WorksheetItem.MrbQty,
        Wk_WorksheetItem.InStorage,
        Wk_WorksheetItem.EnabledMark,
        Wk_WorksheetItem.Closed,
        Wk_WorksheetItem.StartWpid,
        Wk_WorksheetItem.EndWpid,
        Wk_WorksheetItem.Remark,
        Wk_WorksheetItem.StateCode,
        Wk_WorksheetItem.StateDate,
        Wk_WorksheetItem.RowNum,
        Wk_WorksheetItem.MachType,
        Wk_WorksheetItem.MachUid,
        Wk_WorksheetItem.MachItemid,
        Wk_WorksheetItem.MachBatch,
        Wk_WorksheetItem.MachGroupid,
        Wk_WorksheetItem.MrpUid,
        Wk_WorksheetItem.MrpItemid,
        Wk_WorksheetItem.Customer,
        Wk_WorksheetItem.CustPO,
        Wk_WorksheetItem.CiteUid,
        Wk_WorksheetItem.CiteItemid,
        Wk_WorksheetItem.MainPlanUid,
        Wk_WorksheetItem.MainPlanItemid,
        Wk_WorksheetItem.Location,
        Wk_WorksheetItem.BatchNo,
        Wk_WorksheetItem.WipUsed,
        Wk_WorksheetItem.WkWpid,
        Wk_WorksheetItem.WkWpCode,
        Wk_WorksheetItem.WkWpName,
        Wk_WorksheetItem.WkRowNum,
        Wk_WorksheetItem.DisannulListerid,
        Wk_WorksheetItem.DisannulLister,
        Wk_WorksheetItem.DisannulDate,
        Wk_WorksheetItem.DisannulMark,
        Wk_WorksheetItem.AttributeJson,
        Wk_WorksheetItem.ReportQty,
        Wk_WorksheetItem.CompQty,
        Wk_WorksheetItem.FinishRate,
        Wk_WorksheetItem.SecQty,
        Wk_WorksheetItem.PanelWidth,
        Wk_WorksheetItem.PanelHeight,
        Wk_WorksheetItem.PcsInPanel,
        Wk_WorksheetItem.PanelThick,
        Wk_WorksheetItem.MatCode,
        Wk_WorksheetItem.MatUsed,
        Wk_WorksheetItem.RowCode,
        Wk_WorksheetItem.WkPcsQty,
        Wk_WorksheetItem.WkSecQty,
        Wk_WorksheetItem.MergeMark,
        Wk_WorksheetItem.SourceType,
        Wk_WorksheetItem.CostItemJson,
        Wk_WorksheetItem.CostGroupJson,
        Wk_WorksheetItem.VirtualItem,
        Wk_WorksheetItem.Custom1,
        Wk_WorksheetItem.Custom2,
        Wk_WorksheetItem.Custom3,
        Wk_WorksheetItem.Custom4,
        Wk_WorksheetItem.Custom5,
        Wk_WorksheetItem.Custom6,
        Wk_WorksheetItem.Custom7,
        Wk_WorksheetItem.Custom8,
        Wk_WorksheetItem.Custom9,
        Wk_WorksheetItem.Custom10,
        Wk_WorksheetItem.Tenantid,
        Wk_WorksheetItem.Revision,
        Bus_MachiningItem.Quantity as machquantity
        FROM Mat_Goods
                 RIGHT JOIN Wk_WorksheetItem ON Mat_Goods.id = Wk_WorksheetItem.Goodsid
                 LEFT JOIN App_Workgroup on Wk_WorksheetItem.MachGroupid = App_Workgroup.id
                 LEFT JOIN Bus_MachiningItem on Wk_WorksheetItem.MachItemid = Bus_MachiningItem.id
        where Wk_WorksheetItem.Pid = #{pid}
        and Wk_WorksheetItem.id in (${ids})
          and Wk_WorksheetItem.Tenantid = #{tid}
        order by RowNum
    </select>
</mapper>

