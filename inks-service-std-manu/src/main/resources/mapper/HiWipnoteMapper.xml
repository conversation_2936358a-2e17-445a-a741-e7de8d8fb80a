<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.HiWipnoteMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.HiWipnotePojo">
        SELECT Hi_WipNote.id,
        Hi_WipNote.RefNo,
        Hi_WipNote.BillDate,
        Hi_WipNote.WorkType,
        Hi_WipNote.Workshopid,
        Hi_WipNote.Workshop,
        Hi_WipNote.Goodsid,
        Hi_WipNote.PlanDate,
        Hi_WipNote.Quantity,
        Hi_WipNote.WkPcsQty,
        Hi_WipNote.WkSecQty,
        Hi_WipNote.MrbP<PERSON><PERSON>ty,
        Hi_WipNote.Mrb<PERSON><PERSON><PERSON><PERSON>,
        Hi_WipNote.Supplement,
        Hi_WipNote.CreateBy,
        Hi_WipNote.CreateByid,
        Hi_WipNote.CreateDate,
        Hi_WipNote.Lister,
        Hi_WipNote.Listerid,
        Hi_WipNote.ModifyDate,
        Hi_WipNote.StateCode,
        Hi_WipNote.StateDate,
        Hi_WipNote.WkWpid,
        Hi_WipNote.WkWpCode,
        Hi_WipNote.WkWpName,
        Hi_WipNote.WkRowNum,
        Hi_WipNote.Customer,
        Hi_WipNote.CustPO,
        Hi_WipNote.MachUid,
        Hi_WipNote.MachItemid,
        Hi_WipNote.MachGroupid,
        Hi_WipNote.MainPlanUid,
        Hi_WipNote.MainPlanItemid,
        Hi_WipNote.WorkUid,
        Hi_WipNote.WorkItemid,
        Hi_WipNote.SubStWpid,
        Hi_WipNote.SubStWpCode,
        Hi_WipNote.SubStWpName,
        Hi_WipNote.SubEndWpid,
        Hi_WipNote.SubEndWpCode,
        Hi_WipNote.SubEndWpName,
        Hi_WipNote.SubUid,
        Hi_WipNote.WorkDate,
        Hi_WipNote.CompWpid,
        Hi_WipNote.CompWpCode,
        Hi_WipNote.CompWpName,
        Hi_WipNote.CompPcsQty,
        Hi_WipNote.WipGroupid,
        Hi_WipNote.Summary,
        Hi_WipNote.MatCode,
        Hi_WipNote.MatUsed,
        Hi_WipNote.AttributeJson,
        Hi_WipNote.AttributeStr,
        Hi_WipNote.WkSpecJson,
        Hi_WipNote.ItemCount,
        Hi_WipNote.FinishCount,
        Hi_WipNote.PrintCount,
        Hi_WipNote.ColorLevel,
        Hi_WipNote.SizeX,
        Hi_WipNote.SizeY,
        Hi_WipNote.SizeZ,
        Hi_WipNote.Closed,
        Hi_WipNote.DisannulListerid,
        Hi_WipNote.DisannulLister,
        Hi_WipNote.DisannulDate,
        Hi_WipNote.DisannulMark,
        Hi_WipNote.MergeMark,
        Hi_WipNote.SourceType,
        Hi_WipNote.Custom1,
        Hi_WipNote.Custom2,
        Hi_WipNote.Custom3,
        Hi_WipNote.Custom4,
        Hi_WipNote.Custom5,
        Hi_WipNote.Custom6,
        Hi_WipNote.Custom7,
        Hi_WipNote.Custom8,
        Hi_WipNote.Custom9,
        Hi_WipNote.Custom10,
        Hi_WipNote.Tenantid,
        Hi_WipNote.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM Mat_Goods
        RIGHT JOIN Hi_WipNote ON Hi_WipNote.Goodsid = Mat_Goods.id
        LEFT JOIN App_Workgroup ON Hi_WipNote.MachGroupid = App_Workgroup.id
        where Hi_WipNote.id = #{key}
        and Hi_WipNote.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Hi_WipNote.id,
               Hi_WipNote.RefNo,
               Hi_WipNote.BillDate,
               Hi_WipNote.WorkType,
               Hi_WipNote.Workshopid,
               Hi_WipNote.Workshop,
               Hi_WipNote.Goodsid,
               Hi_WipNote.PlanDate,
               Hi_WipNote.Quantity,
               Hi_WipNote.WkPcsQty,
               Hi_WipNote.WkSecQty,
               Hi_WipNote.MrbPcsQty,
               Hi_WipNote.MrbSecQty,
               Hi_WipNote.Supplement,
               Hi_WipNote.CreateBy,
               Hi_WipNote.CreateByid,
               Hi_WipNote.CreateDate,
               Hi_WipNote.Lister,
               Hi_WipNote.Listerid,
               Hi_WipNote.ModifyDate,
               Hi_WipNote.StateCode,
               Hi_WipNote.StateDate,
               Hi_WipNote.WkWpid,
               Hi_WipNote.WkWpCode,
               Hi_WipNote.WkWpName,
               Hi_WipNote.WkRowNum,
               Hi_WipNote.Customer,
               Hi_WipNote.CustPO,
               Hi_WipNote.MachUid,
               Hi_WipNote.MachItemid,
               Hi_WipNote.MachGroupid,
               Hi_WipNote.MainPlanUid,
               Hi_WipNote.MainPlanItemid,
               Hi_WipNote.WorkUid,
               Hi_WipNote.WorkItemid,
               Hi_WipNote.SubStWpid,
               Hi_WipNote.SubStWpCode,
               Hi_WipNote.SubStWpName,
               Hi_WipNote.SubEndWpid,
               Hi_WipNote.SubEndWpCode,
               Hi_WipNote.SubEndWpName,
               Hi_WipNote.SubUid,
               Hi_WipNote.WorkDate,
               Hi_WipNote.CompWpid,
               Hi_WipNote.CompWpCode,
               Hi_WipNote.CompWpName,
               Hi_WipNote.CompPcsQty,
               Hi_WipNote.WipGroupid,
               Hi_WipNote.Summary,
               Hi_WipNote.MatCode,
               Hi_WipNote.MatUsed,
               Hi_WipNote.AttributeJson,
               Hi_WipNote.AttributeStr,
               Hi_WipNote.WkSpecJson,
               Hi_WipNote.ItemCount,
               Hi_WipNote.FinishCount,
               Hi_WipNote.PrintCount,
               Hi_WipNote.ColorLevel,
               Hi_WipNote.SizeX,
               Hi_WipNote.SizeY,
               Hi_WipNote.SizeZ,
               Hi_WipNote.Closed,
               Hi_WipNote.DisannulListerid,
               Hi_WipNote.DisannulLister,
               Hi_WipNote.DisannulDate,
               Hi_WipNote.DisannulMark,
               Hi_WipNote.MergeMark,
               Hi_WipNote.SourceType,
               Hi_WipNote.Custom1,
               Hi_WipNote.Custom2,
               Hi_WipNote.Custom3,
               Hi_WipNote.Custom4,
               Hi_WipNote.Custom5,
               Hi_WipNote.Custom6,
               Hi_WipNote.Custom7,
               Hi_WipNote.Custom8,
               Hi_WipNote.Custom9,
               Hi_WipNote.Custom10,
               Hi_WipNote.Tenantid,
               Hi_WipNote.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.GroupLevel
        FROM Mat_Goods
                 RIGHT JOIN Hi_WipNote ON Hi_WipNote.Goodsid = Mat_Goods.id
                 LEFT JOIN App_Workgroup ON Hi_WipNote.MachGroupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.GroupLevel,
               Hi_WipNoteItem.id,
               Hi_WipNoteItem.Pid,
               Hi_WipNoteItem.Wpid,
               Hi_WipNoteItem.WpCode,
               Hi_WipNoteItem.WpName,
               Hi_WipNoteItem.RowNum,
               Hi_WipNoteItem.PlanDate,
               Hi_WipNoteItem.Remark,
               Hi_WipNoteItem.InPcsQty,
               Hi_WipNoteItem.InSecQty,
               Hi_WipNoteItem.OutPcsQty,
               Hi_WipNoteItem.OutSecQty,
               Hi_WipNoteItem.MrbPcsQty,
               Hi_WipNoteItem.MrbSecQty,
               Hi_WipNoteItem.CompPcsQty,
               Hi_WipNoteItem.CompSecQty,
               Hi_WipNoteItem.SubQty,
               Hi_WipNoteItem.SubUnit,
               Hi_WipNoteItem.StartDate,
               Hi_WipNoteItem.EndDate,
               Hi_WipNoteItem.ItemWorker,
               Hi_WipNoteItem.EpibolePcsQty,
               Hi_WipNoteItem.EpiboleSecQty,
               Hi_WipNoteItem.LastWp,
               Hi_WipNoteItem.SpecJson,
               Hi_WipNoteItem.SpecPackJson,
               Hi_WipNoteItem.Lister,
               Hi_WipNoteItem.CreateDate,
               Hi_WipNoteItem.ModifyDate,
               Hi_WipNoteItem.StartPlan,
               Hi_WipNoteItem.Inspid,
               Hi_WipNoteItem.InspUid,
               Hi_WipNoteItem.InspResult,
               Hi_WipNoteItem.DisableIn,
               Hi_WipNoteItem.DisableOut,
               Hi_WipNoteItem.Custom1,
               Hi_WipNoteItem.Custom2,
               Hi_WipNoteItem.Custom3,
               Hi_WipNoteItem.Custom4,
               Hi_WipNoteItem.Custom5,
               Hi_WipNoteItem.Custom6,
               Hi_WipNoteItem.Custom7,
               Hi_WipNoteItem.Custom8,
               Hi_WipNoteItem.Custom9,
               Hi_WipNoteItem.Custom10,
               Hi_WipNoteItem.Tenantid,
               Hi_WipNoteItem.Revision,
               Hi_WipNote.RefNo,
               Hi_WipNote.WorkType,
               Hi_WipNote.BillDate,
               Hi_WipNote.Workshopid,
               Hi_WipNote.Workshop,
               Hi_WipNote.Quantity,
               Hi_WipNote.WkPcsQty,
               Hi_WipNote.WkSecQty,
               Hi_WipNote.WkWpCode,
               Hi_WipNote.WkWpName,
               Hi_WipNote.WkWpid,
               Hi_WipNote.CustPO,
               Hi_WipNote.MachUid,
               Hi_WipNote.MainPlanUid,
               Hi_WipNote.WorkUid,
               Hi_WipNote.AttributeJson,
               Hi_WipNote.AttributeStr,
               Hi_WipNote.MatCode,
               Hi_WipNote.MatUsed,
               Hi_WipNote.ItemCount,
               Hi_WipNote.FinishCount,
               Hi_WipNote.PrintCount,
               Hi_WipNote.SizeX,
               Hi_WipNote.SizeY,
               Hi_WipNote.SizeZ,
               Hi_WipNote.Closed,
               Hi_WipNote.DisannulListerid,
               Hi_WipNote.DisannulLister,
               Hi_WipNote.DisannulDate,
               Hi_WipNote.DisannulMark,
               Hi_WipNote.MergeMark,
               Hi_WipNote.SourceType,
               Hi_WipNote.Summary
        FROM Mat_Goods
                 RIGHT JOIN Hi_WipNote ON Hi_WipNote.Goodsid = Mat_Goods.id
                 RIGHT JOIN Hi_WipNoteItem ON Hi_WipNoteItem.Pid = Hi_WipNote.id
                 LEFT JOIN App_Workgroup ON Hi_WipNote.MachGroupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.HiWipnoteitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Hi_WipNote.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Hi_WipNote.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Hi_WipNote.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.worktype != null">
            and Hi_WipNote.worktype like concat('%',
                #{SearchPojo.worktype}, '%')
        </if>
        <if test="SearchPojo.workshopid != null">
            and Hi_WipNote.workshopid like concat('%',
                #{SearchPojo.workshopid}, '%')
        </if>
        <if test="SearchPojo.workshop != null">
            and Hi_WipNote.workshop like concat('%',
                #{SearchPojo.workshop}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Hi_WipNote.goodsid like concat('%',
                #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Hi_WipNote.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Hi_WipNote.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Hi_WipNote.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Hi_WipNote.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.statecode != null">
            and Hi_WipNote.statecode like concat('%',
                #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.wkwpid != null">
            and Hi_WipNote.wkwpid like concat('%',
                #{SearchPojo.wkwpid}, '%')
        </if>
        <if test="SearchPojo.wkwpcode != null">
            and Hi_WipNote.wkwpcode like concat('%',
                #{SearchPojo.wkwpcode}, '%')
        </if>
        <if test="SearchPojo.wkwpname != null">
            and Hi_WipNote.wkwpname like concat('%',
                #{SearchPojo.wkwpname}, '%')
        </if>
        <if test="SearchPojo.customer != null">
            and Hi_WipNote.customer like concat('%',
                #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null">
            and Hi_WipNote.custpo like concat('%',
                #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.machuid != null">
            and Hi_WipNote.machuid like concat('%',
                #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null">
            and Hi_WipNote.machitemid like concat('%',
                #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null">
            and Hi_WipNote.machgroupid like concat('%',
                #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null">
            and Hi_WipNote.mainplanuid like concat('%',
                #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null">
            and Hi_WipNote.mainplanitemid like concat('%',
                #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.workuid != null">
            and Hi_WipNote.workuid like concat('%',
                #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null">
            and Hi_WipNote.workitemid like concat('%',
                #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.substwpid != null">
            and Hi_WipNote.substwpid like concat('%',
                #{SearchPojo.substwpid}, '%')
        </if>
        <if test="SearchPojo.substwpcode != null">
            and Hi_WipNote.substwpcode like concat('%',
                #{SearchPojo.substwpcode}, '%')
        </if>
        <if test="SearchPojo.substwpname != null">
            and Hi_WipNote.substwpname like concat('%',
                #{SearchPojo.substwpname}, '%')
        </if>
        <if test="SearchPojo.subendwpid != null">
            and Hi_WipNote.subendwpid like concat('%',
                #{SearchPojo.subendwpid}, '%')
        </if>
        <if test="SearchPojo.subendwpcode != null">
            and Hi_WipNote.subendwpcode like concat('%',
                #{SearchPojo.subendwpcode}, '%')
        </if>
        <if test="SearchPojo.subendwpname != null">
            and Hi_WipNote.subendwpname like concat('%',
                #{SearchPojo.subendwpname}, '%')
        </if>
        <if test="SearchPojo.subuid != null">
            and Hi_WipNote.subuid like concat('%',
                #{SearchPojo.subuid}, '%')
        </if>
        <if test="SearchPojo.compwpid != null">
            and Hi_WipNote.compwpid like concat('%',
                #{SearchPojo.compwpid}, '%')
        </if>
        <if test="SearchPojo.compwpcode != null">
            and Hi_WipNote.compwpcode like concat('%',
                #{SearchPojo.compwpcode}, '%')
        </if>
        <if test="SearchPojo.compwpname != null">
            and Hi_WipNote.compwpname like concat('%',
                #{SearchPojo.compwpname}, '%')
        </if>
        <if test="SearchPojo.wipgroupid != null">
            and Hi_WipNote.wipgroupid like concat('%',
                #{SearchPojo.wipgroupid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Hi_WipNote.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.attributejson != null">
            and Hi_WipNote.attributejson like concat('%',
                #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.attributestr != null">
            and Hi_WipNote.attributestr like concat('%',
                #{SearchPojo.attributestr}, '%')
        </if>
        <if test="SearchPojo.matcode != null">
            and Hi_WipNote.matcode like concat('%',
                #{SearchPojo.matcode}, '%')
        </if>
        <if test="SearchPojo.wkspecjson != null ">
            and Hi_WipNote.WkSpecJson like concat('%', #{SearchPojo.wkspecjson}, '%')
        </if>
        <if test="SearchPojo.colorlevel != null">
            and Hi_WipNote.colorlevel like concat('%',
                #{SearchPojo.colorlevel}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null">
            and Hi_WipNote.disannullisterid like concat('%',
                #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null">
            and Hi_WipNote.disannullister like concat('%',
                #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.itemjson != null">
            and Hi_WipNote.itemjson like concat('%',
                #{SearchPojo.itemjson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Hi_WipNote.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Hi_WipNote.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Hi_WipNote.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Hi_WipNote.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Hi_WipNote.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Hi_WipNote.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Hi_WipNote.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Hi_WipNote.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Hi_WipNote.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Hi_WipNote.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Hi_WipNote.tenantname like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Hi_WipNote.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.worktype != null">
                or Hi_WipNote.WorkType like concat('%', #{SearchPojo.worktype}, '%')
            </if>
            <if test="SearchPojo.workshopid != null">
                or Hi_WipNote.Workshopid like concat('%', #{SearchPojo.workshopid}, '%')
            </if>
            <if test="SearchPojo.workshop != null">
                or Hi_WipNote.Workshop like concat('%', #{SearchPojo.workshop}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Hi_WipNote.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Hi_WipNote.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Hi_WipNote.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Hi_WipNote.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Hi_WipNote.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.statecode != null">
                or Hi_WipNote.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.wkwpid != null">
                or Hi_WipNote.WkWpid like concat('%', #{SearchPojo.wkwpid}, '%')
            </if>
            <if test="SearchPojo.wkwpcode != null">
                or Hi_WipNote.WkWpCode like concat('%', #{SearchPojo.wkwpcode}, '%')
            </if>
            <if test="SearchPojo.wkwpname != null">
                or Hi_WipNote.WkWpName like concat('%', #{SearchPojo.wkwpname}, '%')
            </if>
            <if test="SearchPojo.customer != null">
                or Hi_WipNote.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null">
                or Hi_WipNote.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.machuid != null">
                or Hi_WipNote.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null">
                or Hi_WipNote.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null">
                or Hi_WipNote.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null">
                or Hi_WipNote.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null">
                or Hi_WipNote.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.workuid != null">
                or Hi_WipNote.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workrefno != null ">
                or Hi_WipNote.WorkRefNo like concat('%', #{SearchPojo.workrefno}, '%')
            </if>
            <if test="SearchPojo.workitemid != null">
                or Hi_WipNote.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.substwpid != null">
                or Hi_WipNote.SubStWpid like concat('%', #{SearchPojo.substwpid}, '%')
            </if>
            <if test="SearchPojo.substwpcode != null">
                or Hi_WipNote.SubStWpCode like concat('%', #{SearchPojo.substwpcode}, '%')
            </if>
            <if test="SearchPojo.substwpname != null">
                or Hi_WipNote.SubStWpName like concat('%', #{SearchPojo.substwpname}, '%')
            </if>
            <if test="SearchPojo.subendwpid != null">
                or Hi_WipNote.SubEndWpid like concat('%', #{SearchPojo.subendwpid}, '%')
            </if>
            <if test="SearchPojo.subendwpcode != null">
                or Hi_WipNote.SubEndWpCode like concat('%', #{SearchPojo.subendwpcode}, '%')
            </if>
            <if test="SearchPojo.subendwpname != null">
                or Hi_WipNote.SubEndWpName like concat('%', #{SearchPojo.subendwpname}, '%')
            </if>
            <if test="SearchPojo.subuid != null">
                or Hi_WipNote.SubUid like concat('%', #{SearchPojo.subuid}, '%')
            </if>
            <if test="SearchPojo.compwpid != null">
                or Hi_WipNote.CompWpid like concat('%', #{SearchPojo.compwpid}, '%')
            </if>
            <if test="SearchPojo.compwpcode != null">
                or Hi_WipNote.CompWpCode like concat('%', #{SearchPojo.compwpcode}, '%')
            </if>
            <if test="SearchPojo.compwpname != null">
                or Hi_WipNote.CompWpName like concat('%', #{SearchPojo.compwpname}, '%')
            </if>
            <if test="SearchPojo.wipgroupid != null">
                or Hi_WipNote.WipGroupid like concat('%', #{SearchPojo.wipgroupid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Hi_WipNote.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.attributejson != null">
                or Hi_WipNote.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.attributestr != null">
                or Hi_WipNote.AttributeStr like concat('%', #{SearchPojo.attributestr}, '%')
            </if>
            <if test="SearchPojo.matcode != null">
                or Hi_WipNote.MatCode like concat('%', #{SearchPojo.matcode}, '%')
            </if>
            <if test="SearchPojo.wkspecjson != null">
                or Hi_WipNote.WkSpecJson like concat('%', #{SearchPojo.wkspecjson}, '%')
            </if>
            <if test="SearchPojo.colorlevel != null">
                or Hi_WipNote.ColorLevel like concat('%', #{SearchPojo.colorlevel}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null">
                or Hi_WipNote.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null">
                or Hi_WipNote.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.itemjson != null">
                or Hi_WipNote.ItemJson like concat('%', #{SearchPojo.itemjson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Hi_WipNote.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Hi_WipNote.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Hi_WipNote.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Hi_WipNote.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Hi_WipNote.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Hi_WipNote.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Hi_WipNote.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Hi_WipNote.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Hi_WipNote.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Hi_WipNote.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Hi_WipNote.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.HiWipnotePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Hi_WipNote.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Hi_WipNote.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Hi_WipNote.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.worktype != null">
            and Hi_WipNote.WorkType like concat('%',
            #{SearchPojo.worktype}, '%')
        </if>
        <if test="SearchPojo.workshopid != null">
            and Hi_WipNote.Workshopid like concat('%',
            #{SearchPojo.workshopid}, '%')
        </if>
        <if test="SearchPojo.workshop != null">
            and Hi_WipNote.Workshop like concat('%',
            #{SearchPojo.workshop}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Hi_WipNote.Goodsid like concat('%',
            #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Hi_WipNote.CreateBy like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Hi_WipNote.CreateByid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Hi_WipNote.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Hi_WipNote.Listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.statecode != null">
            and Hi_WipNote.StateCode like concat('%',
            #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.wkwpid != null">
            and Hi_WipNote.WkWpid like concat('%',
            #{SearchPojo.wkwpid}, '%')
        </if>
        <if test="SearchPojo.wkwpcode != null">
            and Hi_WipNote.WkWpCode like concat('%',
            #{SearchPojo.wkwpcode}, '%')
        </if>
        <if test="SearchPojo.wkwpname != null">
            and Hi_WipNote.WkWpName like concat('%',
            #{SearchPojo.wkwpname}, '%')
        </if>
        <if test="SearchPojo.customer != null">
            and Hi_WipNote.Customer like concat('%',
            #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null">
            and Hi_WipNote.CustPO like concat('%',
            #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.machuid != null">
            and Hi_WipNote.MachUid like concat('%',
            #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null">
            and Hi_WipNote.MachItemid like concat('%',
            #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null">
            and Hi_WipNote.MachGroupid like concat('%',
            #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null">
            and Hi_WipNote.MainPlanUid like concat('%',
            #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null">
            and Hi_WipNote.MainPlanItemid like concat('%',
            #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.workuid != null">
            and Hi_WipNote.WorkUid like concat('%',
            #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workrefno != null ">
            and Hi_WipNote.WorkRefNo like concat('%', #{SearchPojo.workrefno}, '%')
        </if>
        <if test="SearchPojo.workitemid != null">
            and Hi_WipNote.WorkItemid like concat('%',
            #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.substwpid != null">
            and Hi_WipNote.SubStWpid like concat('%',
            #{SearchPojo.substwpid}, '%')
        </if>
        <if test="SearchPojo.substwpcode != null">
            and Hi_WipNote.SubStWpCode like concat('%',
            #{SearchPojo.substwpcode}, '%')
        </if>
        <if test="SearchPojo.substwpname != null">
            and Hi_WipNote.SubStWpName like concat('%',
            #{SearchPojo.substwpname}, '%')
        </if>
        <if test="SearchPojo.subendwpid != null">
            and Hi_WipNote.SubEndWpid like concat('%',
            #{SearchPojo.subendwpid}, '%')
        </if>
        <if test="SearchPojo.subendwpcode != null">
            and Hi_WipNote.SubEndWpCode like concat('%',
            #{SearchPojo.subendwpcode}, '%')
        </if>
        <if test="SearchPojo.subendwpname != null">
            and Hi_WipNote.SubEndWpName like concat('%',
            #{SearchPojo.subendwpname}, '%')
        </if>
        <if test="SearchPojo.subuid != null">
            and Hi_WipNote.SubUid like concat('%',
            #{SearchPojo.subuid}, '%')
        </if>
        <if test="SearchPojo.compwpid != null">
            and Hi_WipNote.CompWpid like concat('%',
            #{SearchPojo.compwpid}, '%')
        </if>
        <if test="SearchPojo.compwpcode != null">
            and Hi_WipNote.CompWpCode like concat('%',
            #{SearchPojo.compwpcode}, '%')
        </if>
        <if test="SearchPojo.compwpname != null">
            and Hi_WipNote.CompWpName like concat('%',
            #{SearchPojo.compwpname}, '%')
        </if>
        <if test="SearchPojo.wipgroupid != null">
            and Hi_WipNote.WipGroupid like concat('%',
            #{SearchPojo.wipgroupid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Hi_WipNote.Summary like concat('%',
            #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.attributejson != null">
            and Hi_WipNote.AttributeJson like concat('%',
            #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.attributestr != null">
            and Hi_WipNote.AttributeStr like concat('%',
            #{SearchPojo.attributestr}, '%')
        </if>
        <if test="SearchPojo.matcode != null">
            and Hi_WipNote.MatCode like concat('%',
            #{SearchPojo.matcode}, '%')
        </if>
        <if test="SearchPojo.wkspecjson != null">
            and Hi_WipNote.WkSpecJson like concat('%',
            #{SearchPojo.wkspecjson}, '%')
        </if>
        <if test="SearchPojo.colorlevel != null">
            and Hi_WipNote.ColorLevel like concat('%',
            #{SearchPojo.colorlevel}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null">
            and Hi_WipNote.DisannulListerid like concat('%',
            #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null">
            and Hi_WipNote.DisannulLister like concat('%',
            #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.itemjson != null">
            and Hi_WipNote.ItemJson like concat('%',
            #{SearchPojo.itemjson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Hi_WipNote.Custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Hi_WipNote.Custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Hi_WipNote.Custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Hi_WipNote.Custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Hi_WipNote.Custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Hi_WipNote.Custom6 like concat('%',
            #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Hi_WipNote.Custom7 like concat('%',
            #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Hi_WipNote.Custom8 like concat('%',
            #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Hi_WipNote.Custom9 like concat('%',
            #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Hi_WipNote.Custom10 like concat('%',
            #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Hi_WipNote.TenantName like concat('%',
            #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Hi_WipNote.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.worktype != null">
                or Hi_WipNote.WorkType like concat('%', #{SearchPojo.worktype}, '%')
            </if>
            <if test="SearchPojo.workshopid != null">
                or Hi_WipNote.Workshopid like concat('%', #{SearchPojo.workshopid}, '%')
            </if>
            <if test="SearchPojo.workshop != null">
                or Hi_WipNote.Workshop like concat('%', #{SearchPojo.workshop}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Hi_WipNote.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Hi_WipNote.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Hi_WipNote.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Hi_WipNote.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Hi_WipNote.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.statecode != null">
                or Hi_WipNote.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.wkwpid != null">
                or Hi_WipNote.WkWpid like concat('%', #{SearchPojo.wkwpid}, '%')
            </if>
            <if test="SearchPojo.wkwpcode != null">
                or Hi_WipNote.WkWpCode like concat('%', #{SearchPojo.wkwpcode}, '%')
            </if>
            <if test="SearchPojo.wkwpname != null">
                or Hi_WipNote.WkWpName like concat('%', #{SearchPojo.wkwpname}, '%')
            </if>
            <if test="SearchPojo.customer != null">
                or Hi_WipNote.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null">
                or Hi_WipNote.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.machuid != null">
                or Hi_WipNote.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null">
                or Hi_WipNote.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null">
                or Hi_WipNote.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null">
                or Hi_WipNote.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null">
                or Hi_WipNote.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.workuid != null">
                or Hi_WipNote.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workrefno != null ">
                or Hi_WipNote.WorkRefNo like concat('%', #{SearchPojo.workrefno}, '%')
            </if>
            <if test="SearchPojo.workitemid != null">
                or Hi_WipNote.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.substwpid != null">
                or Hi_WipNote.SubStWpid like concat('%', #{SearchPojo.substwpid}, '%')
            </if>
            <if test="SearchPojo.substwpcode != null">
                or Hi_WipNote.SubStWpCode like concat('%', #{SearchPojo.substwpcode}, '%')
            </if>
            <if test="SearchPojo.substwpname != null">
                or Hi_WipNote.SubStWpName like concat('%', #{SearchPojo.substwpname}, '%')
            </if>
            <if test="SearchPojo.subendwpid != null">
                or Hi_WipNote.SubEndWpid like concat('%', #{SearchPojo.subendwpid}, '%')
            </if>
            <if test="SearchPojo.subendwpcode != null">
                or Hi_WipNote.SubEndWpCode like concat('%', #{SearchPojo.subendwpcode}, '%')
            </if>
            <if test="SearchPojo.subendwpname != null">
                or Hi_WipNote.SubEndWpName like concat('%', #{SearchPojo.subendwpname}, '%')
            </if>
            <if test="SearchPojo.subuid != null">
                or Hi_WipNote.SubUid like concat('%', #{SearchPojo.subuid}, '%')
            </if>
            <if test="SearchPojo.compwpid != null">
                or Hi_WipNote.CompWpid like concat('%', #{SearchPojo.compwpid}, '%')
            </if>
            <if test="SearchPojo.compwpcode != null">
                or Hi_WipNote.CompWpCode like concat('%', #{SearchPojo.compwpcode}, '%')
            </if>
            <if test="SearchPojo.compwpname != null">
                or Hi_WipNote.CompWpName like concat('%', #{SearchPojo.compwpname}, '%')
            </if>
            <if test="SearchPojo.wipgroupid != null">
                or Hi_WipNote.WipGroupid like concat('%', #{SearchPojo.wipgroupid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Hi_WipNote.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.attributejson != null">
                or Hi_WipNote.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.attributestr != null">
                or Hi_WipNote.AttributeStr like concat('%', #{SearchPojo.attributestr}, '%')
            </if>
            <if test="SearchPojo.matcode != null">
                or Hi_WipNote.MatCode like concat('%', #{SearchPojo.matcode}, '%')
            </if>
            <if test="SearchPojo.wkspecjson != null">
                or Hi_WipNote.WkSpecJson like concat('%', #{SearchPojo.wkspecjson}, '%')
            </if>
            <if test="SearchPojo.colorlevel != null">
                or Hi_WipNote.ColorLevel like concat('%', #{SearchPojo.colorlevel}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null">
                or Hi_WipNote.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null">
                or Hi_WipNote.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.itemjson != null">
                or Hi_WipNote.ItemJson like concat('%', #{SearchPojo.itemjson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Hi_WipNote.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Hi_WipNote.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Hi_WipNote.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Hi_WipNote.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Hi_WipNote.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Hi_WipNote.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Hi_WipNote.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Hi_WipNote.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Hi_WipNote.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Hi_WipNote.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Hi_WipNote.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Hi_WipNote(id, RefNo, BillDate, WorkType, Workshopid, Workshop, Goodsid, PlanDate, Quantity, WkPcsQty, WkSecQty, MrbPcsQty, MrbSecQty, Supplement, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, StateCode, StateDate, WkWpid, WkWpCode, WkWpName, WkRowNum, Customer, CustPO, MachUid, MachItemid, MachGroupid, MainPlanUid, MainPlanItemid, WorkUid, WorkRefNo, WorkRowNum, WorkItemid, SubStWpid, SubStWpCode, SubStWpName, SubEndWpid, SubEndWpCode, SubEndWpName, SubUid, WorkDate, CompWpid, CompWpCode, CompWpName, CompPcsQty, WipGroupid, Summary, AttributeJson, AttributeStr, MatCode, MatUsed, WkSpecJson, ItemCount, FinishCount, PrintCount, ColorLevel, SizeX, SizeY, SizeZ, Closed, DisannulListerid, DisannulLister, DisannulDate, DisannulMark, MergeMark, SourceType, ItemJson, Isolation, Exponent, JobPcsQty, JobSecQty, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billdate}, #{worktype}, #{workshopid}, #{workshop}, #{goodsid}, #{plandate}, #{quantity}, #{wkpcsqty}, #{wksecqty}, #{mrbpcsqty}, #{mrbsecqty}, #{supplement}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{statecode}, #{statedate}, #{wkwpid}, #{wkwpcode}, #{wkwpname}, #{wkrownum}, #{customer}, #{custpo}, #{machuid}, #{machitemid}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid}, #{workuid}, #{workrefno}, #{workrownum}, #{workitemid}, #{substwpid}, #{substwpcode}, #{substwpname}, #{subendwpid}, #{subendwpcode}, #{subendwpname}, #{subuid}, #{workdate}, #{compwpid}, #{compwpcode}, #{compwpname}, #{comppcsqty}, #{wipgroupid}, #{summary}, #{attributejson}, #{attributestr}, #{matcode}, #{matused}, #{wkspecjson}, #{itemcount}, #{finishcount}, #{printcount}, #{colorlevel}, #{sizex}, #{sizey}, #{sizez}, #{closed}, #{disannullisterid}, #{disannullister}, #{disannuldate}, #{disannulmark}, #{mergemark}, #{sourcetype}, #{itemjson}, #{isolation}, #{exponent}, #{jobpcsqty}, #{jobsecqty}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Hi_WipNote
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="worktype != null">
                WorkType =#{worktype},
            </if>
            <if test="workshopid != null">
                Workshopid =#{workshopid},
            </if>
            <if test="workshop != null">
                Workshop =#{workshop},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="plandate != null">
                PlanDate =#{plandate},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="wkpcsqty != null">
                WkPcsQty =#{wkpcsqty},
            </if>
            <if test="wksecqty != null">
                WkSecQty =#{wksecqty},
            </if>
            <if test="mrbpcsqty != null">
                MrbPcsQty =#{mrbpcsqty},
            </if>
            <if test="mrbsecqty != null">
                MrbSecQty =#{mrbsecqty},
            </if>
            <if test="supplement != null">
                Supplement =#{supplement},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="statecode != null">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="wkwpid != null">
                WkWpid =#{wkwpid},
            </if>
            <if test="wkwpcode != null">
                WkWpCode =#{wkwpcode},
            </if>
            <if test="wkwpname != null">
                WkWpName =#{wkwpname},
            </if>
            <if test="wkrownum != null">
                WkRowNum =#{wkrownum},
            </if>
            <if test="customer != null">
                Customer =#{customer},
            </if>
            <if test="custpo != null">
                CustPO =#{custpo},
            </if>
            <if test="machuid != null">
                MachUid =#{machuid},
            </if>
            <if test="machitemid != null">
                MachItemid =#{machitemid},
            </if>
            <if test="machgroupid != null">
                MachGroupid =#{machgroupid},
            </if>
            <if test="mainplanuid != null">
                MainPlanUid =#{mainplanuid},
            </if>
            <if test="mainplanitemid != null">
                MainPlanItemid =#{mainplanitemid},
            </if>
            <if test="workuid != null">
                WorkUid =#{workuid},
            </if>
            <if test="workrefno != null ">
            WorkRefNo =#{workrefno},
        </if>
            <if test="workrownum != null">
            WorkRowNum =#{workrownum},
        </if>
            <if test="workitemid != null ">
                WorkItemid =#{workitemid},
            </if>
            <if test="substwpid != null">
                SubStWpid =#{substwpid},
            </if>
            <if test="substwpcode != null">
                SubStWpCode =#{substwpcode},
            </if>
            <if test="substwpname != null">
                SubStWpName =#{substwpname},
            </if>
            <if test="subendwpid != null">
                SubEndWpid =#{subendwpid},
            </if>
            <if test="subendwpcode != null">
                SubEndWpCode =#{subendwpcode},
            </if>
            <if test="subendwpname != null">
                SubEndWpName =#{subendwpname},
            </if>
            <if test="subuid != null">
                SubUid =#{subuid},
            </if>
            <if test="workdate != null">
                WorkDate =#{workdate},
            </if>
            <if test="compwpid != null">
                CompWpid =#{compwpid},
            </if>
            <if test="compwpcode != null">
                CompWpCode =#{compwpcode},
            </if>
            <if test="compwpname != null">
                CompWpName =#{compwpname},
            </if>
            <if test="comppcsqty != null">
                CompPcsQty =#{comppcsqty},
            </if>
            <if test="wipgroupid != null">
                WipGroupid =#{wipgroupid},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="attributejson != null">
                AttributeJson =#{attributejson},
            </if>
            <if test="attributestr != null">
                AttributeStr =#{attributestr},
            </if>
            <if test="matcode != null">
                MatCode =#{matcode},
            </if>
            <if test="matused != null">
                MatUsed =#{matused},
            </if>
            <if test="wkspecjson != null">
                WkSpecJson =#{wkspecjson},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="colorlevel != null ">
                ColorLevel =#{colorlevel},
            </if>
            <if test="sizex != null">
                SizeX =#{sizex},
            </if>
            <if test="sizey != null">
                SizeY =#{sizey},
            </if>
            <if test="sizez != null">
                SizeZ =#{sizez},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="disannullisterid != null">
                DisannulListerid =#{disannullisterid},
            </if>
            <if test="disannullister != null">
                DisannulLister =#{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate =#{disannuldate},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="mergemark != null">
                MergeMark =#{mergemark},
            </if>
            <if test="sourcetype != null">
                SourceType =#{sourcetype},
            </if>
            <if test="itemjson != null">
                ItemJson =#{itemjson},
            </if>
            <if test="isolation != null">
                Isolation =#{isolation},
            </if>
            <if test="exponent != null">
            Exponent =#{exponent},
        </if>
            <if test="jobpcsqty != null">
            JobPcsQty =#{jobpcsqty},
        </if>
            <if test="jobsecqty != null">
            JobSecQty =#{jobsecqty},
        </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Hi_WipNote where id = #{key} and Tenantid=#{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.HiWipnotePojo">
        select
        id
        from Hi_WipNoteItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>


    <update id="updateMachWipUsed" parameterType="inks.service.std.manu.domain.pojo.HiWipnotePojo">
        UPDATE Bus_MachiningItem
        SET WipUsed=1
        WHERE Bus_MachiningItem.id = #{machitemid}
        and Bus_MachiningItem.Tenantid = #{tenantid}
    </update>

    <update id="updateWorkWipUsed" parameterType="inks.service.std.manu.domain.pojo.HiWipnotePojo">
        UPDATE Wk_WorksheetItem
        SET WipUsed=1
        WHERE Wk_WorksheetItem.id = #{machitemid}
        and Wk_WorksheetItem.Tenantid = #{tenantid}
    </update>

    <update id="updateUnMachWipUsed" parameterType="inks.service.std.manu.domain.pojo.HiWipnotePojo">
        UPDATE Bus_MachiningItem
        SET WipUsed=0
        WHERE Bus_MachiningItem.id = #{machitemid}
        and Bus_MachiningItem.Tenantid = #{tenantid}
    </update>

    <update id="updateUnWorkWipUsed" parameterType="inks.service.std.manu.domain.pojo.HiWipnotePojo">
        UPDATE Wk_WorksheetItem
        SET WipUsed=0
        WHERE Wk_WorksheetItem.id = #{machitemid}
        and Wk_WorksheetItem.Tenantid = #{tenantid}
    </update>
    <select id="getByWorkuidLike" resultType="java.lang.String">
        SELECT Hi_WipNote.WorkUid
        FROM Hi_WipNote
        WHERE Hi_WipNote.WorkUid LIKE '${workuid}%'
        and Hi_WipNote.Tenantid = #{tid}
        ORDER BY Hi_WipNote.WorkUid DESC limit 1
    </select>

    <!--    &lt;!&ndash;     DATE_FORMAT(CURDATE(), '%Y-%m-01') 用于获取当前日期的年份和月份，并将日期设置为该月的第1天。&ndash;&gt;-->
    <!--    &lt;!&ndash;     DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 1 MONTH) 将得到上个月的第1天的日期。&ndash;&gt;-->
    <!--    &lt;!&ndash;     迁移Wk_WipNote表的数据   &lt;是小于号&ndash;&gt;-->
    <!--    <insert id="toHistory">-->
    <!--        INSERT INTO Hi_WipNote-->
    <!--        SELECT *-->
    <!--        FROM Wk_WipNote-->
    <!--        WHERE CreateDate &lt; DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 1 MONTH)-->
    <!--        and Tenantid = #{tid}-->
    <!--    </insert>-->
    <!--    <insert id="toHistoryItem">-->
    <!--        INSERT INTO Hi_WipNoteItem-->
    <!--        SELECT *-->
    <!--        FROM Wk_WipNoteItem-->
    <!--        WHERE Pid IN (SELECT id FROM Wk_WipNote WHERE CreateDate &lt; DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 1 MONTH))-->
    <!--        and Tenantid = #{tid}-->
    <!--    </insert>-->




<!--================================================================以下为历史表数据迁移==========================================================================-->

<!--    &lt;!&ndash; 0.先拿到所有在线的WipNote.id(禁止迁移到Hi) &ndash;&gt;-->
<!--    <select id="getOnlineNowWipNoteIds" resultType="java.lang.String">-->
<!--        select id-->
<!--        from Wk_WipNote-->
<!--        where Wk_WipNote.Tenantid = #{tid}-->
<!--        and Wk_WipNote.MrbPcsQty + Wk_WipNote.CompPcsQty <![CDATA[<]]> Wk_WipNote.WkPcsQty-->
<!--        and Wk_WipNote.DisannulMark = 0-->
<!--        and Wk_WipNote.Closed = 0-->
<!--        and Wk_WipNote.CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--    </select>-->

<!--    &lt;!&ndash;    !!!!!!!!!!!!!!!!!!!!NowToHi!!!!!!!!!!!!!!!!!!!!注意:以下4个接口where条件必须完全相同(id,Pid不同)!!!!!!&ndash;&gt;-->
<!--    <insert id="copyNowToHi">-->
<!--        INSERT INTO Hi_WipNote-->
<!--        SELECT *-->
<!--        FROM Wk_WipNote-->
<!--        WHERE CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--        and Tenantid = #{tid}-->
<!--        <if test="onlineIds != null and onlineIds.size() != 0">-->
<!--            and id not in-->
<!--            <foreach collection="onlineIds" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </insert>-->
<!--    <insert id="copyItemNowToHi">-->
<!--        INSERT INTO Hi_WipNoteItem-->
<!--        SELECT *-->
<!--        FROM Wk_WipNoteItem-->
<!--        WHERE CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--        and Tenantid = #{tid}-->
<!--        <if test="onlineIds != null and onlineIds.size() != 0">-->
<!--            and Pid not in-->
<!--            <foreach collection="onlineIds" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </insert>-->
<!--    <delete id="deleteNow">-->
<!--        DELETE-->
<!--        FROM Wk_WipNote-->
<!--        WHERE CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--        and Tenantid = #{tid}-->
<!--        <if test="onlineIds != null and onlineIds.size() != 0">-->
<!--            and id not in-->
<!--            <foreach collection="onlineIds" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </delete>-->
<!--    <delete id="deleteItemNow">-->
<!--        DELETE-->
<!--        FROM Wk_WipNoteItem-->
<!--        WHERE CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--        and Tenantid = #{tid}-->
<!--        <if test="onlineIds != null and onlineIds.size() != 0">-->
<!--            and Pid not in-->
<!--            <foreach collection="onlineIds" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </delete>-->



<!--    &lt;!&ndash;    !!!!!!!!!!!!!!!!!!!!HiToNow!!!!!!!!!!!!!!!!!!!!注意:以下4个接口where条件必须完全相同(id,Pid不同)!!!!!!&ndash;&gt;-->
<!--    &lt;!&ndash;    如果勾选指定了要迁出的ids(HiToNow),则无需时间范围了&ndash;&gt;-->
<!--    <insert id="copyHiToNow">-->
<!--        INSERT INTO Wk_WipNote-->
<!--        SELECT *-->
<!--        FROM Hi_WipNote WHERE Tenantid = #{tid}-->
<!--        <if test="ids == null">-->
<!--            and CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--        </if>-->
<!--        <if test="ids != null and ids.size() != 0">-->
<!--            and id in-->
<!--            <foreach collection="ids" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </insert>-->
<!--    <insert id="copyItemHiToNow">-->
<!--        INSERT INTO Wk_WipNoteItem-->
<!--        SELECT *-->
<!--        FROM Hi_WipNoteItem WHERE Tenantid = #{tid}-->
<!--        <if test="ids == null">-->
<!--            and CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--        </if>-->
<!--        <if test="ids != null and ids.size() != 0">-->
<!--            and Pid in-->
<!--            <foreach collection="ids" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </insert>-->

<!--    <delete id="deleteHi">-->
<!--        DELETE-->
<!--        FROM Hi_WipNote WHERE Tenantid = #{tid}-->
<!--        <if test="ids == null">-->
<!--            and CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--        </if>-->
<!--        <if test="ids != null and ids.size() != 0">-->
<!--            and id in-->
<!--            <foreach collection="ids" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </delete>-->
<!--    <delete id="deleteItemHi">-->
<!--        DELETE-->
<!--        FROM Hi_WipNoteItem WHERE Tenantid = #{tid}-->
<!--        <if test="ids == null">-->
<!--            and CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--        </if>-->
<!--        <if test="ids != null and ids.size() != 0">-->
<!--            and Pid in-->
<!--            <foreach collection="ids" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </delete>-->

<!--    &lt;!&ndash; 获取指定表的字段信息 &ndash;&gt;-->
<!--    <select id="getTableColumns" resultType="java.util.LinkedHashMap">-->
<!--        SELECT COLUMN_NAME, DATA_TYPE, ORDINAL_POSITION-->
<!--        FROM INFORMATION_SCHEMA.COLUMNS-->
<!--        WHERE TABLE_SCHEMA = DATABASE()-->
<!--          AND TABLE_NAME = #{tablename}-->
<!--        ORDER BY ORDINAL_POSITION-->
<!--    </select>-->


<!--    ==========================================================-->
    <!-- 0.先拿到所有在线的WipNote.id(禁止迁移到Hi) -->
    <select id="getOnlineNowWipNoteIds" resultType="java.lang.String">
        select id
        from Wk_WipNote
        where Wk_WipNote.Tenantid = #{tid}
        and Wk_WipNote.MrbPcsQty + Wk_WipNote.CompPcsQty <![CDATA[<]]> Wk_WipNote.WkPcsQty
        and Wk_WipNote.DisannulMark = 0
        and Wk_WipNote.Closed = 0
        and Wk_WipNote.CreateDate BETWEEN #{startdate} and #{enddate}
    </select>

    <!-- 获取符合条件的所有可迁移ID -->
    <select id="getAllMigratableNowIds" resultType="java.lang.String">
        select id
        from Wk_WipNote
        WHERE CreateDate BETWEEN #{startdate} and #{enddate}
        and Tenantid = #{tid}
        <if test="onlineIds != null and onlineIds.size() != 0">
            and id not in
            <foreach collection="onlineIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 获取符合条件的所有历史表可迁移ID -->
    <select id="getAllMigratableHiIds" resultType="java.lang.String">
        select id
        from Hi_WipNote
        WHERE Tenantid = #{tid}
        <if test="startdate != null and enddate != null">
            and CreateDate BETWEEN #{startdate} and #{enddate}
        </if>
    </select>

    <!-- 基于ID列表批量复制主表 Now->Hi -->
    <insert id="copyNowToHiByIds">
        INSERT INTO Hi_WipNote
        SELECT *
        FROM Wk_WipNote
        WHERE Tenantid = #{tid}
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <!-- 基于ID列表批量复制子表 Now->Hi -->
    <insert id="copyItemNowToHiByIds">
        INSERT INTO Hi_WipNoteItem
        SELECT *
        FROM Wk_WipNoteItem
        WHERE Tenantid = #{tid}
        and Pid in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <!-- 基于ID列表批量删除主表数据 -->
    <delete id="deleteNowByIds">
        DELETE FROM Wk_WipNote
        WHERE Tenantid = #{tid}
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 基于ID列表批量删除子表数据 -->
    <delete id="deleteItemNowByIds">
        DELETE FROM Wk_WipNoteItem
        WHERE Tenantid = #{tid}
        and Pid in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 基于ID列表批量复制主表 Hi->Now -->
    <insert id="copyHiToNowByIds">
        INSERT INTO Wk_WipNote
        SELECT *
        FROM Hi_WipNote
        WHERE Tenantid = #{tid}
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <!-- 基于ID列表批量复制子表 Hi->Now -->
    <insert id="copyItemHiToNowByIds">
        INSERT INTO Wk_WipNoteItem
        SELECT *
        FROM Hi_WipNoteItem
        WHERE Tenantid = #{tid}
        and Pid in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <!-- 基于ID列表批量删除历史主表数据 -->
    <delete id="deleteHiByIds">
        DELETE FROM Hi_WipNote
        WHERE Tenantid = #{tid}
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 基于ID列表批量删除历史子表数据 -->
    <delete id="deleteItemHiByIds">
        DELETE FROM Hi_WipNoteItem
        WHERE Tenantid = #{tid}
        and Pid in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 获取指定表的字段信息 -->
    <select id="getTableColumns" resultType="java.util.LinkedHashMap">
        SELECT COLUMN_NAME, DATA_TYPE, ORDINAL_POSITION
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = #{tablename}
        ORDER BY ORDINAL_POSITION
    </select>
</mapper>

