<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkManureportMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkManureportPojo">
        SELECT Wk_ManuReport.id,
               Wk_ManuReport.RefNo,
               Wk_ManuReport.BillType,
               Wk_ManuReport.BillDate,
               Wk_ManuReport.BillTitle,
               Wk_ManuReport.Operator,
               Wk_ManuReport.Operatorid,
               Wk_ManuReport.Groupid,
               Wk_ManuReport.TraderCode,
               Wk_ManuReport.TraderName,
               Wk_ManuReport.Summary,
               Wk_ManuReport.CreateBy,
               Wk_ManuReport.CreateByid,
               Wk_ManuReport.CreateDate,
               Wk_ManuReport.Lister,
               Wk_ManuReport.Listerid,
               Wk_ManuReport.ModifyDate,
               Wk_ManuReport.Assessor,
               Wk_ManuReport.Assessorid,
               Wk_ManuReport.AssessDate,
               Wk_ManuReport.ItemCount,
               Wk_ManuReport.DisannulCount,
               Wk_ManuReport.FinishCount,
               Wk_ManuReport.PrintCount,
               Wk_ManuReport.Custom1,
               Wk_ManuReport.Custom2,
               Wk_ManuReport.Custom3,
               Wk_ManuReport.Custom4,
               Wk_ManuReport.Custom5,
               Wk_ManuReport.Custom6,
               Wk_ManuReport.Custom7,
               Wk_ManuReport.Custom8,
               Wk_ManuReport.Custom9,
               Wk_ManuReport.Custom10,
               Wk_ManuReport.Tenantid,
               Wk_ManuReport.TenantName,
               Wk_ManuReport.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Wk_ManuReport
                 LEFT JOIN App_Workgroup ON Wk_ManuReport.Groupid = App_Workgroup.id
        where Wk_ManuReport.id = #{key}
          and Wk_ManuReport.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Wk_ManuReport.id,
               Wk_ManuReport.RefNo,
               Wk_ManuReport.BillType,
               Wk_ManuReport.BillDate,
               Wk_ManuReport.BillTitle,
               Wk_ManuReport.Operator,
               Wk_ManuReport.Operatorid,
               Wk_ManuReport.Groupid,
               Wk_ManuReport.TraderCode,
               Wk_ManuReport.TraderName,
               Wk_ManuReport.Summary,
               Wk_ManuReport.CreateBy,
               Wk_ManuReport.CreateByid,
               Wk_ManuReport.CreateDate,
               Wk_ManuReport.Lister,
               Wk_ManuReport.Listerid,
               Wk_ManuReport.ModifyDate,
               Wk_ManuReport.Assessor,
               Wk_ManuReport.Assessorid,
               Wk_ManuReport.AssessDate,
               Wk_ManuReport.ItemCount,
               Wk_ManuReport.DisannulCount,
               Wk_ManuReport.FinishCount,
               Wk_ManuReport.PrintCount,
               Wk_ManuReport.Custom1,
               Wk_ManuReport.Custom2,
               Wk_ManuReport.Custom3,
               Wk_ManuReport.Custom4,
               Wk_ManuReport.Custom5,
               Wk_ManuReport.Custom6,
               Wk_ManuReport.Custom7,
               Wk_ManuReport.Custom8,
               Wk_ManuReport.Custom9,
               Wk_ManuReport.Custom10,
               Wk_ManuReport.Tenantid,
               Wk_ManuReport.TenantName,
               Wk_ManuReport.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Wk_ManuReport
                 LEFT JOIN App_Workgroup ON Wk_ManuReport.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Wk_ManuReportItem.id,
               Wk_ManuReportItem.Pid,
               Wk_ManuReportItem.WorkDate,
               Wk_ManuReportItem.WorkUid,
               Wk_ManuReportItem.WorkItemid,
               Wk_ManuReportItem.Goodsid,
               Wk_ManuReportItem.ItemCode,
               Wk_ManuReportItem.ItemName,
               Wk_ManuReportItem.ItemSpec,
               Wk_ManuReportItem.ItemUnit,
               Wk_ManuReportItem.WorkQty,
               Wk_ManuReportItem.Quantity,
               Wk_ManuReportItem.MrbQty,
               Wk_ManuReportItem.SubItemid,
               Wk_ManuReportItem.SubUse,
               Wk_ManuReportItem.SubUnit,
               Wk_ManuReportItem.SubQty,
               Wk_ManuReportItem.TaxPrice,
               Wk_ManuReportItem.TaxAmount,
               Wk_ManuReportItem.Price,
               Wk_ManuReportItem.Amount,
               Wk_ManuReportItem.TaxTotal,
               Wk_ManuReportItem.ItemTaxrate,
               Wk_ManuReportItem.StartDate,
               Wk_ManuReportItem.PlanDate,
               Wk_ManuReportItem.FinishQty,
               Wk_ManuReportItem.FinishHour,
               Wk_ManuReportItem.Closed,
               Wk_ManuReportItem.Remark,
               Wk_ManuReportItem.StateCode,
               Wk_ManuReportItem.StateDate,
               Wk_ManuReportItem.RowNum,
               Wk_ManuReportItem.MachUid,
               Wk_ManuReportItem.MachItemid,
               Wk_ManuReportItem.MachGroupid,
               Wk_ManuReportItem.MrpUid,
               Wk_ManuReportItem.MrpItemid,
               Wk_ManuReportItem.Customer,
               Wk_ManuReportItem.CustPO,
               Wk_ManuReportItem.MainPlanUid,
               Wk_ManuReportItem.MainPlanItemid,
               Wk_ManuReportItem.Location,
               Wk_ManuReportItem.BatchNo,
               Wk_ManuReportItem.DisannulMark,
               Wk_ManuReportItem.DisannulLister,
               Wk_ManuReportItem.DisannulListerid,
               Wk_ManuReportItem.DisannulDate,
               Wk_ManuReportItem.Custom1,
               Wk_ManuReportItem.Custom2,
               Wk_ManuReportItem.Custom3,
               Wk_ManuReportItem.Custom4,
               Wk_ManuReportItem.Custom5,
               Wk_ManuReportItem.Custom6,
               Wk_ManuReportItem.Custom7,
               Wk_ManuReportItem.Custom8,
               Wk_ManuReportItem.Custom9,
               Wk_ManuReportItem.Custom10,
               Wk_ManuReportItem.Tenantid,
               Wk_ManuReportItem.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_ManuReport.RefNo,
               Wk_ManuReport.BillType,
               Wk_ManuReport.BillDate,
               Wk_ManuReport.BillTitle,
               Wk_ManuReport.Operator,
               Wk_ManuReport.TraderCode,
               Wk_ManuReport.TraderName,
               Wk_ManuReport.Summary,
               Wk_ManuReport.CreateBy,
               Wk_ManuReport.Lister,
               Wk_ManuReport.Assessor
        FROM Wk_ManuReport
                 LEFT JOIN App_Workgroup ON Wk_ManuReport.Groupid = App_Workgroup.id
                 RIGHT JOIN Wk_ManuReportItem ON Wk_ManuReportItem.Pid = Wk_ManuReport.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Wk_ManuReportItem.Goodsid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkManureportitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_ManuReport.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_ManuReport.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Wk_ManuReport.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_ManuReport.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_ManuReport.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_ManuReport.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Wk_ManuReport.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Wk_ManuReport.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.tradercode != null ">
            and Wk_ManuReport.tradercode like concat('%', #{SearchPojo.tradercode}, '%')
        </if>
        <if test="SearchPojo.tradername != null ">
            and Wk_ManuReport.tradername like concat('%', #{SearchPojo.tradername}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_ManuReport.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_ManuReport.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_ManuReport.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_ManuReport.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_ManuReport.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_ManuReport.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_ManuReport.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_ManuReport.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_ManuReport.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_ManuReport.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_ManuReport.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_ManuReport.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_ManuReport.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_ManuReport.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_ManuReport.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_ManuReport.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_ManuReport.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_ManuReport.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_ManuReportItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.citeuid != ''">
            and Wk_ManuReportItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Wk_ManuReportItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_ManuReport.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_ManuReport.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_ManuReport.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_ManuReport.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Wk_ManuReport.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Wk_ManuReport.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.tradercode != null ">
                or Wk_ManuReport.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
            </if>
            <if test="SearchPojo.tradername != null ">
                or Wk_ManuReport.TraderName like concat('%', #{SearchPojo.tradername}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_ManuReport.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_ManuReport.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_ManuReport.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_ManuReport.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_ManuReport.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_ManuReport.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_ManuReport.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_ManuReport.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_ManuReport.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_ManuReport.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_ManuReport.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_ManuReport.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_ManuReport.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_ManuReport.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_ManuReport.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_ManuReport.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_ManuReport.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_ManuReport.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_ManuReportItem.remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.citeuid != ''">
                or Wk_ManuReportItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Wk_ManuReportItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkManureportPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_ManuReport.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_ManuReport.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Wk_ManuReport.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_ManuReport.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_ManuReport.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_ManuReport.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Wk_ManuReport.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Wk_ManuReport.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.tradercode != null ">
            and Wk_ManuReport.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
        </if>
        <if test="SearchPojo.tradername != null ">
            and Wk_ManuReport.TraderName like concat('%', #{SearchPojo.tradername}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_ManuReport.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_ManuReport.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_ManuReport.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_ManuReport.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_ManuReport.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_ManuReport.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_ManuReport.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_ManuReport.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_ManuReport.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_ManuReport.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_ManuReport.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_ManuReport.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_ManuReport.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_ManuReport.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_ManuReport.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_ManuReport.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_ManuReport.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_ManuReport.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_ManuReport.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_ManuReport.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_ManuReport.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_ManuReport.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Wk_ManuReport.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Wk_ManuReport.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.tradercode != null ">
                or Wk_ManuReport.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
            </if>
            <if test="SearchPojo.tradername != null ">
                or Wk_ManuReport.TraderName like concat('%', #{SearchPojo.tradername}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_ManuReport.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_ManuReport.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_ManuReport.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_ManuReport.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_ManuReport.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_ManuReport.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_ManuReport.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_ManuReport.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_ManuReport.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_ManuReport.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_ManuReport.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_ManuReport.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_ManuReport.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_ManuReport.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_ManuReport.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_ManuReport.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_ManuReport.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_ManuReport.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_ManuReport(id, RefNo, BillType, BillDate, BillTitle, Operator, Operatorid, Groupid, TraderCode,
                                  TraderName, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate,
                                  Assessor, Assessorid, AssessDate, ItemCount, DisannulCount, FinishCount, PrintCount,
                                  Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9,
                                  Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{operator}, #{operatorid}, #{groupid},
                #{tradercode}, #{tradername}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister},
                #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{itemcount}, #{disannulcount},
                #{finishcount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_ManuReport
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="tradercode != null ">
                TraderCode =#{tradercode},
            </if>
            <if test="tradername != null ">
                TraderName =#{tradername},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_ManuReport
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_ManuReport
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--刷新打印次数-->
    <update id="updatePrintcount">
        update Wk_ManuReport
        SET PrintCount = #{printcount}
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>    
<!--    刷新作废行数-->
    <update id="updateDisannulCount">
        update Wk_ManuReport
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Wk_ManuReportItem
                                     where Wk_ManuReportItem.Pid = #{key}
                                       and Wk_ManuReportItem.Tenantid = #{tid}
                                       and Wk_ManuReportItem.DisannulMark = 1), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
<!--    刷新中止行数-->
    <update id="updateFinishCount">
        update Wk_ManuReport
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_ManuReportItem
                                   where Wk_ManuReportItem.Pid =#{key}
                                     and Wk_ManuReportItem.Tenantid = #{tid}
                                     and (Wk_ManuReportItem.FinishQty >= Wk_ManuReportItem.Quantity or
                                          Wk_ManuReportItem.Closed = 1)), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkManureportPojo">
        select
        id
        from Wk_ManuReportItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

    <!--查询DelListIds-->
    <select id="getSumQtyByWork" resultType="inks.common.core.domain.ChartPojo">
        SELECT WorkUid as name, SUM(Quantity) as value
        FROM Wk_ManuReportItem
        where WorkUid=#{code}
          and WorkItemid=#{key}
          and Tenantid = #{tid}
        Order by WorkUid
    </select>
</mapper>

