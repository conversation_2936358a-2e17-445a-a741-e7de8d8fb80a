<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWscarryoverMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWscarryoverPojo">
        select
          id, RefNo, BillType, BillDate, BillTitle, Groupid, TraderCode, TraderName, CarryYear, CarryMonth, StartDate, EndDate, Operator, Operatorid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, BillOpenAmount, BillInAmount, BillOutAmount, BillCloseAmount, ItemCount, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision
        from Wk_WsCarryover
        where Wk_WsCarryover.id = #{key} and Wk_WsCarryover.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
          id, RefNo, BillType, BillDate, BillTitle, Groupid, TraderCode, TraderName, CarryYear, CarryMonth, StartDate, EndDate, Operator, Operatorid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, BillOpenAmount, BillInAmount, BillOutAmount, BillCloseAmount, ItemCount, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Wk_WsCarryover
    </sql>
    <sql id="selectdetailVo">
         select
          id, RefNo, BillType, BillDate, BillTitle, Groupid, TraderCode, TraderName, CarryYear, CarryMonth, StartDate, EndDate, Operator, Operatorid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, BillOpenAmount, BillInAmount, BillOutAmount, BillCloseAmount, ItemCount, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Wk_WsCarryover
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkWscarryoveritemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 and Wk_WsCarryover.Tenantid =#{tenantid}
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_WsCarryover.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Wk_WsCarryover.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Wk_WsCarryover.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Wk_WsCarryover.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Wk_WsCarryover.groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.tradercode != null ">
   and Wk_WsCarryover.tradercode like concat('%', #{SearchPojo.tradercode}, '%')
</if>
<if test="SearchPojo.tradername != null ">
   and Wk_WsCarryover.tradername like concat('%', #{SearchPojo.tradername}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Wk_WsCarryover.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Wk_WsCarryover.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Wk_WsCarryover.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Wk_WsCarryover.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Wk_WsCarryover.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Wk_WsCarryover.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Wk_WsCarryover.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Wk_WsCarryover.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Wk_WsCarryover.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Wk_WsCarryover.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Wk_WsCarryover.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Wk_WsCarryover.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Wk_WsCarryover.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Wk_WsCarryover.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Wk_WsCarryover.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Wk_WsCarryover.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Wk_WsCarryover.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Wk_WsCarryover.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Wk_WsCarryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Wk_WsCarryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Wk_WsCarryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Wk_WsCarryover.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.tradercode != null ">
   or Wk_WsCarryover.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
</if>
<if test="SearchPojo.tradername != null ">
   or Wk_WsCarryover.TraderName like concat('%', #{SearchPojo.tradername}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Wk_WsCarryover.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Wk_WsCarryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Wk_WsCarryover.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Wk_WsCarryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Wk_WsCarryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Wk_WsCarryover.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Wk_WsCarryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Wk_WsCarryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Wk_WsCarryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Wk_WsCarryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Wk_WsCarryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Wk_WsCarryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Wk_WsCarryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Wk_WsCarryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Wk_WsCarryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Wk_WsCarryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Wk_WsCarryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Wk_WsCarryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkWscarryoverPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 and Wk_WsCarryover.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_WsCarryover.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Wk_WsCarryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Wk_WsCarryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Wk_WsCarryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Wk_WsCarryover.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.tradercode != null ">
   and Wk_WsCarryover.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
</if>
<if test="SearchPojo.tradername != null ">
   and Wk_WsCarryover.TraderName like concat('%', #{SearchPojo.tradername}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Wk_WsCarryover.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Wk_WsCarryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Wk_WsCarryover.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Wk_WsCarryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Wk_WsCarryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Wk_WsCarryover.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Wk_WsCarryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Wk_WsCarryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Wk_WsCarryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Wk_WsCarryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Wk_WsCarryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Wk_WsCarryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Wk_WsCarryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Wk_WsCarryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Wk_WsCarryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Wk_WsCarryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Wk_WsCarryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Wk_WsCarryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Wk_WsCarryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Wk_WsCarryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Wk_WsCarryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Wk_WsCarryover.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.tradercode != null ">
   or Wk_WsCarryover.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
</if>
<if test="SearchPojo.tradername != null ">
   or Wk_WsCarryover.TraderName like concat('%', #{SearchPojo.tradername}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Wk_WsCarryover.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Wk_WsCarryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Wk_WsCarryover.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Wk_WsCarryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Wk_WsCarryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Wk_WsCarryover.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Wk_WsCarryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Wk_WsCarryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Wk_WsCarryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Wk_WsCarryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Wk_WsCarryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Wk_WsCarryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Wk_WsCarryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Wk_WsCarryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Wk_WsCarryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Wk_WsCarryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Wk_WsCarryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Wk_WsCarryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_WsCarryover(id, RefNo, BillType, BillDate, BillTitle, Groupid, TraderCode, TraderName, CarryYear, CarryMonth, StartDate, EndDate, Operator, Operatorid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, BillOpenAmount, BillInAmount, BillOutAmount, BillCloseAmount, ItemCount, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{groupid}, #{tradercode}, #{tradername}, #{carryyear}, #{carrymonth}, #{startdate}, #{enddate}, #{operator}, #{operatorid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{billopenamount}, #{billinamount}, #{billoutamount}, #{billcloseamount}, #{itemcount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WsCarryover
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="tradercode != null ">
                TraderCode =#{tradercode},
            </if>
            <if test="tradername != null ">
                TraderName =#{tradername},
            </if>
            <if test="carryyear != null">
                CarryYear =#{carryyear},
            </if>
            <if test="carrymonth != null">
                CarryMonth =#{carrymonth},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="billopenamount != null">
                BillOpenAmount =#{billopenamount},
            </if>
            <if test="billinamount != null">
                BillInAmount =#{billinamount},
            </if>
            <if test="billoutamount != null">
                BillOutAmount =#{billoutamount},
            </if>
            <if test="billcloseamount != null">
                BillCloseAmount =#{billcloseamount},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Wk_WsCarryover where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                                                                                    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.manu.domain.pojo.WkWscarryoverPojo">
        select
          id
        from Wk_WsCarryoverItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
    </select>



    <!--查询指定行数据-->
    <select id="getGoodsWsList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWscarryoveritemPojo">
        SELECT Mat_Goods.id,Mat_Goods.GoodsUid,
        SUM(Wk_WorksheetItem.Quantity) as InQty,
        SUM(Mat_Goods.InPrice*Wk_WorksheetItem.Quantity) as InAmount
        FROM Wk_Worksheet
        RIGHT JOIN Wk_WorksheetItem ON Wk_WorksheetItem.Pid = Wk_Worksheet.id
        LEFT JOIN Mat_Goods ON Mat_Goods.id = Wk_WorksheetItem.Goodsid
        where Wk_Worksheet.Tenantid =#{tenantid}
        and Wk_Worksheet.BillDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        Group by Mat_Goods.id,Mat_Goods.GoodsUid
        Order by Mat_Goods.GoodsUid
    </select>


    <!--查询指定行数据-->
    <select id="getGoodsAcceList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWscarryoveritemPojo">
        SELECT Mat_Goods.id,Mat_Goods.GoodsUid,
        SUM(CASE
        WHEN Mat_Access.BillType IN ('生产入库') THEN Mat_AccessItem.Quantity
        WHEN Mat_Access.BillType IN ('生产红冲') THEN 0 - Mat_AccessItem.Quantity END) as OutQty,
        SUM(CASE
        WHEN Mat_Access.BillType IN ('生产入库') THEN Mat_Goods.InPrice*Mat_AccessItem.Quantity
        WHEN Mat_Access.BillType IN ('生产红冲') THEN (0 - Mat_AccessItem.Quantity)*Mat_Goods.InPrice END) as
        OutAmount
        FROM Mat_Access
        RIGHT JOIN Mat_AccessItem ON Mat_Access.id = Mat_AccessItem.Pid
        LEFT JOIN Mat_Goods ON Mat_AccessItem.Goodsid = Mat_Goods.id
        where Mat_Access.Tenantid =#{tenantid}
        and Mat_Access.BillDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        Group by Mat_Goods.id,Mat_Goods.GoodsUid
        Order by Mat_Goods.GoodsUid
    </select>

</mapper>

