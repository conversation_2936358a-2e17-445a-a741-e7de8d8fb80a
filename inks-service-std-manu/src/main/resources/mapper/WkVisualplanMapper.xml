<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkVisualplanMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkVisualplanPojo">
        select
          id, GenGroupid, Billid, BillCode, BillDate, BillType, Workshopid, Workshop, Wpid, WpCode, WpName, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, StartDate, EndDate, Efficiency, FinishQty, Remark, JsonContent, Closed, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Wk_VisualPlan
        where Wk_VisualPlan.id = #{key} and Wk_VisualPlan.Tenantid=#{tid}
    </select>
    <sql id="selectWkVisualplanVo">
         select
          id, GenGroupid, Billid, BillCode, BillDate, BillType, Workshopid, Workshop, Wpid, WpCode, WpName, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, StartDate, EndDate, Efficiency, FinishQty, Remark, JsonContent, Closed, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Wk_VisualPlan
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkVisualplanPojo">
        <include refid="selectWkVisualplanVo"/>
         where 1 = 1 and Wk_VisualPlan.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_VisualPlan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.gengroupid != null ">
   and Wk_VisualPlan.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.billid != null ">
   and Wk_VisualPlan.Billid like concat('%', #{SearchPojo.billid}, '%')
</if>
<if test="SearchPojo.billcode != null ">
   and Wk_VisualPlan.BillCode like concat('%', #{SearchPojo.billcode}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Wk_VisualPlan.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.workshopid != null ">
   and Wk_VisualPlan.Workshopid like concat('%', #{SearchPojo.workshopid}, '%')
</if>
<if test="SearchPojo.workshop != null ">
   and Wk_VisualPlan.Workshop like concat('%', #{SearchPojo.workshop}, '%')
</if>
<if test="SearchPojo.wpid != null ">
   and Wk_VisualPlan.Wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null ">
   and Wk_VisualPlan.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null ">
   and Wk_VisualPlan.WpName like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   and Wk_VisualPlan.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null ">
   and Wk_VisualPlan.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   and Wk_VisualPlan.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null ">
   and Wk_VisualPlan.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null ">
   and Wk_VisualPlan.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Wk_VisualPlan.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.jsoncontent != null ">
   and Wk_VisualPlan.JsonContent like concat('%', #{SearchPojo.jsoncontent}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Wk_VisualPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Wk_VisualPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Wk_VisualPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Wk_VisualPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Wk_VisualPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Wk_VisualPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Wk_VisualPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Wk_VisualPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Wk_VisualPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Wk_VisualPlan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Wk_VisualPlan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Wk_VisualPlan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Wk_VisualPlan.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Wk_VisualPlan.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Wk_VisualPlan.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.gengroupid != null ">
   or Wk_VisualPlan.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.billid != null ">
   or Wk_VisualPlan.Billid like concat('%', #{SearchPojo.billid}, '%')
</if>
<if test="SearchPojo.billcode != null ">
   or Wk_VisualPlan.BillCode like concat('%', #{SearchPojo.billcode}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Wk_VisualPlan.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.workshopid != null ">
   or Wk_VisualPlan.Workshopid like concat('%', #{SearchPojo.workshopid}, '%')
</if>
<if test="SearchPojo.workshop != null ">
   or Wk_VisualPlan.Workshop like concat('%', #{SearchPojo.workshop}, '%')
</if>
<if test="SearchPojo.wpid != null ">
   or Wk_VisualPlan.Wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null ">
   or Wk_VisualPlan.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null ">
   or Wk_VisualPlan.WpName like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   or Wk_VisualPlan.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null ">
   or Wk_VisualPlan.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   or Wk_VisualPlan.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null ">
   or Wk_VisualPlan.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null ">
   or Wk_VisualPlan.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Wk_VisualPlan.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.jsoncontent != null ">
   or Wk_VisualPlan.JsonContent like concat('%', #{SearchPojo.jsoncontent}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Wk_VisualPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Wk_VisualPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Wk_VisualPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Wk_VisualPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Wk_VisualPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Wk_VisualPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Wk_VisualPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Wk_VisualPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Wk_VisualPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Wk_VisualPlan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Wk_VisualPlan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Wk_VisualPlan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Wk_VisualPlan.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Wk_VisualPlan.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Wk_VisualPlan.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_VisualPlan(id, GenGroupid, Billid, BillCode, BillDate, BillType, Workshopid, Workshop, Wpid, WpCode, WpName, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, StartDate, EndDate, Efficiency, FinishQty, Remark, JsonContent, Closed, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{gengroupid}, #{billid}, #{billcode}, #{billdate}, #{billtype}, #{workshopid}, #{workshop}, #{wpid}, #{wpcode}, #{wpname}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{startdate}, #{enddate}, #{efficiency}, #{finishqty}, #{remark}, #{jsoncontent}, #{closed}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_VisualPlan
        <set>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="billid != null ">
                Billid =#{billid},
            </if>
            <if test="billcode != null ">
                BillCode =#{billcode},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="workshopid != null ">
                Workshopid =#{workshopid},
            </if>
            <if test="workshop != null ">
                Workshop =#{workshop},
            </if>
            <if test="wpid != null ">
                Wpid =#{wpid},
            </if>
            <if test="wpcode != null ">
                WpCode =#{wpcode},
            </if>
            <if test="wpname != null ">
                WpName =#{wpname},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit =#{itemunit},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="efficiency != null">
                Efficiency =#{efficiency},
            </if>
            <if test="finishqty != null">
                FinishQty =#{finishqty},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="jsoncontent != null ">
                JsonContent =#{jsoncontent},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Wk_VisualPlan where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                                                                                            </mapper>

