<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWorksheetmatmergeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWorksheetmatmergePojo">
        <include refid="selectWkWorksheetmatmergeVo"/>
        where Wk_WorksheetMatMerge.id = #{key} and Wk_WorksheetMatMerge.Tenantid=#{tid}
    </select>
    <sql id="selectWkWorksheetmatmergeVo">
        select <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_WorksheetMatMerge.id,
               Wk_WorksheetMatMerge.Pid,
               Wk_WorksheetMatMerge.Itemid,
               Wk_WorksheetMatMerge.Goodsid,
               Wk_WorksheetMatMerge.Quantity,
               Wk_WorksheetMatMerge.FinishQty,
               Wk_WorksheetMatMerge.MrpUid,
               Wk_WorksheetMatMerge.MrpItemid,
               Wk_WorksheetMatMerge.SubQty,
               Wk_WorksheetMatMerge.MainQty,
               Wk_WorksheetMatMerge.LossRate,
               Wk_WorksheetMatMerge.Bomid,
               Wk_WorksheetMatMerge.BomType,
               Wk_WorksheetMatMerge.BomItemid,
               Wk_WorksheetMatMerge.ItemRowCode,
               Wk_WorksheetMatMerge.RowNum,
               Wk_WorksheetMatMerge.Closed,
               Wk_WorksheetMatMerge.BomQty,
               Wk_WorksheetMatMerge.AvaiQty,
               Wk_WorksheetMatMerge.NeedQty,
               Wk_WorksheetMatMerge.StoPlanQty,
               Wk_WorksheetMatMerge.RealQty,
               Wk_WorksheetMatMerge.FlowCode,
               Wk_WorksheetMatMerge.AttributeJson,
               Wk_WorksheetMatMerge.ItemCount,
               Wk_WorksheetMatMerge.MachUid,
               Wk_WorksheetMatMerge.Custom1,
               Wk_WorksheetMatMerge.Custom2,
               Wk_WorksheetMatMerge.Custom3,
               Wk_WorksheetMatMerge.Custom4,
               Wk_WorksheetMatMerge.Custom5,
               Wk_WorksheetMatMerge.Custom6,
               Wk_WorksheetMatMerge.Custom7,
               Wk_WorksheetMatMerge.Custom8,
               Wk_WorksheetMatMerge.Custom9,
               Wk_WorksheetMatMerge.Custom10,
               Wk_WorksheetMatMerge.Tenantid,
               Wk_WorksheetMatMerge.Revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_WorksheetMatMerge ON Mat_Goods.id = Wk_WorksheetMatMerge.Goodsid
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkWorksheetmatmergePojo">
        <include refid="selectWkWorksheetmatmergeVo"/>
        where Wk_WorksheetMatMerge.Pid = #{Pid} and Wk_WorksheetMatMerge.Tenantid=#{tid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkWorksheetmatmergePojo">
        <include refid="selectWkWorksheetmatmergeVo"/>
         where 1 = 1 and Wk_WorksheetMatMerge.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_WorksheetMatMerge.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Wk_WorksheetMatMerge.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemid != null and SearchPojo.itemid != ''">
   and Wk_WorksheetMatMerge.itemid like concat('%', #{SearchPojo.itemid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   and Wk_WorksheetMatMerge.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
   and Wk_WorksheetMatMerge.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
</if>
<if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
   and Wk_WorksheetMatMerge.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
</if>
<if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
   and Wk_WorksheetMatMerge.bomid like concat('%', #{SearchPojo.bomid}, '%')
</if>
<if test="SearchPojo.bomitemid != null and SearchPojo.bomitemid != ''">
   and Wk_WorksheetMatMerge.bomitemid like concat('%', #{SearchPojo.bomitemid}, '%')
</if>
<if test="SearchPojo.itemrowcode != null and SearchPojo.itemrowcode != ''">
   and Wk_WorksheetMatMerge.itemrowcode like concat('%', #{SearchPojo.itemrowcode}, '%')
</if>
<if test="SearchPojo.flowcode != null and SearchPojo.flowcode != ''">
   and Wk_WorksheetMatMerge.flowcode like concat('%', #{SearchPojo.flowcode}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   and Wk_WorksheetMatMerge.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
   and Wk_WorksheetMatMerge.machuid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Wk_WorksheetMatMerge.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Wk_WorksheetMatMerge.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Wk_WorksheetMatMerge.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Wk_WorksheetMatMerge.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Wk_WorksheetMatMerge.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Wk_WorksheetMatMerge.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Wk_WorksheetMatMerge.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Wk_WorksheetMatMerge.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Wk_WorksheetMatMerge.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Wk_WorksheetMatMerge.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Wk_WorksheetMatMerge.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemid != null and SearchPojo.itemid != ''">
   or Wk_WorksheetMatMerge.Itemid like concat('%', #{SearchPojo.itemid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   or Wk_WorksheetMatMerge.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
   or Wk_WorksheetMatMerge.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
</if>
<if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
   or Wk_WorksheetMatMerge.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
</if>
<if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
   or Wk_WorksheetMatMerge.Bomid like concat('%', #{SearchPojo.bomid}, '%')
</if>
<if test="SearchPojo.bomitemid != null and SearchPojo.bomitemid != ''">
   or Wk_WorksheetMatMerge.BomItemid like concat('%', #{SearchPojo.bomitemid}, '%')
</if>
<if test="SearchPojo.itemrowcode != null and SearchPojo.itemrowcode != ''">
   or Wk_WorksheetMatMerge.ItemRowCode like concat('%', #{SearchPojo.itemrowcode}, '%')
</if>
<if test="SearchPojo.flowcode != null and SearchPojo.flowcode != ''">
   or Wk_WorksheetMatMerge.FlowCode like concat('%', #{SearchPojo.flowcode}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   or Wk_WorksheetMatMerge.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
   or Wk_WorksheetMatMerge.MachUid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Wk_WorksheetMatMerge.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Wk_WorksheetMatMerge.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Wk_WorksheetMatMerge.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Wk_WorksheetMatMerge.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Wk_WorksheetMatMerge.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Wk_WorksheetMatMerge.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Wk_WorksheetMatMerge.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Wk_WorksheetMatMerge.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Wk_WorksheetMatMerge.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Wk_WorksheetMatMerge.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_WorksheetMatMerge(id, Pid, Itemid, Goodsid, Quantity, FinishQty, MrpUid, MrpItemid, SubQty, MainQty, LossRate, Bomid, BomType, BomItemid, ItemRowCode, RowNum, Closed, BomQty, AvaiQty, NeedQty, StoPlanQty, RealQty, FlowCode, AttributeJson, ItemCount, MachUid, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{itemid}, #{goodsid}, #{quantity}, #{finishqty}, #{mrpuid}, #{mrpitemid}, #{subqty}, #{mainqty}, #{lossrate}, #{bomid}, #{bomtype}, #{bomitemid}, #{itemrowcode}, #{rownum}, #{closed}, #{bomqty}, #{avaiqty}, #{needqty}, #{stoplanqty}, #{realqty}, #{flowcode}, #{attributejson}, #{itemcount}, #{machuid}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WorksheetMatMerge
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemid != null ">
                Itemid = #{itemid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="mainqty != null">
                MainQty = #{mainqty},
            </if>
            <if test="lossrate != null">
                LossRate = #{lossrate},
            </if>
            <if test="bomid != null ">
                Bomid = #{bomid},
            </if>
            <if test="bomtype != null">
                BomType = #{bomtype},
            </if>
            <if test="bomitemid != null ">
                BomItemid = #{bomitemid},
            </if>
            <if test="itemrowcode != null ">
                ItemRowCode = #{itemrowcode},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="bomqty != null">
                BomQty = #{bomqty},
            </if>
            <if test="avaiqty != null">
                AvaiQty = #{avaiqty},
            </if>
            <if test="needqty != null">
                NeedQty = #{needqty},
            </if>
            <if test="stoplanqty != null">
                StoPlanQty = #{stoplanqty},
            </if>
            <if test="realqty != null">
                RealQty = #{realqty},
            </if>
            <if test="flowcode != null ">
                FlowCode = #{flowcode},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="itemcount != null">
                ItemCount = #{itemcount},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Wk_WorksheetMatMerge where id = #{key} and Tenantid=#{tid}
    </delete>

    <delete id="deleteAllByPid">
        delete from Wk_WorksheetMatMerge where Pid = #{Pid} and Tenantid=#{tid}
    </delete>
</mapper>

