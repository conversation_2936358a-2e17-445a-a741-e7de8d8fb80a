<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkScdeductionitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkScdeductionitemPojo">
        select
id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, TaxTotal, ItemTaxrate, Price, Amount, Remark, CiteUid, CiteItemid, OrderUid, OrderItemid, CustPO, RowNum, InvoQty, InvoClosed, DisannulMark, DisannulListerid, <PERSON>sannulLister, DisannulDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Wk_ScDeductionItem
        where Wk_ScDeductionItem.id = #{key} and Wk_ScDeductionItem.Tenantid=#{tid}
    </select>
    <sql id="selectWkScdeductionitemVo">
         select
id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, TaxTotal, ItemTaxrate, Price, Amount, Remark, CiteUid, CiteItemid, OrderUid, OrderItemid, CustPO, RowNum, InvoQty, InvoClosed, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Wk_ScDeductionItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkScdeductionitemPojo">
        <include refid="selectWkScdeductionitemVo"/>
         where 1 = 1 and Wk_ScDeductionItem.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_ScDeductionItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Wk_ScDeductionItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   and Wk_ScDeductionItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   and Wk_ScDeductionItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   and Wk_ScDeductionItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   and Wk_ScDeductionItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   and Wk_ScDeductionItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Wk_ScDeductionItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
   and Wk_ScDeductionItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
   and Wk_ScDeductionItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
</if>
<if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
   and Wk_ScDeductionItem.orderuid like concat('%', #{SearchPojo.orderuid}, '%')
</if>
<if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
   and Wk_ScDeductionItem.orderitemid like concat('%', #{SearchPojo.orderitemid}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   and Wk_ScDeductionItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
   and Wk_ScDeductionItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
</if>
<if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
   and Wk_ScDeductionItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Wk_ScDeductionItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Wk_ScDeductionItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Wk_ScDeductionItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Wk_ScDeductionItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Wk_ScDeductionItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Wk_ScDeductionItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Wk_ScDeductionItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Wk_ScDeductionItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Wk_ScDeductionItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Wk_ScDeductionItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Wk_ScDeductionItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   or Wk_ScDeductionItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   or Wk_ScDeductionItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   or Wk_ScDeductionItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   or Wk_ScDeductionItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   or Wk_ScDeductionItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Wk_ScDeductionItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
   or Wk_ScDeductionItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
   or Wk_ScDeductionItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
</if>
<if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
   or Wk_ScDeductionItem.OrderUid like concat('%', #{SearchPojo.orderuid}, '%')
</if>
<if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
   or Wk_ScDeductionItem.OrderItemid like concat('%', #{SearchPojo.orderitemid}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   or Wk_ScDeductionItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
   or Wk_ScDeductionItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
</if>
<if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
   or Wk_ScDeductionItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Wk_ScDeductionItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Wk_ScDeductionItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Wk_ScDeductionItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Wk_ScDeductionItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Wk_ScDeductionItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Wk_ScDeductionItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Wk_ScDeductionItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Wk_ScDeductionItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Wk_ScDeductionItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Wk_ScDeductionItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkScdeductionitemPojo">
        select
id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, TaxTotal, ItemTaxrate, Price, Amount, Remark, CiteUid, CiteItemid, OrderUid, OrderItemid, CustPO, RowNum, InvoQty, InvoClosed, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Wk_ScDeductionItem
        where Wk_ScDeductionItem.Pid = #{Pid} and Wk_ScDeductionItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_ScDeductionItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, TaxTotal, ItemTaxrate, Price, Amount, Remark, CiteUid, CiteItemid, OrderUid, OrderItemid, CustPO, RowNum, InvoQty, InvoClosed, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{taxprice}, #{taxamount}, #{taxtotal}, #{itemtaxrate}, #{price}, #{amount}, #{remark}, #{citeuid}, #{citeitemid}, #{orderuid}, #{orderitemid}, #{custpo}, #{rownum}, #{invoqty}, #{invoclosed}, #{disannulmark}, #{disannullisterid}, #{disannullister}, #{disannuldate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_ScDeductionItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="orderuid != null ">
                OrderUid = #{orderuid},
            </if>
            <if test="orderitemid != null ">
                OrderItemid = #{orderitemid},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="invoqty != null">
                InvoQty = #{invoqty},
            </if>
            <if test="invoclosed != null">
                InvoClosed = #{invoclosed},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Wk_ScDeductionItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

