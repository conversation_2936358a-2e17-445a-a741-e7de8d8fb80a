<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.HiWipnoteitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.HiWipnoteitemPojo">
        select
          id, Pid, Wpid, WpCode, WpName, RowNum, PlanDate, Remark, InPcsQty, InSecQty, OutPcsQty, OutSecQty, MrbPcsQty, MrbSecQty, CompPcsQty, CompSecQty, SubQty, SubUnit, StartDate, EndDate, ItemWorker, EpibolePcsQty, EpiboleSecQty, LastWp, Spec<PERSON><PERSON>, SpecPack<PERSON>son, WorkParam, <PERSON>er, <PERSON>reateDate, <PERSON>difyDate, StartPlan, Inspid, InspUid, InspResult, DisableIn, DisableOut, InWorker, OutWorker, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Hi_WipNoteItem
        where Hi_WipNoteItem.id = #{key} and Hi_WipNoteItem.Tenantid=#{tid}
    </select>
    <sql id="selectHiWipnoteitemVo">
         select
          id, Pid, Wpid, WpCode, WpName, RowNum, PlanDate, Remark, InPcsQty, InSecQty, OutPcsQty, OutSecQty, MrbPcsQty, MrbSecQty, CompPcsQty, CompSecQty, SubQty, SubUnit, StartDate, EndDate, ItemWorker, EpibolePcsQty, EpiboleSecQty, LastWp, SpecJson, SpecPackJson, WorkParam, Lister, CreateDate, ModifyDate, StartPlan, Inspid, InspUid, InspResult, DisableIn, DisableOut, InWorker, OutWorker, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Hi_WipNoteItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.HiWipnoteitemPojo">
        <include refid="selectHiWipnoteitemVo"/>
         where 1 = 1 and Hi_WipNoteItem.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Hi_WipNoteItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Hi_WipNoteItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
   and Hi_WipNoteItem.wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null and SearchPojo.wpcode != ''">
   and Hi_WipNoteItem.wpcode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null and SearchPojo.wpname != ''">
   and Hi_WipNoteItem.wpname like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Hi_WipNoteItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
   and Hi_WipNoteItem.subunit like concat('%', #{SearchPojo.subunit}, '%')
</if>
<if test="SearchPojo.itemworker != null and SearchPojo.itemworker != ''">
   and Hi_WipNoteItem.itemworker like concat('%', #{SearchPojo.itemworker}, '%')
</if>
<if test="SearchPojo.specjson != null and SearchPojo.specjson != ''">
   and Hi_WipNoteItem.specjson like concat('%', #{SearchPojo.specjson}, '%')
</if>
<if test="SearchPojo.specpackjson != null and SearchPojo.specpackjson != ''">
   and Hi_WipNoteItem.specpackjson like concat('%', #{SearchPojo.specpackjson}, '%')
</if>
<if test="SearchPojo.workparam != null and SearchPojo.workparam != ''">
   and Hi_WipNoteItem.workparam like concat('%', #{SearchPojo.workparam}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   and Hi_WipNoteItem.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.inspid != null and SearchPojo.inspid != ''">
   and Hi_WipNoteItem.inspid like concat('%', #{SearchPojo.inspid}, '%')
</if>
<if test="SearchPojo.inspuid != null and SearchPojo.inspuid != ''">
   and Hi_WipNoteItem.inspuid like concat('%', #{SearchPojo.inspuid}, '%')
</if>
<if test="SearchPojo.inworker != null and SearchPojo.inworker != ''">
   and Hi_WipNoteItem.inworker like concat('%', #{SearchPojo.inworker}, '%')
</if>
<if test="SearchPojo.outworker != null and SearchPojo.outworker != ''">
   and Hi_WipNoteItem.outworker like concat('%', #{SearchPojo.outworker}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Hi_WipNoteItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Hi_WipNoteItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Hi_WipNoteItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Hi_WipNoteItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Hi_WipNoteItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Hi_WipNoteItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Hi_WipNoteItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Hi_WipNoteItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Hi_WipNoteItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Hi_WipNoteItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Hi_WipNoteItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
   or Hi_WipNoteItem.Wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null and SearchPojo.wpcode != ''">
   or Hi_WipNoteItem.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null and SearchPojo.wpname != ''">
   or Hi_WipNoteItem.WpName like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Hi_WipNoteItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
   or Hi_WipNoteItem.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
</if>
<if test="SearchPojo.itemworker != null and SearchPojo.itemworker != ''">
   or Hi_WipNoteItem.ItemWorker like concat('%', #{SearchPojo.itemworker}, '%')
</if>
<if test="SearchPojo.specjson != null and SearchPojo.specjson != ''">
   or Hi_WipNoteItem.SpecJson like concat('%', #{SearchPojo.specjson}, '%')
</if>
<if test="SearchPojo.specpackjson != null and SearchPojo.specpackjson != ''">
   or Hi_WipNoteItem.SpecPackJson like concat('%', #{SearchPojo.specpackjson}, '%')
</if>
<if test="SearchPojo.workparam != null and SearchPojo.workparam != ''">
   or Hi_WipNoteItem.WorkParam like concat('%', #{SearchPojo.workparam}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Hi_WipNoteItem.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.inspid != null and SearchPojo.inspid != ''">
   or Hi_WipNoteItem.Inspid like concat('%', #{SearchPojo.inspid}, '%')
</if>
<if test="SearchPojo.inspuid != null and SearchPojo.inspuid != ''">
   or Hi_WipNoteItem.InspUid like concat('%', #{SearchPojo.inspuid}, '%')
</if>
<if test="SearchPojo.inworker != null and SearchPojo.inworker != ''">
   or Hi_WipNoteItem.InWorker like concat('%', #{SearchPojo.inworker}, '%')
</if>
<if test="SearchPojo.outworker != null and SearchPojo.outworker != ''">
   or Hi_WipNoteItem.OutWorker like concat('%', #{SearchPojo.outworker}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Hi_WipNoteItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Hi_WipNoteItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Hi_WipNoteItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Hi_WipNoteItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Hi_WipNoteItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Hi_WipNoteItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Hi_WipNoteItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Hi_WipNoteItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Hi_WipNoteItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Hi_WipNoteItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.HiWipnoteitemPojo">
        select
          id, Pid, Wpid, WpCode, WpName, RowNum, PlanDate, Remark, InPcsQty, InSecQty, OutPcsQty, OutSecQty, MrbPcsQty, MrbSecQty, CompPcsQty, CompSecQty, SubQty, SubUnit, StartDate, EndDate, ItemWorker, EpibolePcsQty, EpiboleSecQty, LastWp, SpecJson, SpecPackJson, WorkParam, Lister, CreateDate, ModifyDate, StartPlan, Inspid, InspUid, InspResult, DisableIn, DisableOut, InWorker, OutWorker, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Hi_WipNoteItem
        where Hi_WipNoteItem.Pid = #{Pid} and Hi_WipNoteItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Hi_WipNoteItem(id, Pid, Wpid, WpCode, WpName, RowNum, PlanDate, Remark, InPcsQty, InSecQty, OutPcsQty, OutSecQty, MrbPcsQty, MrbSecQty, CompPcsQty, CompSecQty, SubQty, SubUnit, StartDate, EndDate, ItemWorker, EpibolePcsQty, EpiboleSecQty, LastWp, SpecJson, SpecPackJson, WorkParam, Lister, CreateDate, ModifyDate, StartPlan, Inspid, InspUid, InspResult, DisableIn, DisableOut, InWorker, OutWorker, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{wpid}, #{wpcode}, #{wpname}, #{rownum}, #{plandate}, #{remark}, #{inpcsqty}, #{insecqty}, #{outpcsqty}, #{outsecqty}, #{mrbpcsqty}, #{mrbsecqty}, #{comppcsqty}, #{compsecqty}, #{subqty}, #{subunit}, #{startdate}, #{enddate}, #{itemworker}, #{epibolepcsqty}, #{epibolesecqty}, #{lastwp}, #{specjson}, #{specpackjson}, #{workparam}, #{lister}, #{createdate}, #{modifydate}, #{startplan}, #{inspid}, #{inspuid}, #{inspresult}, #{disablein}, #{disableout}, #{inworker}, #{outworker}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Hi_WipNoteItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="wpid != null ">
                Wpid = #{wpid},
            </if>
            <if test="wpcode != null ">
                WpCode = #{wpcode},
            </if>
            <if test="wpname != null ">
                WpName = #{wpname},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="inpcsqty != null">
                InPcsQty = #{inpcsqty},
            </if>
            <if test="insecqty != null">
                InSecQty = #{insecqty},
            </if>
            <if test="outpcsqty != null">
                OutPcsQty = #{outpcsqty},
            </if>
            <if test="outsecqty != null">
                OutSecQty = #{outsecqty},
            </if>
            <if test="mrbpcsqty != null">
                MrbPcsQty = #{mrbpcsqty},
            </if>
            <if test="mrbsecqty != null">
                MrbSecQty = #{mrbsecqty},
            </if>
            <if test="comppcsqty != null">
                CompPcsQty = #{comppcsqty},
            </if>
            <if test="compsecqty != null">
                CompSecQty = #{compsecqty},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="subunit != null ">
                SubUnit = #{subunit},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="enddate != null">
                EndDate = #{enddate},
            </if>
            <if test="itemworker != null ">
                ItemWorker = #{itemworker},
            </if>
            <if test="epibolepcsqty != null">
                EpibolePcsQty = #{epibolepcsqty},
            </if>
            <if test="epibolesecqty != null">
                EpiboleSecQty = #{epibolesecqty},
            </if>
            <if test="lastwp != null">
                LastWp = #{lastwp},
            </if>
            <if test="specjson != null ">
                SpecJson = #{specjson},
            </if>
            <if test="specpackjson != null ">
                SpecPackJson = #{specpackjson},
            </if>
            <if test="workparam != null ">
                WorkParam = #{workparam},
            </if>
            <if test="lister != null ">
                Lister = #{lister},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="startplan != null">
                StartPlan = #{startplan},
            </if>
            <if test="inspid != null ">
                Inspid = #{inspid},
            </if>
            <if test="inspuid != null ">
                InspUid = #{inspuid},
            </if>
            <if test="inspresult != null">
                InspResult = #{inspresult},
            </if>
            <if test="disablein != null">
                DisableIn = #{disablein},
            </if>
            <if test="disableout != null">
                DisableOut = #{disableout},
            </if>
            <if test="inworker != null ">
                InWorker = #{inworker},
            </if>
            <if test="outworker != null ">
                OutWorker = #{outworker},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Hi_WipNoteItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

