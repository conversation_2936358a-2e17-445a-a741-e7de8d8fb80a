<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSubcontractMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSubcontractPojo">
        <include refid="selectbillVo"/>
        where Wk_Subcontract.id = #{key} and Wk_Subcontract.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Wk_Subcontract.id,
               Wk_Subcontract.RefNo,
               Wk_Subcontract.BillType,
               Wk_Subcontract.BillDate,
               Wk_Subcontract.BillTitle,
               Wk_Subcontract.Taxrate,
               Wk_Subcontract.Operator,
               Wk_Subcontract.Groupid,
               Wk_Subcontract.Summary,
               Wk_Subcontract.CreateBy,
               Wk_Subcontract.CreateByid,
               Wk_Subcontract.CreateDate,
               Wk_Subcontract.Lister,
               Wk_Subcontract.Listerid,
               Wk_Subcontract.ModifyDate,
               Wk_Subcontract.Assessor,
               Wk_Subcontract.Assessorid,
               Wk_Subcontract.AssessDate,
               Wk_Subcontract.BillStateCode,
               Wk_Subcontract.BillStateDate,
               Wk_Subcontract.BillStartDate,
               Wk_Subcontract.BillPlanDate,
               Wk_Subcontract.BillTaxAmount,
               Wk_Subcontract.BillTaxTotal,
               Wk_Subcontract.BillAmount,
               Wk_Subcontract.ItemCount,
               Wk_Subcontract.DisannulCount,
               Wk_Subcontract.FinishCount,
               Wk_Subcontract.PrintCount,
               Wk_Subcontract.OaFlowMark,
               Wk_Subcontract.Prepayments,
               Wk_Subcontract.Payment,
               Wk_Subcontract.Arrivaladd,
               Wk_Subcontract.Custom1,
               Wk_Subcontract.Custom2,
               Wk_Subcontract.Custom3,
               Wk_Subcontract.Custom4,
               Wk_Subcontract.Custom5,
               Wk_Subcontract.Custom6,
               Wk_Subcontract.Custom7,
               Wk_Subcontract.Custom8,
               Wk_Subcontract.Custom9,
               Wk_Subcontract.Custom10,
               Wk_Subcontract.Tenantid,
               Wk_Subcontract.Revision
        FROM App_Workgroup
                 RIGHT JOIN Wk_Subcontract ON Wk_Subcontract.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT Wk_SubcontractItem.id,
               Wk_SubcontractItem.Pid,
               Wk_SubcontractItem.Goodsid,
               Wk_SubcontractItem.SubItemid,
               Wk_SubcontractItem.SubUse,
               Wk_SubcontractItem.SubUnit,
               Wk_SubcontractItem.SubQty,
               Wk_SubcontractItem.TaxPrice,
               Wk_SubcontractItem.TaxAmount,
               Wk_SubcontractItem.Price,
               Wk_SubcontractItem.Amount,
               Wk_SubcontractItem.TaxTotal,
               Wk_SubcontractItem.ItemTaxrate,
               Wk_SubcontractItem.StartDate,
               Wk_SubcontractItem.PlanDate,
               Wk_SubcontractItem.Quantity,
               Wk_SubcontractItem.FinishQty,
               Wk_SubcontractItem.MrbQty,
               Wk_SubcontractItem.InStorage,
               Wk_SubcontractItem.EnabledMark,
               Wk_SubcontractItem.Closed,
               Wk_SubcontractItem.Remark,
               Wk_SubcontractItem.StateCode,
               Wk_SubcontractItem.StateDate,
               Wk_SubcontractItem.RowNum,
               Wk_SubcontractItem.MachUid,
               Wk_SubcontractItem.MachItemid,
               Wk_SubcontractItem.MachGroupid,
               Wk_SubcontractItem.Customer,
               Wk_SubcontractItem.CustPO,
               Wk_SubcontractItem.MainPlanUid,
               Wk_SubcontractItem.MainPlanItemid,
               Wk_SubcontractItem.MrpUid,
               Wk_SubcontractItem.MrpItemid,
               Wk_SubcontractItem.CiteUid,
               Wk_SubcontractItem.CiteItemid,
               Wk_SubcontractItem.DisannulListerid,
               Wk_SubcontractItem.DisannulLister,
               Wk_SubcontractItem.DisannulDate,
               Wk_SubcontractItem.DisannulMark,
               Wk_SubcontractItem.AttributeJson,
               Wk_SubcontractItem.SourceType,
               Wk_SubcontractItem.SubByPcs,
               Wk_SubcontractItem.Custom1,
               Wk_SubcontractItem.Custom2,
               Wk_SubcontractItem.Custom3,
               Wk_SubcontractItem.Custom4,
               Wk_SubcontractItem.Custom5,
               Wk_SubcontractItem.Custom6,
               Wk_SubcontractItem.Custom7,
               Wk_SubcontractItem.Custom8,
               Wk_SubcontractItem.Custom9,
               Wk_SubcontractItem.Custom10,
               Wk_SubcontractItem.Tenantid,
               Wk_SubcontractItem.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_Subcontract.RefNo,
               Wk_Subcontract.BillType,
               Wk_Subcontract.BillDate,
               Wk_Subcontract.BillTitle,
               Wk_Subcontract.Taxrate,
               Wk_Subcontract.Operator,
               Wk_Subcontract.CreateBy,
               Wk_Subcontract.Lister,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Wk_SubcontractItem
                 LEFT JOIN Wk_Subcontract ON Wk_SubcontractItem.Pid = Wk_Subcontract.id
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Wk_Subcontract.Groupid
                 LEFT JOIN Mat_Goods ON Wk_SubcontractItem.Goodsid = Mat_Goods.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSubcontractitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_Subcontract.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_Subcontract.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Wk_Subcontract.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_Subcontract.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_Subcontract.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_Subcontract.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Wk_Subcontract.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_Subcontract.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_Subcontract.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_Subcontract.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_Subcontract.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_Subcontract.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_Subcontract.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_Subcontract.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Wk_Subcontract.billstatecode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_Subcontract.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_Subcontract.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_Subcontract.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_Subcontract.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_Subcontract.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_Subcontract.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_Subcontract.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_Subcontract.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_Subcontract.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_Subcontract.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_Subcontract.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_SubcontractItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_SubcontractItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Wk_SubcontractItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Wk_SubcontractItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_SubcontractItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_SubcontractItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_SubcontractItem.goodsid= #{SearchPojo.goodsid}
        </if>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_Subcontract.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_Subcontract.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_Subcontract.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_Subcontract.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Wk_Subcontract.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_Subcontract.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_Subcontract.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_Subcontract.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_Subcontract.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_Subcontract.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_Subcontract.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_Subcontract.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Wk_Subcontract.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_Subcontract.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_Subcontract.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_Subcontract.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_Subcontract.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_Subcontract.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_Subcontract.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_Subcontract.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_Subcontract.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_Subcontract.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_Subcontract.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_Subcontract.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_SubcontractItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_SubcontractItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_SubcontractItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_SubcontractItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Wk_SubcontractItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Wk_SubcontractItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_SubcontractItem.goodsid= #{SearchPojo.goodsid}
            </if>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSubcontractPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_Subcontract.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_Subcontract.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Wk_Subcontract.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_Subcontract.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_Subcontract.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_Subcontract.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Wk_Subcontract.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_Subcontract.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_Subcontract.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_Subcontract.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_Subcontract.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_Subcontract.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_Subcontract.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_Subcontract.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Wk_Subcontract.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_Subcontract.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_Subcontract.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_Subcontract.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_Subcontract.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_Subcontract.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_Subcontract.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_Subcontract.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_Subcontract.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_Subcontract.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_Subcontract.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_Subcontract.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_Subcontract.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_Subcontract.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_Subcontract.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_Subcontract.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Wk_Subcontract.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_Subcontract.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_Subcontract.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_Subcontract.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_Subcontract.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_Subcontract.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_Subcontract.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_Subcontract.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Wk_Subcontract.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_Subcontract.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_Subcontract.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_Subcontract.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_Subcontract.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_Subcontract.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_Subcontract.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_Subcontract.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_Subcontract.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_Subcontract.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_Subcontract.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_Subcontract.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_Subcontract(id, RefNo, BillType, BillDate, BillTitle, Taxrate, Operator, Groupid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, BillStateCode, BillStateDate, BillStartDate, BillPlanDate, BillTaxAmount, BillTaxTotal, BillAmount, ItemCount, DisannulCount, FinishCount, PrintCount, OaFlowMark, Prepayments, Custom1, Arrivaladd, Payment, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{taxrate}, #{operator}, #{groupid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{billstatecode}, #{billstatedate}, #{billstartdate}, #{billplandate}, #{billtaxamount}, #{billtaxtotal}, #{billamount}, #{itemcount}, #{disannulcount}, #{finishcount}, #{printcount}, #{oaflowmark}, #{prepayments}, #{custom1}, #{arrivaladd}, #{payment}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_Subcontract
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billstatecode != null ">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billstartdate != null">
                BillStartDate =#{billstartdate},
            </if>
            <if test="billplandate != null">
                BillPlanDate =#{billplandate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="prepayments != null">
            Prepayments =#{prepayments},
        </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="arrivaladd != null ">
            Arrivaladd =#{arrivaladd},
        </if>
            <if test="payment != null ">
            Payment =#{payment},
        </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_Subcontract
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_Subcontract
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--刷新打印次数-->
    <update id="updatePrintcount">
        update Wk_Subcontract
        SET PrintCount = #{printcount}
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateDisannulCount">
        update Wk_Subcontract
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Wk_SubcontractItem
                                     where Wk_SubcontractItem.Pid = #{key}
                                       and Wk_SubcontractItem.Tenantid = #{tid}
                                       and Wk_SubcontractItem.DisannulMark = 1), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成行数 Eric 20211213-->
    <update id="updateFinishCount">
        update Wk_Subcontract
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_SubcontractItem
                                   where Wk_SubcontractItem.Pid = #{key}
                                     and Wk_SubcontractItem.Tenantid = #{tid}
                                     and (Wk_SubcontractItem.Closed = 1
                                       or Wk_SubcontractItem.FinishQty >= Wk_SubcontractItem.Quantity)), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkSubcontractPojo">
        select
        id
        from Wk_SubcontractItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--查询DelListIds-->
    <select id="getDelMatIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkSubcontractPojo">
        select
        id
        from Wk_SubcontractMat
        where Pid = #{id}
        <if test="mat !=null and mat.size()>0">
            and id not in
            <foreach collection="mat" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateMrpScFinish">
        update Wk_MrpItem
        SET WkScQty =COALESCE((SELECT SUM(Wk_SubcontractItem.quantity)
                               FROM Wk_SubcontractItem
                                        LEFT OUTER JOIN Wk_Subcontract
                                                        ON Wk_SubcontractItem.pid = Wk_Subcontract.id
                               where Wk_Subcontract.BillType = 'MRP需求'
                                 and Wk_SubcontractItem.MrpUid = #{refno}
                                 and Wk_SubcontractItem.MrpItemid = #{key}
                                 and Wk_SubcontractItem.DisannulMark = 0
                                 and Wk_SubcontractItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--  查询委制单据是否被引用  -->
    <select id="getItemCiteBillName" resultType="java.lang.String">
        (SELECT '领料单' as billname From Mat_RequisitionItem where (WorkItemid = #{key} or CiteItemid = #{key}) and Tenantid = #{tid} LIMIT 1)
    </select>

</mapper>

