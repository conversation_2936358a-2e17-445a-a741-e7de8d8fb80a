<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.manu_ExtFilterMapper">
    <!-- 定义查询货品字段的SQL片段 -->
    <sql id="selectGoods">
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Partid,
        Mat_Goods.surface,
        Mat_Goods.drawing,
        Mat_Goods.brandname,
        Mat_Goods.Material as GoodsMaterial,
        Mat_Goods.PcsX,
        Mat_Goods.PcsY,
        Mat_Goods.SetX,
        Mat_Goods.SetY,
        Mat_Goods.Set2Pcs,
        Mat_Goods.PnlX,
        Mat_Goods.Pnly,
        Mat_Goods.Pnl2Pcs,
        Mat_Goods.<PERSON><PERSON>,
        Mat_Goods.CustJson,
    </sql>
    <sql id="selectGoodsNoComma">
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Partid,
        Mat_Goods.surface,
        Mat_Goods.drawing,
        Mat_Goods.brandname,
        Mat_Goods.Material as GoodsMaterial,
        Mat_Goods.PcsX,
        Mat_Goods.PcsY,
        Mat_Goods.SetX,
        Mat_Goods.SetY,
        Mat_Goods.Set2Pcs,
        Mat_Goods.PnlX,
        Mat_Goods.Pnly,
        Mat_Goods.Pnl2Pcs,
        Mat_Goods.Storeid,
        Mat_Goods.CustJson
    </sql>
    <!--货品And过滤-->
    <sql id="goodsandfilter">
        <!--货品查询 -->
        <if test="SearchPojo.goodsuid != null and SearchPojo.goodsuid != ''">
            and Mat_Goods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
        </if>
        <if test="SearchPojo.goodsname != null and SearchPojo.goodsname != ''">
            and Mat_Goods.GoodsName like concat('%',
                #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null and SearchPojo.goodsspec != ''">
            and Mat_Goods.GoodsSpec like concat('%',
                #{SearchPojo.goodsspec}, '%')
        </if>
        <if test="SearchPojo.partid != null and SearchPojo.partid != ''">
            and Mat_Goods.Partid like concat('%',
                #{SearchPojo.partid}, '%')
        </if>
        <if test="SearchPojo.goodsmaterial != null and SearchPojo.goodsmaterial != ''">
            and Mat_Goods.Material like concat('%',
                #{SearchPojo.goodsmaterial}, '%')
        </if>
    </sql>
    <!--货品Or过滤-->
    <sql id="goodsorfilter">
        <!--货品查询 -->
        <if test="SearchPojo.goodsuid != null and SearchPojo.goodsuid != ''">
            or Mat_Goods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
        </if>
        <if test="SearchPojo.goodsname != null and SearchPojo.goodsname != ''">
            or Mat_Goods.GoodsName like concat('%',
                #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null and SearchPojo.goodsspec != ''">
            or Mat_Goods.GoodsSpec like concat('%',
                #{SearchPojo.goodsspec}, '%')
        </if>
        <if test="SearchPojo.partid != null and SearchPojo.partid != ''">
            or Mat_Goods.Partid like concat('%',
                #{SearchPojo.partid}, '%')
        </if>
        <if test="SearchPojo.goodsmaterial != null and SearchPojo.goodsmaterial != ''">
            or Mat_Goods.Material like concat('%',
                #{SearchPojo.goodsmaterial}, '%')
        </if>
    </sql>
    <!--往来单位And过滤-->
    <sql id="workgroupandfilter">
        <if test="SearchPojo.groupuid != null and SearchPojo.groupuid != ''">
            and App_Workgroup.GroupUid = #{SearchPojo.groupuid}
        </if>
        <if test="SearchPojo.groupname != null and SearchPojo.groupname != ''">
            and App_Workgroup.GroupName like concat('%',
                #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.grouplevel != null and SearchPojo.grouplevel != ''">
            and App_Workgroup.GroupLevel =
                #{SearchPojo.grouplevel}
        </if>
        <if test="SearchPojo.abbreviate != null and SearchPojo.abbreviate != ''">
            and App_Workgroup.Abbreviate = #{SearchPojo.abbreviate}
        </if>
    </sql>
    <!--往来单位Or过滤-->
    <sql id="workgrouporfilter">
        <if test="SearchPojo.groupuid != null and SearchPojo.groupuid != ''">
            or App_Workgroup.GroupUid = #{SearchPojo.groupuid}
        </if>
        <if test="SearchPojo.groupname != null and SearchPojo.groupname != ''">
            or App_Workgroup.GroupName like concat('%',
                #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.grouplevel != null and SearchPojo.grouplevel != ''">
            or App_Workgroup.GroupLevel =
                #{SearchPojo.groupname}
        </if>
        <if test="SearchPojo.abbreviate != null and SearchPojo.abbreviate != ''">
            or App_Workgroup.Abbreviate = #{SearchPojo.abbreviate}
        </if>
    </sql>
</mapper>

