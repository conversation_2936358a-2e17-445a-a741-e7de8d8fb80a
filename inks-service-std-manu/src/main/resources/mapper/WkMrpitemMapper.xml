<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkMrpitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkMrpitemPojo">
        <include refid="selectWkMrpitemVo"/>
        where Wk_MrpItem.id = #{key}
          and Wk_MrpItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkMrpitemVo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            App_Workgroup.GroupName as goodsgroupname,
            Wk_MrpItem.id,
            Wk_MrpItem.Pid,
            Wk_MrpItem.ItemParentid,
            Wk_MrpItem.MrpObjid,
            Wk_MrpItem.LevelNum,
            Wk_MrpItem.LevelSymbol,
            Wk_MrpItem.Goodsid,
            Wk_MrpItem.ItemCode,
            Wk_MrpItem.ItemName,
            Wk_MrpItem.ItemSpec,
            Wk_MrpItem.ItemUnit,
            Wk_MrpItem.Bomid,
            Wk_MrpItem.BomType,
            Wk_MrpItem.BomItemid,
            Wk_MrpItem.SubQty,
            Wk_MrpItem.MainQty,
            Wk_MrpItem.LossRate,
            Wk_MrpItem.AttrCode,
            Wk_MrpItem.FlowCode,
            Wk_MrpItem.BomQty,
            Wk_MrpItem.StoQty,
            Wk_MrpItem.SafeStock,
            Wk_MrpItem.NeedQty,
            Wk_MrpItem.RealQty,
            Wk_MrpItem.WorkDate,
            Wk_MrpItem.PlanDate,
            Wk_MrpItem.Remark,
            Wk_MrpItem.EnabledMark,
            Wk_MrpItem.Closed,
            Wk_MrpItem.RowNum,
            Wk_MrpItem.BuyPlanQty,
            Wk_MrpItem.BuyOrderQty,
            Wk_MrpItem.CustSuppQty,
            Wk_MrpItem.WkWsQty,
            Wk_MrpItem.WkScQty,
            Wk_MrpItem.OtherQty,
            Wk_MrpItem.MatReqQty,
            Wk_MrpItem.MatCompQty,
            Wk_MrpItem.MatIvQty,
            Wk_MrpItem.BuyRemQty,
            Wk_MrpItem.WkWsRemQty,
            Wk_MrpItem.WkScRemQty,
            Wk_MrpItem.BusRemQty,
            Wk_MrpItem.MrpRemQty,
            Wk_MrpItem.FreeReqRemQty,
            Wk_MrpItem.ReqRemQty,
            Wk_MrpItem.Groupid,
            Wk_MrpItem.GroupName,
            Wk_MrpItem.MatReqRtQty,
            Wk_MrpItem.MatCompRtQty,
            Wk_MrpItem.WkFinishQty,
            Wk_MrpItem.BuyFinishQty,
            Wk_MrpItem.CustFinishQty,
            Wk_MrpItem.MachUid,
            Wk_MrpItem.MachItemid,
            Wk_MrpItem.MachBatch,
            Wk_MrpItem.MachGroupid,
            Wk_MrpItem.MainPlanUid,
            Wk_MrpItem.MainPlanItemid,
            Wk_MrpItem.BomRound,
            Wk_MrpItem.BomDate,
            Wk_MrpItem.BomMark,
            Wk_MrpItem.AttributeJson,
            Wk_MrpItem.GrossQty,
            Wk_MrpItem.MergeMark,
            Wk_MrpItem.MatType,
            Wk_MrpItem.Custom1,
            Wk_MrpItem.Custom2,
            Wk_MrpItem.Custom3,
            Wk_MrpItem.Custom4,
            Wk_MrpItem.Custom5,
            Wk_MrpItem.Custom6,
            Wk_MrpItem.Custom7,
            Wk_MrpItem.Custom8,
            Wk_MrpItem.Custom9,
            Wk_MrpItem.Custom10,
            Wk_MrpItem.Tenantid,
            Wk_MrpItem.TenantName,
            Wk_MrpItem.Revision,
            Bus_Machining.BillType as machtype
        FROM
            Mat_Goods
                RIGHT JOIN Wk_MrpItem ON Wk_MrpItem.Goodsid = Mat_Goods.id
                LEFT JOIN Bus_MachiningItem ON Wk_MrpItem.MachItemid= Bus_MachiningItem.id
                LEFT JOIN Bus_Machining ON Bus_MachiningItem.Pid = Bus_Machining.id
                LEFT JOIN App_Workgroup ON Mat_Goods.Groupid = App_Workgroup.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkMrpitemPojo">
        <include refid="selectWkMrpitemVo"/>
        where 1 = 1 and Wk_MrpItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_MrpItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_MrpItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.itemparentid != null and SearchPojo.itemparentid != ''">
            and Wk_MrpItem.itemparentid like concat('%', #{SearchPojo.itemparentid}, '%')
        </if>
        <if test="SearchPojo.mrpobjid != null and SearchPojo.mrpobjid != ''">
            and Wk_MrpItem.mrpobjid like concat('%', #{SearchPojo.mrpobjid}, '%')
        </if>
        <if test="SearchPojo.levelsymbol != null and SearchPojo.levelsymbol != ''">
            and Wk_MrpItem.levelsymbol like concat('%', #{SearchPojo.levelsymbol}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_MrpItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_MrpItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_MrpItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_MrpItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_MrpItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
            and Wk_MrpItem.bomid like concat('%', #{SearchPojo.bomid}, '%')
        </if>
        <if test="SearchPojo.bomtype != null and SearchPojo.bomtype != ''">
            and Wk_MrpItem.bomtype like concat('%', #{SearchPojo.bomtype}, '%')
        </if>
        <if test="SearchPojo.bomitemid != null and SearchPojo.bomitemid != ''">
            and Wk_MrpItem.bomitemid like concat('%', #{SearchPojo.bomitemid}, '%')
        </if>
        <if test="SearchPojo.attrcode != null and SearchPojo.attrcode != ''">
            and Wk_MrpItem.attrcode like concat('%', #{SearchPojo.attrcode}, '%')
        </if>
        <if test="SearchPojo.flowcode != null and SearchPojo.flowcode != ''">
            and Wk_MrpItem.flowcode like concat('%', #{SearchPojo.flowcode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_MrpItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Wk_MrpItem.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.groupname != null and SearchPojo.groupname != ''">
            and Wk_MrpItem.groupname like concat('%', #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_MrpItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_MrpItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_MrpItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_MrpItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_MrpItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_MrpItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_MrpItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_MrpItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_MrpItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_MrpItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_MrpItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_MrpItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_MrpItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_MrpItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_MrpItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and Wk_MrpItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_MrpItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.itemparentid != null and SearchPojo.itemparentid != ''">
                or Wk_MrpItem.ItemParentid like concat('%', #{SearchPojo.itemparentid}, '%')
            </if>
            <if test="SearchPojo.mrpobjid != null and SearchPojo.mrpobjid != ''">
                or Wk_MrpItem.MrpObjid like concat('%', #{SearchPojo.mrpobjid}, '%')
            </if>
            <if test="SearchPojo.levelsymbol != null and SearchPojo.levelsymbol != ''">
                or Wk_MrpItem.LevelSymbol like concat('%', #{SearchPojo.levelsymbol}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_MrpItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_MrpItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_MrpItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_MrpItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_MrpItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
                or Wk_MrpItem.Bomid like concat('%', #{SearchPojo.bomid}, '%')
            </if>
            <if test="SearchPojo.bomtype != null and SearchPojo.bomtype != ''">
                or Wk_MrpItem.BomType like concat('%', #{SearchPojo.bomtype}, '%')
            </if>
            <if test="SearchPojo.bomitemid != null and SearchPojo.bomitemid != ''">
                or Wk_MrpItem.BomItemid like concat('%', #{SearchPojo.bomitemid}, '%')
            </if>
            <if test="SearchPojo.attrcode != null and SearchPojo.attrcode != ''">
                or Wk_MrpItem.AttrCode like concat('%', #{SearchPojo.attrcode}, '%')
            </if>
            <if test="SearchPojo.flowcode != null and SearchPojo.flowcode != ''">
                or Wk_MrpItem.FlowCode like concat('%', #{SearchPojo.flowcode}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_MrpItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Wk_MrpItem.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.groupname != null and SearchPojo.groupname != ''">
                or Wk_MrpItem.GroupName like concat('%', #{SearchPojo.groupname}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_MrpItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_MrpItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_MrpItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_MrpItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_MrpItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_MrpItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_MrpItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_MrpItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_MrpItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_MrpItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_MrpItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_MrpItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_MrpItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_MrpItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_MrpItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
                or Wk_MrpItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>




    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkMrpitemPojo">
        <include refid="selectWkMrpitemVo"/>
        where Wk_MrpItem.Pid = #{Pid}
          and Wk_MrpItem.Tenantid = #{tid}
        order by RowNum
    </select>



    <select id="getListByMrpObjId" resultType="inks.service.std.manu.domain.pojo.WkMrpitemPojo">
        <include refid="selectWkMrpitemVo"/>
        where Wk_MrpItem.MrpObjid = #{mrpobjid}
        and Wk_MrpItem.Tenantid = #{tid}
        order by RowNum
    </select>


    <select id="getListByMrpObjIdNoneBomid" resultType="inks.service.std.manu.domain.pojo.vo.WkMrpitemAndPatentVO">
        <include refid="selectWkMrpitemVo"/>
        where Wk_MrpItem.MrpObjid
            in
        <foreach collection="mrpobjids" item="mrpobjid" open="(" separator="," close=")">
        #{mrpobjid}
        </foreach>
        and Wk_MrpItem.Tenantid = #{tid}
        and Wk_MrpItem.Bomid =''
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_MrpItem(id, Pid, ItemParentid, MrpObjid, LevelNum, LevelSymbol, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Bomid, BomType, BomItemid, SubQty, MainQty, LossRate, AttrCode, FlowCode, BomQty, StoQty, SafeStock, NeedQty, RealQty, WorkDate, PlanDate, Remark, EnabledMark, Closed, RowNum, BuyPlanQty, BuyOrderQty, CustSuppQty, WkWsQty, WkScQty, OtherQty, MatReqQty, MatCompQty, MatIvQty, BuyRemQty, WkWsRemQty, WkScRemQty, BusRemQty, MrpRemQty, FreeReqRemQty, ReqRemQty, Groupid, GroupName, MatReqRtQty, MatCompRtQty, WkFinishQty, BuyFinishQty, CustFinishQty, MachUid, MachItemid, MachBatch, MachGroupid, MainPlanUid, MainPlanItemid, BomRound, BomDate, BomMark, AttributeJson, GrossQty, MergeMark, MatType, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{itemparentid}, #{mrpobjid}, #{levelnum}, #{levelsymbol}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{bomid}, #{bomtype}, #{bomitemid}, #{subqty}, #{mainqty}, #{lossrate}, #{attrcode}, #{flowcode}, #{bomqty}, #{stoqty}, #{safestock}, #{needqty}, #{realqty}, #{workdate}, #{plandate}, #{remark}, #{enabledmark}, #{closed}, #{rownum}, #{buyplanqty}, #{buyorderqty}, #{custsuppqty}, #{wkwsqty}, #{wkscqty}, #{otherqty}, #{matreqqty}, #{matcompqty}, #{mativqty}, #{buyremqty}, #{wkwsremqty}, #{wkscremqty}, #{busremqty}, #{mrpremqty}, #{freereqremqty}, #{reqremqty}, #{groupid}, #{groupname}, #{matreqrtqty}, #{matcomprtqty}, #{wkfinishqty}, #{buyfinishqty}, #{custfinishqty}, #{machuid}, #{machitemid}, #{machbatch}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid}, #{bomround}, #{bomdate}, #{bommark}, #{attributejson}, #{grossqty}, #{mergemark}, #{mattype}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_MrpItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemparentid != null ">
                ItemParentid = #{itemparentid},
            </if>
            <if test="mrpobjid != null ">
                MrpObjid = #{mrpobjid},
            </if>
            <if test="levelnum != null">
                LevelNum = #{levelnum},
            </if>
            <if test="levelsymbol != null ">
                LevelSymbol = #{levelsymbol},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="bomid != null ">
                Bomid = #{bomid},
            </if>
            <if test="bomtype != null ">
                BomType = #{bomtype},
            </if>
            <if test="bomitemid != null ">
                BomItemid = #{bomitemid},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="mainqty != null">
                MainQty = #{mainqty},
            </if>
            <if test="lossrate != null">
                LossRate = #{lossrate},
            </if>
            <if test="attrcode != null ">
                AttrCode = #{attrcode},
            </if>
            <if test="flowcode != null ">
                FlowCode = #{flowcode},
            </if>
            <if test="bomqty != null">
                BomQty = #{bomqty},
            </if>
            <if test="stoqty != null">
                StoQty = #{stoqty},
            </if>
            <if test="safestock != null">
                SafeStock = #{safestock},
            </if>
            <if test="needqty != null">
                NeedQty = #{needqty},
            </if>
            <if test="realqty != null">
                RealQty = #{realqty},
            </if>
            <if test="workdate != null">
                WorkDate = #{workdate},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="buyplanqty != null">
                BuyPlanQty = #{buyplanqty},
            </if>
            <if test="buyorderqty != null">
                BuyOrderQty = #{buyorderqty},
            </if>
            <if test="custsuppqty != null">
                CustSuppQty = #{custsuppqty},
            </if>
            <if test="wkwsqty != null">
                WkWsQty = #{wkwsqty},
            </if>
            <if test="wkscqty != null">
                WkScQty = #{wkscqty},
            </if>
            <if test="otherqty != null">
                OtherQty = #{otherqty},
            </if>
            <if test="matreqqty != null">
                MatReqQty = #{matreqqty},
            </if>
            <if test="matcompqty != null">
                MatCompQty = #{matcompqty},
            </if>
            <if test="mativqty != null">
                MatIvQty = #{mativqty},
            </if>
            <if test="buyremqty != null">
                BuyRemQty = #{buyremqty},
            </if>
            <if test="wkwsremqty != null">
                WkWsRemQty = #{wkwsremqty},
            </if>
            <if test="wkscremqty != null">
                WkScRemQty = #{wkscremqty},
            </if>
            <if test="busremqty != null">
                BusRemQty = #{busremqty},
            </if>
            <if test="mrpremqty != null">
                MrpRemQty = #{mrpremqty},
            </if>
            <if test="freereqremqty != null">
                FreeReqRemQty = #{freereqremqty},
            </if>
            <if test="reqremqty != null">
                ReqRemQty = #{reqremqty},
            </if>
            <if test="groupid != null ">
                Groupid = #{groupid},
            </if>
            <if test="groupname != null ">
                GroupName = #{groupname},
            </if>
            <if test="matreqrtqty != null">
                MatReqRtQty = #{matreqrtqty},
            </if>
            <if test="matcomprtqty != null">
                MatCompRtQty = #{matcomprtqty},
            </if>
            <if test="wkfinishqty != null">
                WkFinishQty = #{wkfinishqty},
            </if>
            <if test="buyfinishqty != null">
                BuyFinishQty = #{buyfinishqty},
            </if>
            <if test="custfinishqty != null">
                CustFinishQty = #{custfinishqty},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machbatch != null ">
                MachBatch = #{machbatch},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="bomround != null">
                BomRound = #{bomround},
            </if>
            <if test="bomdate != null">
                BomDate = #{bomdate},
            </if>
            <if test="bommark != null">
                BomMark = #{bommark},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="grossqty != null">
                GrossQty = #{grossqty},
            </if>
            <if test="mergemark != null">
                MergeMark = #{mergemark},
            </if>
            <if test="mattype != null">
                MatType = #{mattype},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_MrpItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <update id="setBomMarkByPid">
        update Wk_MrpItem
        set BomMark = #{bomMark}
        where Pid = #{pid}
          and Tenantid = #{tid}
    </update>

    <update id="updateItemAttrCode">
        update Wk_MrpItem
        set AttrCode = #{attrcode}
        where id
            in
        <foreach collection="itemids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and Tenantid = #{tid}
    </update>

    <select id="getPid" resultType="java.lang.String">
        select Pid
        from Wk_MrpItem
        where id = #{id}
          and Tenantid = #{tid}
    </select>

    <select id="getAttrCode" resultType="java.lang.String">
        select AttrCode
        from Wk_MrpItem
        where id = #{itemid}
          and Tenantid = #{tid}
    </select>

    <select id="checkWk_WorkSheetItem" resultType="java.lang.String">
        select Mat_Goods.GoodsUid
        from Wk_WorksheetItem Left join Mat_Goods on Wk_WorksheetItem.Goodsid = Mat_Goods.id
        where Wk_WorksheetItem.MrpItemid in
        <foreach collection="itemids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
          and Wk_WorksheetItem.Tenantid = #{tid}
    </select>

    <select id="checkLastMrpitem" resultType="int">
        select count(*)
        from Wk_MrpItem
        where Pid = #{key}
          and Tenantid = #{tid}
          and Custom1 = ''
    </select>

    <update id="setCustom1Null">
        update Wk_MrpItem
        set Custom1 = ''
        where Pid = #{pid}
          and Tenantid = #{tid}
    </update>

    <select id="getListBySheetid" resultType="inks.service.std.manu.domain.pojo.WkMrpitemPojo">
        <include refid="selectWkMrpitemVo"/>
        where Wk_MrpItem.ItemParentid in (select MrpItemid from Wk_WorksheetItem where Pid = #{sheetid} and Tenantid = #{tid})
    </select>


    <select id="getMrpItemListByMachItemids" resultType="inks.service.std.manu.domain.pojo.WkMrpitemPojo">
        SELECT Wk_MrpItem.id,
               Wk_MrpItem.Pid,
               Wk_MrpItem.ItemParentid,
               Wk_MrpItem.MrpObjid,
               Wk_MrpItem.LevelNum,
               Wk_MrpItem.LevelSymbol,
               Wk_MrpItem.Goodsid,
               Wk_MrpItem.ItemCode,
               Wk_MrpItem.ItemName,
               Wk_MrpItem.ItemSpec,
               Wk_MrpItem.ItemUnit,
               Wk_MrpItem.Bomid,
               Wk_MrpItem.BomType,
               Wk_MrpItem.BomItemid,
               Wk_MrpItem.SubQty,
               Wk_MrpItem.MainQty,
               Wk_MrpItem.LossRate,
               Wk_MrpItem.AttrCode,
               Wk_MrpItem.FlowCode,
               Wk_MrpItem.BomQty,
               Wk_MrpItem.StoQty,
               Wk_MrpItem.SafeStock,
               Wk_MrpItem.NeedQty,
               Wk_MrpItem.RealQty,
               Wk_MrpItem.WorkDate,
               Wk_MrpItem.PlanDate,
               Wk_MrpItem.Remark,
               Wk_MrpItem.EnabledMark,
               Wk_MrpItem.Closed,
               Wk_MrpItem.RowNum,
               Wk_MrpItem.BuyPlanQty,
               Wk_MrpItem.BuyOrderQty,
               Wk_MrpItem.CustSuppQty,
               Wk_MrpItem.WkWsQty,
               Wk_MrpItem.WkScQty,
               Wk_MrpItem.OtherQty,
               Wk_MrpItem.MatReqQty,
               Wk_MrpItem.MatCompQty,
               Wk_MrpItem.MatIvQty,
               Wk_MrpItem.BuyRemQty,
               Wk_MrpItem.WkWsRemQty,
               Wk_MrpItem.WkScRemQty,
               Wk_MrpItem.BusRemQty,
               Wk_MrpItem.MrpRemQty,
               Wk_MrpItem.FreeReqRemQty,
               Wk_MrpItem.ReqRemQty,
               Wk_MrpItem.Groupid,
               Wk_MrpItem.GroupName,
               Wk_MrpItem.MatReqRtQty,
               Wk_MrpItem.MatCompRtQty,
               Wk_MrpItem.WkFinishQty,
               Wk_MrpItem.BuyFinishQty,
               Wk_MrpItem.CustFinishQty,
               Wk_MrpItem.MachUid,
               Wk_MrpItem.MachItemid,
               Wk_MrpItem.MachBatch,
               Wk_MrpItem.MachGroupid,
               Wk_MrpItem.MainPlanUid,
               Wk_MrpItem.MainPlanItemid,
               Wk_MrpItem.BomRound,
               Wk_MrpItem.BomDate,
               Wk_MrpItem.BomMark,
               Wk_MrpItem.AttributeJson,
               Wk_MrpItem.MatType,
               Wk_MrpItem.Custom1,
               Wk_MrpItem.Custom2,
               Wk_MrpItem.Custom3,
               Wk_MrpItem.Custom4,
               Wk_MrpItem.Custom5,
               Wk_MrpItem.Custom6,
               Wk_MrpItem.Custom7,
               Wk_MrpItem.Custom8,
               Wk_MrpItem.Custom9,
               Wk_MrpItem.Custom10,
               Wk_MrpItem.Tenantid,
               Wk_MrpItem.TenantName,
               Wk_MrpItem.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               Wk_MrpObj.AttributeJson
        FROM Mat_Goods
                 RIGHT JOIN Wk_MrpItem ON Wk_MrpItem.Goodsid = Mat_Goods.id
                 LEFT JOIN Wk_MrpObj ON Wk_MrpItem.MrpObjid = Wk_MrpObj.id
        where Wk_MrpItem.MachItemid in
        <foreach collection="machitems" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and Wk_MrpItem.Tenantid = #{tenantid}
        and AttrCode = '外购'
    </select>

    <select id="getListInObjids" resultType="inks.service.std.manu.domain.pojo.WkMrpitemPojo">
        <include refid="selectWkMrpitemVo"/>
        where Wk_MrpItem.MrpObjid in
        <foreach collection="objids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>

