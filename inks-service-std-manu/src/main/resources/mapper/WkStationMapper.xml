<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkStationMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkStationPojo">
        <include refid="selectbillVo"/>
        where Wk_Station.id = #{key} and Wk_Station.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Wk_Station.id,
               Wk_Station.Sectionid,
               Wk_Station.StatCode,
               Wk_Station.StatName,
               Wk_Station.StatType,
               Wk_Station.StatDesc,
               Wk_Station.StateRefresh,
               Wk_Station.RowNum,
               Wk_Station.Summary,
               Wk_Station.EnabledMark,
               Wk_Station.CreateBy,
               Wk_Station.CreateByid,
               Wk_Station.CreateDate,
               Wk_Station.Lister,
               Wk_Station.Listerid,
               Wk_Station.ModifyDate,
               Wk_Station.DeleteMark,
               Wk_Station.DeleteListerid,
               Wk_Station.DeleteLister,
               Wk_Station.DeleteDate,
               Wk_Station.DisableWip,
               Wk_Station.LoadTime,
               Wk_Station.LoadQty,
               Wk_Station.LoadAmt,
               Wk_Station.Custom1,
               Wk_Station.Custom2,
               Wk_Station.Custom3,
               Wk_Station.Custom4,
               Wk_Station.Custom5,
               Wk_Station.Custom6,
               Wk_Station.Custom7,
               Wk_Station.Custom8,
               Wk_Station.Custom9,
               Wk_Station.Custom10,
               Wk_Station.Tenantid,
               Wk_Station.TenantName,
               Wk_Station.Revision,
               Wk_Section.SectName,
               Wk_Section.SectCode
        from Wk_Station
        Left join Wk_Section on Wk_Station.Sectionid = Wk_Section.id
    </sql>
    <sql id="selectdetailVo">
        <include refid="selectbillVo"/>
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkStationitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_Station.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_Station.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.statcode != null">
            and Wk_Station.statcode like concat('%', #{SearchPojo.statcode}, '%')
        </if>
        <if test="SearchPojo.statname != null">
            and Wk_Station.statname like concat('%',
                #{SearchPojo.statname}, '%')
        </if>
        <if test="SearchPojo.stattype != null">
            and Wk_Station.stattype like concat('%',
                #{SearchPojo.stattype}, '%')
        </if>
        <if test="SearchPojo.statdesc != null">
            and Wk_Station.statdesc like concat('%',
                #{SearchPojo.statdesc}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Wk_Station.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_Station.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_Station.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_Station.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_Station.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null">
            and Wk_Station.deletelisterid like concat('%',
                #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null">
            and Wk_Station.deletelister like concat('%',
                #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_Station.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_Station.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_Station.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_Station.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_Station.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_Station.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_Station.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_Station.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_Station.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_Station.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_Station.tenantname like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.statcode != null">
                or Wk_Station.StatCode like concat('%', #{SearchPojo.statcode}, '%')
            </if>
            <if test="SearchPojo.statname != null">
                or Wk_Station.StatName like concat('%', #{SearchPojo.statname}, '%')
            </if>
            <if test="SearchPojo.stattype != null">
                or Wk_Station.StatType like concat('%', #{SearchPojo.stattype}, '%')
            </if>
            <if test="SearchPojo.statdesc != null">
                or Wk_Station.StatDesc like concat('%', #{SearchPojo.statdesc}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Wk_Station.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_Station.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_Station.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_Station.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_Station.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null">
                or Wk_Station.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.deletelister != null">
                or Wk_Station.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_Station.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_Station.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_Station.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_Station.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_Station.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_Station.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_Station.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_Station.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_Station.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_Station.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_Station.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkStationPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_Station.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_Station.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.statcode != null">
            and Wk_Station.StatCode like concat('%', #{SearchPojo.statcode}, '%')
        </if>
        <if test="SearchPojo.statname != null">
            and Wk_Station.StatName like concat('%',
                #{SearchPojo.statname}, '%')
        </if>
        <if test="SearchPojo.stattype != null">
            and Wk_Station.StatType like concat('%',
                #{SearchPojo.stattype}, '%')
        </if>
        <if test="SearchPojo.statdesc != null">
            and Wk_Station.StatDesc like concat('%',
                #{SearchPojo.statdesc}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Wk_Station.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_Station.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_Station.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_Station.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_Station.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null">
            and Wk_Station.DeleteListerid like concat('%',
                #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null">
            and Wk_Station.DeleteLister like concat('%',
                #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_Station.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_Station.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_Station.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_Station.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_Station.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_Station.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_Station.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_Station.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_Station.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_Station.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_Station.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.statcode != null">
                or Wk_Station.StatCode like concat('%', #{SearchPojo.statcode}, '%')
            </if>
            <if test="SearchPojo.statname != null">
                or Wk_Station.StatName like concat('%', #{SearchPojo.statname}, '%')
            </if>
            <if test="SearchPojo.stattype != null">
                or Wk_Station.StatType like concat('%', #{SearchPojo.stattype}, '%')
            </if>
            <if test="SearchPojo.statdesc != null">
                or Wk_Station.StatDesc like concat('%', #{SearchPojo.statdesc}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Wk_Station.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_Station.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_Station.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_Station.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_Station.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null">
                or Wk_Station.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.deletelister != null">
                or Wk_Station.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_Station.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_Station.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_Station.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_Station.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_Station.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_Station.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_Station.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_Station.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_Station.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_Station.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_Station.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_Station(id, Sectionid, StatCode, StatName, StatType, StatDesc, StateRefresh, RowNum, Summary, EnabledMark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, DeleteMark, DeleteListerid, DeleteLister, DeleteDate, DisableWip, LoadTime, LoadQty, LoadAmt, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{sectionid}, #{statcode}, #{statname}, #{stattype}, #{statdesc}, #{staterefresh}, #{rownum}, #{summary}, #{enabledmark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{deletemark}, #{deletelisterid}, #{deletelister}, #{deletedate}, #{disablewip}, #{loadtime}, #{loadqty}, #{loadamt}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_Station
        <set>
            <if test="sectionid != null ">
            Sectionid =#{sectionid},
        </if>
            <if test="statcode != null">
                StatCode =#{statcode},
            </if>
            <if test="statname != null">
                StatName =#{statname},
            </if>
            <if test="stattype != null">
                StatType =#{stattype},
            </if>
            <if test="statdesc != null">
                StatDesc =#{statdesc},
            </if>
            <if test="staterefresh != null">
                StateRefresh =#{staterefresh},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelisterid != null">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletelister != null">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="disablewip != null">
                DisableWip =#{disablewip},
            </if>
            <if test="loadtime != null">
            LoadTime =#{loadtime},
        </if>
            <if test="loadqty != null">
            LoadQty =#{loadqty},
        </if>
            <if test="loadamt != null">
            LoadAmt =#{loadamt},
        </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_Station
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkStationPojo">
        select id
        from Wk_StationItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <select id="getAllList" resultType="inks.service.std.manu.domain.pojo.WkStationPojo">
        select *
        from Wk_Station
        where Tenantid = #{tenantid}
        order by RowNum
    </select>

    <select id="getListBySectid" resultType="inks.service.std.manu.domain.pojo.WkStationPojo">
        <include refid="selectbillVo"/>
        where Wk_Station.Tenantid = #{tid}
          and Wk_Station.Sectionid = #{sectid}
        order by Wk_Station.RowNum
    </select>

    <select id="getStationStateAndWk" resultType="java.util.HashMap">
        select Wk_Station.id,
        Wk_Station.StatName       as statname,
        Wk_Station.DisableWip     as disablewip,
        Wk_StationState.StateJson as statejson,
        Wk_Process.WpName         as wpname,
        Wk_Process.id             as wpid,
        Wk_Process.WorkParam      as workparam
        from Wk_Station
        left join Wk_ProcessStat on Wk_ProcessStat.Statid = Wk_Station.id
        left join Wk_Process on Wk_Process.id = Wk_ProcessStat.Pid
        left join Wk_StationState on Wk_StationState.Statid = Wk_Station.id
        where Wk_Station.id = #{id}
        and Wk_Station.Tenantid = #{tid}
        order by Wk_StationState.CreateDate desc
        limit 1
    </select>

    <select id="getStateJsonByWpid" resultType="java.lang.String">
        select Wk_StationState.StateJson
        from Wk_StationState
        where Wpid = #{wpid}
        and Tenantid = #{tid}
        order by CreateDate desc
        limit 1
    </select>


    <select id="getStationItemRealNameByWpid" resultType="java.lang.String">
        select Wk_StationItem.RealName
        from Wk_StationItem
        where Pid = (select Statid
        from Wk_ProcessStat
        where Pid = #{wpid}
        and Tenantid = #{tid})
        order by RowNum
        limit 1
    </select>
</mapper>

