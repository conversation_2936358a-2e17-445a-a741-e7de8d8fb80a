<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkCostbudgetMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkCostbudgetPojo">
        SELECT
            Wk_CostBudget.id,
            Wk_CostBudget.RefNo,
            Wk_CostBudget.BillType,
            Wk_CostBudget.BillDate,
            Wk_CostBudget.BillTitle,
            Wk_CostBudget.Operator,
            Wk_CostBudget.Groupid,
            Wk_CostBudget.Summary,
            Wk_CostBudget.CreateBy,
            Wk_CostBudget.CreateByid,
            Wk_CostBudget.CreateDate,
            Wk_CostBudget.Lister,
            Wk_CostBudget.Listerid,
            Wk_CostBudget.ModifyDate,
            Wk_CostBudget.Assessor,
            Wk_CostBudget.Assessorid,
            Wk_CostBudget.AssessDate,
            Wk_CostBudget.Custom1,
            Wk_CostBudget.Custom2,
            Wk_CostBudget.Custom3,
            Wk_CostBudget.Custom4,
            Wk_CostBudget.Custom5,
            Wk_CostBudget.Custom6,
            Wk_CostBudget.Custom7,
            Wk_CostBudget.Custom8,
            Wk_CostBudget.Custom9,
            Wk_CostBudget.Custom10,
            Wk_CostBudget.Tenantid,
            Wk_CostBudget.Revision,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate
        FROM
            Wk_CostBudget
                LEFT JOIN App_Workgroup ON Wk_CostBudget.Groupid = App_Workgroup.id
        where Wk_CostBudget.id = #{key}
          and Wk_CostBudget.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT
            Wk_CostBudget.id,
            Wk_CostBudget.RefNo,
            Wk_CostBudget.BillType,
            Wk_CostBudget.BillDate,
            Wk_CostBudget.BillTitle,
            Wk_CostBudget.Operator,
            Wk_CostBudget.Groupid,
            Wk_CostBudget.Summary,
            Wk_CostBudget.CreateBy,
            Wk_CostBudget.CreateByid,
            Wk_CostBudget.CreateDate,
            Wk_CostBudget.Lister,
            Wk_CostBudget.Listerid,
            Wk_CostBudget.ModifyDate,
            Wk_CostBudget.Assessor,
            Wk_CostBudget.Assessorid,
            Wk_CostBudget.AssessDate,
            Wk_CostBudget.Custom1,
            Wk_CostBudget.Custom2,
            Wk_CostBudget.Custom3,
            Wk_CostBudget.Custom4,
            Wk_CostBudget.Custom5,
            Wk_CostBudget.Custom6,
            Wk_CostBudget.Custom7,
            Wk_CostBudget.Custom8,
            Wk_CostBudget.Custom9,
            Wk_CostBudget.Custom10,
            Wk_CostBudget.Tenantid,
            Wk_CostBudget.Revision,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate
        FROM
            Wk_CostBudget
                LEFT JOIN App_Workgroup ON Wk_CostBudget.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT
            Wk_CostBudgetItem.id,
            Wk_CostBudgetItem.Pid,
            Wk_CostBudgetItem.Goodsid,
            Wk_CostBudgetItem.ItemCode,
            Wk_CostBudgetItem.ItemName,
            Wk_CostBudgetItem.ItemSpec,
            Wk_CostBudgetItem.ItemUnit,
            Wk_CostBudgetItem.Quantity,
            Wk_CostBudgetItem.TaxPrice,
            Wk_CostBudgetItem.TaxAmount,
            Wk_CostBudgetItem.Price,
            Wk_CostBudgetItem.Amount,
            Wk_CostBudgetItem.TaxTotal,
            Wk_CostBudgetItem.ItemTaxrate,
            Wk_CostBudgetItem.StartDate,
            Wk_CostBudgetItem.PlanDate,
            Wk_CostBudgetItem.EnabledMark,
            Wk_CostBudgetItem.Closed,
            Wk_CostBudgetItem.Remark,
            Wk_CostBudgetItem.StateCode,
            Wk_CostBudgetItem.StateDate,
            Wk_CostBudgetItem.RowNum,
            Wk_CostBudgetItem.MachUid,
            Wk_CostBudgetItem.MachItemid,
            Wk_CostBudgetItem.Customer,
            Wk_CostBudgetItem.CustPO,
            Wk_CostBudgetItem.Custom1,
            Wk_CostBudgetItem.Custom2,
            Wk_CostBudgetItem.Custom3,
            Wk_CostBudgetItem.Custom4,
            Wk_CostBudgetItem.Custom5,
            Wk_CostBudgetItem.Custom6,
            Wk_CostBudgetItem.Custom7,
            Wk_CostBudgetItem.Custom8,
            Wk_CostBudgetItem.Custom9,
            Wk_CostBudgetItem.Custom10,
            Wk_CostBudgetItem.Tenantid,
            Wk_CostBudgetItem.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_CostBudget.RefNo,
            Wk_CostBudget.BillType,
            Wk_CostBudget.BillDate,
            Wk_CostBudget.BillTitle,
            Wk_CostBudget.Operator,
            Wk_CostBudget.Summary,
            Wk_CostBudget.CreateBy,
            Wk_CostBudget.Lister,
            Wk_CostBudget.Assessor,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate
        FROM
            Wk_CostBudget
                RIGHT JOIN Wk_CostBudgetItem ON Wk_CostBudgetItem.Pid = Wk_CostBudget.id
                LEFT JOIN Mat_Goods ON Wk_CostBudgetItem.Goodsid = Mat_Goods.id
                LEFT JOIN App_Workgroup ON App_Workgroup.id = Wk_CostBudget.Groupid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkCostbudgetitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_CostBudget.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_CostBudget.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Wk_CostBudget.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_CostBudget.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_CostBudget.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_CostBudget.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Wk_CostBudget.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_CostBudget.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_CostBudget.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_CostBudget.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_CostBudget.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_CostBudget.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_CostBudget.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_CostBudget.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_CostBudget.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_CostBudget.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_CostBudget.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_CostBudget.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_CostBudget.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_CostBudget.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_CostBudget.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_CostBudget.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_CostBudget.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_CostBudget.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_CostBudget.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_CostBudget.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_CostBudget.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_CostBudget.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Wk_CostBudget.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_CostBudget.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_CostBudget.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_CostBudget.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_CostBudget.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_CostBudget.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_CostBudget.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_CostBudget.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_CostBudget.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_CostBudget.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_CostBudget.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_CostBudget.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_CostBudget.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_CostBudget.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_CostBudget.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_CostBudget.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_CostBudget.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_CostBudget.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkCostbudgetPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_CostBudget.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_CostBudget.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Wk_CostBudget.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_CostBudget.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_CostBudget.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_CostBudget.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Wk_CostBudget.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_CostBudget.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_CostBudget.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_CostBudget.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_CostBudget.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_CostBudget.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_CostBudget.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_CostBudget.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_CostBudget.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_CostBudget.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_CostBudget.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_CostBudget.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_CostBudget.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_CostBudget.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_CostBudget.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_CostBudget.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_CostBudget.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_CostBudget.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_CostBudget.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_CostBudget.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_CostBudget.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_CostBudget.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Wk_CostBudget.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_CostBudget.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_CostBudget.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_CostBudget.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_CostBudget.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_CostBudget.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_CostBudget.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_CostBudget.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_CostBudget.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_CostBudget.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_CostBudget.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_CostBudget.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_CostBudget.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_CostBudget.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_CostBudget.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_CostBudget.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_CostBudget.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_CostBudget.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_CostBudget(id, RefNo, BillType, BillDate, BillTitle, Operator, Groupid, Summary, CreateBy,
                                  CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid,
                                  AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                  Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{operator}, #{groupid}, #{summary},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor},
                #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_CostBudget
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_CostBudget
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_CostBudget
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkCostbudgetPojo">
        select
        id
        from Wk_CostBudgetItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
    <!--查询DelListIds-->
    <select id="getDelMatIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkCostbudgetPojo">
        select
        id
        from Wk_CostBudgetMat
        where Pid = #{id} and id not in
        <foreach collection="mat" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

    <!--查询DelListIds-->
    <select id="getDelCostIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkCostbudgetPojo">
        select
        id
        from Wk_CostBudgetCost
        where Pid = #{id} and id not in
        <foreach collection="cost" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
</mapper>

