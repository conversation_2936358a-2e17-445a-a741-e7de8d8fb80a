<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.HiWipqtyMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.HiWipqtyPojo">
        SELECT Hi_WipQty.id,
        Hi_WipQty.RefNo,
        Hi_WipQty.Wpid,
        Hi_WipQty.WpCode,
        Hi_WipQty.WpName,
        Hi_WipQty.WkDate,
        Hi_WipQty.Direction,
        Hi_WipQty.Goodsid,
        Hi_WipQty.Worker,
        Hi_WipQty.PcsQty,
        Hi_WipQty.SecQty,
        Hi_WipQty.Remark,
        Hi_WipQty.WorkUid,
        Hi_WipQty.WipUid,
        Hi_WipQty.WipRowNum,
        Hi_WipQty.WipItemid,
        Hi_WipQty.MrbPcsQty,
        Hi_WipQty.MrbSecQty,
        Hi_WipQty.Mrbid,
        Hi_WipQty.Acceid,
        Hi_WipQty.Inspector,
        Hi_WipQty.CreateBy,
        Hi_WipQty.CreateByid,
        Hi_WipQty.CreateDate,
        Hi_WipQty.Lister,
        Hi_WipQty.Listerid,
        Hi_WipQty.ModifyDate,
        Hi_WipQty.MachUid,
        Hi_WipQty.MachItemid,
        Hi_WipQty.MachGroupid,
        Hi_WipQty.MainPlanUid,
        Hi_WipQty.MainPlanItemid,
        Hi_WipQty.WorkItemid,
        Hi_WipQty.AttributeJson,
        Hi_WipQty.SpecJson,
        Hi_WipQty.SizeX,
        Hi_WipQty.SizeY,
        Hi_WipQty.SizeZ,
        Hi_WipQty.WorkParam,
        Hi_WipQty.Statid,
        Hi_WipQty.StatCode,
        Hi_WipQty.StatName,
        Hi_WipQty.WorkTime,
        Hi_WipQty.Custom1,
        Hi_WipQty.Custom2,
        Hi_WipQty.Custom3,
        Hi_WipQty.Custom4,
        Hi_WipQty.Custom5,
        Hi_WipQty.Custom6,
        Hi_WipQty.Custom7,
        Hi_WipQty.Custom8,
        Hi_WipQty.Custom9,
        Hi_WipQty.Custom10,
        Hi_WipQty.Tenantid,
        Hi_WipQty.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM Hi_WipQty
        LEFT JOIN Mat_Goods ON Hi_WipQty.Goodsid = Mat_Goods.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Hi_WipQty.MachGroupid
        where Hi_WipQty.id = #{key}
        and Hi_WipQty.Tenantid = #{tid}
    </select>
    <sql id="selectHiWipqtyVo">
        SELECT Hi_WipQty.id,
               Hi_WipQty.RefNo,
               Hi_WipQty.Wpid,
               Hi_WipQty.WpCode,
               Hi_WipQty.WpName,
               Hi_WipQty.WkDate,
               Hi_WipQty.Direction,
               Hi_WipQty.Goodsid,
               Hi_WipQty.Worker,
               Hi_WipQty.PcsQty,
               Hi_WipQty.SecQty,
               Hi_WipQty.Remark,
               Hi_WipQty.WorkUid,
               Hi_WipQty.WipUid,
               Hi_WipQty.WipRowNum,
               Hi_WipQty.WipItemid,
               Hi_WipQty.MrbPcsQty,
               Hi_WipQty.MrbSecQty,
               Hi_WipQty.Mrbid,
               Hi_WipQty.Acceid,
               Hi_WipQty.Inspector,
               Hi_WipQty.CreateBy,
               Hi_WipQty.CreateByid,
               Hi_WipQty.CreateDate,
               Hi_WipQty.Lister,
               Hi_WipQty.Listerid,
               Hi_WipQty.ModifyDate,
               Hi_WipQty.MachUid,
               Hi_WipQty.MachItemid,
               Hi_WipQty.MachGroupid,
               Hi_WipQty.MainPlanUid,
               Hi_WipQty.MainPlanItemid,
               Hi_WipQty.WorkItemid,
               Hi_WipQty.AttributeJson,
               Hi_WipQty.SpecJson,
               Hi_WipQty.SizeX,
               Hi_WipQty.SizeY,
               Hi_WipQty.SizeZ,
               Hi_WipQty.WorkParam,
               Hi_WipQty.Statid,
               Hi_WipQty.StatCode,
               Hi_WipQty.StatName,
               Hi_WipQty.WorkTime,
               Hi_WipQty.Custom1,
               Hi_WipQty.Custom2,
               Hi_WipQty.Custom3,
               Hi_WipQty.Custom4,
               Hi_WipQty.Custom5,
               Hi_WipQty.Custom6,
               Hi_WipQty.Custom7,
               Hi_WipQty.Custom8,
               Hi_WipQty.Custom9,
               Hi_WipQty.Custom10,
               Hi_WipQty.Tenantid,
               Hi_WipQty.Revision,
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Hi_WipQty
                 LEFT JOIN Mat_Goods ON Hi_WipQty.Goodsid = Mat_Goods.id
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Hi_WipQty.MachGroupid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.HiWipqtyPojo">
        <include refid="selectHiWipqtyVo"/>
         where 1 = 1 and Hi_WipQty.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Hi_WipQty.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Hi_WipQty.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.wpid != null ">
   and Hi_WipQty.Wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null ">
   and Hi_WipQty.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null ">
   and Hi_WipQty.WpName like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.direction != null ">
   and Hi_WipQty.Direction like concat('%', #{SearchPojo.direction}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   and Hi_WipQty.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.worker != null ">
   and Hi_WipQty.Worker like concat('%', #{SearchPojo.worker}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Hi_WipQty.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.workuid != null ">
   and Hi_WipQty.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
</if>
<if test="SearchPojo.wipuid != null ">
   and Hi_WipQty.WipUid like concat('%', #{SearchPojo.wipuid}, '%')
</if>
<if test="SearchPojo.wipitemid != null ">
   and Hi_WipQty.WipItemid like concat('%', #{SearchPojo.wipitemid}, '%')
</if>
<if test="SearchPojo.mrbid != null ">
   and Hi_WipQty.Mrbid like concat('%', #{SearchPojo.mrbid}, '%')
</if>
<if test="SearchPojo.acceid != null ">
   and Hi_WipQty.Acceid like concat('%', #{SearchPojo.acceid}, '%')
</if>
<if test="SearchPojo.inspector != null ">
   and Hi_WipQty.Inspector like concat('%', #{SearchPojo.inspector}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Hi_WipQty.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Hi_WipQty.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Hi_WipQty.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Hi_WipQty.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.machuid != null ">
   and Hi_WipQty.MachUid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.machitemid != null ">
   and Hi_WipQty.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
</if>
<if test="SearchPojo.machgroupid != null ">
   and Hi_WipQty.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
</if>
<if test="SearchPojo.mainplanuid != null ">
   and Hi_WipQty.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
</if>
<if test="SearchPojo.mainplanitemid != null ">
   and Hi_WipQty.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
</if>
<if test="SearchPojo.workitemid != null ">
   and Hi_WipQty.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
</if>
<if test="SearchPojo.attributejson != null ">
   and Hi_WipQty.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.specjson != null ">
   and Hi_WipQty.SpecJson like concat('%', #{SearchPojo.specjson}, '%')
</if>
<if test="SearchPojo.workparam != null ">
   and Hi_WipQty.WorkParam like concat('%', #{SearchPojo.workparam}, '%')
</if>
<if test="SearchPojo.statid != null ">
   and Hi_WipQty.Statid like concat('%', #{SearchPojo.statid}, '%')
</if>
<if test="SearchPojo.statcode != null ">
   and Hi_WipQty.StatCode like concat('%', #{SearchPojo.statcode}, '%')
</if>
<if test="SearchPojo.statname != null ">
   and Hi_WipQty.StatName like concat('%', #{SearchPojo.statname}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Hi_WipQty.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Hi_WipQty.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Hi_WipQty.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Hi_WipQty.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Hi_WipQty.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Hi_WipQty.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Hi_WipQty.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Hi_WipQty.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Hi_WipQty.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Hi_WipQty.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Hi_WipQty.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.wpid != null ">
   or Hi_WipQty.Wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null ">
   or Hi_WipQty.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null ">
   or Hi_WipQty.WpName like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.direction != null ">
   or Hi_WipQty.Direction like concat('%', #{SearchPojo.direction}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   or Hi_WipQty.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.worker != null ">
   or Hi_WipQty.Worker like concat('%', #{SearchPojo.worker}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Hi_WipQty.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.workuid != null ">
   or Hi_WipQty.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
</if>
<if test="SearchPojo.wipuid != null ">
   or Hi_WipQty.WipUid like concat('%', #{SearchPojo.wipuid}, '%')
</if>
<if test="SearchPojo.wipitemid != null ">
   or Hi_WipQty.WipItemid like concat('%', #{SearchPojo.wipitemid}, '%')
</if>
<if test="SearchPojo.mrbid != null ">
   or Hi_WipQty.Mrbid like concat('%', #{SearchPojo.mrbid}, '%')
</if>
<if test="SearchPojo.acceid != null ">
   or Hi_WipQty.Acceid like concat('%', #{SearchPojo.acceid}, '%')
</if>
<if test="SearchPojo.inspector != null ">
   or Hi_WipQty.Inspector like concat('%', #{SearchPojo.inspector}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Hi_WipQty.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Hi_WipQty.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Hi_WipQty.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Hi_WipQty.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.machuid != null ">
   or Hi_WipQty.MachUid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.machitemid != null ">
   or Hi_WipQty.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
</if>
<if test="SearchPojo.machgroupid != null ">
   or Hi_WipQty.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
</if>
<if test="SearchPojo.mainplanuid != null ">
   or Hi_WipQty.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
</if>
<if test="SearchPojo.mainplanitemid != null ">
   or Hi_WipQty.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
</if>
<if test="SearchPojo.workitemid != null ">
   or Hi_WipQty.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
</if>
<if test="SearchPojo.attributejson != null ">
   or Hi_WipQty.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.specjson != null ">
   or Hi_WipQty.SpecJson like concat('%', #{SearchPojo.specjson}, '%')
</if>
<if test="SearchPojo.workparam != null ">
   or Hi_WipQty.WorkParam like concat('%', #{SearchPojo.workparam}, '%')
</if>
<if test="SearchPojo.statid != null ">
   or Hi_WipQty.Statid like concat('%', #{SearchPojo.statid}, '%')
</if>
<if test="SearchPojo.statcode != null ">
   or Hi_WipQty.StatCode like concat('%', #{SearchPojo.statcode}, '%')
</if>
<if test="SearchPojo.statname != null ">
   or Hi_WipQty.StatName like concat('%', #{SearchPojo.statname}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Hi_WipQty.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Hi_WipQty.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Hi_WipQty.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Hi_WipQty.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Hi_WipQty.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Hi_WipQty.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Hi_WipQty.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Hi_WipQty.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Hi_WipQty.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Hi_WipQty.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Hi_WipQty(id, RefNo, Wpid, WpCode, WpName, WkDate, Direction, Goodsid, Worker, PcsQty, SecQty, Remark, WorkUid, WipUid, WipRowNum, WipItemid, MrbPcsQty, MrbSecQty, Mrbid, Acceid, Inspector, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, MachUid, MachItemid, MachGroupid, MainPlanUid, MainPlanItemid, WorkItemid, AttributeJson, SpecJson, SizeX, SizeY, SizeZ, WorkParam, Statid, StatCode, StatName, WorkTime, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{refno}, #{wpid}, #{wpcode}, #{wpname}, #{wkdate}, #{direction}, #{goodsid}, #{worker}, #{pcsqty}, #{secqty}, #{remark}, #{workuid}, #{wipuid}, #{wiprownum}, #{wipitemid}, #{mrbpcsqty}, #{mrbsecqty}, #{mrbid}, #{acceid}, #{inspector}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{machuid}, #{machitemid}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid}, #{workitemid}, #{attributejson}, #{specjson}, #{sizex}, #{sizey}, #{sizez}, #{workparam}, #{statid}, #{statcode}, #{statname}, #{worktime}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Hi_WipQty
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="wpid != null ">
                Wpid =#{wpid},
            </if>
            <if test="wpcode != null ">
                WpCode =#{wpcode},
            </if>
            <if test="wpname != null ">
                WpName =#{wpname},
            </if>
            <if test="wkdate != null">
                WkDate =#{wkdate},
            </if>
            <if test="direction != null ">
                Direction =#{direction},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="worker != null ">
                Worker =#{worker},
            </if>
            <if test="pcsqty != null">
                PcsQty =#{pcsqty},
            </if>
            <if test="secqty != null">
                SecQty =#{secqty},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="workuid != null ">
                WorkUid =#{workuid},
            </if>
            <if test="wipuid != null ">
                WipUid =#{wipuid},
            </if>
            <if test="wiprownum != null">
                WipRowNum =#{wiprownum},
            </if>
            <if test="wipitemid != null ">
                WipItemid =#{wipitemid},
            </if>
            <if test="mrbpcsqty != null">
                MrbPcsQty =#{mrbpcsqty},
            </if>
            <if test="mrbsecqty != null">
                MrbSecQty =#{mrbsecqty},
            </if>
            <if test="mrbid != null ">
                Mrbid =#{mrbid},
            </if>
            <if test="acceid != null ">
                Acceid =#{acceid},
            </if>
            <if test="inspector != null ">
                Inspector =#{inspector},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="machuid != null ">
                MachUid =#{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid =#{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid =#{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid =#{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid =#{mainplanitemid},
            </if>
            <if test="workitemid != null ">
                WorkItemid =#{workitemid},
            </if>
            <if test="attributejson != null ">
                AttributeJson =#{attributejson},
            </if>
            <if test="specjson != null ">
                SpecJson =#{specjson},
            </if>
            <if test="sizex != null">
                SizeX =#{sizex},
            </if>
            <if test="sizey != null">
                SizeY =#{sizey},
            </if>
            <if test="sizez != null">
                SizeZ =#{sizez},
            </if>
            <if test="workparam != null ">
                WorkParam =#{workparam},
            </if>
            <if test="statid != null ">
                Statid =#{statid},
            </if>
            <if test="statcode != null ">
                StatCode =#{statcode},
            </if>
            <if test="statname != null ">
                StatName =#{statname},
            </if>
            <if test="worktime != null">
                WorkTime =#{worktime},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Hi_WipQty where id = #{key} and Tenantid=#{tid}
    </delete>


<!--&lt;!&ndash;=====================================================================以下为历史表数据迁移==========================================================================&ndash;&gt;-->

<!--    &lt;!&ndash; 0.先拿到所有在线的WipNote.RefNo(禁止迁移到Hi)&ndash;&gt;-->
<!--    <select id="getOnlineNowWipNoteRefNos" resultType="java.lang.String">-->
<!--        select RefNo-->
<!--        from Wk_WipNote-->
<!--        where Wk_WipNote.Tenantid = #{tid}-->
<!--        and Wk_WipNote.MrbPcsQty + Wk_WipNote.CompPcsQty <![CDATA[<]]> Wk_WipNote.WkPcsQty-->
<!--        and Wk_WipNote.DisannulMark = 0-->
<!--        and Wk_WipNote.Closed = 0-->
<!--        and Wk_WipNote.CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--    </select>-->
<!--    &lt;!&ndash;1.先将Wk数据复制到Hi    Wk_WipQty.WipUid NOT IN(结存的Wip,即getOnlinePageTh的条件)-->
<!--    !!!!!!!!!!!!!!注意:copyNowToHi必须和deleteNow保持SQL的where条件相同!!!!!!!!!!!&ndash;&gt;-->
<!--    <insert id="copyNowToHi">-->
<!--        INSERT INTO Hi_WipQty-->
<!--        SELECT *-->
<!--        FROM Wk_WipQty-->
<!--        WHERE CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--          and Tenantid = #{tid}-->
<!--        <if test="onlineIds != null and onlineIds.size() != 0">-->
<!--            and WipUid not in-->
<!--            <foreach collection="onlineIds" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </insert>-->
<!--    <delete id="deleteNow">-->
<!--        delete-->
<!--        from Wk_WipQty-->
<!--        where CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--          and Tenantid = #{tid}-->
<!--        <if test="onlineIds != null and onlineIds.size() != 0">-->
<!--            and WipUid not in-->
<!--            <foreach collection="onlineIds" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </delete>-->



<!--&lt;!&ndash; !!!!!!!注意copyHiToNow和deleteHi的where条件必须完全相同 !!!!!!!!!!!!!&ndash;&gt;-->
<!--&lt;!&ndash;    如果勾选指定了要迁出的ids(HiToNow),则无需时间范围了&ndash;&gt;-->
<!--    <insert id="copyHiToNow">-->
<!--        INSERT INTO Wk_WipQty-->
<!--        SELECT * FROM Hi_WipQty WHERE Tenantid=#{tid}-->
<!--        <if test="ids == null">-->
<!--            and CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--        </if>-->
<!--        <if test="ids != null and ids.size() != 0">-->
<!--            and id in-->
<!--            <foreach collection="ids" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </insert>-->

<!--    <delete id="deleteHi">-->
<!--        delete from Hi_WipQty where Tenantid=#{tid}-->
<!--        <if test="ids == null">-->
<!--            and CreateDate BETWEEN #{startdate} and #{enddate}-->
<!--        </if>-->
<!--        <if test="ids != null and ids.size() != 0">-->
<!--            and id in-->
<!--            <foreach collection="ids" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </delete>-->

<!--    ====================================-->
    <!-- 0.先拿到所有在线的WipNote.RefNo(禁止迁移到Hi)-->
    <select id="getOnlineNowWipNoteRefNos" resultType="java.lang.String">
        select RefNo
        from Wk_WipNote
        where Wk_WipNote.Tenantid = #{tid}
        and Wk_WipNote.MrbPcsQty + Wk_WipNote.CompPcsQty <![CDATA[<]]> Wk_WipNote.WkPcsQty
        and Wk_WipNote.DisannulMark = 0
        and Wk_WipNote.Closed = 0
        and Wk_WipNote.CreateDate BETWEEN #{startdate} and #{enddate}
    </select>

    <!-- 获取符合条件的所有可迁移ID -->
    <select id="getAllMigratableNowIds" resultType="java.lang.String">
        select id
        from Wk_WipQty
        WHERE CreateDate BETWEEN #{startdate} and #{enddate}
        and Tenantid = #{tid}
        <if test="onlineRefNos != null and onlineRefNos.size() != 0">
            and WipUid not in
            <foreach collection="onlineRefNos" item="refno" open="(" separator="," close=")">
                #{refno}
            </foreach>
        </if>
    </select>

    <!-- 获取符合条件的所有历史表可迁移ID -->
    <select id="getAllMigratableHiIds" resultType="java.lang.String">
        select id
        from Hi_WipQty
        WHERE Tenantid = #{tid}
        <if test="startdate != null and enddate != null">
            and CreateDate BETWEEN #{startdate} and #{enddate}
        </if>
    </select>

    <!-- 基于ID列表批量复制 Now->Hi -->
    <insert id="copyNowToHiByIds">
        INSERT INTO Hi_WipQty
        SELECT *
        FROM Wk_WipQty
        WHERE Tenantid = #{tid}
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <!-- 基于ID列表批量删除Wk表数据 -->
    <delete id="deleteNowByIds">
        DELETE FROM Wk_WipQty
        WHERE Tenantid = #{tid}
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 基于ID列表批量复制 Hi->Now -->
    <insert id="copyHiToNowByIds">
        INSERT INTO Wk_WipQty
        SELECT *
        FROM Hi_WipQty
        WHERE Tenantid = #{tid}
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <!-- 基于ID列表批量删除历史表数据 -->
    <delete id="deleteHiByIds">
        DELETE FROM Hi_WipQty
        WHERE Tenantid = #{tid}
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 保留原来的方法以兼容旧代码 -->
    <insert id="copyNowToHi">
        INSERT INTO Hi_WipQty
        SELECT *
        FROM Wk_WipQty
        WHERE CreateDate BETWEEN #{startdate} and #{enddate}
        and Tenantid = #{tid}
        <if test="onlineIds != null and onlineIds.size() != 0">
            and WipUid not in
            <foreach collection="onlineIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </insert>

    <delete id="deleteNow">
        delete
        from Wk_WipQty
        where CreateDate BETWEEN #{startdate} and #{enddate}
        and Tenantid = #{tid}
        <if test="onlineIds != null and onlineIds.size() != 0">
            and WipUid not in
            <foreach collection="onlineIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>

    <insert id="copyHiToNow">
        INSERT INTO Wk_WipQty
        SELECT * FROM Hi_WipQty WHERE Tenantid=#{tid}
        <if test="ids == null">
            and CreateDate BETWEEN #{startdate} and #{enddate}
        </if>
        <if test="ids != null and ids.size() != 0">
            and id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </insert>

    <delete id="deleteHi">
        delete from Hi_WipQty where Tenantid=#{tid}
        <if test="ids == null">
            and CreateDate BETWEEN #{startdate} and #{enddate}
        </if>
        <if test="ids != null and ids.size() != 0">
            and id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>

