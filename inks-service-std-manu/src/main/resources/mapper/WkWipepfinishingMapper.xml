<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipepfinishingMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipepfinishingPojo">
        SELECT
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Wk_WipEpFinishing.id,
            Wk_WipEpFinishing.RefNo,
            Wk_WipEpFinishing.BillType,
            Wk_WipEpFinishing.BillDate,
            Wk_WipEpFinishing.Groupid,
            Wk_WipEpFinishing.BillTitle,
            Wk_WipEpFinishing.Operator,
            Wk_WipEpFinishing.Summary,
            Wk_WipEpFinishing.CreateBy,
            Wk_WipEpFinishing.CreateByid,
            Wk_WipEpFinishing.CreateDate,
            Wk_WipEpFinishing.Lister,
            Wk_WipEpFinishing.Listerid,
            Wk_WipEpFinishing.ModifyDate,
            Wk_WipEpFinishing.Assessor,
            Wk_WipEpFinishing.Assessorid,
            Wk_WipEpFinishing.AssessDate,
            Wk_WipEpFinishing.BillTaxAmount,
            Wk_WipEpFinishing.BillAmount,
            Wk_WipEpFinishing.ItemCount,
            Wk_WipEpFinishing.DisannulCount,
            Wk_WipEpFinishing.FinishCount,
            Wk_WipEpFinishing.PrintCount,
            Wk_WipEpFinishing.Custom1,
            Wk_WipEpFinishing.Custom2,
            Wk_WipEpFinishing.Custom3,
            Wk_WipEpFinishing.Custom4,
            Wk_WipEpFinishing.Custom5,
            Wk_WipEpFinishing.Custom6,
            Wk_WipEpFinishing.Custom7,
            Wk_WipEpFinishing.Custom8,
            Wk_WipEpFinishing.Custom9,
            Wk_WipEpFinishing.Custom10,
            Wk_WipEpFinishing.Tenantid,
            Wk_WipEpFinishing.TenantName,
            Wk_WipEpFinishing.Revision
        FROM
            App_Workgroup
                RIGHT JOIN Wk_WipEpFinishing ON App_Workgroup.id = Wk_WipEpFinishing.Groupid
        where Wk_WipEpFinishing.id = #{key}
          and Wk_WipEpFinishing.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Wk_WipEpFinishing.id,
            Wk_WipEpFinishing.RefNo,
            Wk_WipEpFinishing.BillType,
            Wk_WipEpFinishing.BillDate,
            Wk_WipEpFinishing.Groupid,
            Wk_WipEpFinishing.BillTitle,
            Wk_WipEpFinishing.Operator,
            Wk_WipEpFinishing.Summary,
            Wk_WipEpFinishing.CreateBy,
            Wk_WipEpFinishing.CreateByid,
            Wk_WipEpFinishing.CreateDate,
            Wk_WipEpFinishing.Lister,
            Wk_WipEpFinishing.Listerid,
            Wk_WipEpFinishing.ModifyDate,
            Wk_WipEpFinishing.Assessor,
            Wk_WipEpFinishing.Assessorid,
            Wk_WipEpFinishing.AssessDate,
            Wk_WipEpFinishing.BillTaxAmount,
            Wk_WipEpFinishing.BillAmount,
            Wk_WipEpFinishing.ItemCount,
            Wk_WipEpFinishing.DisannulCount,
            Wk_WipEpFinishing.FinishCount,
            Wk_WipEpFinishing.PrintCount,
            Wk_WipEpFinishing.Custom1,
            Wk_WipEpFinishing.Custom2,
            Wk_WipEpFinishing.Custom3,
            Wk_WipEpFinishing.Custom4,
            Wk_WipEpFinishing.Custom5,
            Wk_WipEpFinishing.Custom6,
            Wk_WipEpFinishing.Custom7,
            Wk_WipEpFinishing.Custom8,
            Wk_WipEpFinishing.Custom9,
            Wk_WipEpFinishing.Custom10,
            Wk_WipEpFinishing.Tenantid,
            Wk_WipEpFinishing.TenantName,
            Wk_WipEpFinishing.Revision
        FROM
            App_Workgroup
                RIGHT JOIN Wk_WipEpFinishing ON App_Workgroup.id = Wk_WipEpFinishing.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_WipEpFinishingItem.id,
            Wk_WipEpFinishingItem.Pid,
            Wk_WipEpFinishingItem.Goodsid,
            Wk_WipEpFinishingItem.Quantity,
            Wk_WipEpFinishingItem.MrbQty,
            Wk_WipEpFinishingItem.SubItemid,
            Wk_WipEpFinishingItem.SubUse,
            Wk_WipEpFinishingItem.SubUnit,
            Wk_WipEpFinishingItem.SubQty,
            Wk_WipEpFinishingItem.TaxPrice,
            Wk_WipEpFinishingItem.TaxAmount,
            Wk_WipEpFinishingItem.TaxTotal,
            Wk_WipEpFinishingItem.Price,
            Wk_WipEpFinishingItem.Amount,
            Wk_WipEpFinishingItem.ItemTaxrate,
            Wk_WipEpFinishingItem.Remark,
            Wk_WipEpFinishingItem.CiteUid,
            Wk_WipEpFinishingItem.CiteItemid,
            Wk_WipEpFinishingItem.StateCode,
            Wk_WipEpFinishingItem.StateDate,
            Wk_WipEpFinishingItem.Inspected,
            Wk_WipEpFinishingItem.Closed,
            Wk_WipEpFinishingItem.RowNum,
            Wk_WipEpFinishingItem.InvoQty,
            Wk_WipEpFinishingItem.InvoClosed,
            Wk_WipEpFinishingItem.VirtualItem,
            Wk_WipEpFinishingItem.WipItemid,
            Wk_WipEpFinishingItem.Wsid,
            Wk_WipEpFinishingItem.WsUid,
            Wk_WipEpFinishingItem.Wpid,
            Wk_WipEpFinishingItem.EndWpid,
            Wk_WipEpFinishingItem.Customer,
            Wk_WipEpFinishingItem.CustPO,
            Wk_WipEpFinishingItem.MachUid,
            Wk_WipEpFinishingItem.MachItemid,
            Wk_WipEpFinishingItem.MainPlanUid,
            Wk_WipEpFinishingItem.MainPlanItemid,
            Wk_WipEpFinishingItem.MachGroupid,
            Wk_WipEpFinishingItem.DisannulMark,
            Wk_WipEpFinishingItem.DisannulListerid,
            Wk_WipEpFinishingItem.DisannulLister,
            Wk_WipEpFinishingItem.DisannulDate,
            Wk_WipEpFinishingItem.AttributeJson,
            Wk_WipEpFinishingItem.Custom1,
            Wk_WipEpFinishingItem.Custom2,
            Wk_WipEpFinishingItem.Custom3,
            Wk_WipEpFinishingItem.Custom4,
            Wk_WipEpFinishingItem.Custom5,
            Wk_WipEpFinishingItem.Custom6,
            Wk_WipEpFinishingItem.Custom7,
            Wk_WipEpFinishingItem.Custom8,
            Wk_WipEpFinishingItem.Custom9,
            Wk_WipEpFinishingItem.Custom10,
            Wk_WipEpFinishingItem.Tenantid,
            Wk_WipEpFinishingItem.Revision,
            Wk_WipEpFinishing.RefNo,
            Wk_WipEpFinishing.BillType,
            Wk_WipEpFinishing.BillDate,
            Wk_WipEpFinishing.BillTitle,
            Wk_WipEpFinishing.Operator,
            Wk_WipEpFinishing.CreateBy,
            Wk_WipEpFinishing.Lister,
            Wk_WipEpFinishing.Assessor,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate
        FROM
            Mat_Goods
                RIGHT JOIN Wk_WipEpFinishingItem ON Mat_Goods.id = Wk_WipEpFinishingItem.Goodsid
                LEFT JOIN Wk_WipEpFinishing ON Wk_WipEpFinishingItem.Pid = Wk_WipEpFinishing.id
                LEFT JOIN App_Workgroup ON Wk_WipEpFinishing.Groupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipepfinishingitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_WipEpFinishing.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipEpFinishing.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Wk_WipEpFinishing.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_WipEpFinishing.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Wk_WipEpFinishing.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_WipEpFinishing.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_WipEpFinishing.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_WipEpFinishing.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_WipEpFinishing.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_WipEpFinishing.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_WipEpFinishing.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_WipEpFinishing.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_WipEpFinishing.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_WipEpFinishing.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_WipEpFinishing.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_WipEpFinishing.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_WipEpFinishing.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_WipEpFinishing.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_WipEpFinishing.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_WipEpFinishing.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_WipEpFinishing.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_WipEpFinishing.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_WipEpFinishing.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_WipEpFinishing.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_WipEpFinishing.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_WipEpFinishing.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_WipEpFinishing.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Wk_WipEpFinishing.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_WipEpFinishing.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_WipEpFinishing.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_WipEpFinishing.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_WipEpFinishing.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_WipEpFinishing.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_WipEpFinishing.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_WipEpFinishing.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_WipEpFinishing.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_WipEpFinishing.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_WipEpFinishing.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_WipEpFinishing.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_WipEpFinishing.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_WipEpFinishing.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_WipEpFinishing.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_WipEpFinishing.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_WipEpFinishing.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_WipEpFinishing.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_WipEpFinishing.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_WipEpFinishing.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_WipEpFinishing.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipepfinishingPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_WipEpFinishing.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipEpFinishing.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Wk_WipEpFinishing.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_WipEpFinishing.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Wk_WipEpFinishing.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_WipEpFinishing.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_WipEpFinishing.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_WipEpFinishing.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_WipEpFinishing.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_WipEpFinishing.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_WipEpFinishing.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_WipEpFinishing.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Wk_WipEpFinishing.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Wk_WipEpFinishing.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_WipEpFinishing.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_WipEpFinishing.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_WipEpFinishing.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_WipEpFinishing.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_WipEpFinishing.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_WipEpFinishing.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_WipEpFinishing.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_WipEpFinishing.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_WipEpFinishing.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_WipEpFinishing.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_WipEpFinishing.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_WipEpFinishing.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_WipEpFinishing.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Wk_WipEpFinishing.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_WipEpFinishing.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_WipEpFinishing.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_WipEpFinishing.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_WipEpFinishing.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_WipEpFinishing.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_WipEpFinishing.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_WipEpFinishing.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Wk_WipEpFinishing.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Wk_WipEpFinishing.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_WipEpFinishing.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_WipEpFinishing.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_WipEpFinishing.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_WipEpFinishing.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_WipEpFinishing.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_WipEpFinishing.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_WipEpFinishing.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_WipEpFinishing.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_WipEpFinishing.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_WipEpFinishing.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_WipEpFinishing.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_WipEpFinishing(id, RefNo, BillType, BillDate, Groupid, BillTitle, Operator, Summary, CreateBy,
                                      CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid,
                                      AssessDate, BillTaxAmount, BillAmount, ItemCount, DisannulCount, FinishCount,
                                      PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7,
                                      Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{groupid}, #{billtitle}, #{operator}, #{summary},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor},
                #{assessorid}, #{assessdate}, #{billtaxamount}, #{billamount}, #{itemcount}, #{disannulcount},
                #{finishcount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipEpFinishing
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WipEpFinishing
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Wk_WipEpFinishing
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--刷新打印次数-->
    <update id="updatePrintcount">
        update Wk_WipEpFinishing
        SET PrintCount = #{printcount}
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>    
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.manu.domain.pojo.WkWipepfinishingPojo">
        select
        id
        from Wk_WipEpFinishingItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateEpibFinish">
        update Wk_WipEpiboleItem
        SET FinishQty =COALESCE((SELECT sum(Wk_WipEpFinishingItem.quantity)
                                 FROM Wk_WipEpFinishingItem
                                          LEFT OUTER JOIN Wk_WipEpFinishing
                                                          ON Wk_WipEpFinishingItem.pid = Wk_WipEpFinishing.id
                                 where Wk_WipEpFinishingItem.citeUid = #{refno}
                                   and Wk_WipEpFinishingItem.citeitemid = #{key}
                                   and Wk_WipEpFinishingItem.DisannulMark = 0
                                   and Wk_WipEpFinishingItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateEpibFinishCount">
        update Wk_WipEpibole
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_WipEpiboleItem
                                   where Wk_WipEpiboleItem.Pid = (SELECT Pid FROM Wk_WipEpiboleItem where id = #{key})
                                     and Wk_WipEpiboleItem.Tenantid = #{tid}
                                     and (Wk_WipEpiboleItem.Closed = 1
                                       or Wk_WipEpiboleItem.FinishQty >= Wk_WipEpiboleItem.Quantity)), 0)
        where id = (SELECT Pid FROM Wk_WipEpiboleItem where id = #{key})
          and Tenantid = #{tid}
    </update>
</mapper>

