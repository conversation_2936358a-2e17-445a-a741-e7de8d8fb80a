<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSmtpartrecMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSmtpartrecPojo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_SmtPartRec.id,
        Wk_SmtPartRec.RefNo,
        Wk_SmtPartRec.BillType,
        Wk_SmtPartRec.BillTitle,
        Wk_SmtPartRec.BillDate,
        Wk_SmtPartRec.PartUid,
        Wk_SmtPartRec.PartItemid,
        Wk_SmtPartRec.WorkUid,
        Wk_SmtPartRec.WorkItemid,
        Wk_SmtPartRec.MachUid,
        Wk_SmtPartRec.MachItemid,
        Wk_SmtPartRec.MachGroupid,
        Wk_SmtPartRec.DevCode,
        Wk_SmtPartRec.FeederNum,
        Wk_SmtPartRec.FeederCode,
        Wk_SmtPartRec.StationNum,
        Wk_SmtPartRec.StationCode,
        Wk_SmtPartRec.PointMark,
        Wk_SmtPartRec.Goodsid,
        Wk_SmtPartRec.PrevPackSn,
        Wk_SmtPartRec.PackSn,
        Wk_SmtPartRec.BatchNo,
        Wk_SmtPartRec.ItemCode,
        Wk_SmtPartRec.ItemName,
        Wk_SmtPartRec.ItemSpec,
        Wk_SmtPartRec.ItemUnit,
        Wk_SmtPartRec.SingleQty,
        Wk_SmtPartRec.Quantity,
        Wk_SmtPartRec.Operatorid,
        Wk_SmtPartRec.Operator,
        Wk_SmtPartRec.RowNum,
        Wk_SmtPartRec.QcFeederCode,
        Wk_SmtPartRec.QcStationCode,
        Wk_SmtPartRec.QcPrevPackSn,
        Wk_SmtPartRec.QcPackSn,
        Wk_SmtPartRec.QcStatusNum,
        Wk_SmtPartRec.Inspector,
        Wk_SmtPartRec.Inspectorid,
        Wk_SmtPartRec.InspDate,
        Wk_SmtPartRec.Remark,
        Wk_SmtPartRec.CreateBy,
        Wk_SmtPartRec.CreateByid,
        Wk_SmtPartRec.CreateDate,
        Wk_SmtPartRec.Lister,
        Wk_SmtPartRec.Listerid,
        Wk_SmtPartRec.ModifyDate,
        Wk_SmtPartRec.Custom1,
        Wk_SmtPartRec.Custom2,
        Wk_SmtPartRec.Custom3,
        Wk_SmtPartRec.Custom4,
        Wk_SmtPartRec.Custom5,
        Wk_SmtPartRec.Custom6,
        Wk_SmtPartRec.Custom7,
        Wk_SmtPartRec.Custom8,
        Wk_SmtPartRec.Custom9,
        Wk_SmtPartRec.Custom10,
        Wk_SmtPartRec.Tenantid,
        Wk_SmtPartRec.TenantName,
        Wk_SmtPartRec.Revision
        FROM Wk_SmtPartRec
        LEFT JOIN Mat_Goods ON Wk_SmtPartRec.Goodsid = Mat_Goods.id
        where Wk_SmtPartRec.id = #{key}
        and Wk_SmtPartRec.Tenantid = #{tid}
    </select>
    <sql id="selectWkSmtpartrecVo">
        SELECT
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
        Wk_SmtPartRec.id,
        Wk_SmtPartRec.RefNo,
        Wk_SmtPartRec.BillType,
        Wk_SmtPartRec.BillTitle,
        Wk_SmtPartRec.BillDate,
        Wk_SmtPartRec.PartUid,
        Wk_SmtPartRec.PartItemid,
        Wk_SmtPartRec.WorkUid,
        Wk_SmtPartRec.WorkItemid,
        Wk_SmtPartRec.MachUid,
        Wk_SmtPartRec.MachItemid,
        Wk_SmtPartRec.MachGroupid,
        Wk_SmtPartRec.DevCode,
        Wk_SmtPartRec.FeederNum,
        Wk_SmtPartRec.FeederCode,
        Wk_SmtPartRec.StationNum,
        Wk_SmtPartRec.StationCode,
        Wk_SmtPartRec.PointMark,
        Wk_SmtPartRec.Goodsid,
        Wk_SmtPartRec.PrevPackSn,
        Wk_SmtPartRec.PackSn,
        Wk_SmtPartRec.BatchNo,
        Wk_SmtPartRec.ItemCode,
        Wk_SmtPartRec.ItemName,
        Wk_SmtPartRec.ItemSpec,
        Wk_SmtPartRec.ItemUnit,
        Wk_SmtPartRec.SingleQty,
        Wk_SmtPartRec.Quantity,
        Wk_SmtPartRec.Operatorid,
        Wk_SmtPartRec.Operator,
        Wk_SmtPartRec.RowNum,
        Wk_SmtPartRec.QcFeederCode,
        Wk_SmtPartRec.QcStationCode,
        Wk_SmtPartRec.QcPrevPackSn,
        Wk_SmtPartRec.QcPackSn,
        Wk_SmtPartRec.QcStatusNum,
        Wk_SmtPartRec.Inspector,
        Wk_SmtPartRec.Inspectorid,
        Wk_SmtPartRec.InspDate,
        Wk_SmtPartRec.Remark,
        Wk_SmtPartRec.CreateBy,
        Wk_SmtPartRec.CreateByid,
        Wk_SmtPartRec.CreateDate,
        Wk_SmtPartRec.Lister,
        Wk_SmtPartRec.Listerid,
        Wk_SmtPartRec.ModifyDate,
        Wk_SmtPartRec.Custom1,
        Wk_SmtPartRec.Custom2,
        Wk_SmtPartRec.Custom3,
        Wk_SmtPartRec.Custom4,
        Wk_SmtPartRec.Custom5,
        Wk_SmtPartRec.Custom6,
        Wk_SmtPartRec.Custom7,
        Wk_SmtPartRec.Custom8,
        Wk_SmtPartRec.Custom9,
        Wk_SmtPartRec.Custom10,
        Wk_SmtPartRec.Tenantid,
        Wk_SmtPartRec.TenantName,
        Wk_SmtPartRec.Revision
        FROM Wk_SmtPartRec
        LEFT JOIN Mat_Goods ON Wk_SmtPartRec.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkSmtpartrecPojo">
        <include refid="selectWkSmtpartrecVo"/>
        where 1 = 1 and Wk_SmtPartRec.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_SmtPartRec.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Wk_SmtPartRec.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Wk_SmtPartRec.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Wk_SmtPartRec.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.partuid != null">
            and Wk_SmtPartRec.PartUid like concat('%', #{SearchPojo.partuid}, '%')
        </if>
        <if test="SearchPojo.partitemid != null">
            and Wk_SmtPartRec.PartItemid like concat('%', #{SearchPojo.partitemid}, '%')
        </if>
        <if test="SearchPojo.workuid != null">
            and Wk_SmtPartRec.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null">
            and Wk_SmtPartRec.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.machuid != null">
            and Wk_SmtPartRec.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null">
            and Wk_SmtPartRec.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null">
            and Wk_SmtPartRec.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.devcode != null">
            and Wk_SmtPartRec.DevCode like concat('%', #{SearchPojo.devcode}, '%')
        </if>
        <if test="SearchPojo.feedercode != null">
            and Wk_SmtPartRec.FeederCode like concat('%', #{SearchPojo.feedercode}, '%')
        </if>
        <if test="SearchPojo.stationcode != null">
            and Wk_SmtPartRec.StationCode like concat('%', #{SearchPojo.stationcode}, '%')
        </if>
        <if test="SearchPojo.pointmark != null">
            and Wk_SmtPartRec.PointMark like concat('%', #{SearchPojo.pointmark}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Wk_SmtPartRec.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.prevpacksn != null">
            and Wk_SmtPartRec.PrevPackSn like concat('%', #{SearchPojo.prevpacksn}, '%')
        </if>
        <if test="SearchPojo.packsn != null">
            and Wk_SmtPartRec.PackSn like concat('%', #{SearchPojo.packsn}, '%')
        </if>
        <if test="SearchPojo.batchno != null">
            and Wk_SmtPartRec.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.itemcode != null">
            and Wk_SmtPartRec.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null">
            and Wk_SmtPartRec.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null">
            and Wk_SmtPartRec.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null">
            and Wk_SmtPartRec.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Wk_SmtPartRec.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Wk_SmtPartRec.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.qcfeedercode != null">
            and Wk_SmtPartRec.QcFeederCode like concat('%', #{SearchPojo.qcfeedercode}, '%')
        </if>
        <if test="SearchPojo.qcstationcode != null">
            and Wk_SmtPartRec.QcStationCode like concat('%', #{SearchPojo.qcstationcode}, '%')
        </if>
        <if test="SearchPojo.qcprevpacksn != null">
            and Wk_SmtPartRec.QcPrevPackSn like concat('%', #{SearchPojo.qcprevpacksn}, '%')
        </if>
        <if test="SearchPojo.qcpacksn != null">
            and Wk_SmtPartRec.QcPackSn like concat('%', #{SearchPojo.qcpacksn}, '%')
        </if>
        <if test="SearchPojo.qcstatusnum != null">
            and Wk_SmtPartRec.QcStatusNum=#{SearchPojo.qcstatusnum}
        </if>
        <if test="SearchPojo.inspector != null">
            and Wk_SmtPartRec.Inspector like concat('%', #{SearchPojo.inspector}, '%')
        </if>
        <if test="SearchPojo.inspectorid != null">
            and Wk_SmtPartRec.Inspectorid =#{SearchPojo.inspectorid}
        </if>
        <if test="SearchPojo.remark != null">
            and Wk_SmtPartRec.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Wk_SmtPartRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Wk_SmtPartRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Wk_SmtPartRec.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Wk_SmtPartRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Wk_SmtPartRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Wk_SmtPartRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Wk_SmtPartRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Wk_SmtPartRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Wk_SmtPartRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Wk_SmtPartRec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Wk_SmtPartRec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Wk_SmtPartRec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Wk_SmtPartRec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Wk_SmtPartRec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Wk_SmtPartRec.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Wk_SmtPartRec.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Wk_SmtPartRec.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Wk_SmtPartRec.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.partuid != null">
                or Wk_SmtPartRec.PartUid like concat('%', #{SearchPojo.partuid}, '%')
            </if>
            <if test="SearchPojo.partitemid != null">
                or Wk_SmtPartRec.PartItemid like concat('%', #{SearchPojo.partitemid}, '%')
            </if>
            <if test="SearchPojo.workuid != null">
                or Wk_SmtPartRec.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workitemid != null">
                or Wk_SmtPartRec.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.machuid != null">
                or Wk_SmtPartRec.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null">
                or Wk_SmtPartRec.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null">
                or Wk_SmtPartRec.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.devcode != null">
                or Wk_SmtPartRec.DevCode like concat('%', #{SearchPojo.devcode}, '%')
            </if>
            <if test="SearchPojo.feedercode != null">
                or Wk_SmtPartRec.FeederCode like concat('%', #{SearchPojo.feedercode}, '%')
            </if>
            <if test="SearchPojo.stationcode != null">
                or Wk_SmtPartRec.StationCode like concat('%', #{SearchPojo.stationcode}, '%')
            </if>
            <if test="SearchPojo.pointmark != null">
                or Wk_SmtPartRec.PointMark like concat('%', #{SearchPojo.pointmark}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Wk_SmtPartRec.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.prevpacksn != null">
                or Wk_SmtPartRec.PrevPackSn like concat('%', #{SearchPojo.prevpacksn}, '%')
            </if>
            <if test="SearchPojo.packsn != null">
                or Wk_SmtPartRec.PackSn like concat('%', #{SearchPojo.packsn}, '%')
            </if>
            <if test="SearchPojo.batchno != null">
                or Wk_SmtPartRec.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.itemcode != null">
                or Wk_SmtPartRec.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null">
                or Wk_SmtPartRec.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null">
                or Wk_SmtPartRec.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null">
                or Wk_SmtPartRec.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Wk_SmtPartRec.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Wk_SmtPartRec.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.qcfeedercode != null">
                or Wk_SmtPartRec.QcFeederCode like concat('%', #{SearchPojo.qcfeedercode}, '%')
            </if>
            <if test="SearchPojo.qcstationcode != null">
                or Wk_SmtPartRec.QcStationCode like concat('%', #{SearchPojo.qcstationcode}, '%')
            </if>
            <if test="SearchPojo.qcprevpacksn != null">
                or Wk_SmtPartRec.QcPrevPackSn like concat('%', #{SearchPojo.qcprevpacksn}, '%')
            </if>
            <if test="SearchPojo.qcpacksn != null">
                or Wk_SmtPartRec.QcPackSn like concat('%', #{SearchPojo.qcpacksn}, '%')
            </if>
            <if test="SearchPojo.inspector != null">
                or Wk_SmtPartRec.Inspector like concat('%', #{SearchPojo.inspector}, '%')
            </if>
            <if test="SearchPojo.inspectorid != null">
                or Wk_SmtPartRec.Inspectorid like concat('%', #{SearchPojo.inspectorid}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Wk_SmtPartRec.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Wk_SmtPartRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Wk_SmtPartRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Wk_SmtPartRec.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Wk_SmtPartRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Wk_SmtPartRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Wk_SmtPartRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Wk_SmtPartRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Wk_SmtPartRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Wk_SmtPartRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Wk_SmtPartRec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Wk_SmtPartRec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Wk_SmtPartRec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Wk_SmtPartRec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Wk_SmtPartRec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Wk_SmtPartRec.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_SmtPartRec(id, RefNo, BillType, BillTitle, BillDate, PartUid, PartItemid, WorkUid, WorkItemid,
        MachUid, MachItemid, MachGroupid, DevCode, FeederNum, FeederCode, StationNum,
        StationCode, PointMark, Goodsid, PrevPackSn, PackSn, BatchNo, ItemCode, ItemName,
        ItemSpec, ItemUnit, SingleQty, Quantity, Operatorid, Operator, RowNum, QcFeederCode,
        QcStationCode, QcPrevPackSn, QcPackSn, QcStatusNum, Inspector, Inspectorid, InspDate,
        Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1,
        Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
        Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{partuid}, #{partitemid}, #{workuid},
        #{workitemid}, #{machuid}, #{machitemid}, #{machgroupid}, #{devcode}, #{feedernum}, #{feedercode},
        #{stationnum}, #{stationcode}, #{pointmark}, #{goodsid}, #{prevpacksn}, #{packsn}, #{batchno},
        #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{singleqty}, #{quantity}, #{operatorid},
        #{operator}, #{rownum}, #{qcfeedercode}, #{qcstationcode}, #{qcprevpacksn}, #{qcpacksn}, #{qcstatusnum},
        #{inspector}, #{inspectorid}, #{inspdate}, #{remark}, #{createby}, #{createbyid}, #{createdate},
        #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
        #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_SmtPartRec
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="partuid != null">
                PartUid =#{partuid},
            </if>
            <if test="partitemid != null">
                PartItemid =#{partitemid},
            </if>
            <if test="workuid != null">
                WorkUid =#{workuid},
            </if>
            <if test="workitemid != null">
                WorkItemid =#{workitemid},
            </if>
            <if test="machuid != null">
                MachUid =#{machuid},
            </if>
            <if test="machitemid != null">
                MachItemid =#{machitemid},
            </if>
            <if test="machgroupid != null">
                MachGroupid =#{machgroupid},
            </if>
            <if test="devcode != null">
                DevCode =#{devcode},
            </if>
            <if test="feedernum != null">
                FeederNum =#{feedernum},
            </if>
            <if test="feedercode != null">
                FeederCode =#{feedercode},
            </if>
            <if test="stationnum != null">
                StationNum =#{stationnum},
            </if>
            <if test="stationcode != null">
                StationCode =#{stationcode},
            </if>
            <if test="pointmark != null">
                PointMark =#{pointmark},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="prevpacksn != null">
                PrevPackSn =#{prevpacksn},
            </if>
            <if test="packsn != null">
                PackSn =#{packsn},
            </if>
            <if test="batchno != null">
                BatchNo =#{batchno},
            </if>
            <if test="itemcode != null">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null">
                ItemUnit =#{itemunit},
            </if>
            <if test="singleqty != null">
                SingleQty =#{singleqty},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="operatorid != null">
                Operatorid =#{operatorid},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="qcfeedercode != null">
                QcFeederCode =#{qcfeedercode},
            </if>
            <if test="qcstationcode != null">
                QcStationCode =#{qcstationcode},
            </if>
            <if test="qcprevpacksn != null">
                QcPrevPackSn =#{qcprevpacksn},
            </if>
            <if test="qcpacksn != null">
                QcPackSn =#{qcpacksn},
            </if>
            <if test="qcstatusnum != null">
                QcStatusNum =#{qcstatusnum},
            </if>
            <if test="inspector != null">
                Inspector =#{inspector},
            </if>
            <if test="inspectorid != null">
                Inspectorid =#{inspectorid},
            </if>
            <if test="inspdate != null">
                InspDate =#{inspdate},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_SmtPartRec
        where id = #{key}
        and Tenantid = #{tid}
    </delete>


    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updatePartFinish">
        update Wk_SmtPartItem
        SET FinishQty =COALESCE((SELECT SUM(Wk_SmtPartRec.quantity)
        FROM Wk_SmtPartRec
        where (QcStatusNum = 0 or QcStatusNum = 1)
        and Wk_SmtPartRec.PartUid = #{refno}
        and Wk_SmtPartRec.PartItemid = #{key}
        and Wk_SmtPartRec.Tenantid = #{tid}), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updatePartAccept">
        update Wk_SmtPartItem
        SET AcceptQty =COALESCE((SELECT SUM(Wk_SmtPartRec.quantity)
        FROM Wk_SmtPartRec
        where Wk_SmtPartRec.QcStatusNum = 1
        and Wk_SmtPartRec.PartUid = #{refno}
        and Wk_SmtPartRec.PartItemid = #{key}
        and Wk_SmtPartRec.Tenantid = #{tid}), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>
</mapper>

