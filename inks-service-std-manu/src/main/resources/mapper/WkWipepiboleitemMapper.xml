<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipepiboleitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipepiboleitemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_WipEpiboleItem.id,
            Wk_WipEpiboleItem.Pid,
            Wk_WipEpiboleItem.WipItemid,
            Wk_WipEpiboleItem.Wsid,
            Wk_WipEpiboleItem.WsUid,
            Wk_WipEpiboleItem.Wpid,
            Wk_WipEpiboleItem.EndWpid,
            Wk_WipEpiboleItem.Goodsid,
            Wk_WipEpiboleItem.SubItemid,
            Wk_WipEpiboleItem.SubUse,
            Wk_WipEpiboleItem.SubUnit,
            Wk_WipEpiboleItem.SubQty,
            Wk_WipEpiboleItem.TaxPrice,
            Wk_WipEpiboleItem.TaxAmount,
            Wk_WipEpiboleItem.TaxTotal,
            Wk_WipEpiboleItem.Price,
            Wk_WipEpiboleItem.Amount,
            Wk_WipEpiboleItem.ItemTaxrate,
            Wk_WipEpiboleItem.StartDate,
            Wk_WipEpiboleItem.PlanDate,
            Wk_WipEpiboleItem.Quantity,
            Wk_WipEpiboleItem.RequQty,
            Wk_WipEpiboleItem.FinishQty,
            Wk_WipEpiboleItem.MrbQty,
            Wk_WipEpiboleItem.Closed,
            Wk_WipEpiboleItem.Remark,
            Wk_WipEpiboleItem.VirtualItem,
            Wk_WipEpiboleItem.RowNum,
            Wk_WipEpiboleItem.WkWpName,
            Wk_WipEpiboleItem.Customer,
            Wk_WipEpiboleItem.CustPO,
            Wk_WipEpiboleItem.MachUid,
            Wk_WipEpiboleItem.MachGroupid,
            Wk_WipEpiboleItem.MachItemid,
            Wk_WipEpiboleItem.MainPlanUid,
            Wk_WipEpiboleItem.MainPlanItemid,
            Wk_WipEpiboleItem.StateCode,
            Wk_WipEpiboleItem.StateDate,
            Wk_WipEpiboleItem.DisannulListerid,
            Wk_WipEpiboleItem.DisannulLister,
            Wk_WipEpiboleItem.DisannulDate,
            Wk_WipEpiboleItem.DisannulMark,
            Wk_WipEpiboleItem.AttributeJson,
            Wk_WipEpiboleItem.Custom1,
            Wk_WipEpiboleItem.Custom2,
            Wk_WipEpiboleItem.Custom3,
            Wk_WipEpiboleItem.Custom4,
            Wk_WipEpiboleItem.Custom5,
            Wk_WipEpiboleItem.Custom6,
            Wk_WipEpiboleItem.Custom7,
            Wk_WipEpiboleItem.Custom8,
            Wk_WipEpiboleItem.Custom9,
            Wk_WipEpiboleItem.Custom10,
            Wk_WipEpiboleItem.Tenantid,
            Wk_WipEpiboleItem.Revision
        FROM
            Wk_WipEpiboleItem
                LEFT JOIN Mat_Goods ON Wk_WipEpiboleItem.Goodsid = Mat_Goods.id
        where Wk_WipEpiboleItem.id = #{key}
          and Wk_WipEpiboleItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkWipepiboleitemVo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_WipEpiboleItem.id,
            Wk_WipEpiboleItem.Pid,
            Wk_WipEpiboleItem.WipItemid,
            Wk_WipEpiboleItem.Wsid,
            Wk_WipEpiboleItem.WsUid,
            Wk_WipEpiboleItem.Wpid,
            Wk_WipEpiboleItem.EndWpid,
            Wk_WipEpiboleItem.Goodsid,
            Wk_WipEpiboleItem.SubItemid,
            Wk_WipEpiboleItem.SubUse,
            Wk_WipEpiboleItem.SubUnit,
            Wk_WipEpiboleItem.SubQty,
            Wk_WipEpiboleItem.TaxPrice,
            Wk_WipEpiboleItem.TaxAmount,
            Wk_WipEpiboleItem.TaxTotal,
            Wk_WipEpiboleItem.Price,
            Wk_WipEpiboleItem.Amount,
            Wk_WipEpiboleItem.ItemTaxrate,
            Wk_WipEpiboleItem.StartDate,
            Wk_WipEpiboleItem.PlanDate,
            Wk_WipEpiboleItem.Quantity,
            Wk_WipEpiboleItem.RequQty,
            Wk_WipEpiboleItem.FinishQty,
            Wk_WipEpiboleItem.MrbQty,
            Wk_WipEpiboleItem.Closed,
            Wk_WipEpiboleItem.Remark,
            Wk_WipEpiboleItem.VirtualItem,
            Wk_WipEpiboleItem.RowNum,
            Wk_WipEpiboleItem.WkWpName,
            Wk_WipEpiboleItem.Customer,
            Wk_WipEpiboleItem.CustPO,
            Wk_WipEpiboleItem.MachUid,
            Wk_WipEpiboleItem.MachGroupid,
            Wk_WipEpiboleItem.MachItemid,
            Wk_WipEpiboleItem.MainPlanUid,
            Wk_WipEpiboleItem.MainPlanItemid,
            Wk_WipEpiboleItem.StateCode,
            Wk_WipEpiboleItem.StateDate,
            Wk_WipEpiboleItem.DisannulListerid,
            Wk_WipEpiboleItem.DisannulLister,
            Wk_WipEpiboleItem.DisannulDate,
            Wk_WipEpiboleItem.DisannulMark,
            Wk_WipEpiboleItem.AttributeJson,
            Wk_WipEpiboleItem.Custom1,
            Wk_WipEpiboleItem.Custom2,
            Wk_WipEpiboleItem.Custom3,
            Wk_WipEpiboleItem.Custom4,
            Wk_WipEpiboleItem.Custom5,
            Wk_WipEpiboleItem.Custom6,
            Wk_WipEpiboleItem.Custom7,
            Wk_WipEpiboleItem.Custom8,
            Wk_WipEpiboleItem.Custom9,
            Wk_WipEpiboleItem.Custom10,
            Wk_WipEpiboleItem.Tenantid,
            Wk_WipEpiboleItem.Revision
        FROM
            Wk_WipEpiboleItem
                LEFT JOIN Mat_Goods ON Wk_WipEpiboleItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipepiboleitemPojo">
        <include refid="selectWkWipepiboleitemVo"/>
        where 1 = 1 and Wk_WipEpiboleItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_WipEpiboleItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_WipEpiboleItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.wipitemid != null and SearchPojo.wipitemid != ''">
            and Wk_WipEpiboleItem.wipitemid like concat('%', #{SearchPojo.wipitemid}, '%')
        </if>
        <if test="SearchPojo.wsid != null and SearchPojo.wsid != ''">
            and Wk_WipEpiboleItem.wsid like concat('%', #{SearchPojo.wsid}, '%')
        </if>
        <if test="SearchPojo.wsuid != null and SearchPojo.wsuid != ''">
            and Wk_WipEpiboleItem.wsuid like concat('%', #{SearchPojo.wsuid}, '%')
        </if>
        <if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
            and Wk_WipEpiboleItem.wpid like concat('%', #{SearchPojo.wpid}, '%')
        </if>
        <if test="SearchPojo.endwpid != null and SearchPojo.endwpid != ''">
            and Wk_WipEpiboleItem.endwpid like concat('%', #{SearchPojo.endwpid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_WipEpiboleItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
            and Wk_WipEpiboleItem.subitemid like concat('%', #{SearchPojo.subitemid}, '%')
        </if>
        <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
            and Wk_WipEpiboleItem.subuse like concat('%', #{SearchPojo.subuse}, '%')
        </if>
        <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
            and Wk_WipEpiboleItem.subunit like concat('%', #{SearchPojo.subunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_WipEpiboleItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
            and Wk_WipEpiboleItem.wkwpname like concat('%', #{SearchPojo.wkwpname}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_WipEpiboleItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_WipEpiboleItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_WipEpiboleItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_WipEpiboleItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_WipEpiboleItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_WipEpiboleItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_WipEpiboleItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Wk_WipEpiboleItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Wk_WipEpiboleItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Wk_WipEpiboleItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_WipEpiboleItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_WipEpiboleItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_WipEpiboleItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_WipEpiboleItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_WipEpiboleItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_WipEpiboleItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_WipEpiboleItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_WipEpiboleItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_WipEpiboleItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_WipEpiboleItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_WipEpiboleItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_WipEpiboleItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.wipitemid != null and SearchPojo.wipitemid != ''">
                or Wk_WipEpiboleItem.WipItemid like concat('%', #{SearchPojo.wipitemid}, '%')
            </if>
            <if test="SearchPojo.wsid != null and SearchPojo.wsid != ''">
                or Wk_WipEpiboleItem.Wsid like concat('%', #{SearchPojo.wsid}, '%')
            </if>
            <if test="SearchPojo.wsuid != null and SearchPojo.wsuid != ''">
                or Wk_WipEpiboleItem.WsUid like concat('%', #{SearchPojo.wsuid}, '%')
            </if>
            <if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
                or Wk_WipEpiboleItem.Wpid like concat('%', #{SearchPojo.wpid}, '%')
            </if>
            <if test="SearchPojo.endwpid != null and SearchPojo.endwpid != ''">
                or Wk_WipEpiboleItem.EndWpid like concat('%', #{SearchPojo.endwpid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_WipEpiboleItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.subitemid != null and SearchPojo.subitemid != ''">
                or Wk_WipEpiboleItem.SubItemid like concat('%', #{SearchPojo.subitemid}, '%')
            </if>
            <if test="SearchPojo.subuse != null and SearchPojo.subuse != ''">
                or Wk_WipEpiboleItem.SubUse like concat('%', #{SearchPojo.subuse}, '%')
            </if>
            <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
                or Wk_WipEpiboleItem.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_WipEpiboleItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
                or Wk_WipEpiboleItem.WkWpName like concat('%', #{SearchPojo.wkwpname}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_WipEpiboleItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_WipEpiboleItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_WipEpiboleItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_WipEpiboleItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_WipEpiboleItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_WipEpiboleItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_WipEpiboleItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Wk_WipEpiboleItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Wk_WipEpiboleItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Wk_WipEpiboleItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_WipEpiboleItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_WipEpiboleItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_WipEpiboleItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_WipEpiboleItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_WipEpiboleItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_WipEpiboleItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_WipEpiboleItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_WipEpiboleItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_WipEpiboleItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_WipEpiboleItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_WipEpiboleItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkWipepiboleitemPojo">
        SELECT <include refid="inks.service.std.manu.mapper.manu_ExtFilterMapper.selectGoods"/>
            Wk_WipEpiboleItem.id,
            Wk_WipEpiboleItem.Pid,
            Wk_WipEpiboleItem.WipItemid,
            Wk_WipEpiboleItem.Wsid,
            Wk_WipEpiboleItem.WsUid,
            Wk_WipEpiboleItem.Wpid,
            Wk_WipEpiboleItem.EndWpid,
            Wk_WipEpiboleItem.Goodsid,
            Wk_WipEpiboleItem.SubItemid,
            Wk_WipEpiboleItem.SubUse,
            Wk_WipEpiboleItem.SubUnit,
            Wk_WipEpiboleItem.SubQty,
            Wk_WipEpiboleItem.TaxPrice,
            Wk_WipEpiboleItem.TaxAmount,
            Wk_WipEpiboleItem.TaxTotal,
            Wk_WipEpiboleItem.Price,
            Wk_WipEpiboleItem.Amount,
            Wk_WipEpiboleItem.ItemTaxrate,
            Wk_WipEpiboleItem.StartDate,
            Wk_WipEpiboleItem.PlanDate,
            Wk_WipEpiboleItem.Quantity,
            Wk_WipEpiboleItem.RequQty,
            Wk_WipEpiboleItem.FinishQty,
            Wk_WipEpiboleItem.MrbQty,
            Wk_WipEpiboleItem.Closed,
            Wk_WipEpiboleItem.Remark,
            Wk_WipEpiboleItem.VirtualItem,
            Wk_WipEpiboleItem.RowNum,
            Wk_WipEpiboleItem.WkWpName,
            Wk_WipEpiboleItem.Customer,
            Wk_WipEpiboleItem.CustPO,
            Wk_WipEpiboleItem.MachUid,
            Wk_WipEpiboleItem.MachGroupid,
            Wk_WipEpiboleItem.MachItemid,
            Wk_WipEpiboleItem.MainPlanUid,
            Wk_WipEpiboleItem.MainPlanItemid,
            Wk_WipEpiboleItem.StateCode,
            Wk_WipEpiboleItem.StateDate,
            Wk_WipEpiboleItem.DisannulListerid,
            Wk_WipEpiboleItem.DisannulLister,
            Wk_WipEpiboleItem.DisannulDate,
            Wk_WipEpiboleItem.DisannulMark,
            Wk_WipEpiboleItem.AttributeJson,
            Wk_WipEpiboleItem.Custom1,
            Wk_WipEpiboleItem.Custom2,
            Wk_WipEpiboleItem.Custom3,
            Wk_WipEpiboleItem.Custom4,
            Wk_WipEpiboleItem.Custom5,
            Wk_WipEpiboleItem.Custom6,
            Wk_WipEpiboleItem.Custom7,
            Wk_WipEpiboleItem.Custom8,
            Wk_WipEpiboleItem.Custom9,
            Wk_WipEpiboleItem.Custom10,
            Wk_WipEpiboleItem.Tenantid,
            Wk_WipEpiboleItem.Revision
        FROM
            Wk_WipEpiboleItem
                LEFT JOIN Mat_Goods ON Wk_WipEpiboleItem.Goodsid = Mat_Goods.id
        where Wk_WipEpiboleItem.Pid = #{Pid}
          and Wk_WipEpiboleItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_WipEpiboleItem(id, Pid, WipItemid, Wsid, WsUid, Wpid, EndWpid, Goodsid, SubItemid, SubUse,
                                      SubUnit, SubQty, TaxPrice, TaxAmount, TaxTotal, Price, Amount, ItemTaxrate,
                                      StartDate, PlanDate, Quantity, RequQty, FinishQty, MrbQty, Closed, Remark,
                                      VirtualItem, RowNum, WkWpName, Customer, CustPO, MachUid, MachGroupid, MachItemid,
                                      MainPlanUid, MainPlanItemid, StateCode, StateDate, DisannulListerid,
                                      DisannulLister, DisannulDate, DisannulMark, AttributeJson, Custom1, Custom2,
                                      Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                      Revision)
        values (#{id}, #{pid}, #{wipitemid}, #{wsid}, #{wsuid}, #{wpid}, #{endwpid}, #{goodsid}, #{subitemid},
                #{subuse}, #{subunit}, #{subqty}, #{taxprice}, #{taxamount}, #{taxtotal}, #{price}, #{amount},
                #{itemtaxrate}, #{startdate}, #{plandate}, #{quantity}, #{requqty}, #{finishqty}, #{mrbqty}, #{closed},
                #{remark}, #{virtualitem}, #{rownum}, #{wkwpname}, #{customer}, #{custpo}, #{machuid}, #{machgroupid},
                #{machitemid}, #{mainplanuid}, #{mainplanitemid}, #{statecode}, #{statedate}, #{disannullisterid},
                #{disannullister}, #{disannuldate}, #{disannulmark}, #{attributejson}, #{custom1}, #{custom2},
                #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10},
                #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipEpiboleItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="wipitemid != null ">
                WipItemid = #{wipitemid},
            </if>
            <if test="wsid != null ">
                Wsid = #{wsid},
            </if>
            <if test="wsuid != null ">
                WsUid = #{wsuid},
            </if>
            <if test="wpid != null ">
                Wpid = #{wpid},
            </if>
            <if test="endwpid != null ">
                EndWpid = #{endwpid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="subitemid != null ">
                SubItemid = #{subitemid},
            </if>
            <if test="subuse != null ">
                SubUse = #{subuse},
            </if>
            <if test="subunit != null ">
                SubUnit = #{subunit},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="requqty != null">
                RequQty = #{requqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="mrbqty != null">
                MrbQty = #{mrbqty},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="wkwpname != null ">
                WkWpName = #{wkwpname},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WipEpiboleItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

