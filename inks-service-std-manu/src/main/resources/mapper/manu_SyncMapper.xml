<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.manu_SyncMapper">

<!--    最后更新时间：2024-05-21    -->
<!--    =============================================同步货品数量  ======================================-->
    <!--    刷新销售待出数 Eric 20211213-->
    <update id="updateGoodsBusRemQty">
        update Mat_Goods
        SET BusRemQty = COALESCE((SELECT SUM(Bus_MachiningItem.Quantity - Bus_MachiningItem.FinishQty)
                                  FROM Bus_MachiningItem
                                  where Bus_MachiningItem.Goodsid = #{key}
                                    and Bus_MachiningItem.Quantity &gt; Bus_MachiningItem.FinishQty
                                    and Bus_MachiningItem.Closed = 0
                                    and Bus_MachiningItem.DisannulMark = 0
                                    and Bus_MachiningItem.Tenantid = #{tid}), 0) +
                        COALESCE((SELECT SUM(IF(Bus_Deliery.BillType IN ('发出商品', '其他发货'),
                                                Bus_DelieryItem.Quantity - Bus_DelieryItem.FinishQty,
                                                Bus_DelieryItem.FinishQty - Bus_DelieryItem.Quantity))
                                  FROM Bus_DelieryItem
                                           LEFT OUTER JOIN Bus_Deliery ON Bus_DelieryItem.pid =
                                                                          Bus_Deliery.id
                                  where Bus_Deliery.BillType IN ('发出商品', '其他发货', '订单退货', '其他退货')
                                    and Bus_DelieryItem.Goodsid = #{key}
                                    and Bus_DelieryItem.Quantity &gt; Bus_DelieryItem.FinishQty
                                    and Bus_DelieryItem.FinishClosed = 0
                                    and Bus_DelieryItem.DisannulMark = 0
                                    and Bus_Deliery.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新收货待入数 Eric 20211213-->
    <update id="updateGoodsBuyRemQty">
        update Mat_Goods
        SET BuyRemQty = COALESCE((SELECT SUM(Buy_OrderItem.Quantity - Buy_OrderItem.FinishQty)
                                  FROM Buy_OrderItem
                                  where Buy_OrderItem.Goodsid = #{key}
                                    and Buy_OrderItem.Quantity &gt; Buy_OrderItem.FinishQty
                                    and Buy_OrderItem.Closed = 0
                                    and Buy_OrderItem.DisannulMark = 0
                                    and Buy_OrderItem.Tenantid = #{tid}), 0) +
                        COALESCE((SELECT SUM(IF(Buy_Finishing.BillType IN ('采购验收', '其他收货'),
                                                Buy_FinishingItem.Quantity - Buy_FinishingItem.FinishQty,
                                                Buy_FinishingItem.FinishQty -
                                                Buy_FinishingItem.Quantity))
                                  FROM Buy_FinishingItem
                                           LEFT OUTER JOIN Buy_Finishing ON Buy_FinishingItem.pid =
                                                                            Buy_Finishing.id
                                  where Buy_Finishing.BillType IN ('采购验收', '其他收货', '采购退货', '其他退货')
                                    and Buy_FinishingItem.Goodsid = #{key}
                                    and Buy_FinishingItem.Quantity &gt; Buy_FinishingItem.FinishQty
                                    and Buy_FinishingItem.Closed = 0
                                    and Buy_FinishingItem.DisannulMark = 0
                                    and Buy_Finishing.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新生产待入数   -->
    <update id="updateGoodsWkWsRemQty">
        update Mat_Goods
        SET WkWsRemQty =(SELECT COALESCE(sum(Wk_WorksheetItem.Quantity - Wk_WorksheetItem.FinishQty), 0) as Qty
                         FROM Wk_WorksheetItem
                         where Wk_WorksheetItem.Goodsid = #{key}
                           and Wk_WorksheetItem.Quantity
                             &gt; Wk_WorksheetItem.FinishQty
                           and Wk_WorksheetItem.Closed = 0
                           and Wk_WorksheetItem.DisannulMark = 0
                           and Wk_WorksheetItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新加工待入数   -->
    <update id="updateGoodsWkScRemQty">
        update Mat_Goods
        SET WkScRemQty =(SELECT COALESCE(sum(Wk_SubcontractItem.Quantity - Wk_SubcontractItem.FinishQty), 0) as Qty
                         FROM Wk_SubcontractItem
                         where Wk_SubcontractItem.Goodsid = #{key}
                           and Wk_SubcontractItem.Quantity
                             &gt; Wk_SubcontractItem.FinishQty
                           and Wk_SubcontractItem.Closed = 0
                           and Wk_SubcontractItem.DisannulMark = 0
                           and Wk_SubcontractItem.Tenantid = #{tid})
            + (SELECT COALESCE(sum(Wk_ScCompleteItem.Quantity - Wk_ScCompleteItem.FinishQty), 0) as Qty
               FROM Wk_ScCompleteItem
               where Wk_ScCompleteItem.Goodsid = #{key}
                 and Wk_ScCompleteItem.Quantity
                   &gt; Wk_ScCompleteItem.FinishQty
                 and Wk_ScCompleteItem.Closed = 0
                 and Wk_ScCompleteItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新领料待出数-->
    <update id="updateGoodsRequRemQty">
        update Mat_Goods
        SET RequRemQty =(SELECT COALESCE(SUM(IF(Mat_Requisition.BillType IN ('领料单', '生产领料'),
                                                Mat_RequisitionItem.Quantity - Mat_RequisitionItem.FinishQty,
                                                Mat_RequisitionItem.FinishQty -
                                                Mat_RequisitionItem.Quantity)),
                                         0) as Qty
                         FROM Mat_Requisition
                                  RIGHT JOIN Mat_RequisitionItem ON Mat_RequisitionItem.Pid = Mat_Requisition.id
                         where Mat_RequisitionItem.Goodsid = #{key}
                           and Mat_RequisitionItem.Closed = 0
                           and Mat_RequisitionItem.DisannulMark = 0
                           and Mat_RequisitionItem.Quantity
                             &gt; Mat_RequisitionItem.FinishQty
                           and Mat_Requisition.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新当前库存数和单价-->
    <update id="updateGoodsIvQuantity">
        update Mat_Goods
        SET IvQuantity =(select COALESCE(sum(Mat_Inventory.Quantity), 0) as Qty
                         from Mat_Inventory
                                  left join Mat_Storage on Mat_Inventory.Storeid = Mat_Storage.id
                         where Mat_Inventory.Goodsid = #{key}
                           and Mat_Inventory.Tenantid = #{tid}
                           and Mat_Storage.UsableMark = 1),
            AgePrice   =(select CASE
                                    WHEN COALESCE(sum(Mat_Inventory.Quantity), 0) = 0 THEN 0
                                    ELSE sum(Mat_Inventory.Amount) / sum(Mat_Inventory.Quantity) END
                         from Mat_Inventory
                                  left join Mat_Storage on Mat_Inventory.Storeid = Mat_Storage.id
                         where Mat_Inventory.Goodsid = #{key}
                           and Mat_Inventory.Tenantid = #{tid}
                           and Mat_Storage.UsableMark = 1)
        where id = #{key}
          and Tenantid = #{tid}
    </update>





<!--    =========================================同步客户各项余额===========================================-->
    <!--    刷新销售订单结余额-->
    <update id="updateWorkgroupBusMachRemAmt">
        update App_Workgroup
        SET BusMachRemAmt =(SELECT COALESCE(sum(Bus_Machining.BillTaxAmount - Bus_Machining.AdvaAmount), 0) as Qty
                            FROM Bus_Machining
                            where Bus_Machining.Groupid = #{key}
                              and Bus_Machining.ItemCount
                                &gt; Bus_Machining.FinishCount
                              and Bus_Machining.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--    刷新销售发货结余额-->
    <update id="updateWorkgroupBusDeliRemAmt">
        update App_Workgroup
        SET BusDeliRemAmt =(SELECT COALESCE(sum(Bus_DelieryItem.TaxAmount -
                                                (Bus_DelieryItem.Quantity - Bus_DelieryItem.InvoQty) *
                                                Bus_DelieryItem.TaxPrice), 0)
                            FROM Bus_Deliery
                                     RIGHT JOIN Bus_DelieryItem ON Bus_DelieryItem.Pid = Bus_Deliery.id
                            where Bus_Deliery.Groupid = #{key}
                              and Bus_DelieryItem.Quantity
                                &gt; Bus_DelieryItem.InvoQty
                              and Bus_DelieryItem.InvoClosed = 0
                              and Bus_DelieryItem.DisannulMark = 0
                              and Bus_DelieryItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新销售发票结余额-->
    <update id="updateWorkgroupBusInvoRemAmt">
        update App_Workgroup
        SET BusInvoRemAmt =(select COALESCE(sum(Bus_InvoiceItem.TaxAmount -
                                                (Bus_InvoiceItem.BillQty - Bus_InvoiceItem.Quantity) *
                                                Bus_InvoiceItem.TaxPrice), 0)
                            from Bus_Invoice
                                     right join Bus_InvoiceItem on Bus_InvoiceItem.Pid = Bus_Invoice.id
                            where Bus_Invoice.Groupid = #{key}
                              and Bus_InvoiceItem.BillQty
                                &gt; Bus_InvoiceItem.Quantity
                              and Bus_Invoice.DisannulMark = 0
                              and Bus_Invoice.Closed = 0
                              and Bus_Invoice.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--    刷新销售结转期末额-->
    <update id="updateWorkgroupBusAccoCloseAmt">
        update App_Workgroup
        SET BusAccoCloseAmt =0
        where id = #{key}
    </update>

    <!--    刷新销售结转本期额-->
    <update id="updateWorkgroupBusAccoNowAmt">
        update App_Workgroup
        SET BusAccoNowAmt =0
        where id = #{key}
    </update>

    <!--    刷新采购订单结余额-->
    <update id="updateWorkgroupBuyOrderRemAmt">
        update App_Workgroup
        SET BuyOrderRemAmt =(SELECT COALESCE(sum(Buy_Order.BillTaxAmount - Buy_Order.Prepayments), 0)
                             FROM Buy_Order
                             where Buy_Order.Groupid = #{key}
                               and Buy_Order.ItemCount
                                 &gt; Buy_Order.FinishCount
                               and Buy_Order.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新采购收货结余额 -->
    <update id="updateWorkgroupBuyFiniRemAmt">
        update App_Workgroup
        SET BuyFiniRemAmt =(SELECT COALESCE(sum(Buy_FinishingItem.TaxAmount -
                                                (Buy_FinishingItem.Quantity - Buy_FinishingItem.InvoQty) *
                                                Buy_FinishingItem.TaxPrice), 0)
                            FROM Buy_Finishing
                                     RIGHT JOIN Buy_FinishingItem ON Buy_FinishingItem.Pid = Buy_Finishing.id
                            where Buy_Finishing.Groupid = #{key}
                              and Buy_FinishingItem.Quantity
                                &gt; Buy_FinishingItem.InvoQty
                              and Buy_FinishingItem.InvoClosed = 0
                              and Buy_FinishingItem.DisannulMark = 0
                              and Buy_FinishingItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新采购发票结余额-->
    <update id="updateWorkgroupBuyInvoRemAmt">
        update App_Workgroup
        SET BuyInvoRemAmt =(select COALESCE(sum(Buy_InvoiceItem.TaxAmount -
                                                (Buy_InvoiceItem.BillQty - Buy_InvoiceItem.Quantity) *
                                                Buy_InvoiceItem.TaxPrice), 0)
                            from Buy_Invoice
                                     right join Buy_InvoiceItem on Buy_InvoiceItem.Pid = Buy_Invoice.id
                            where Buy_Invoice.Groupid = #{key}
                              and Buy_InvoiceItem.BillQty
                                &gt; Buy_InvoiceItem.Quantity
                              and Buy_Invoice.DisannulMark = 0
                              and Buy_Invoice.Closed = 0
                              and Buy_Invoice.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新采购结转期末额-->
    <update id="updateWorkgroupBuyAccoCloseAmt">
        update App_Workgroup
        SET BuyAccoCloseAmt =0
        where id = #{key}
    </update>

    <!--    刷新采购结转本期额-->
    <update id="updateWorkgroupBuyAccoNowAmt">
        update App_Workgroup
        SET BuyAccoNowAmt =0
        where id = #{key}
    </update>
</mapper>

