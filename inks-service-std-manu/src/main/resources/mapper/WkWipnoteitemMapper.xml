<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipnoteitemMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemPojo">
        <include refid="selectWkWipnoteitemVo"/>
        where Wk_WipNoteItem.id = #{key}
          and Wk_WipNoteItem.Tenantid = #{tid}
    </select>
    <sql id="selectWkWipnoteitemVo">
        select id,
               Pid,
               Wpid,
               WpCode,
               WpName,
               RowNum,
               PlanDate,
               Remark,
               InPcsQty,
               InSecQty,
               OutPcsQty,
               OutSecQty,
               Mr<PERSON><PERSON><PERSON><PERSON><PERSON>,
               Mr<PERSON><PERSON>ec<PERSON>ty,
               CompPcsQty,
               CompSecQty,
               SubQty,
               SubUnit,
               StartDate,
               EndDate,
               ItemWorker,
               EpibolePcsQty,
               EpiboleSecQty,
               LastWp,
               SpecJson,
               SpecPackJson,
               WorkParam,
               Lister,
               CreateDate,
               ModifyDate,
               StartPlan,
               Inspid,
               InspUid,
               InspResult,
               DisableIn,
               DisableOut,
               InWorker,
               OutWorker,
               TasksQty,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Wk_WipNoteItem
    </sql>
    <!--查询List-->
    <select id="getList" resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemPojo">
        <include refid="selectWkWipnoteitemVo"/>
        where Wk_WipNoteItem.Pid = #{Pid}
        and Wk_WipNoteItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemPojo">
        <include refid="selectWkWipnoteitemVo"/>
        where 1 = 1 and Wk_WipNoteItem.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_WipNoteItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_WipNoteItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
            and Wk_WipNoteItem.wpid like concat('%',
                #{SearchPojo.wpid}, '%')
        </if>
        <if test="SearchPojo.wpcode != null and SearchPojo.wpcode != ''">
            and Wk_WipNoteItem.wpcode like concat('%',
                #{SearchPojo.wpcode}, '%')
        </if>
        <if test="SearchPojo.wpname != null and SearchPojo.wpname != ''">
            and Wk_WipNoteItem.wpname like concat('%',
                #{SearchPojo.wpname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_WipNoteItem.remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
            and Wk_WipNoteItem.subunit like concat('%',
                #{SearchPojo.subunit}, '%')
        </if>
        <if test="SearchPojo.itemworker != null and SearchPojo.itemworker != ''">
            and Wk_WipNoteItem.itemworker like concat('%',
                #{SearchPojo.itemworker}, '%')
        </if>
        <if test="SearchPojo.specjson != null and SearchPojo.specjson != ''">
            and Wk_WipNoteItem.specjson like concat('%',
                #{SearchPojo.specjson}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Wk_WipNoteItem.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_WipNoteItem.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_WipNoteItem.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_WipNoteItem.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_WipNoteItem.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_WipNoteItem.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_WipNoteItem.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_WipNoteItem.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_WipNoteItem.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_WipNoteItem.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_WipNoteItem.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_WipNoteItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
                or Wk_WipNoteItem.Wpid like concat('%', #{SearchPojo.wpid}, '%')
            </if>
            <if test="SearchPojo.wpcode != null and SearchPojo.wpcode != ''">
                or Wk_WipNoteItem.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
            </if>
            <if test="SearchPojo.wpname != null and SearchPojo.wpname != ''">
                or Wk_WipNoteItem.WpName like concat('%', #{SearchPojo.wpname}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_WipNoteItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.subunit != null and SearchPojo.subunit != ''">
                or Wk_WipNoteItem.SubUnit like concat('%', #{SearchPojo.subunit}, '%')
            </if>
            <if test="SearchPojo.itemworker != null and SearchPojo.itemworker != ''">
                or Wk_WipNoteItem.ItemWorker like concat('%', #{SearchPojo.itemworker}, '%')
            </if>
            <if test="SearchPojo.specjson != null and SearchPojo.specjson != ''">
                or Wk_WipNoteItem.SpecJson like concat('%', #{SearchPojo.specjson}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Wk_WipNoteItem.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_WipNoteItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_WipNoteItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_WipNoteItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_WipNoteItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_WipNoteItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_WipNoteItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_WipNoteItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_WipNoteItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_WipNoteItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_WipNoteItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>



    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_WipNoteItem(id, Pid, Wpid, WpCode, WpName, RowNum, PlanDate, Remark, InPcsQty, InSecQty, OutPcsQty, OutSecQty, MrbPcsQty, MrbSecQty, CompPcsQty, CompSecQty, SubQty, SubUnit, StartDate, EndDate, ItemWorker, EpibolePcsQty, EpiboleSecQty, LastWp, SpecJson, SpecPackJson, WorkParam, Lister, CreateDate, ModifyDate, StartPlan, Inspid, InspUid, InspResult, DisableIn, DisableOut, InWorker, OutWorker, TasksQty, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{wpid}, #{wpcode}, #{wpname}, #{rownum}, #{plandate}, #{remark}, #{inpcsqty}, #{insecqty}, #{outpcsqty}, #{outsecqty}, #{mrbpcsqty}, #{mrbsecqty}, #{comppcsqty}, #{compsecqty}, #{subqty}, #{subunit}, #{startdate}, #{enddate}, #{itemworker}, #{epibolepcsqty}, #{epibolesecqty}, #{lastwp}, #{specjson}, #{specpackjson}, #{workparam}, #{lister}, #{createdate}, #{modifydate}, #{startplan}, #{inspid}, #{inspuid}, #{inspresult}, #{disablein}, #{disableout}, #{inworker}, #{outworker}, #{tasksqty}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipNoteItem
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="wpid != null">
                Wpid = #{wpid},
            </if>
            <if test="wpcode != null">
                WpCode = #{wpcode},
            </if>
            <if test="wpname != null">
                WpName = #{wpname},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            <if test="inpcsqty != null">
                InPcsQty = #{inpcsqty},
            </if>
            <if test="insecqty != null">
                InSecQty = #{insecqty},
            </if>
            <if test="outpcsqty != null">
                OutPcsQty = #{outpcsqty},
            </if>
            <if test="outsecqty != null">
                OutSecQty = #{outsecqty},
            </if>
            <if test="mrbpcsqty != null">
                MrbPcsQty = #{mrbpcsqty},
            </if>
            <if test="mrbsecqty != null">
                MrbSecQty = #{mrbsecqty},
            </if>
            <if test="comppcsqty != null">
                CompPcsQty = #{comppcsqty},
            </if>
            <if test="compsecqty != null">
                CompSecQty = #{compsecqty},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="subunit != null">
                SubUnit = #{subunit},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="enddate != null">
                EndDate = #{enddate},
            </if>
            <if test="itemworker != null">
                ItemWorker = #{itemworker},
            </if>
            <if test="epibolepcsqty != null">
                EpibolePcsQty = #{epibolepcsqty},
            </if>
            <if test="epibolesecqty != null">
                EpiboleSecQty = #{epibolesecqty},
            </if>
            <if test="lastwp != null">
                LastWp = #{lastwp},
            </if>
            <if test="specjson != null">
                SpecJson = #{specjson},
            </if>
            <if test="specpackjson != null">
                SpecPackJson = #{specpackjson},
            </if>
            <if test="workparam != null">
                WorkParam = #{workparam},
            </if>
            <if test="lister != null">
                Lister = #{lister},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="startplan != null">
                StartPlan = #{startplan},
            </if>
            <if test="inspid != null">
                Inspid = #{inspid},
            </if>
            <if test="inspuid != null">
                InspUid = #{inspuid},
            </if>
            <if test="inspresult != null">
                InspResult = #{inspresult},
            </if>
            <if test="disablein != null">
                DisableIn = #{disablein},
            </if>
            <if test="disableout != null">
                DisableOut = #{disableout},
            </if>
            <if test="inworker != null ">
                InWorker = #{inworker},
            </if>
            <if test="outworker != null ">
                OutWorker = #{outworker},
            </if>
            <if test="tasksqty != null">
                TasksQty = #{tasksqty},
            </if>
            <if test="custom1 != null">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 = #{custom10},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_WipNoteItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询List-->
    <select id="getListByWpid" resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemPojo">
        <include refid="selectWkWipnoteitemVo"/>
        where Wk_WipNoteItem.Wpid = #{Wpid}
          and Wk_WipNoteItem.Pid = #{Pid}
          and Wk_WipNoteItem.Tenantid = #{tid}
        order by RowNum
    </select>
    <!--查询List-->
    <select id="getRemListByWpid" resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemPojo">
        <include refid="selectWkWipnoteitemVo"/>
        where Wk_WipNoteItem.Wpid = #{Wpid}
          and Wk_WipNoteItem.Pid = #{Pid}
        <if test="qty != null">
            and Wk_WipNoteItem.InPcsQty <![CDATA[<]]> #{qty}
        </if>
        and Wk_WipNoteItem.Tenantid = #{tid}
        order by RowNum
    </select>
    <select id="getRemListByWpid2" resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemPojo">
        <include refid="selectWkWipnoteitemVo"/>
        where Wk_WipNoteItem.Wpid = #{Wpid}
          and Wk_WipNoteItem.Pid = #{Pid}
        <if test="qty != null">
            and Wk_WipNoteItem.InPcsQty <![CDATA[<=]]> #{qty}
        </if>
        and Wk_WipNoteItem.Tenantid = #{tid}
        order by RowNum
    </select>
    <!--查询List-->
    <select id="getEntityByRownum" resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemPojo">
        <include refid="selectWkWipnoteitemVo"/>
        where Wk_WipNoteItem.Pid = #{Pid}
          and Wk_WipNoteItem.RowNum = #{Rownum}
          and Wk_WipNoteItem.Tenantid = #{tid}
        Limit 1
    </select>

    <!--查询List-->
    <select id="getPrevPostEntityByRownum" resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemPojo">
        select Wk_WipNoteItem.id,
               Wk_WipNoteItem.Pid,
               Wk_WipNoteItem.Wpid,
               Wk_WipNoteItem.WpCode,
               Wk_WipNoteItem.WpName,
               Wk_WipNoteItem.RowNum,
               Wk_WipNoteItem.PlanDate,
               Wk_WipNoteItem.Remark,
               Wk_WipNoteItem.InPcsQty,
               Wk_WipNoteItem.InSecQty,
               Wk_WipNoteItem.OutPcsQty,
               Wk_WipNoteItem.OutSecQty,
               Wk_WipNoteItem.MrbPcsQty,
               Wk_WipNoteItem.MrbSecQty,
               Wk_WipNoteItem.CompPcsQty,
               Wk_WipNoteItem.CompSecQty,
               Wk_WipNoteItem.SubQty,
               Wk_WipNoteItem.SubUnit,
               Wk_WipNoteItem.StartDate,
               Wk_WipNoteItem.EndDate,
               Wk_WipNoteItem.ItemWorker,
               Wk_WipNoteItem.EpibolePcsQty,
               Wk_WipNoteItem.EpiboleSecQty,
               Wk_WipNoteItem.LastWp,
               Wk_WipNoteItem.SpecJson,
               Wk_WipNoteItem.WorkParam,
               Wk_WipNoteItem.Lister,
               Wk_WipNoteItem.CreateDate,
               Wk_WipNoteItem.ModifyDate,
               Wk_WipNoteItem.DisableIn,
               Wk_WipNoteItem.DisableOut,
               Wk_WipNoteItem.InWorker,
               Wk_WipNoteItem.OutWorker,
               Wk_WipNoteItem.TasksQty,
               Wk_WipNoteItem.Custom1,
               Wk_WipNoteItem.Custom2,
               Wk_WipNoteItem.Custom3,
               Wk_WipNoteItem.Custom4,
               Wk_WipNoteItem.Custom5,
               Wk_WipNoteItem.Custom6,
               Wk_WipNoteItem.Custom7,
               Wk_WipNoteItem.Custom8,
               Wk_WipNoteItem.Custom9,
               Wk_WipNoteItem.Custom10,
               Wk_WipNoteItem.Tenantid,
               Wk_WipNoteItem.Revision
        From Wk_WipNoteItem
                 LEFT JOIN Wk_Process ON Wk_WipNoteItem.Wpid = Wk_Process.id
        where Wk_WipNoteItem.Pid = #{Pid}
          and Wk_WipNoteItem.RowNum &lt;= #{Rownum}
          and Wk_Process.Post = 1
          and Wk_WipNoteItem.Tenantid = #{tid}
        Order by Rownum Desc
        Limit 1
    </select>

    <!--查询List-->
    <select id="getNextPostEntityByRownum" resultType="inks.service.std.manu.domain.pojo.WkWipnoteitemPojo">
        select Wk_WipNoteItem.id,
               Wk_WipNoteItem.Pid,
               Wk_WipNoteItem.Wpid,
               Wk_WipNoteItem.WpCode,
               Wk_WipNoteItem.WpName,
               Wk_WipNoteItem.RowNum,
               Wk_WipNoteItem.PlanDate,
               Wk_WipNoteItem.Remark,
               Wk_WipNoteItem.InPcsQty,
               Wk_WipNoteItem.InSecQty,
               Wk_WipNoteItem.OutPcsQty,
               Wk_WipNoteItem.OutSecQty,
               Wk_WipNoteItem.MrbPcsQty,
               Wk_WipNoteItem.MrbSecQty,
               Wk_WipNoteItem.CompPcsQty,
               Wk_WipNoteItem.CompSecQty,
               Wk_WipNoteItem.SubQty,
               Wk_WipNoteItem.SubUnit,
               Wk_WipNoteItem.StartDate,
               Wk_WipNoteItem.EndDate,
               Wk_WipNoteItem.ItemWorker,
               Wk_WipNoteItem.EpibolePcsQty,
               Wk_WipNoteItem.EpiboleSecQty,
               Wk_WipNoteItem.LastWp,
               Wk_WipNoteItem.SpecJson,
               Wk_WipNoteItem.WorkParam,
               Wk_WipNoteItem.Lister,
               Wk_WipNoteItem.CreateDate,
               Wk_WipNoteItem.ModifyDate,
               Wk_WipNoteItem.DisableIn,
               Wk_WipNoteItem.DisableOut,
               Wk_WipNoteItem.InWorker,
               Wk_WipNoteItem.OutWorker,
               Wk_WipNoteItem.TasksQty,
               Wk_WipNoteItem.Custom1,
               Wk_WipNoteItem.Custom2,
               Wk_WipNoteItem.Custom3,
               Wk_WipNoteItem.Custom4,
               Wk_WipNoteItem.Custom5,
               Wk_WipNoteItem.Custom6,
               Wk_WipNoteItem.Custom7,
               Wk_WipNoteItem.Custom8,
               Wk_WipNoteItem.Custom9,
               Wk_WipNoteItem.Custom10,
               Wk_WipNoteItem.Tenantid,
               Wk_WipNoteItem.Revision
        From Wk_WipNoteItem
                 LEFT JOIN Wk_Process ON Wk_WipNoteItem.Wpid = Wk_Process.id
        where Wk_WipNoteItem.Pid = #{Pid}
          and Wk_WipNoteItem.RowNum >= #{Rownum}
          and Wk_Process.Post = 1
          and Wk_WipNoteItem.Tenantid = #{tid}
        Order by Rownum
        Limit 1
    </select>


    <!--    InWorker是在前端传入quickPojo.getWorker()才会赋值 -->
    <update id="updateInQty">
        UPDATE Wk_WipNoteItem
        SET InPcsQty = IFNULL(InPcsQty, 0) + IFNULL(#{pcsqty}, 0),
            InSecQty = IFNULL(InSecQty, 0) + IFNULL(#{secqty}, 0)
        <if test="startdate != null">
            ,StartDate = #{startdate}
        </if>
        <if test="inworker != null">
            ,InWorker = #{inworker}
        </if>
        WHERE id = #{key}
          AND Tenantid = #{tid}
    </update>

    <update id="updateOutQty">
        UPDATE Wk_WipNoteItem
        SET OutPcsQty = IFNULL(OutPcsQty, 0) + IFNULL(#{pcsqty}, 0),
            OutSecQty = IFNULL(OutSecQty, 0) + IFNULL(#{secqty}, 0),
            EndDate=#{enddate}
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <update id="updateMrbQty">
        UPDATE Wk_WipNoteItem
        SET MrbPcsQty=COALESCE((SELECT Sum(PcsQty)
                                FROM Wk_WipQty
                                where WipItemid = #{key}
                                  and Tenantid = #{tid}
                                  and Direction = '报废'), 0),
            MrbSecQty=COALESCE((SELECT Sum(SecQty)
                                FROM Wk_WipQty
                                where WipItemid = #{key}
                                  and Tenantid = #{tid}
                                  and Direction = '报废'), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <update id="updateJobPcsQty">
        UPDATE Wk_WipNote
        SET JobPcsQty=COALESCE((SELECT Sum(PcsQty)
                                FROM Wk_WipQty
                                where WipItemid = #{itemid}
                                  and Tenantid = #{tid}
                                  and Direction = '出组'), 0)
        where id = #{pid}
          and Tenantid = #{tid}
    </update>

    <update id="updateOutQtyAndOutWorker">
        UPDATE Wk_WipNoteItem
        SET OutPcsQty = IFNULL(OutPcsQty, 0) + IFNULL(#{pcsqty}, 0),
        OutSecQty = IFNULL(OutSecQty, 0) + IFNULL(#{secqty}, 0),
            EndDate=#{enddate}
        <if test="outworker != null">
            ,
                OutWorker=#{outworker}
        </if>
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <update id="updateStartDate">
        UPDATE Wk_WipNoteItem
        SET StartDate=#{startdate}
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <select id="hasNextByRownum" resultType="int">
        select count(1)
        From Wk_WipNoteItem
                 LEFT JOIN Wk_Process ON Wk_WipNoteItem.Wpid = Wk_Process.id
        where Wk_WipNoteItem.Pid = #{Pid}
          and Wk_WipNoteItem.RowNum >= #{Rownum}
          and Wk_Process.Post = 1
          and Wk_WipNoteItem.Tenantid = #{tid}
        Order by Wk_WipNoteItem.Rownum
        Limit 1
    </select>

    <update id="updateWipItemRowNum">
        UPDATE Wk_WipNoteItem
        SET RowNum=#{maxWipItemRowNum}
        where id = #{maxWipItemId}
          and Tenantid = #{tid}
    </update>

    <update id="update3Field">
            UPDATE Wk_WipNoteItem
            SET outpcsqty  = #{wkWipnoteitemPojo.outpcsqty},
                rownum     = #{wkWipnoteitemPojo.rownum},
                modifydate = #{wkWipnoteitemPojo.modifydate}
            WHERE id = #{wkWipnoteitemPojo.id}
    </update>


    <delete id="batchDeleteByIds">
        delete
        from Wk_WipNoteItem
        where id in
        <foreach collection="deletedIds" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkKeyMark" resultType="int">
        select COALESCE(KeyMark, 0) from Wk_Process where id = #{wpid} and Tenantid = #{tid}
    </select>

    <select id="countWipItemWpid" resultType="int">
        select count(*)
        from Wk_WipNoteItem
        where Wpid = #{wpid} and Pid = #{wipid}
        and Tenantid = #{tid}
    </select>

    <update id="updateStartdateAndInworker">
        UPDATE Wk_WipNoteItem
        SET
        StartDate=#{startdate},
        PlanDate=#{plandate},
        InWorker=#{inworker}
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>
</mapper>

