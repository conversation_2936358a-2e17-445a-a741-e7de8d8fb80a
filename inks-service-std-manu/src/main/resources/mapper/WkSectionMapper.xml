<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkSectionMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkSectionPojo">
        <include refid="selectbillVo"/>
        where Wk_Section.id = #{key} and Wk_Section.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select
id, SectType, SectCode, SectName, FlowDesc, Summary, EnabledMark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, ItemCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Wk_Section
    </sql>
    <sql id="selectdetailVo">
        select
               Wk_Section.CreateBy,
               Wk_Section.Lister,
               Wk_SectionItem.*
        from Wk_SectionItem left join Wk_Section on Wk_Section.id = Wk_SectionItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkSectionitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_Section.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_Section.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.secttype != null ">
            and Wk_Section.SectType like concat('%', #{SearchPojo.secttype}, '%')
        </if>
        <if test="SearchPojo.sectcode != null ">
            and Wk_Section.SectCode like concat('%', #{SearchPojo.sectcode}, '%')
        </if>
        <if test="SearchPojo.sectname != null ">
            and Wk_Section.SectName like concat('%', #{SearchPojo.sectname}, '%')
        </if>
        <if test="SearchPojo.flowdesc != null ">
            and Wk_Section.FlowDesc like concat('%', #{SearchPojo.flowdesc}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_Section.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_Section.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_Section.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_Section.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_Section.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_Section.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_Section.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_Section.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_Section.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_Section.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_Section.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_Section.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_Section.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_Section.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_Section.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_Section.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.secttype != null ">
                or Wk_Section.SectType like concat('%', #{SearchPojo.secttype}, '%')
            </if>
            <if test="SearchPojo.sectcode != null ">
                or Wk_Section.SectCode like concat('%', #{SearchPojo.sectcode}, '%')
            </if>
            <if test="SearchPojo.sectname != null ">
                or Wk_Section.SectName like concat('%', #{SearchPojo.sectname}, '%')
            </if>
            <if test="SearchPojo.flowdesc != null ">
                or Wk_Section.FlowDesc like concat('%', #{SearchPojo.flowdesc}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_Section.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_Section.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_Section.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_Section.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_Section.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_Section.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_Section.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_Section.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_Section.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_Section.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_Section.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_Section.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_Section.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_Section.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_Section.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_Section.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkSectionPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_Section.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_Section.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.secttype != null ">
            and Wk_Section.SectType like concat('%', #{SearchPojo.secttype}, '%')
        </if>
        <if test="SearchPojo.sectcode != null ">
            and Wk_Section.SectCode like concat('%', #{SearchPojo.sectcode}, '%')
        </if>
        <if test="SearchPojo.sectname != null ">
            and Wk_Section.SectName like concat('%', #{SearchPojo.sectname}, '%')
        </if>
        <if test="SearchPojo.flowdesc != null ">
            and Wk_Section.FlowDesc like concat('%', #{SearchPojo.flowdesc}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_Section.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_Section.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_Section.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_Section.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_Section.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_Section.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_Section.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_Section.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_Section.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_Section.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_Section.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_Section.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_Section.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_Section.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_Section.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_Section.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.secttype != null ">
                or Wk_Section.SectType like concat('%', #{SearchPojo.secttype}, '%')
            </if>
            <if test="SearchPojo.sectcode != null ">
                or Wk_Section.SectCode like concat('%', #{SearchPojo.sectcode}, '%')
            </if>
            <if test="SearchPojo.sectname != null ">
                or Wk_Section.SectName like concat('%', #{SearchPojo.sectname}, '%')
            </if>
            <if test="SearchPojo.flowdesc != null ">
                or Wk_Section.FlowDesc like concat('%', #{SearchPojo.flowdesc}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_Section.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_Section.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_Section.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_Section.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_Section.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_Section.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_Section.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_Section.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_Section.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_Section.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_Section.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_Section.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_Section.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_Section.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_Section.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_Section.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_Section(id, SectType, SectCode, SectName, FlowDesc, Summary, EnabledMark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, ItemCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{secttype}, #{sectcode}, #{sectname}, #{flowdesc}, #{summary}, #{enabledmark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{itemcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_Section
        <set>
            <if test="secttype != null ">
            SectType =#{secttype},
        </if>
            <if test="sectcode != null ">
            SectCode =#{sectcode},
        </if>
            <if test="sectname != null ">
            SectName =#{sectname},
        </if>
            <if test="flowdesc != null ">
            FlowDesc =#{flowdesc},
        </if>
            <if test="summary != null ">
            Summary =#{summary},
        </if>
            <if test="enabledmark != null">
            EnabledMark =#{enabledmark},
        </if>
            <if test="rownum != null">
            RowNum =#{rownum},
        </if>
            <if test="createby != null ">
            CreateBy =#{createby},
        </if>
            <if test="createbyid != null ">
            CreateByid =#{createbyid},
        </if>
            <if test="createdate != null">
            CreateDate =#{createdate},
        </if>
            <if test="lister != null ">
            Lister =#{lister},
        </if>
            <if test="listerid != null ">
            Listerid =#{listerid},
        </if>
            <if test="modifydate != null">
            ModifyDate =#{modifydate},
        </if>
            <if test="itemcount != null">
            ItemCount =#{itemcount},
        </if>
            <if test="custom1 != null ">
            Custom1 =#{custom1},
        </if>
            <if test="custom2 != null ">
            Custom2 =#{custom2},
        </if>
            <if test="custom3 != null ">
            Custom3 =#{custom3},
        </if>
            <if test="custom4 != null ">
            Custom4 =#{custom4},
        </if>
            <if test="custom5 != null ">
            Custom5 =#{custom5},
        </if>
            <if test="custom6 != null ">
            Custom6 =#{custom6},
        </if>
            <if test="custom7 != null ">
            Custom7 =#{custom7},
        </if>
            <if test="custom8 != null ">
            Custom8 =#{custom8},
        </if>
            <if test="custom9 != null ">
            Custom9 =#{custom9},
        </if>
            <if test="custom10 != null ">
            Custom10 =#{custom10},
        </if>
            <if test="tenantname != null ">
            TenantName =#{tenantname},
        </if>
        Revision=Revision+1
    </set>
    where id = #{id} and Tenantid =#{tenantid}
</update>

        <!--通过主键删除-->
<delete id="delete">
delete from Wk_Section where id = #{key} and Tenantid=#{tid}
</delete>
        <!--查询DelListIds-->
<select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.manu.domain.pojo.WkSectionPojo">
select
id
from Wk_SectionItem
where Pid = #{id}
<if test="item !=null and item.size()>0">
    and id not in
    <foreach collection="item" open="(" close=")" separator="," item="item">
        <if test="item.id != null">
            #{item.id}
        </if>
        <if test="item.id == null">
            ''
        </if>
    </foreach>
</if>
</select>

    <select id="getListAll" resultType="inks.service.std.manu.domain.pojo.WkSectionPojo">
        <include refid="selectbillVo"/>
        where Tenantid= #{tid}
        <if test="key != null and key != ''">
                and id = #{key}
        </if>
    </select>

    <select id="getItemCiteBillName" resultType="java.lang.String">
        (SELECT 'Aps排程' as billname From Aps_Schedule where Sectid=#{key} and Tenantid= #{tid} LIMIT 1)
        UNION
        (SELECT '工位' as billname From Wk_Station where Sectionid=#{key} and Tenantid= #{tid} LIMIT 1)
    </select>
</mapper>

