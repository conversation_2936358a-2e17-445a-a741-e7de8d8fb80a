<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.manu.mapper.WkWipgroupMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.manu.domain.pojo.WkWipgroupPojo">
        select
          id, WipName, WipInfo, WipIcon, WipManager, EnabledMark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision
        from Wk_WipGroup
        where Wk_WipGroup.id = #{key} and Wk_WipGroup.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
          id, WipName, WipInfo, WipIcon, WipManager, EnabledMark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Wk_WipGroup
    </sql>
    <sql id="selectdetailVo">
         select
          id, WipName, WipInfo, WipIcon, WipManager, EnabledMark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Wk_WipGroup
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkWipgroupitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 and Wk_WipGroup.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
         <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_WipGroup.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>

             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.wipname != null and SearchPojo.wipname != ''">
   and Wk_WipGroup.wipname like concat('%', #{SearchPojo.wipname}, '%')
</if>
<if test="SearchPojo.wipinfo != null and SearchPojo.wipinfo != ''">
   and Wk_WipGroup.wipinfo like concat('%', #{SearchPojo.wipinfo}, '%')
</if>
<if test="SearchPojo.wipicon != null and SearchPojo.wipicon != ''">
   and Wk_WipGroup.wipicon like concat('%', #{SearchPojo.wipicon}, '%')
</if>
<if test="SearchPojo.wipmanager != null and SearchPojo.wipmanager != ''">
   and Wk_WipGroup.wipmanager like concat('%', #{SearchPojo.wipmanager}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   and Wk_WipGroup.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   and Wk_WipGroup.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   and Wk_WipGroup.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   and Wk_WipGroup.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Wk_WipGroup.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Wk_WipGroup.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Wk_WipGroup.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Wk_WipGroup.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Wk_WipGroup.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Wk_WipGroup.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Wk_WipGroup.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Wk_WipGroup.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Wk_WipGroup.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Wk_WipGroup.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
     and (1=0 
<if test="SearchPojo.wipname != null and SearchPojo.wipname != ''">
   or Wk_WipGroup.WipName like concat('%', #{SearchPojo.wipname}, '%')
</if>
<if test="SearchPojo.wipinfo != null and SearchPojo.wipinfo != ''">
   or Wk_WipGroup.WipInfo like concat('%', #{SearchPojo.wipinfo}, '%')
</if>
<if test="SearchPojo.wipicon != null and SearchPojo.wipicon != ''">
   or Wk_WipGroup.WipIcon like concat('%', #{SearchPojo.wipicon}, '%')
</if>
<if test="SearchPojo.wipmanager != null and SearchPojo.wipmanager != ''">
   or Wk_WipGroup.WipManager like concat('%', #{SearchPojo.wipmanager}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   or Wk_WipGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   or Wk_WipGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Wk_WipGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or Wk_WipGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Wk_WipGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Wk_WipGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Wk_WipGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Wk_WipGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Wk_WipGroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Wk_WipGroup.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Wk_WipGroup.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Wk_WipGroup.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Wk_WipGroup.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Wk_WipGroup.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
)
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.manu.domain.pojo.WkWipgroupPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 and Wk_WipGroup.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
         <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_WipGroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>

             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.wipname != null and SearchPojo.wipname != ''">
   and Wk_WipGroup.WipName like concat('%', #{SearchPojo.wipname}, '%')
</if>
<if test="SearchPojo.wipinfo != null and SearchPojo.wipinfo != ''">
   and Wk_WipGroup.WipInfo like concat('%', #{SearchPojo.wipinfo}, '%')
</if>
<if test="SearchPojo.wipicon != null and SearchPojo.wipicon != ''">
   and Wk_WipGroup.WipIcon like concat('%', #{SearchPojo.wipicon}, '%')
</if>
<if test="SearchPojo.wipmanager != null and SearchPojo.wipmanager != ''">
   and Wk_WipGroup.WipManager like concat('%', #{SearchPojo.wipmanager}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   and Wk_WipGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   and Wk_WipGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   and Wk_WipGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   and Wk_WipGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Wk_WipGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Wk_WipGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Wk_WipGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Wk_WipGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Wk_WipGroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Wk_WipGroup.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Wk_WipGroup.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Wk_WipGroup.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Wk_WipGroup.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Wk_WipGroup.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="thor">
     and (1=0 
<if test="SearchPojo.wipname != null and SearchPojo.wipname != ''">
   or Wk_WipGroup.WipName like concat('%', #{SearchPojo.wipname}, '%')
</if>
<if test="SearchPojo.wipinfo != null and SearchPojo.wipinfo != ''">
   or Wk_WipGroup.WipInfo like concat('%', #{SearchPojo.wipinfo}, '%')
</if>
<if test="SearchPojo.wipicon != null and SearchPojo.wipicon != ''">
   or Wk_WipGroup.WipIcon like concat('%', #{SearchPojo.wipicon}, '%')
</if>
<if test="SearchPojo.wipmanager != null and SearchPojo.wipmanager != ''">
   or Wk_WipGroup.WipManager like concat('%', #{SearchPojo.wipmanager}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   or Wk_WipGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   or Wk_WipGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Wk_WipGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or Wk_WipGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Wk_WipGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Wk_WipGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Wk_WipGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Wk_WipGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Wk_WipGroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Wk_WipGroup.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Wk_WipGroup.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Wk_WipGroup.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Wk_WipGroup.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Wk_WipGroup.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
)
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_WipGroup(id, WipName, WipInfo, WipIcon, WipManager, EnabledMark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{wipname}, #{wipinfo}, #{wipicon}, #{wipmanager}, #{enabledmark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_WipGroup
        <set>
            <if test="wipname != null ">
                WipName =#{wipname},
            </if>
            <if test="wipinfo != null ">
                WipInfo =#{wipinfo},
            </if>
            <if test="wipicon != null ">
                WipIcon =#{wipicon},
            </if>
            <if test="wipmanager != null ">
                WipManager =#{wipmanager},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Wk_WipGroup where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.manu.domain.pojo.WkWipgroupPojo">
        select
          id
        from Wk_WipGroupItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
    </select>

</mapper>

